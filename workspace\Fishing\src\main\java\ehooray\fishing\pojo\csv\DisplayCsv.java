package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("DisplayTable.xlsx")
public class DisplayCsv extends ACsv<DisplayCsv.Row>
{
    public enum Type
    {
        Quest(140000),
        Shop(140100),
        Gacha(140300),
        Exchange(140400)
        ;

        protected int id;
        private Type(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<Type> valueOf(int id)
        {
            return Arrays.stream(Type.values()).filter(x-> x.getId() == id).findFirst();
        }

    }


    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Type")
        protected int type;

        @ExcelCellName("MapId")
        protected int mapId;

        @ExcelCellName("Schedule")
        protected int schedule;

        @Override
        public int getId()
        {
            return id;
        }

        public int getType()
        {
            return type;
        }

        public int getMapId()
        {
            return mapId;
        }

        public int getSchedule()
        {
            return schedule;
        }


    }


    // another utility function at here
    // <Integer, Integer, List<Row>> mean <type, mapId, rows>
    protected Table<Integer, Integer, List<Row>> typeMapIdMap;

    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();

        Collection<Row> rows = getRows();
        typeMapIdMap = HashBasedTable.create();
        for(Row row : rows)
        {
            if(!typeMapIdMap.contains(row.getType(), row.getMapId()))
                typeMapIdMap.put(row.getType(), row.getMapId(), new ArrayList<DisplayCsv.Row>());
            
            typeMapIdMap.get(row.getType(), row.getMapId()).add(row);
        }
    }

    public List<Row> findByTypeAndMapId(int type, int mapId)
    {
        List<Row> result = typeMapIdMap.get(type, mapId); 
        return (result != null) ? result : List.of();
    }
}
