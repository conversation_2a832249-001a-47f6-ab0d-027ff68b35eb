// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/fishitem/SellFishResult.proto

package ehooray.fishing.dto.proto.response.fishitem;

public final class SellFishResultProto {
  private SellFishResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SellFishResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.fishitem.SellFishResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> 
        getFishItemsList();
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItems(int index);
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    int getFishItemsCount();
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
        getFishItemsOrBuilderList();
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemsOrBuilder(
        int index);

    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getProduceItemsList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index);
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    int getProduceItemsCount();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.fishitem.SellFishResult}
   */
  public static final class SellFishResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.fishitem.SellFishResult)
      SellFishResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SellFishResult.newBuilder() to construct.
    private SellFishResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SellFishResult() {
      fishItems_ = java.util.Collections.emptyList();
      produceItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SellFishResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.internal_static_response_fishitem_SellFishResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.internal_static_response_fishitem_SellFishResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.class, ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.Builder.class);
    }

    public static final int FISHITEMS_FIELD_NUMBER = 1;
    private java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> fishItems_;
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> getFishItemsList() {
      return fishItems_;
    }
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
        getFishItemsOrBuilderList() {
      return fishItems_;
    }
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    @java.lang.Override
    public int getFishItemsCount() {
      return fishItems_.size();
    }
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItems(int index) {
      return fishItems_.get(index);
    }
    /**
     * <code>repeated .model.FishItem fishItems = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemsOrBuilder(
        int index) {
      return fishItems_.get(index);
    }

    public static final int PRODUCEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_;
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public int getProduceItemsCount() {
      return produceItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
      return produceItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index) {
      return produceItems_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.fishitem.SellFishResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.fishitem.SellFishResult)
        ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.internal_static_response_fishitem_SellFishResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.internal_static_response_fishitem_SellFishResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.class, ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFishItemsFieldBuilder();
          getProduceItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (fishItemsBuilder_ == null) {
          fishItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fishItemsBuilder_.clear();
        }
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.internal_static_response_fishitem_SellFishResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult build() {
        ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult buildPartial() {
        ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult result = new ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult(this);
        int from_bitField0_ = bitField0_;
        if (fishItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fishItems_ = java.util.Collections.unmodifiableList(fishItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fishItems_ = fishItems_;
        } else {
          result.fishItems_ = fishItemsBuilder_.build();
        }
        if (produceItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            produceItems_ = java.util.Collections.unmodifiableList(produceItems_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.produceItems_ = produceItems_;
        } else {
          result.produceItems_ = produceItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> fishItems_ =
        java.util.Collections.emptyList();
      private void ensureFishItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fishItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.FishItemProto.FishItem>(fishItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> fishItemsBuilder_;

      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> getFishItemsList() {
        if (fishItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fishItems_);
        } else {
          return fishItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public int getFishItemsCount() {
        if (fishItemsBuilder_ == null) {
          return fishItems_.size();
        } else {
          return fishItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItems(int index) {
        if (fishItemsBuilder_ == null) {
          return fishItems_.get(index);
        } else {
          return fishItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder setFishItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (fishItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishItemsIsMutable();
          fishItems_.set(index, value);
          onChanged();
        } else {
          fishItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder setFishItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (fishItemsBuilder_ == null) {
          ensureFishItemsIsMutable();
          fishItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          fishItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder addFishItems(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (fishItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishItemsIsMutable();
          fishItems_.add(value);
          onChanged();
        } else {
          fishItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder addFishItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (fishItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishItemsIsMutable();
          fishItems_.add(index, value);
          onChanged();
        } else {
          fishItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder addFishItems(
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (fishItemsBuilder_ == null) {
          ensureFishItemsIsMutable();
          fishItems_.add(builderForValue.build());
          onChanged();
        } else {
          fishItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder addFishItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (fishItemsBuilder_ == null) {
          ensureFishItemsIsMutable();
          fishItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          fishItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder addAllFishItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItem> values) {
        if (fishItemsBuilder_ == null) {
          ensureFishItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fishItems_);
          onChanged();
        } else {
          fishItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder clearFishItems() {
        if (fishItemsBuilder_ == null) {
          fishItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fishItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public Builder removeFishItems(int index) {
        if (fishItemsBuilder_ == null) {
          ensureFishItemsIsMutable();
          fishItems_.remove(index);
          onChanged();
        } else {
          fishItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder getFishItemsBuilder(
          int index) {
        return getFishItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemsOrBuilder(
          int index) {
        if (fishItemsBuilder_ == null) {
          return fishItems_.get(index);  } else {
          return fishItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
           getFishItemsOrBuilderList() {
        if (fishItemsBuilder_ != null) {
          return fishItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fishItems_);
        }
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder addFishItemsBuilder() {
        return getFishItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder addFishItemsBuilder(
          int index) {
        return getFishItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishItem fishItems = 1;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder> 
           getFishItemsBuilderList() {
        return getFishItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
          getFishItemsFieldBuilder() {
        if (fishItemsBuilder_ == null) {
          fishItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder>(
                  fishItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fishItems_ = null;
        }
        return fishItemsBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_ =
        java.util.Collections.emptyList();
      private void ensureProduceItemsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          produceItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(produceItems_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> produceItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
        if (produceItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(produceItems_);
        } else {
          return produceItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public int getProduceItemsCount() {
        if (produceItemsBuilder_ == null) {
          return produceItems_.size();
        } else {
          return produceItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);
        } else {
          return produceItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.set(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addAllProduceItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, produceItems_);
          onChanged();
        } else {
          produceItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder clearProduceItems() {
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder removeProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.remove(index);
          onChanged();
        } else {
          produceItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
          int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);  } else {
          return produceItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getProduceItemsOrBuilderList() {
        if (produceItemsBuilder_ != null) {
          return produceItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(produceItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder() {
        return getProduceItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getProduceItemsBuilderList() {
        return getProduceItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getProduceItemsFieldBuilder() {
        if (produceItemsBuilder_ == null) {
          produceItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  produceItems_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          produceItems_ = null;
        }
        return produceItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.fishitem.SellFishResult)
    }

    // @@protoc_insertion_point(class_scope:response.fishitem.SellFishResult)
    private static final ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult();
    }

    public static ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SellFishResult>
        PARSER = new com.google.protobuf.AbstractParser<SellFishResult>() {
      @java.lang.Override
      public SellFishResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SellFishResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SellFishResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto.SellFishResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_fishitem_SellFishResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_fishitem_SellFishResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&response/fishitem/SellFishResult.proto" +
      "\022\021response.fishitem\032\024model/FishItem.prot" +
      "o\032\026model/PlayerItem.proto\"]\n\016SellFishRes" +
      "ult\022\"\n\tfishItems\030\001 \003(\0132\017.model.FishItem\022" +
      "\'\n\014produceItems\030\002 \003(\0132\021.model.PlayerItem" +
      "B\\\n+ehooray.fishing.dto.proto.response.f" +
      "ishitemB\023SellFishResultProtoH\002\252\002\025Fishing" +
      ".Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_fishitem_SellFishResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_fishitem_SellFishResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_fishitem_SellFishResult_descriptor,
        new java.lang.String[] { "FishItems", "ProduceItems", });
    ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
