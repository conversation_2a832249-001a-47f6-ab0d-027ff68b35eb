package ehooray.fishing.component.iab.unity;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Optional;

import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;

import ehooray.fishing.pojo.iab.unity.UnityIabReceiptResult;
import ehooray.fishing.pojo.iab.unity.UnityIabReceiptVerifyException;

@Component
public class AndroidIabReceiptVerifier implements IIabReceiptVerifier
{
	protected static final String KeyFactoryAlgorithm = "RSA";
	protected static final String SigNatureAlgorithm = "SHA1withRSA";

	protected PublicKey iabPublicKey;

	public enum ResultCode
	{
		Success(0),
		Cancel(1),
		Refund(2)
		;

		private int id;
	    private ResultCode(int id)
	    {
	        this.id = id;
	    }
	    public int getId()
	    {
	        return id;
	    }

	    public static Optional<ResultCode> valueOf(int id)
        {
            return Arrays.stream(ResultCode.values()).filter(x-> x.getId() == id).findFirst();
        }
	}

	@Override
    public UnityIabReceiptResult verify(String receiptStr) throws Exception
	{
		Receipt receipt = new ObjectMapper().readValue(receiptStr, Receipt.class);
		Payload receiptPayload = new ObjectMapper().readValue(receipt.Payload, Payload.class);
		ReceiptDetail receiptDetail = new ObjectMapper().readValue(receiptPayload.json, ReceiptDetail.class);

		UnityIabReceiptResult receiptResult = new UnityIabReceiptResult();
        // Note: when app not publish to player, the receipt won't have order id
        //       but when app is online, the order will show up in receipt.....
        if(Strings.isNullOrEmpty(receiptDetail.orderId))
        {
            // app not online case
            receiptResult.transactionId = receiptDetail.purchaseToken;
        }
        else
        {
            receiptResult.transactionId = receiptDetail.orderId;
        }
		
		if(receiptDetail.purchaseState != ResultCode.Success.getId())
			throw new UnityIabReceiptVerifyException(receiptResult.transactionId, receiptDetail.purchaseState,
			        "purchaseState != Success, currennt = " + receiptDetail.purchaseState);

		// Don't need check app bundle id like iOS verification, because android have only one public key

		Signature signature = Signature.getInstance(SigNatureAlgorithm);
		signature.initVerify(getPublicKey());
		signature.update(receiptPayload.json.getBytes());

		if(!signature.verify(Base64.decodeBase64(receiptPayload.signature)))
		    throw new UnityIabReceiptVerifyException(receiptResult.transactionId, receiptDetail.purchaseState,
		            "signature verify failed");

		// update purchase
		// Note: should save verified ReceiptDetail, or may saved hacked data

		LocalDateTime purchaseDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(receiptDetail.purchaseTime), ZoneId.systemDefault());
		
		// Note: there is no info to recognize android receipt is from sandbox or not.
		receiptResult.isTest = false;
		receiptResult.productId = receiptDetail.productId;
		receiptResult.purchaseDate = purchaseDate;
		receiptResult.receiptVerifyResultStr = receiptPayload.json;
		receiptResult.platformState = receiptDetail.purchaseState;

		return receiptResult;
	}

	protected PublicKey getPublicKey()
    {
		return iabPublicKey;
    }

    public void initial(String keyString) throws Exception
    {
    	if(Strings.isNullOrEmpty(keyString)) return;

		byte[] decodedKey = Base64.decodeBase64(keyString);
		KeyFactory keyFactory = KeyFactory.getInstance(KeyFactoryAlgorithm);
		iabPublicKey = keyFactory.generatePublic(new X509EncodedKeySpec(decodedKey));
    }

	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class ReceiptDetail
	{
		@JsonProperty public String orderId;
		@JsonProperty public String packageName;
		@JsonProperty public String productId;
		@JsonProperty public long purchaseTime;
		@JsonProperty public int purchaseState;
		@JsonProperty public String purchaseToken;

		public ReceiptDetail() {}
	}

	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class Payload
	{
		@JsonProperty public String json;
		@JsonProperty public String signature;

		public Payload() {}

	}// end class Payload


	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class Receipt
	{
		@JsonProperty public String TransactionID;
		@JsonProperty public String Payload;
		public Receipt() {}

	}// end class Receipt



}
