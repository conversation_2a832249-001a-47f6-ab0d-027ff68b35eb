// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/fishkit/ActiveFishKit.proto

package ehooray.fishing.dto.proto.request.fishkit;

public final class ActiveFishKitProto {
  private ActiveFishKitProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ActiveFishKitOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.fishkit.ActiveFishKit)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 fishKit = 1;</code>
     * @return The fishKit.
     */
    long getFishKit();
  }
  /**
   * Protobuf type {@code request.fishkit.ActiveFishKit}
   */
  public static final class ActiveFishKit extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.fishkit.ActiveFishKit)
      ActiveFishKitOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ActiveFishKit.newBuilder() to construct.
    private ActiveFishKit(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ActiveFishKit() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ActiveFishKit();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.internal_static_request_fishkit_ActiveFishKit_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.internal_static_request_fishkit_ActiveFishKit_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.class, ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.Builder.class);
    }

    public static final int FISHKIT_FIELD_NUMBER = 1;
    private long fishKit_;
    /**
     * <code>int64 fishKit = 1;</code>
     * @return The fishKit.
     */
    @java.lang.Override
    public long getFishKit() {
      return fishKit_;
    }

    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.fishkit.ActiveFishKit}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.fishkit.ActiveFishKit)
        ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKitOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.internal_static_request_fishkit_ActiveFishKit_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.internal_static_request_fishkit_ActiveFishKit_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.class, ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fishKit_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.internal_static_request_fishkit_ActiveFishKit_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit build() {
        ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit buildPartial() {
        ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit result = new ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit(this);
        result.fishKit_ = fishKit_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long fishKit_ ;
      /**
       * <code>int64 fishKit = 1;</code>
       * @return The fishKit.
       */
      @java.lang.Override
      public long getFishKit() {
        return fishKit_;
      }
      /**
       * <code>int64 fishKit = 1;</code>
       * @param value The fishKit to set.
       * @return This builder for chaining.
       */
      public Builder setFishKit(long value) {
        
        fishKit_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 fishKit = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishKit() {
        
        fishKit_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.fishkit.ActiveFishKit)
    }

    // @@protoc_insertion_point(class_scope:request.fishkit.ActiveFishKit)
    private static final ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit();
    }

    public static ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ActiveFishKit>
        PARSER = new com.google.protobuf.AbstractParser<ActiveFishKit>() {
      @java.lang.Override
      public ActiveFishKit parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ActiveFishKit> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ActiveFishKit> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto.ActiveFishKit getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_fishkit_ActiveFishKit_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_fishkit_ActiveFishKit_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#request/fishkit/ActiveFishKit.proto\022\017r" +
      "equest.fishkit\" \n\rActiveFishKit\022\017\n\007fishK" +
      "it\030\001 \001(\003BY\n)ehooray.fishing.dto.proto.re" +
      "quest.fishkitB\022ActiveFishKitProtoH\002\252\002\025Fi" +
      "shing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_fishkit_ActiveFishKit_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_fishkit_ActiveFishKit_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_fishkit_ActiveFishKit_descriptor,
        new java.lang.String[] { "FishKit", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
