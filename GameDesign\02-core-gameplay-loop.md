# 2. 核心遊戲循環系統設計文檔

## 2.1 主要遊戲循環流程圖

### 遊戲流程架構
```
標題畫面 (MenuScene)
    ↓
船隻選單 (BoatMenuScene) - 時間、天氣、位置顯示
    ↓
玩家選擇行動：
    ├── 旅行 → 選擇地圖 (1/5) → 選擇地點 (1/10) → 抵達 (時間+1小時, 能量-2)
    ├── 釣魚 → 選擇模式：
    │   ├── 故事模式 → 故事地圖
    │   └── 任務模式 → 任務地圖 (排除故事基礎魚地圖)
    │       └── 釣魚 (拋竿、誘魚、收線; 時間+30分, 能量-5)
    │           ├── 成功 → 收集魚卡 (如果魚缸未滿) → 返回船隻選單
    │           └── 失敗 → 返回船隻選單
    ├── 船艱 → 巡航艦艙 (社交互動) → 返回船隻選單
    ├── 商店 (僅限起始港口) → 商店選單 (購買物品) → 返回船隻選單
    ├── 庫存 → 庫存管理 (管理卡片、裝備) → 返回船隻選單
    └── 返回港口 (時間+1小時, 能量-2) → 出售魚卡 → 進入商店
```

### 技術實現架構
相關檔案：
- [GameLoop.js](../src/scripts/GameLoop.js): 主遊戲循環
- [SceneManager.js](../src/scripts/SceneManager.js): 場景切換管理
- [BoatMenuScene.js](../src/scenes/BoatMenuScene.js): 船隻選單主場景
- [GameScene.js](../src/scenes/GameScene.js): 核心釣魚場景

## 2.2 遊戲循環詳細描述

### 入口點流程
1. **標題畫面**: 
   - 新遊戲或載入遊戲
   - 教學或存檔載入
   - 相關檔案: [MenuScene.js](../src/scenes/MenuScene.js)

2. **船隻選單中心**:
   - 顯示當前時間 (例如：黎明)
   - 顯示天氣狀態 (例如：晴天)
   - 顯示船隻位置 (例如：起始港口 或 地圖3地點7)
   - 相關檔案: [BoatMenuScene.js](../src/scenes/BoatMenuScene.js)

### 核心行動選項

#### 2.2.1 旅行系統
- **功能**: 在起始港口和5個釣魚地圖間移動
- **地圖結構**: 每個地圖包含10個獨特地點 (例如：珊瑚礁、深海溝渠)
- **消耗**: 旅行需要1小時和2點能量
- **相關檔案**:
  - [LocationManager.js](../src/scripts/LocationManager.js): 地點管理
  - [LocationData.js](../src/data/LocationData.js): 地點數據
  - [MapSelectionUI.js](../src/ui/MapSelectionUI.js): 地圖選擇介面

#### 2.2.2 釣魚系統核心循環
**故事模式 vs 任務模式**:
- **故事模式**: 
  - 存取與敘事相關的地圖
  - 包含獨特魚類和挑戰
  - 綁定主要故事線
  
- **任務模式**: 
  - 存取任務地圖 (排除故事基礎魚地圖)
  - 用於技能磨練或捕捉特定魚類
  - 不影響故事進程

**釣魚流程**:
1. **選擇模式和地圖**
2. **執行三階段釣魚**:
   - 拋竿 ([CastingMiniGame.js](../src/scripts/CastingMiniGame.js))
   - 誘魚 ([LuringMiniGame.js](../src/scripts/LuringMiniGame.js))
   - 收線 ([ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js))
3. **結果處理**: 成功收集魚卡或失敗
4. **資源消耗**: 30分鐘和5點能量

相關檔案：
- [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚遊戲協調
- [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成
- [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據庫

#### 2.2.3 社交互動系統
- **船艙活動**: 與伴侶進行社交互動
- **功能**: 
  - 與10位比基尼女伴互動
  - 增進關係
  - 解鎖獎勵和HCG場景
- **相關檔案**:
  - [CabinScene.js](../src/scenes/CabinScene.js): 船艙場景
  - [DialogScene.js](../src/scenes/DialogScene.js): 對話系統
  - [DialogManager.js](../src/scripts/DialogManager.js): 對話管理

#### 2.2.4 商店與庫存管理
**商店系統**:
- **限制**: 僅在停靠起始港口時可用
- **功能**: 購買魚卡、裝備、寶石
- **相關檔案**: 
  - [ShopScene.js](../src/scenes/ShopScene.js): 商店場景
  - [ShopUI.js](../src/ui/ShopUI.js): 商店介面

**庫存管理**:
- **功能**: 管理魚卡、裝備和其他物品
- **魚缸限制**: 當魚缸滿載時必須返回港口出售
- **相關檔案**:
  - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理
  - [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面

## 2.3 時間與能量系統

### 時間管理
- **時間週期**: 8個時間段 (例如：黎明、正午)，每段3遊戲小時
- **時間進行方式**:
  - 釣魚: +30分鐘
  - 旅行: +1小時
- **相關檔案**: [TimeManager.js](../src/scripts/TimeManager.js)

### 能量系統
- **能量消耗**:
  - 釣魚: -5點能量
  - 旅行: -2點能量
- **能量管理**: 需要合理安排行動避免能量耗盡
- **相關檔案**: [PlayerController.js](../src/scripts/PlayerController.js)

## 2.4 魚缸與經濟循環

### 魚缸機制
- **容量限制**: 魚缸有固定容量
- **強制返回**: 魚缸滿載時必須返回起始港口
- **出售流程**: 
  1. 返回港口 (消耗1小時和2能量)
  2. 出售所有魚卡
  3. 釋放魚缸空間
  4. 可進入商店購買物品

### 經濟循環
- **收入來源**: 出售捕獲的魚卡
- **支出項目**: 購買裝備、魚餌、船隻升級
- **循環模式**: 釣魚 → 收集 → 出售 → 購買 → 強化 → 更好釣魚

相關檔案：
- [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 玩家進程

## 2.5 場景切換與狀態管理

### 場景架構
```
MenuScene (主選單)
    ↓
BootScene → PreloadScene → BoatMenuScene
    ↓
GameScene ← → CabinScene ← → ShopScene
    ↓           ↓              ↓
HUDScene    DialogScene    QuestScene
```

### 狀態持續性
- **遊戲狀態**: 跨場景保持玩家數據
- **場景數據**: 場景間的數據傳遞
- **存檔系統**: 自動和手動存檔

相關檔案：
- [GameState.js](../src/scripts/GameState.js): 遊戲狀態管理
- [SaveUtility.js](../src/scripts/SaveUtility.js): 存檔工具
- [SceneManager.js](../src/scripts/SceneManager.js): 場景管理

## 2.6 循環驅動機制

### 主循環結構
```javascript
// 偽代碼示例
class GameLoop {
    update(delta) {
        // 更新時間系統
        this.timeManager.update(delta);
        
        // 更新天氣系統
        this.weatherManager.update(delta);
        
        // 更新當前場景
        this.sceneManager.updateCurrentScene(delta);
        
        // 處理玩家輸入
        this.inputManager.processInput();
        
        // 更新UI
        this.uiManager.updateUI();
    }
}
```

### 系統更新順序
1. **時間系統更新**: 推進遊戲時間
2. **天氣系統更新**: 檢查天氣變化
3. **場景邏輯更新**: 當前場景的遊戲邏輯
4. **輸入處理**: 玩家操作響應
5. **UI更新**: 介面元素更新
6. **渲染**: 畫面繪製

相關檔案：
- [TimeManager.js](../src/scripts/TimeManager.js): 時間系統
- [WeatherManager.js](../src/scripts/WeatherManager.js): 天氣系統
- [InputManager.js](../src/scripts/InputManager.js): 輸入管理
- [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 時間天氣UI

## 2.7 循環中的進程追蹤

### 玩家進度指標
- **等級進程**: 50等級系統
- **地圖解鎖**: 逐步開放新地圖
- **魚類收集**: 50張魚卡收集進度
- **社交關係**: 與伴侶的關係值

### 成就與里程碑
- **釣魚成就**: 捕獲特定魚類
- **收集成就**: 完成魚卡收集
- **社交成就**: 伴侶關係里程碑
- **探索成就**: 發現所有地點

相關檔案：
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 進程管理
- [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
- [QuestManager.js](../src/scripts/QuestManager.js): 任務管理 