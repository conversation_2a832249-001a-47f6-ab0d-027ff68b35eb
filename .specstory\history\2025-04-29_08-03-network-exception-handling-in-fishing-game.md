<!-- Generated by SpecStory -->

# Network Exception Handling in Fishing Game (2025-04-29 16:03:22)

_**User**_

NetworkException: Http Error
Fishing.Network.NetworkService.SendTask (Ehooray.Network.NetworkTask task) (at Assets/Fishing/Scripts/Network/Service/NetworkService.cs:36)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2[TStateMachine,T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:342)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Fishing.Network.FirebaseAuthService.Login (Fishing.Network.Proto.FirebaseAuth firebaseAuth) (at Assets/Fishing/Scripts/Network/Service/FirebaseAuthService.cs:29)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2[TStateMachine,T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:342)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Fishing.CurrentLogin.<AuthTokenRunner>b__22_0 () (at Assets/Fishing/Scripts/UI/LoginUI/CurrentLogin.cs:109)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Cysharp.Threading.Tasks.UniTask+DeferPromise.GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:371)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Ehooray.AsyncProgressRunner.RunAsync (System.IProgress`1[T] progress, System.Boolean showMask, System.Threading.CancellationToken cancellationToken) (at Assets/Ehooray/Runtime/Utilities/AsyncProgressRunner.cs:56)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.CurrentLogin.StartGameAsync () (at Assets/Fishing/Scripts/UI/LoginUI/CurrentLogin.cs:91)
UnityEngine.Debug:LogException(Exception)
Cysharp.Threading.Tasks.UniTaskScheduler:PublishUnobservedTaskException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskScheduler.cs:90)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoidMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskVoidMethodBuilder.cs:53)
Fishing.<StartGameAsync>d__21:MoveNext() (at Assets/Fishing/Scripts/UI/LoginUI/CurrentLogin.cs:92)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoid`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:104)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Ehooray.<RunAsync>d__6:MoveNext() (at Assets/Ehooray/Runtime/Utilities/AsyncProgressRunner.cs:87)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Fishing.<<AuthTokenRunner>b__22_0>d:MoveNext() (at Assets/Fishing/Scripts/UI/LoginUI/CurrentLogin.cs:114)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:334)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:186)
Fishing.Network.<Login>d__2:MoveNext() (at Assets/Fishing/Scripts/Network/Service/FirebaseAuthService.cs:32)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:334)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:186)
Fishing.Network.<SendTask>d__5:MoveNext() (at Assets/Fishing/Scripts/Network/Service/NetworkService.cs:36)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySignalCompletion(UniTaskStatus) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:909)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:817)
Ehooray.Network.NetworkTask:SetServerException(Response) (at Assets/Ehooray/Runtime/Network/NetworkTask.cs:91)
Ehooray.Network.NetworkManager:HandleException(NetworkNode, Response) (at Assets/Ehooray/Runtime/Network/NetworkManager.cs:237)
Ehooray.Network.NetworkManager:OnReceive(NetworkNode, NetworkMethod, Response) (at Assets/Ehooray/Runtime/Network/NetworkManager.cs:171)
Ehooray.Network.NetworkNode:OnReceive(NetworkMethod, Response) (at Assets/Ehooray/Runtime/Network/NetworkNode.cs:63)
Ehooray.Network.NetworkMethod:OnReceive(Response) (at Assets/Ehooray/Runtime/Network/Method/NetworkMethod.cs:36)
Ehooray.Network.HttpMethod:OnHttpAuthFinish(HTTPRequest, HTTPResponse) (at Assets/Ehooray/Runtime/Network/Method/HttpMethod.cs:100)
BestHTTP.Core.RequestEventHelper:HandleRequestStateChange(RequestEventInfo) (at Assets/Best HTTP/Source/Core/RequestEvents.cs:475)
BestHTTP.Core.RequestEventHelper:ProcessQueue() (at Assets/Best HTTP/Source/Core/RequestEvents.cs:350)
BestHTTP.HTTPManager:OnUpdate() (at Assets/Best HTTP/Source/HTTPManager.cs:407)
BestHTTP.HTTPUpdateDelegator:Update() (at Assets/Best HTTP/Source/HTTPUpdateDelegator.cs:174)



---

_**Assistant**_



---

好的，看起来你在登录过程中遇到了一个 `NetworkException: Http Error`。

根据堆栈跟踪，这个错误发生在 `Fishing.Network.NetworkService.SendTask` 方法中，该方法在 `Assets/Fishing/Scripts/Network/Service/NetworkService.cs` 文件的第 36 行被调用。这个调用链最终可以追溯到 `Fishing.CurrentLogin.StartGameAsync`，这表明问题出在尝试通过 `FirebaseAuthService` 进行 Firebase 身份验证时。

为了更好地理解错误原因，我需要查看 `NetworkService.cs` 中 `SendTask` 方法是如何处理网络响应和错误的。



---

Read file: Assets/Fishing/Scripts/Network/Service/NetworkService.cs

---

从 `NetworkService.cs` 的代码来看，`SendTask` 方法捕获了 `NetworkException`，并通过 `SignalBus` 发送了一个 `NotifyException` 信号，然后重新抛出了这个异常。这说明异常确实是从 `task.Start()` 这一行（第 30 行）抛出的，也就是 `NetworkTask` 内部的网络请求执行过程中发生的。

接下来，我需要看看 `Ehooray.Network.NetworkTask` 是如何处理异常的，特别是 `SetServerException` 方法（堆栈跟踪中提到）。



---

Read file: Assets/Ehooray/Runtime/Network/NetworkTask.cs

---

`NetworkTask.cs` 中的 `SetServerException` 方法（第 90 行）确实会根据传入的 `Response` 对象创建一个 `NetworkException`，并将响应中的 `response.Exception.ServerMessage` 作为异常消息。同时，它还会打印一条错误日志，包含了请求 ID、控制器、命令、csvId 和服务器消息。

这表明服务器返回了一个包含错误信息的响应。现在，让我们看看调用栈更上层的 `HttpMethod.cs`，看看它是如何处理 HTTP 响应并最终调用 `SetServerException` 的。



---

Read file: Assets/Ehooray/Runtime/Network/Method/HttpMethod.cs

---

在 `HttpMethod.cs` 的 `OnHttpAuthFinish` 方法中，它检查了 `HTTPRequest` 的状态 (`req.State`)。

-   如果请求成功 (`HTTPRequestStates.Finished` 且 `resp.IsSuccess`)，它会解析响应数据。
-   如果请求状态是 `HTTPRequestStates.Error` (第 109 行)，它会调用 `OnReceive(ToErrorRespopnse(..., "Http Error"))`。

`ToErrorRespopnse` 方法（未在此代码段中显示，但在同一文件中）很可能会创建一个包含错误信息的 `Response` 对象，然后 `OnReceive` 方法（在基类 `NetworkMethod` 或 `NetworkNode` 中）会调用 `NetworkManager.HandleException`，接着调用 `NetworkTask.SetServerException`，最终导致你看到的 `NetworkException`。

**结论:**

错误 `NetworkException: Http Error` 是由底层的 BestHTTP 库报告的 HTTP 请求错误 (`HTTPRequestStates.Error`) 引起的。这通常意味着：

1.  **网络连接问题**: 设备无法连接到服务器（例如，没有网络，防火墙阻止，DNS 问题）。
2.  **服务器端问题**: 服务器不可用、返回了错误状态码（如 4xx 或 5xx），或者响应格式不正确。
3.  **请求配置问题**: 请求的 URL (`authUri`，在 `HttpMethod.Send` 中构建)、头部或数据可能有误。

**建议的后续步骤:**

1.  **检查网络连接**: 确保你的设备有稳定的网络连接，并且可以访问 Firebase Authentication 服务器。
2.  **检查服务器状态**: 确认 Firebase Authentication 服务是否正常运行，以及你的项目配置是否正确。
3.  **查看详细的 HTTP 错误**:
    *   你可以在 `HttpMethod.OnHttpAuthFinish` 方法的 `case HTTPRequestStates.Error:` (第 110 行左右) 添加更详细的日志记录，打印出 `resp.StatusCode` 和 `resp.Message`，以了解具体的 HTTP 错误是什么。例如：
        ```csharp
        case HTTPRequestStates.Error:
            // 添加这行日志
            UnityEngine.Debug.LogError($"HTTP Error Details: StatusCode={resp.StatusCode}, Message={resp.Message}, Error={req.Exception?.Message}"); 
            OnReceive(ToErrorRespopnse(findRequestPack.request.RequestId, "Http Error"));
            break;
        ```
    *   重新运行游戏并触发登录，查看控制台中打印出的详细 HTTP 错误信息。这将帮助你确定是客户端问题还是服务器端问题。

让我知道你想如何进行下一步，或者如果你想让我尝试添加上述日志记录。

---

