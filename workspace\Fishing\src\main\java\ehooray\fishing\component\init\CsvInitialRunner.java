package ehooray.fishing.component.init;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.csv.CsvLoader;
import ehooray.fishing.pojo.csv.ACsv;

@Component
@Order(RunnerOrder.Csv)
public class CsvInitialRunner implements ApplicationRunner
{

    @Autowired
    protected CsvLoader csvLoader;

    @Autowired
    protected CsvContainer csvContainer;

    @Value("${game.csv.class-packages}")
    protected List<String> csvClassPackages;

    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        String[] packages = csvClassPackages.toArray(String[]::new);
        List<ACsv<?>> csvList = csvLoader.load(packages);
        csvContainer.addCsv(csvList);
    }

}
