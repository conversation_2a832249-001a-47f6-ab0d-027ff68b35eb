---
description: Defines the core system architecture of the fishing game, including main managers (game state, game loop, scene management, player controller), three fishing mini-games (casting, luring, reeling), 34 attribute systems, time and weather systems, and fish system with 50 fish cards. This file is the core reference for understanding the entire game system operation.
globs: 
alwaysApply: false
---
# 核心游戏系统

## 主要管理器
- **[GameState.js](mdc:FishingPhaser/src/scripts/GameState.js)** - 游戏状态管理，包含玩家属性、进度、时间和天气系统
- **[GameLoop.js](mdc:FishingPhaser/src/sameLoop.js)** - 核心游戏循环逻辑
- **[Sjs](mdc:FishingPhaser/src/scripts/Sc)** - 场景切换和管理
- **[Playeler.js](mdc:FishingPhaser/src/scriptntroller.js)** - 玩家控制器和属性管理

## 钓鱼系统（三个迷你游戏）
1. **[CastingMiniGame.js](mdc:FishingPhaser/src/scripts/CastingMiniGame.js)** - 抛竿迷你游戏
   - 时机为基础的游戏
   - 抛竿计量表，准确区域内点击可到达热点
   - 视觉效果：2D钓竿抛竿动画，鱼线和诱饵投向水中
   
2. **[LuringMiniGame.js](mdc:FishingPhaser/src/scripts/LuringMiniGame.js)** - 诱饵迷你游戏  
   - 鱼影接近诱饵，玩家有3-4次机会钩鱼
   - 5种诱饵类型，各有不同的节奏输入控制
   - 诱饵模拟UI显示诱饵在水中的移动
   - 鱼类兴趣计量表和输入提示
   
3. **[ReelingMiniGame.js](mdc:FishingPhaser/src/scripts/ReelingMiniGame.js)** - 收线迷你游戏
   - 基于张力的游戏，包含QTE反应控制
   - 鱼类挣扎风格和体力系统
   - 张力计量表，防止鱼线断裂
   - 10种鱼类挣扎方式和QTE控制

## 属性系统（34个属性）
- **玩家升级属性**（10个）：抛竿精度、能量、咬钩率、稀有鱼机会、QTE精度等
- **钓竿属性**（6个）：抛竿精度、张力稳定性、稀有鱼机会、抛竿范围、收线速度、挣扎抗性
- **诱饵属性**（4个）：咬钩率、诱饵成功率、诱饵耐久度、诱饵精度
- **比基尼助手属性**（6个）：浪漫效果、聊天室亲和力、娱乐情绪提升等
- **船只属性**（8个）：制作效率、自动钓鱼产量、鱼类探测、热点稳定性等

## 时间和天气系统
- **时间循环**：8个时段（如黎明、正午），每个3小时
- **天气类型**：晴天（提升鱼类探测）、雨天（咬钩率+10%）
- **时间进展**：钓鱼+30分钟，旅行+1小时
- **鱼类活跃时间**：不同鱼类在特定时间段和天气条件下更活跃

## 鱼类系统
- **鱼类属性**（11个）：大小、侵略性、难以捕捉性、力量、稀有度、重量、速度、深度偏好、饵料偏好、耐力、活跃时间偏好
- **50种鱼卡**：从小鱼（0.1kg）到巨型鱼类（500kg）
- **挣扎风格**：快速冲撞、深潜、长距离冲刺等