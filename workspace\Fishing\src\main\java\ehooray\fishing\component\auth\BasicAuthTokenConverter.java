package ehooray.fishing.component.auth;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.javatuples.Pair;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;


/**
 * https://matthung0807.blogspot.com/2020/04/http-basic-authentication.html
 *
 */
@Component
public class BasicAuthTokenConverter
{
    public String encode(int serverId, String account, String password)
    {
        String plainToken = String.format("%s:%s", toAccountCombination(serverId, account), password);
        String base64Token = Base64.getEncoder().encodeToString(plainToken.getBytes(StandardCharsets.UTF_8));
        return base64Token;
    }
    
    protected String toAccountCombination(int serverId, String account)
    {
        String accountCombination = String.format("%d,%s", serverId, account);
        return accountCombination;
    }
    
    public Pair<Integer, String> unpackAccountCombination(String accountCombination)
    {
        String separator = ",";
        String serverIdStr = accountCombination.substring(0, accountCombination.indexOf(separator));
        int serverId = Integer.parseInt(serverIdStr);
        String account = accountCombination.substring(accountCombination.indexOf(separator) + separator.length());
        return Pair.with(serverId, account);
    }
    
    public DecodeResult decode(String token)
    {
        byte[] plainTokenBytes = Base64.getDecoder().decode(token);
        String plainToken = new String(plainTokenBytes, StandardCharsets.UTF_8);
        String[] plainTokenSplit = plainToken.split(":");
        if(plainTokenSplit.length != 2)
            throw new BadCredentialsException("invalid token format");
        
        Pair<Integer, String> unpackAccountCombinationResult = unpackAccountCombination(plainTokenSplit[0]);
        DecodeResult decodeResult = new DecodeResult();
        decodeResult.setServerId(unpackAccountCombinationResult.getValue0());
        decodeResult.setAccount(unpackAccountCombinationResult.getValue1());
        decodeResult.setToken(plainTokenSplit[1]);
        
        return decodeResult;
    }
    
    public class DecodeResult
    {
        protected int serverId;
        protected String account;
        protected String token;
        public int getServerId()
        {
            return serverId;
        }
        public void setServerId(int serverId)
        {
            this.serverId = serverId;
        }
        public String getAccount()
        {
            return account;
        }
        public void setAccount(String account)
        {
            this.account = account;
        }
        public String getToken()
        {
            return token;
        }
        public void setToken(String token)
        {
            this.token = token;
        }
    }
}
