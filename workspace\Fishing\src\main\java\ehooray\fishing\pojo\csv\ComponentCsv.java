package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import org.apache.logging.log4j.util.Strings;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;
//import ehooray.formosa.component.component.CreatePlayerBuffComponent;
import ehooray.fishing.component.component.LuaComponent;
//import ehooray.formosa.component.component.ProduceItemComponent;
//import ehooray.formosa.component.component.RemovePlayerBuffComponent;
//import ehooray.formosa.component.component.ValueOperationComponent;

@CsvPath("ComponentTable.xlsx")
public class ComponentCsv extends ACsv<ComponentCsv.Row>
{
    public enum Main
    {
        Lua(900, LuaComponent.class),
        //ProduceItem(102, ProduceItemComponent.class),
        //ValueOperation(301, ValueOperationComponent.class),
        //CreatePlayerBuff(401, CreatePlayerBuffComponent.class),
        //RemovePlayerBuff(402, RemovePlayerBuffComponent.class),
        ;

        protected int id;
        protected Class<?> invoker;
        private Main(int id, Class<?> invoker)
        {
            this.id = id;
            this.invoker = invoker;
        }

        public int getId()
        {
            return id;
        }

        public Class<?> getInvoker()
        {
            return invoker;
        }

        public static Optional<Main> valueOf(int id)
        {
            return Arrays.stream(Main.values()).filter(x-> x.getId() == id).findFirst();
        }
    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Lua")
        protected String lua;

        @ExcelCellName("Main")
        protected int main;

        @ExcelCellName("Parameter_1")
        protected String parameter1;

        @ExcelCellName("Parameter_2")
        protected String parameter2;

        @ExcelCellName("Parameter_3")
        protected String parameter3;

        @ExcelCellName("Parameter_4")
        protected String parameter4;

        @ExcelCellName("Parameter_5")
        protected String parameter5;

        @ExcelCellName("Parameter_6")
        protected String parameter6;

        @ExcelCellName("Parameter_7")
        protected String parameter7;

        @ExcelCellName("Parameter_8")
        protected String parameter8;

        @ExcelCellName("Parameter_9")
        protected String parameter9;

        @ExcelCellName("Parameter_10")
        protected String parameter10;

        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }

        public String getLua()
        {
            return lua;
        }

        public void setLua(String lua)
        {
            this.lua = lua;
        }

        protected String[] paramters;
        protected String[] getParameters()
        {
            if(paramters != null) return paramters;

            // atomic operation, do not assign separately
            paramters = new String[]
            {
                parameter1,
                parameter2,
                parameter3,
                parameter4,
                parameter5,
                parameter6,
                parameter7,
                parameter8,
                parameter9,
                parameter10
            };
            return paramters;
        }

        public int getParametersCount()
        {
            return getParameters().length;
        }

        public int getParameterByInt(int index)
        {
            if(Strings.isEmpty(getParameters()[index])) return 0;

            return Integer.parseInt(getParameters()[index]);
        }

        public int getMain()
        {
            return main;
        }

        public void setMain(int main)
        {
            this.main = main;
        }

    }

    // another utility function at here
}
