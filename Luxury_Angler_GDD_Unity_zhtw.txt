Luxury Angler：遊戲設計文檔
====================================

日期：2025年5月25日

目錄
-----------------
1. 遊戲概述
2. 核心遊戲迴圈
3. 屬性系統
4. 釣魚系統
   4.1 釣魚機制
      4.1.1 投竿小遊戲
      4.1.2 誘餌小遊戲（強化版）
      4.1.3 收線小遊戲
   4.2 時間系統
   4.3 天氣系統
   4.4 誘餌類型
   4.5 魚類掙扎方式
   4.6 QTE反應控制
5. 製作系統
6. 等級進度
7. 物品欄和裝備
   7.1 物品欄系統
   7.2 裝備系統
8. 社交系統
   8.1 郵輪聊天室
   8.2 故事和對話系統 (Naninovel)
9. 商店系統
10. 成就系統
11. 經濟系統
12. 美術和音訊
13. 技術設計

1. 遊戲概述
----------------

1.1 遊戲概念
Luxury Angler 是一款3D釣魚模擬遊戲，將真實的釣魚機制與豪華生活主題相結合。玩家從新手釣魚者進步到釣魚大師，經歷50個等級，在5個異國地圖的各10個地點釣魚，透過合成系統製作精英裝備，參加錦標賽，並與10位比基尼女伴建立關係。遊戲特色包括50張 Fish Cards，具有8個時段的晝夜循環，天氣系統（晴天/雨天），以及技能導向的小遊戲（投竿、誘餌、收線），搭配掛機自動釣魚選項，吸引休閒和中核玩家。

1.2 目標受眾
16-35歲的休閒到中核玩家，喜愛模擬、生活模擬和RPG遊戲（如 Stardew Valley、Dave the Diver）的玩家，享受進度、製作和具有豪華風格的社交元素。

1.3 平台
主要：行動裝置（iOS、Android）。潛在：PC（Steam）、Nintendo Switch。

1.4 遊戲類型
模擬、釣魚、生活模擬、卡牌收集、社交、掛機。

1.5 獨特賣點
- 擁有高端船隻和異國地點的豪華釣魚生活風格。
- 使用 Fish Cards 進行深度合成製作系統。
- 影響魚類可得性的動態晝夜和天氣系統。
- 技能導向和掛機遊戲玩法的融合。

1.6 關鍵特色
- 釣魚機制：技能導向的投竿、5種誘餌類型、10種魚類掙扎方式和QTE。
- 製作：將 Fish Cards 合成為釣竿、誘餌、船隻和服裝。
- 進度：50個等級，5張地圖各有10個地點，頭目戰。
- 社交：10個具有戀愛機制的同伴。
- 時間/天氣：8個時段，晴天/雨天條件。

2. 核心遊戲迴圈
---------------------

2.1 主要遊戲迴圈流程圖
遊戲迴圈從標題畫面開始，以船隻選單為中心，玩家在此選擇行動：前往釣魚點、釣魚、聊天、購物或管理物品欄。時間和體力消耗與晝夜和天氣系統相關。玩家必須返回起始遊戲港口才能進入商店或從船隻魚艙出售 Fish Cards。

流程圖（簡化版）：
標題畫面 -> 船隻選單（時間、天氣、位置顯示）
  -> 旅行 -> 選擇地圖（5選1）-> 選擇地點（10選1）-> 到達（時間 +1小時，體力 -2）
  -> 去釣魚 -> 選擇模式：Story Mode 或 Practice Mode
    -> 選擇地圖（Story Mode：故事地圖；Practice Mode：練習地圖，排除故事基礎魚類地圖）
    -> 釣魚（投竿、誘餌、收線；時間 +30分鐘，體力 -5）
      -> 成功 -> 收集 Fish Card（如果魚艙未滿）-> 返回船隻選單
      -> 失敗 -> 返回船隻選單
  -> 去聊天室 -> 郵輪聊天室（社交互動）-> 返回船隻選單
  -> 去商店（僅在起始港口）-> 商店選單（購買物品）-> 返回船隻選單
  -> 去物品欄 -> 物品欄（管理卡片、裝備）-> 返回船隻選單
  -> 返回港口（時間 +1小時，體力 -2）-> 出售 Fish Cards -> 進入商店

2.2 遊戲迴圈描述
玩家從標題畫面開始，在教學或載入存檔後進入船隻選單。船隻選單顯示時間（如黎明）、天氣（如晴天）和船隻當前位置（如起始港口或地圖3，地點7）。從這裡，玩家可以：
- **旅行**：在起始港口和5個釣魚地圖之間移動，每個地圖有10個獨特地點（如珊瑚礁、深海溝）。旅行需要1小時和2點體力。
- **釣魚**：在 **Story Mode** 或 **Practice Mode** 之間選擇：
  - **Story Mode**：進入與敘事相關的故事地圖，具有獨特魚類和挑戰。
  - **Practice Mode**：進入練習地圖（排除故事的基礎魚類地圖）進行技能磨練或捕捉特定魚類，不影響故事進度。
- **聊天**：與同伴進行社交互動。
- **商店**：購買物品，但僅當停靠在起始港口時。
- **管理物品欄**：製作或裝備物品。
當魚艙滿時，玩家必須返回港口（1小時，2點體力）出售所有 Fish Cards 並釋放空間。行動會推進時間並消耗體力，循環回到船隻選單。

3. 屬性系統
--------------------

3.1 玩家屬性（34個）
玩家屬性增強釣魚、製作和社交互動。可透過升級、裝備或技能改善，分類為：

3.1.1 玩家升級屬性（10個）
- Cast Accuracy：增加熱點目標區域。透過升級改善（每級+1%，最高20%）。
- Energy：行動耐力（基礎最高100）。透過升級改善（每級+2）。
- Bite Rate：魚類咬餌可能性。透過升級改善（每級+0.5%）。
- Rare Fish Chance：稀有魚類遭遇機率。透過升級改善（每級+0.2%）。
- QTE Precision：延長QTE時機視窗。透過升級改善（每級+0.5%）。
- Lure Success：誘餌輸入成功率。透過升級改善（每級+0.5%）。
- Line Strength：減少線斷裂機會。透過升級改善（每級+0.3%）。
- Casting Range：增加投竿距離。透過升級改善（每級+0.5%）。
- Boat Speed：減少旅行時間。透過升級改善（每級+0.3%）。
- Coin Gain：增加金幣獲得。透過升級改善（每級+0.3%）。

3.1.2 釣竿屬性（6個）
- Cast Accuracy：由釣竿增強（如5星釣竿+25%）。
- Tension Stability：擴大安全張力區域（如5星釣竿+28%）。
- Rare Fish Chance：由釣竿增加（如5星釣竿+15%）。
- Casting Range：由釣竿延長（如5星釣竿+35%）。
- Reel Speed：更快收線（如5星釣竿+15%）。
- Struggle Resistance：減少魚類掙扎強度（如5星釣竿+15%）。

3.1.3 誘餌屬性（4個）
- Bite Rate：由誘餌增強（如5星誘餌+60%）。
- Lure Success：由誘餌改善（如3星誘餌+40%）。
- Lure Durability：減少失敗機會（如5星誘餌+10%）。
- Lure Precision：收緊輸入視窗（如5星誘餌+10%）。

3.1.4 比基尼助手屬性（6個）
- Romance Effectiveness：增加戀愛點數。透過技能改善。
- Chatroom Affinity：增強聊天室互動。透過船隻改善。
- Entertainment Mood Boost：提升同伴心情。透過船隻改善。
- Fish Detection：發現高價值魚類。透過同伴改善。
- Energy：由同伴增強。透過服裝改善。
- QTE Precision：由同伴延長。透過技能改善。

3.1.5 船隻屬性（8個）
- Crafting Efficiency：減少製作時間/成本。透過船隻改善。
- Auto-Fishing Yield：增加自動釣魚獎勵。透過船隻改善。
- Fish Detection：由船隻增強（如5星船隻+10%）。
- Hotspot Stability：增加熱點持續時間。透過船隻改善。
- Companion Slot Capacity：增加同伴槽位。透過船隻改善。
- Auto-Fishing Efficiency：減少自動釣魚時間。透過船隻改善。
- Boat Durability：減少惡劣條件下的懲罰。透過船隻改善。
- Fishtank Storage：擴展 Fish Card 儲存。透過船隻改善。

3.2 魚類屬性（11個）
魚類屬性影響行為和難度：
- Size：1-10（如 Kraken：10）。
- Aggressiveness：1-10（如 Shark：10）。
- Elusiveness：1-10（如 Anglerfish：10）。
- Strength：1-10（如 Tuna：10）。
- Rarity：1-10（如 Blue Marlin：10）。
- Weight：0.1-500公斤（如 Giant Squid：500公斤）。
- Speed：1-10（如 Marlin：10）。
- Depth Preference：1-10（如 Viperfish：10）。
- Bait Preference：1-10（如 Trout：Fly為10）。
- Endurance：1-10（如 Kraken：10）。
- Active Time Period/Weather Preference：特定時間/天氣（如 Moonlight Bass：夜晚，雨天）。

4. 釣魚系統
-----------------

4.1 釣魚機制
Luxury Angler 中的釣魚過程包含三個小遊戲：**投竿**、**誘餌**和**收線**，受到玩家和魚類屬性影響。
- **視覺**：進入釣魚過程後總是會顯示釣竿和誘餌。

4.1.1 投竿小遊戲
- **描述**：基於時機的小遊戲
- **遊戲玩法**：顯示投竿表，如果玩家在投竿表的準確區段內點擊，誘餌將被投到熱點（大型或稀有魚類出現機率較高），但如果玩家錯過準確區段，誘餌將被投到普通地點。
- **修正值**：玩家的 Cast Accuracy 和 Fish Detection 屬性影響準確區段大小和可見度。
- **結果**：成功進入**誘餌**小遊戲；失敗結束嘗試。
- **視覺**：添加3D釣竿投竿動畫的視覺，玩家在投竿表中點擊後，帶有釣線的誘餌被拋向水中的熱點。如果玩家在投竿表的準確區段內點擊，帶有釣線的誘餌將在水中的熱點著陸並發出濺水聲，否則如果玩家未能在準確區段內點擊，帶有釣線的誘餌將著陸在熱點外。

4.1.2 誘餌小遊戲（強化版）
**誘餌**小遊戲涉及魚影接近誘餌，玩家有3到4次機會鉤住魚。

在右上角創建誘餌模擬UI，顯示誘餌在水中的移動。附近的魚會在誘餌模擬UI底部游泳。有興趣的魚會停下，冒泡，並觀察誘餌5秒鐘，如果誘餌根據誘餌控制方案正確行動，然後它會決定咬鉤。如果玩家在該誘餌類型的控制方案中失敗，觀察的魚會游走。

常規誘餌控制：如果玩家按上鍵（WASD控制中的W），誘餌在一秒後快速向上游泳，如果玩家按左鍵或右鍵（WASD中的A或D），誘餌向右上方向船隻游泳（螢幕右側）一秒鐘。所有動作都有1秒冷卻時間。

- **魚影機制**：
  - 陰影（小、中或大）游向誘餌，表示魚的大小/稀有度。
  - 陰影行為（繞圈、衝刺）反映魚的**攻擊性**和**逃避性**。

- **誘餌階段**：
  - 玩家有**3到4次機會**（階段）鉤住魚。
  - 每個階段使用特定於誘餌的節奏輸入（如旋轉餌的脈衝點按）。
  - **成功**：正確輸入讓陰影保持接近。
  - **失敗**：錯誤讓陰影游走；所有失敗都會拉回誘餌。

- **漸進難度**：
  - 後期階段更難（更緊的時機，複雜序列）。
  - 隨魚的**逃避性**和玩家的**誘餌成功率**調整。

- **鉤魚**：
  - 成功階段後，最終的定時輸入鉤住魚。
  - **反饋**：濺水（成功）或砰聲（失敗）。

- **屬性整合**：
  - **玩家屬性**：
    - **Lure Success**：放寬輸入時機。
    - **Bite Rate**：可能減少所需階段。
  - **魚類屬性**：
    - **Aggressiveness**：更快的陰影接近。
    - **Elusiveness**：更高的失敗風險。

- **使用者介面**：
  - **陰影興趣表**：魚興趣的視覺量表。
  - **輸入提示**：每個階段的指導。

- **誘餌類型**
- Spinner：脈衝點按。
- Soft Plastic：拖拽和暫停。
- Fly：滑動彈擊組合。
- Popper：點按和保持爆發。
- Spoon：圓形追蹤。

4.1.3 收線小遊戲
- **描述**：基於張力的小遊戲，帶有QTE來收線魚。
- **修正值**：玩家的張力穩定性和收線速度影響難度。
- **結果**：成功產生獎勵；失敗失去魚。
- **視覺**：一條被鉤住掙扎的魚，用釣線綁住，濺起水花。魚會快速隨機向左右游泳。
- **表計**：張力表顯示，玩家必須確保張力指示器不到達張力表的最大值，否則釣線會斷裂。
- **控制**：滑鼠按下會收線一點並導致魚失去耐力，消耗的耐力數字會從掙扎的魚身上彈出。
- **QTE控制與掙扎相關**：魚有機會進行10種掙扎方式中的2或3種。QTE控制需要快速輸入，否則張力表會突然增加到90%最大值。
- **魚類耐力**：魚類耐力條會在魚的頂部顯示。魚會進行數次掙扎，直到其耐力達到0並被玩家拉起。

- **魚類掙扎方式**（例子）
- Rapid Thrashing，Deep Dive，Long Sprint。

- **QTE反應控制**（例子）
- Quick Tap，Hold and Release，Swipe Chain。

4.2 時間系統
- **循環**：8個時段（如黎明、正午），每個3遊戲小時。
- **進度**：釣魚推進時間30分鐘；旅行1小時。

4.3 天氣系統
- **類型**：晴天（增強魚類偵測），雨天（咬餌率增加10%）。
- **循環**：每2遊戲小時變化。

4.4 地圖選擇
- **Story Mode 地圖**：這些地圖4與主要故事情節相關，具有獨特的魚類、挑戰和敘事進展。例子包括 Coral Cove 和 Deep Abyss。
- **Practice Mode 地圖**：與故事分離，這些地圖（排除故事的基礎魚類地圖）允許玩家練習技能或瞄準特定魚類而不影響故事進度。例子包括 Training Lagoon 和 Open Waters。

5. 製作系統
------------------

5.1 合成機制
在船隻工作坊製作：
- 輸入：Fish Cards（1-5星）。
- 成本：200-16,000金幣，2-30分鐘。
- 輸出：釣竿、誘餌、船隻和服裝。
- 升級：合成低級物品。

5.2 合成配方範例（60個）
釣竿（15個）：
1. Bamboo Rod：2張 Minnow Cards，200金幣，2分鐘。
2. Fiberglass Rod：2張 Perch Cards，400金幣，4分鐘。
3. Carbon Rod：2張 Trout Cards，800金幣，6分鐘。
4. Steel Rod：2張 Pike Cards，1,200金幣，8分鐘。
5. Titanium Rod：2張 Salmon Cards，2,000金幣，10分鐘。
6. Elite Bamboo Rod：2支 Bamboo Rods，1,000金幣，12分鐘。
7. Elite Fiberglass Rod：2支 Fiberglass Rods，2,000金幣，14分鐘。
8. Elite Carbon Rod：2支 Carbon Rods，3,000金幣，16分鐘。
9. Elite Steel Rod：2支 Steel Rods，4,000金幣，18分鐘。
10. Elite Titanium Rod：2支 Titanium Rods，6,000金幣，20分鐘。
11. Master Bamboo Rod：2支 Elite Bamboo Rods，8,000金幣，22分鐘。
12. Master Fiberglass Rod：2支 Elite Fiberglass Rods，10,000金幣，24分鐘。
13. Master Carbon Rod：2支 Elite Carbon Rods，12,000金幣，26分鐘。
14. Master Steel Rod：2支 Elite Steel Rods，14,000金幣，28分鐘。
15. Master Titanium Rod：2支 Elite Titanium Rods，16,000金幣，30分鐘。

誘餌（15個）：
1. Basic Spinner：2張 Perch Cards，300金幣，2分鐘。
2. Soft Worm：2張 Catfish Cards，500金幣，4分鐘。
3. Fly Lure：2張 Trout Cards，700金幣，6分鐘。
4. Popper Lure：2張 Pike Cards，900金幣，8分鐘。
5. Spoon Lure：2張 Salmon Cards，1,100金幣，10分鐘。
6. Elite Spinner：2個 Basic Spinners，1,500金幣，12分鐘。
7. Elite Soft Worm：2個 Soft Worms，2,000金幣，14分鐘。
8. Elite Fly Lure：2個 Fly Lures，2,500金幣，16分鐘。
9. Elite Popper：2個 Popper Lures，3,000金幣，18分鐘。
10. Elite Spoon：2個 Spoon Lures，3,500金幣，20分鐘。
11. Master Spinner：2個 Elite Spinners，4,000金幣，22分鐘。
12. Master Soft Worm：2個 Elite Soft Worms，4,500金幣，24分鐘。
13. Master Fly Lure：2個 Elite Fly Lures，5,000金幣，26分鐘。
14. Master Popper：2個 Elite Poppers，5,500金幣，28分鐘。
15. Master Spoon：2個 Elite Spoons，6,000金幣，30分鐘。

船隻（15個）：
1. Rowboat：3張 Sardine Cards，500金幣，5分鐘。
2. Skiff：3張 Bass Cards，1,000金幣，7分鐘。
3. Speedboat：3張 Tuna Cards，2,000金幣，9分鐘。
4. Yacht：3張 Marlin Cards，4,000金幣，11分鐘。
5. Luxury Liner：3張 Shark Cards，6,000金幣，13分鐘。
6. Elite Rowboat：2艘 Rowboats，2,000金幣，15分鐘。
7. Elite Skiff：2艘 Skiffs，3,000金幣，17分鐘。
8. Elite Speedboat：2艘 Speedboats，5,000金幣，19分鐘。
9. Elite Yacht：2艘 Yachts，8,000金幣，21分鐘。
10. Elite Luxury Liner：2艘 Luxury Liners，12,000金幣，23分鐘。
11. Master Rowboat：2艘 Elite Rowboats，10,000金幣，25分鐘。
12. Master Skiff：2艘 Elite Skiffs，11,000金幣，27分鐘。
13. Master Speedboat：2艘 Elite Speedboats，13,000金幣，29分鐘。
14. Master Yacht：2艘 Elite Yachts，15,000金幣，31分鐘。
15. Master Luxury Liner：2艘 Elite Luxury Liners，16,000金幣，33分鐘。

服裝（15個）：
1. Fisher's Cap：2張 Trout Cards，400金幣，3分鐘。
2. Sunglasses：2張 Pike Cards，600金幣，5分鐘。
3. Fishing Vest：2張 Salmon Cards，800金幣，7分鐘。
4. Bikini Top：2張 Bass Cards，1,000金幣，9分鐘。
5. Sandals：2張 Perch Cards，1,200金幣，11分鐘。
6. Elite Fisher's Cap：2頂 Fisher's Caps，1,500金幣，13分鐘。
7. Elite Sunglasses：2副 Sunglasses，2,000金幣，15分鐘。
8. Elite Fishing Vest：2件 Fishing Vests，2,500金幣，17分鐘。
9. Elite Bikini Top：2件 Bikini Tops，3,000金幣，19分鐘。
10. Elite Sandals：2雙 Sandals，3,500金幣，21分鐘。
11. Master Fisher's Cap：2頂 Elite Fisher's Caps，4,000金幣，23分鐘。
12. Master Sunglasses：2副 Elite Sunglasses，4,500金幣，25分鐘。
13. Master Fishing Vest：2件 Elite Fishing Vests，5,000金幣，27分鐘。
14. Master Bikini Top：2件 Elite Bikini Tops，5,500金幣，29分鐘。
15. Master Sandals：2雙 Elite Sandals，6,000金幣，31分鐘。

6. 等級進度
--------------------

- 等級：50級跨越5張地圖（如 Coral Cove、Deep Abyss），每張有10個地點。
- 頭目戰：每5級（如第5級：Giant Bass，第50級：Kraken）。
- 調整：魚類屬性增加（如重量：第1級0.1公斤到第50級500公斤）。
- **Story Mode 地圖**：整合到敘事中，這些地圖隨著玩家在故事中的進展而解鎖，提供獨特的魚類和挑戰。
- **Practice Mode 地圖**：可用於技能改善或特定魚類狩獵，這些地圖與故事分離，排除 Story Mode 中使用的基礎魚類地圖。

7. 物品欄和裝備
--------------------------

7.1 物品欄系統
物品欄系統允許玩家管理 Fish Cards、裝備和其他物品：
- **容量**：50個槽位（可透過船隻或技能擴展到100個）。
- **組織**：物品分類為 Fish Cards、裝備（釣竿、誘餌、船隻、服裝）和消耗品（如能量藥水）。
- **管理**：玩家可按類型、稀有度或獲得時間排序物品。提供批量操作（如出售所有重複品）。
- **Fish Cards**：透過釣魚或自動釣魚收集，用於製作或贈送。每張卡片顯示魚的名稱、稀有度和屬性。
- **裝備**：包括釣竿、誘餌、船隻和服裝，每個都有獨特的屬性和效果。
- **操作**：裝備、合成、出售或贈送物品給同伴。

7.2 裝備系統
裝備增強玩家屬性，分為四種類型：
- **釣竿**：提升 Cast Accuracy、Tension Stability 等（如 Bamboo Rod：+5% Cast Accuracy）。
- **誘餌**：改善 Bite Rate、Lure Success 等（如 Basic Spinner：+20% Bite Rate）。
- **船隻**：增強 Boat Speed、Fish Detection 等（如 Rowboat：+3% Boat Speed）。
- **服裝**：提供各種增益（如 Fisher's Cap：+5% Energy）。

**裝備槽位**：7個槽位（1個釣竿，1個誘餌，1艘船隻，3件服裝，1-3個同伴）。
- **獲得**：透過合成配方製作或從商店購買。
- **升級**：合成低級裝備創造具有更好屬性的高級版本。
- **效果**：裝備屬性與玩家屬性加成疊加。

8. 社交系統
----------------

8.1 郵輪聊天室
- **同伴**：10個獨特的比基尼女孩同伴（如 Mia、Luna），每個都有不同的個性和故事情節。
- **互動**：贈送（使用 Fish Cards 或製作物品）、聊天（對話選擇）、任務（如自動釣魚分配）。
- **戀愛表**：0-100點，透過互動增加。里程碑（如50、100）解鎖獎勵如HCG場景。

8.2 故事和對話系統 (Naninovel)
- **Naninovel**：用於Unity的視覺小說框架，用於創建和管理故事情節、角色互動和對話。
- **整合**：Naninovel 處理所有敘事內容，包括同伴背景故事、戀愛弧線和事件對話。
- **特色**：分支對話選擇、基於好感度的回應，以及與玩家進度相關的腳本事件。
- **跨系統整合**：對話選擇可以影響遊戲玩法（如解鎖新釣魚點或臨時增益）。

9. 商店系統
--------------

- 選單：購買 Fish Cards、裝備、寶石。
- 貨幣：金幣（透過釣魚獲得）、寶石（付費購買）。
- 刷新：每日00:00更新庫存。

10. 成就系統
----------------------

- 類型：故事（如"捕獲 Kraken"）、進度（如"達到第50級"）、每日/每週任務。
- 獎勵：金幣（如500）、寶石（如10）、Fish Cards（如1星）。

11. 經濟系統
------------------

- 貨幣：金幣（透過釣魚獲得，每次捕獲50-5,000）、寶石（IAP或成就，1-100）。
- 平衡：早期遊戲：專注金幣（如 Bamboo Rod 200金幣）；後期遊戲：寶石實用性（如稀有 Fish Cards 50寶石）。

12. 美術和音訊
-----------------

- 美術風格：高品質3D模型與逼真材質，生動的水域，豪華船隻，詳細魚類。
- 音訊：放鬆的海洋音樂，水花聲，勝利捕獲號角。

13. 技術設計
--------------------

- 引擎：Unity（跨平台支援行動裝置、PC和主機）。
- 數據：雲端存檔，Fish Cards 和玩家進度使用JSON。
- 效能：60 FPS目標，優化的3D渲染和輕量級時間/天氣模擬。
- 視覺小說框架：Naninovel for Unity 處理所有敘事內容和對話系統。
