# Task ID: 4
# Title: Core Fishing Mechanics - Cast Minigame
# Status: pending
# Dependencies: 2, 3
# Priority: high
# Description: Implement the timing-based Cast Minigame. This includes the cast meter UI, logic for accurate casting to hotspots, and visual feedback (rod animation, lure trajectory).
# Details:
Create a Phaser scene for the Cast Minigame. UI: A cast meter (e.g., horizontal bar) with a moving indicator and a highlighted 'Accurate Section'. Logic: Player input (click/tap) stops the indicator. If inside 'Accurate Section', lure lands in a hotspot. 'Accurate Section' size and hotspot visibility are affected by Player's Cast Accuracy and Fish Detection attributes. Visuals: 2D fishing rod casting animation. Lure with fishing line flying towards water. Splash sound on impact. Success proceeds to Lure Minigame. 
```javascript
// CastMinigameScene.js (Phaser Scene)
// create() { setupMeter(); this.accurateSection = calculateAccurateSection(player.getEffectiveAttribute('castAccuracy')); }
// onInput() { if (indicatorPosition >= this.accurateSection.start && indicatorPosition <= this.accurateSection.end) { this.scene.start('LureMinigameScene', { isHotspot: true }); } else { this.scene.start('LureMinigameScene', { isHotspot: false }); } }
```

# Test Strategy:
Unit test cast accuracy logic. Playtest minigame for responsiveness and fairness. Verify visual feedback (animation, splash) matches cast outcome. Test impact of player attributes on 'Accurate Section' size.
