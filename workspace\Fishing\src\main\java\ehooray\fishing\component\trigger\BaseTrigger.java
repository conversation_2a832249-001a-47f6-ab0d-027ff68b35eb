package ehooray.fishing.component.trigger;

import java.lang.reflect.Field;
import java.util.List;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;

import org.springframework.stereotype.Component;

import ehooray.fishing.component.lua.IToLua;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.ComponentCsv;
import ehooray.fishing.pojo.utils.ReflectionUtils;


@Component
//for invoke components, and group their result logic
public class BaseTrigger
{
    public Object invoke(Input input, List<ComponentCsv.Row> componentRows) throws Exception
    {
        return null;
    }

    // Note: make sure all values are primitive value and can convert to lua
    public static class Input implements IToLua
    {
        protected int triggerId;
        protected long playerId;
        protected Object[] targets;

        @Override
        public LuaTable toLua() throws Exception
        {
            LuaTable table = LuaValue.tableOf();
            List<Field> fields = ReflectionUtils.getDeclaredFields(Input.class, this.getClass());
            for(Field field : fields)
            {
                // this class not put with child together, so force field accessible
                field.setAccessible(true);
                
                String fieldName = field.getName();
                Object fieldValue = field.get(this);
                if(fieldValue == null) continue;

                table.set(fieldName, objectToLua(fieldName, fieldValue));
            }

            return table;
        }
        
        

        protected LuaValue objectToLua(String name, Object value) throws ExceptionByCsvId
        {
            if(value.getClass() == Integer.class) return LuaValue.valueOf((int)value);
            if(value.getClass() == Long.class) return LuaValue.valueOf((long)value);
            if(value.getClass().isArray())
            {
                Object[] values = (Object[]) value;
                LuaTable table = LuaValue.tableOf();
                for(int i = 0; i < values.length; i++)
                    table.set(i + 1, objectToLua(name + i, values[i]));
                return table;
            }

            throw new ExceptionByCsvId("not support type = " + value.getClass() + ", field name = " + name);
        }

        public int getTriggerId()
        {
            return triggerId;
        }

        public void setTriggerId(int triggerId)
        {
            this.triggerId = triggerId;
        }

        public long getPlayerId()
        {
            return playerId;
        }

        public void setPlayerId(long playerId)
        {
            this.playerId = playerId;
        }

        public Object[] getTargets()
        {
            return targets;
        }

        public void setTargets(Object[] targets)
        {
            this.targets = targets;
        }

    }
}
