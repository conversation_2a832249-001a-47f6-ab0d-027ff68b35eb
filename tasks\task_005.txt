# Task ID: 5
# Title: Core Fishing Mechanics - Lure Minigame (Enhanced)
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Develop the Lure Minigame, including fish shadow behavior, 3-4 luring phases with rhythm-based inputs for 5 lure types, Lure Simulation UI, and attribute integration.
# Details:
Create a Phaser scene for Lure Minigame. UI: Lure Simulation UI (top-right) showing lure movement. Fish shadow approaches lure. Shadow Interest Meter. Input prompts. Logic: Fish shadow (small/medium/large) behavior reflects Fish Aggressiveness/Elusiveness. 3-4 phases; each requires rhythm inputs specific to equipped lure (Spinner: Pulse Tap; Soft Plastic: Drag and Pause; Fly: Swipe Flick Combo; Popper: Tap and <PERSON> Burst; Spoon: Circular Trace). Player Lure Success eases timing. Fish Elusiveness increases difficulty. Successful phases keep shadow; failure makes it flee. Final timed input hooks fish. Implement 'Regular Lure Control' (W/A/D for up/left/right lure movement with 1s cooldown). 
```javascript
// LureMinigameScene.js
// currentLureType = player.equippedLure.type;
// currentPhase = 0; requiredInputs = getInputsForLure(currentLureType, currentPhase);
// update() { /* handle player inputs, check against requiredInputs, update shadow interest */ }
// onHookSuccess() { this.scene.start('ReelInMinigameScene', { fishData: currentFish }); }
```

# Test Strategy:
Test each lure type's input mechanics. Verify fish shadow behavior based on attributes. Playtest phase difficulty progression. Check Lure Simulation UI responsiveness. Validate attribute effects (Lure Success, Bite Rate, Fish Aggressiveness/Elusiveness).
