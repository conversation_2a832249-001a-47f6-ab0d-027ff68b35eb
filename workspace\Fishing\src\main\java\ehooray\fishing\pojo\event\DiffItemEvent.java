package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;

@SuppressWarnings("serial")
public class DiffItemEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final DiffItem diffItem;
    private final boolean isProduce; // true = produce, false = consume

    public DiffItemEvent(Object source, PlayerEntity player, DiffItem diffItem, boolean isProduce)
    {
        super(source);
        this.player = player;
        this.diffItem = diffItem;
        this.isProduce = isProduce;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }

    public DiffItem getDiffItem()
    {
        return diffItem;
    }
    
    public boolean getIsProduce()
    {
        return isProduce;
    }

}
