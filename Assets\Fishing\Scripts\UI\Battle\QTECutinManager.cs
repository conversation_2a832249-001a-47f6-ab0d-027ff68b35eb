using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Cysharp.Threading.Tasks;
using Ehooray.UI;
using Ehooray;
using Zenject;

namespace Fishing.UI
{
    [AssetInfo("Prefabs/UI/Battle/QTECutinCanvas")]
    public class QTECutinManager : GamePanel
    {
        [Header("立繪設置")]
        [SerializeField] private RectTransform characterPanel;
        [SerializeField] private Image characterImage;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Text successText;
        [SerializeField] private AudioSource cutinAudioSource;
        [SerializeField] private AudioClip cutinSound;

        [Header("動畫設置")]
        [SerializeField] private float slideInDuration = 0.5f;
        [SerializeField] private float stayDuration = 2.0f;
        [SerializeField] private float slideOutDuration = 0.5f;
        [SerializeField] private float slideDistance = 1000f;
        [SerializeField] private Ease slideInEase = Ease.OutBack;
        [SerializeField] private Ease slideOutEase = Ease.InBack;

        // 角色立繪資源路徑
        private const string CHARACTER_SPRITE_PATH = "Picture/Characters/";
        
        #region Zenject Inject
        IResourceProvider resourceProvider;
        #endregion

        [Inject]
        public void Construct(IResourceProvider resourceProvider)
        {
            this.resourceProvider = resourceProvider;
        }

        private void Awake()
        {
            // 初始化時將立繪面板移出畫面
            if (characterPanel != null)
            {
                characterPanel.anchoredPosition = new Vector2(-slideDistance, characterPanel.anchoredPosition.y);
                characterPanel.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 顯示QTE成功的立繪動畫
        /// </summary>
        /// <param name="characterName">角色名稱，用於加載對應立繪</param>
        /// <param name="successLevel">成功等級: 0=普通成功, 1=良好, 2=完美</param>
        public async UniTask ShowCutinAnimation(string characterName = "DefaultFemale", int successLevel = 0)
        {
            // 加載角色立繪
            Sprite characterSprite = await LoadCharacterSprite(characterName);
            if (characterSprite != null && characterImage != null)
            {
                characterImage.sprite = characterSprite;
            }

            // 設置成功文字
            if (successText != null)
            {
                switch (successLevel)
                {
                    case 2:
                        successText.text = "Awesome!";
                        successText.color = new Color(1, 0.5f, 0.1f); // 橙色
                        break;
                    case 1:
                        successText.text = "Good Job!";
                        successText.color = Color.yellow;
                        break;
                    default:
                        successText.text = "Not Bad~";
                        successText.color = Color.white;
                        break;
                }
            }

            // 顯示立繪面板
            if (characterPanel != null)
            {
                characterPanel.gameObject.SetActive(true);
                
                // 播放音效
                if (cutinAudioSource != null && cutinSound != null)
                {
                    cutinAudioSource.PlayOneShot(cutinSound);
                }

                // 滑入動畫
                await characterPanel.DOAnchorPosX(0, slideInDuration)
                    .SetEase(slideInEase)
                    .AsyncWaitForCompletion();

                // 停留一段時間
                await UniTask.Delay((int)(stayDuration * 1000));

                // 滑出動畫
                await characterPanel.DOAnchorPosX(slideDistance, slideOutDuration)
                    .SetEase(slideOutEase)
                    .AsyncWaitForCompletion();

                // 隱藏面板
                characterPanel.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 加載角色立繪
        /// </summary>
        private async UniTask<Sprite> LoadCharacterSprite(string characterName)
        {
            try
            {
                return await resourceProvider.LoadResourceAsync<Sprite>($"{CHARACTER_SPRITE_PATH}{characterName}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load character sprite: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 立即關閉立繪
        /// </summary>
        public void HideCutin()
        {
            if (characterPanel != null)
            {
                DOTween.Kill(characterPanel);
                characterPanel.gameObject.SetActive(false);
            }
        }

        private void OnDestroy()
        {
            // 確保所有動畫在銷毀時被清理
            if (characterPanel != null)
            {
                DOTween.Kill(characterPanel);
            }
        }
    }
} 