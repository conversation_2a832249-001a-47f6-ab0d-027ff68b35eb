using Ehooray;
using Fishing;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

public class BattleEquipeData : MonoBehaviour
{

    public FishRodData rodData;
    public PlayerItem lineData;
    public PlayerItem baitData;

    #region Zenject Inject
    FishingRepositories fishingRepositories;
    FishingManager fishingManager;
    IResourceProvider provider;
    AssetPathConfig assetPathConfig;
    #endregion
    [Inject]
    public void Construct(FishingRepositories fishingRepositories,
                            FishingManager fishingManager,
                            IResourceProvider provider,
                            AssetPathConfig assetPathConfig)
    {
        this.fishingRepositories = fishingRepositories;
        this.fishingManager = fishingManager;
        this.provider = provider;
        this.assetPathConfig = assetPathConfig;

        SetEquipData();
        LoadRodModel();
    }
    public void SetEquipData()
    {
        var kit = GetActiveKit();
        if (kit == null)
            return;
        rodData = fishingRepositories.fishRodRepository.GetFishRod(kit.fishRodEntityId);
        lineData = fishingRepositories.playerItemRepository.GetPlayerItem(kit.fishLineCsvId);
        baitData = fishingRepositories.playerItemRepository.GetPlayerItem(kit.baitCsvId);
    }
    public async void LoadRodModel()
    {
        GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.FishRodModelPrefabPath + rodData.CsvData.Model);
        var targetParent = fishingManager.fishRod.rodModel;
        foreach (Transform child in targetParent.transform)
        {
            Destroy(child.gameObject);
        }
        var rod = Instantiate(obj, targetParent.transform);
        rod.transform.localPosition = Vector3.zero;
        rod.transform.localRotation = Quaternion.Euler(Vector3.zero);
        rod.transform.localScale = Vector3.one;

        fishingManager.fishRod.targetMesh = rod.GetComponent<MeshFilter>();
        fishingManager.fishRod.RestMesh();
    }
    public FishKitData GetActiveKit()
    {
        var fishKits = fishingRepositories.fishKitRepository.GetFishKits();
        foreach (var kit in fishKits.Values)
        {
            if (kit.isActive)
                return kit;
        }
        Debug.Log("no active Kit");
        return null;
    }
}
