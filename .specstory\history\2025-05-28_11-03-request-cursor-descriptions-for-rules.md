<!-- Generated by SpecStory -->

# Request cursor descriptions for rules (2025-05-28 19:03:56)

_**User**_

@core-systems.mdc @data-systems.mdc @fishing-minigames.mdc @project-overview.mdc @scene-systems.mdc @ui-input-system.mdc 這些rules請給我cursor description

---

_**Assistant**_



---

我来帮您查找这些 .mdc 文件并为您提供描述。让我先搜索这些文件的位置。

---

<details>
            <summary>Searched codebase "core-systems.mdc" • **3** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "data-systems.mdc" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "fishing-minigames.mdc" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "project-overview.mdc" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "scene-systems.mdc" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "ui-input-system.mdc" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

現在讓我讀取這些文件的內容來為您提供描述：

---

Read file: .cursor/rules/core-systems.mdc

---

Read file: .cursor/rules/data-systems.mdc

---

Read file: .cursor/rules/fishing-minigames.mdc

---

Read file: .cursor/rules/project-overview.mdc

---

Read file: .cursor/rules/scene-systems.mdc

---

Read file: .cursor/rules/ui-input-system.mdc

---



---

_**User**_

in english

---

_**Assistant**_



---

