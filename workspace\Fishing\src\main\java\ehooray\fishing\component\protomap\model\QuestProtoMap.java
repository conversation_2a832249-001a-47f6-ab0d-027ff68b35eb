package ehooray.fishing.component.protomap.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.QuestProto;
import ehooray.fishing.entity.QuestEntity;
import ehooray.fishing.entity.QuestTargetEntity;

@Component
public class QuestProtoMap
{
    public QuestProto.Quest.Builder toBuilderByEntity(QuestEntity quest)
    {
        QuestProto.Quest.Builder builder = QuestProto.Quest.newBuilder();
        
        builder.setEntityId(quest.getId());
        builder.setCsvId(quest.getCsvId());
        for (QuestTargetEntity target : quest.getTargets())
        {
            QuestProto.Quest.Target.Builder targetBuilder = QuestProto.Quest.Target.newBuilder();
            targetBuilder.setCsvId(target.getCsvId());
            targetBuilder.setCount(target.getTargetCount());
            builder.addTargets(targetBuilder.build());
        }
        builder.setIsAward(quest.getIsAward());
        
        return builder;
    }
    
    public Any toAnyByEntity(QuestEntity quest)
    {
        return Any.pack(toBuilderByEntity(quest).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<QuestEntity> quests)
    {
        ArrayList<Any> questProtos = new ArrayList<Any>(quests.size());
        for(QuestEntity quest : quests)
            questProtos.add(toAnyByEntity(quest));
        
        return questProtos;
    }
}
