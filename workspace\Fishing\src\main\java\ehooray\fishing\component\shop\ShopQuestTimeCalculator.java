//package ehooray.formosa.component.shop;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Optional;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import ehooray.formosa.component.character.CharacterAbilityCalculator;
//import ehooray.formosa.component.component.TriggeredComponentCollector;
//import ehooray.formosa.component.csv.CsvContainer;
//import ehooray.formosa.component.trigger.BaseTrigger;
//import ehooray.formosa.component.trigger.ValueResultPassiveComponentTrigger;
//import ehooray.formosa.entity.CharacterEntity;
//import ehooray.formosa.entity.PlayerEntity;
//import ehooray.formosa.entity.ShopEntity;
//import ehooray.formosa.pojo.csv.CommodityCsv;
//import ehooray.formosa.pojo.csv.ComponentCsv;
//import ehooray.formosa.pojo.csv.EventCsv;
//import ehooray.formosa.pojo.csv.SkillCsv;
//
//@Component
//public class ShopQuestTimeCalculator
//{
//    @Autowired
//    protected CsvContainer csvContainer;
//
//    @Autowired
//    protected TriggeredComponentCollector componentCollector;
//
//    @Autowired
//    protected ValueResultPassiveComponentTrigger passiveComponentTrigger;
//
//    @Autowired
//    protected CharacterAbilityCalculator characterAbilityCalculator;
//
//    public LocalDateTime calculate(PlayerEntity player,
//            ShopEntity shop, CharacterEntity dispatchCharacter, LocalDateTime now) throws Exception
//    {
//
//        CommodityCsv.Row commodityRow = csvContainer.getCsv(CommodityCsv.class).getById(shop.getCommodityCsvId());
//        EventCsv.Row eventRow = csvContainer.getCsv(EventCsv.class).getById(commodityRow.getEventId());
//
//        int characterAbility = characterAbilityCalculator.calculate(player, dispatchCharacter, eventRow.getAbility());
//        int characterAbilityReduce = -(int)(eventRow.getEventTime() * (characterAbility * 0.001f));
//
//
//        Object[] componentTargets = { eventRow.getEventTime() };
//        TriggerInput triggerInput = new TriggerInput();
//        triggerInput.setTargets(componentTargets);
//        triggerInput.setPlayerId(player.getId());
//        triggerInput.setTriggerId(SkillCsv.Trigger.CalculateShopQuestTime.getId());
//        triggerInput.setShopEntityId(shop.getId());
//        triggerInput.setCharacterEntityId(dispatchCharacter.getId());
//
//
//        TriggeredComponentCollector.Input componentCollectorInput = new TriggeredComponentCollector.Input();
//        componentCollectorInput.setCharacter(Optional.of(dispatchCharacter));
//        componentCollectorInput.setTriggerInput(triggerInput);
//
//        List<ComponentCsv.Row> triggeredComponents = componentCollector.collect(componentCollectorInput);
//        List<?> results = (List<?>)passiveComponentTrigger.invoke(triggerInput, triggeredComponents);
//        int skillReduce = 0;
//        for(Object result : results)
//        {
//            Object[] resultArray = (Object[])result;
//            skillReduce += (int)resultArray[0];
//        }
//
//        int finalTime = eventRow.getEventTime() + characterAbilityReduce + skillReduce;
//        if(finalTime < 0) finalTime = 0;
//        return now.plusSeconds(finalTime);
//    }
//
//    public static class TriggerInput extends BaseTrigger.Input
//    {
//        protected long shopEntityId;
//        protected long characterEntityId;
//        public long getShopEntityId()
//        {
//            return shopEntityId;
//        }
//        public void setShopEntityId(long shopEntityId)
//        {
//            this.shopEntityId = shopEntityId;
//        }
//        public long getCharacterEntityId()
//        {
//            return characterEntityId;
//        }
//        public void setCharacterEntityId(long characterEntityId)
//        {
//            this.characterEntityId = characterEntityId;
//        }
//
//    }
//}
