# Task ID: 9
# Title: Boat Menu, Navigation, and Game Loop Integration
# Status: pending
# Dependencies: 1, 2, 4, 7
# Priority: high
# Description: Fully implement the Boat Menu UI and functionality: map/spot selection (5 maps, 10 spots each), travel mechanics, energy system, and integration of core gameplay actions (Fishing, Chat, Shop, Inventory, Return to Port).
# Details:
Enhance `BoatMenuScene`. UI: Display current Time, Weather, Location (Map X, Spot Y or Starting Port), Player Energy. Buttons: Travel, Go Fishing, Go Chatroom, Go Shop (active only at Starting Port), Go Inventory, Return to Port (active only at fishing spots). Travel: UI to select map (1 of 5) then spot (1 of 10). Updates player location, advances time (+1h), consumes Energy (-2). `BoatSpeed` attribute reduces travel time. Fishing: UI to choose Story/Practice Mode. Leads to Cast Minigame. Advances time (+30m), consumes Energy (-5). Energy System: Max 100 (base), +2/level. Actions consume energy. Return to Port: Sets location to Starting Port, advances time (+1h), consumes Energy (-2). Implement data for maps and spots (e.g., in JSON). 
```javascript
// BoatMenuScene.js
// onTravel(mapId, spotId) { player.location = {mapId, spotId}; timeSystem.advance(60); player.energy -= 2; }
// onGoFishing() { /* select mode */ scene.start('CastMinigameScene'); timeSystem.advance(30); player.energy -= 5; }
```

# Test Strategy:
Test all Boat Menu button functionalities. Verify travel logic: map/spot selection, time/energy consumption, location update. Test fishing initiation. Check energy consumption and regeneration (if any). Ensure Shop/Return to Port buttons are conditionally active. Verify UI correctly reflects game state (time, weather, location, energy).
