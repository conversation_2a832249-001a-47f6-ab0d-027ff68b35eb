package ehooray.fishing.unit.entity;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

import ehooray.fishing.entity.FishRodEntity;

public class FishRodEntityTest
{

    @Test
    public void testDiscount_1()
    {
        var fishRodEntity = new FishRodEntity();
        fishRodEntity.setDqr(0);
        assertThat(fishRodEntity.getRepairDiscount()).isEqualTo(1f);
    }
    
    @Test
    public void testDiscount_0()
    {
        var fishRodEntity = new FishRodEntity();
        fishRodEntity.setDqr(500);
        assertThat(fishRodEntity.getRepairDiscount()).isEqualTo(0f);
    }
    
    @Test
    public void testDiscount_96f()
    {
        var fishRodEntity = new FishRodEntity();
        fishRodEntity.setDqr(20);
        assertThat(fishRodEntity.getRepairDiscount()).isEqualTo(.96f);
    }
}
