package ehooray.fishing.pojo.csv;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("SpotTable.xlsx")
public class SpotCsv extends ACsv<SpotCsv.Row>
{

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("Aera")
        protected int area;
        
        @ExcelCellName("UnlockCondition")
        protected int unlockCondition;
        
        @ExcelCellName("FishGroup")
        protected int fishGroup;
        
        @ExcelCellName("ApCost")
        protected int apCost;
        
        public int getRowIndex()
        {
            return rowIndex;
        }

        @Override
        public int getId()
        {
            return id;
        }
        
        public int getArea()
        {
            return area;
        }
        
        public int getUnlockCondition()
        {
            return unlockCondition;
        }
        
        public int getFishGroup()
        {
            return fishGroup;
        }
        
        public int getApCost()
        {
            return apCost;
        }
    }

}
