package ehooray.fishing.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter;

@Configuration
public class HttpProtoConfig 
{
    @Bean
    ProtobufHttpMessageConverter protobufHttpMessageConverter() 
    {
        return new ProtobufHttpMessageConverter();
    }
}
