using UnityEngine;
using UnityEngine.UI;
using Ehooray; // 假設 AssetInfo 在此命名空間中
using DG.Tweening;
using Ehooray.UI; // 使用 DOTween 來實現平滑動畫

[AssetInfo("Prefabs/UI/Battle/LureAnimationCanvas")]
public class LureAnimationController : GamePanel
{
    [Header("UI Elements")]
    [SerializeField] private Image fishImage;
    [SerializeField] private float bottomBoundaryY = -70f; // 新增：底部邊界Y座標值 (可根據需要調整默認值)
    [SerializeField] private float topBoundaryY = 90f; // 新增：頂部邊界Y座標值

    [Header("Animation Parameters")]
    [SerializeField] private float driftSpeed = 50f; // 修改含義：垂直下沉速度 (單位/秒)
    [SerializeField] private float driftMagnitude = 10f; // 水平擺動幅度
    [SerializeField] private float horizontalSwaySegmentDuration = 1f; // 新增：水平擺動單程時間
    [SerializeField] private float dashDuration = 0.2f;
    [SerializeField] private float returnDuration = 0.5f;
    [SerializeField] private float dashDistance = 100f;

    private RectTransform fishRectTransform;
    private Vector2 centerPosition; // 初始水平中心位置 (X軸) 及 初始Y軸位置 ((topBoundaryY + bottomBoundaryY) / 2)
    private Sequence driftSequence; // 用於水平擺動動畫
    private bool isAnimating = false; // 防止並發QTE動畫

    void Start()
    {
        fishRectTransform = fishImage.rectTransform;
        if (fishRectTransform != null)
        {
            // 驗證邊界值
            if (topBoundaryY < bottomBoundaryY)
            {
                Debug.LogWarning($"LureAnimationController: topBoundaryY ({topBoundaryY}) is less than bottomBoundaryY ({bottomBoundaryY}). Adjusting topBoundaryY to be equal to bottomBoundaryY.");
                topBoundaryY = bottomBoundaryY;
            }

            float initialAnchorX = fishRectTransform.anchoredPosition.x;
            float initialYPosition = (topBoundaryY + bottomBoundaryY) / 2f;
            centerPosition = new Vector2(initialAnchorX, initialYPosition);

            fishRectTransform.anchoredPosition = centerPosition; // 設定魚的初始位置到計算出的中心點
        }
        else
        {
            Debug.LogError("LureAnimationController: fishImage.rectTransform is null.");
            centerPosition = new Vector2(0, (topBoundaryY + bottomBoundaryY) / 2f); // 即使RectTransform為空，也嘗試計算一個合理的Y中心
            enabled = false; 
            return;
        }

        StartDrifting();
    }

    void OnDisable()
    {
        // 在對象銷毀時停止 tween 以防止錯誤
        driftSequence?.Kill();
        fishRectTransform?.DOKill();
    }

    private void StartDrifting()
    {
        if (isAnimating) return; // 如果QTE動畫正在播放，則不開始漂移/下沉

        // 確保在啟動新的動畫序列之前，停止之前的序列和所有相關tween
        driftSequence?.Kill();
        fishRectTransform?.DOKill(); // 停止所有在fishRectTransform上的tween (包括下沉和舊的擺動)

        if (fishRectTransform == null) return;

        // 在開始漂移動畫前，確保魚的位置不超過頂部邊界
        if (fishRectTransform.anchoredPosition.y > topBoundaryY)
        {
            fishRectTransform.anchoredPosition = new Vector2(fishRectTransform.anchoredPosition.x, topBoundaryY);
        }
        // 同時也確保不低於底部邊界 (雖然下沉邏輯會處理，但多一層防護)
        if (fishRectTransform.anchoredPosition.y < bottomBoundaryY)
        {
            fishRectTransform.anchoredPosition = new Vector2(fishRectTransform.anchoredPosition.x, bottomBoundaryY);
        }

        // --- 下沉動畫 ---
        float targetSinkAnchorY = bottomBoundaryY; // 直接使用設定的Y值作為目標
        float currentAnchorY = fishRectTransform.anchoredPosition.y;
        float distanceToSink = currentAnchorY - targetSinkAnchorY;

        if (distanceToSink > 0.01f) // 只有當高於底部邊界時才下沉
        {
            float sinkDuration = distanceToSink / driftSpeed;
            if (sinkDuration <= 0.001f) sinkDuration = 0.001f;

            fishRectTransform.DOAnchorPosY(targetSinkAnchorY, sinkDuration)
                .SetEase(Ease.Linear);
        }
        else
        {
            // 如果已經在底部或以下，則將其位置固定在底部目標Y值
            fishRectTransform.anchoredPosition = new Vector2(fishRectTransform.anchoredPosition.x, targetSinkAnchorY);
        }

        // --- 水平擺動動畫 (圍繞 centerPosition.x) ---
        // 即使在下沉或已到達底部，也應進行水平擺動
        driftSequence = DOTween.Sequence();
        // 擺動路徑: center.x -> center.x + magnitude -> center.x - magnitude -> center.x + magnitude ...
        driftSequence.Append(fishRectTransform.DOAnchorPosX(centerPosition.x + driftMagnitude, horizontalSwaySegmentDuration).SetEase(Ease.InOutSine));
        driftSequence.Append(fishRectTransform.DOAnchorPosX(centerPosition.x - driftMagnitude, horizontalSwaySegmentDuration).SetEase(Ease.InOutSine));
        driftSequence.SetLoops(-1, LoopType.Yoyo); // Yoyo循環使其在兩個 Append 點之間來回擺動
        driftSequence.Play();
    }

    /// <summary>
    /// 當 QTE 成功完成時調用此方法。
    /// </summary>
    /// <param name="direction">衝刺的標準化方向向量。</param>
    public void OnQTESuccess(Vector2 direction)
    {
        if (isAnimating || fishRectTransform == null) return;

        isAnimating = true;
        fishRectTransform.DOKill(); 

        Vector2 currentFishPosition = fishRectTransform.anchoredPosition; 
        Vector2 dashOffset = direction.normalized * dashDistance;
        Vector2 dashTargetPosition = currentFishPosition + dashOffset; 

        float finalReturnY;
        if (dashTargetPosition.y > topBoundaryY)
        {
            finalReturnY = topBoundaryY; // 如果衝刺後高於頂部邊界，則Y返回到頂部邊界
        }
        else
        {
            finalReturnY = dashTargetPosition.y; // 否則，Y保持在衝刺後的高度
        }
        // 注意：如果 dashTargetPosition.y < bottomBoundaryY，我們目前不在此處處理，
        // StartDrifting() 中的下沉邏輯和邊界檢查會處理後續行為。

        Vector2 returnTargetPosition = new Vector2(centerPosition.x, finalReturnY);

        DOTween.Sequence()
            .Append(fishRectTransform.DOAnchorPos(dashTargetPosition, dashDuration).SetEase(Ease.OutQuad))
            .Append(fishRectTransform.DOAnchorPos(returnTargetPosition, returnDuration).SetEase(Ease.InCubic))
            .OnComplete(() =>
            {
                isAnimating = false;
                StartDrifting(); 
            })
            .Play();
    }

    public void ResetPosition()
    {
        if (fishRectTransform == null) return;
        
        isAnimating = false; // 重置時，確保isAnimating標記也復位
        fishRectTransform.DOKill(); // 停止任何當前動畫（包括下沉和擺動）
        // driftSequence?.Kill(); // StartDrifting會處理舊的driftSequence

        // centerPosition 的Y現在是topBoundaryY，所以這會將魚重置到 (initialX, topBoundaryY)
        fishRectTransform.anchoredPosition = centerPosition;
        StartDrifting(); // 從(initialX, topBoundaryY)重新開始下沉和擺動
    }
}