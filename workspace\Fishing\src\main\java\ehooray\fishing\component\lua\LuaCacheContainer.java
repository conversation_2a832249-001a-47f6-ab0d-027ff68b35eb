package ehooray.fishing.component.lua;

import java.util.List;
import java.util.Set;

import org.luaj.vm2.LuaTable;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class LuaCacheContainer
{

    @Autowired
    protected BeanFactory beanFactory;

    @Value("${game.lua.cache-class-package}")
    protected List<String> luaCaheClassPackages;

    protected LuaTable cache;

    public void initial() throws Exception
    {
        cache = LuaTable.tableOf();
        
        String[] packages = luaCaheClassPackages.toArray(String[]::new);
        ConfigurationBuilder configBuilder = new ConfigurationBuilder();
        configBuilder.setScanners(new TypeAnnotationsScanner(), new SubTypesScanner(false));
        configBuilder.forPackages(packages);

        Reflections reflections = new Reflections(configBuilder);
        Set<Class<? extends ILuaCacheComponent>> classes = reflections.getSubTypesOf(ILuaCacheComponent.class);
        for (Class<?> singleClass : classes)
        {
            Object instanceObj = beanFactory.getBean(singleClass);

            ILuaCacheComponent luaCacheInstance = (ILuaCacheComponent)instanceObj;
            luaCacheInstance.initial();
            // collect into cache
            cache.set(singleClass.getSimpleName(), luaCacheInstance.toLua());
        }
    }

    public LuaTable toLua()
    {
        return cache;
    }

}
