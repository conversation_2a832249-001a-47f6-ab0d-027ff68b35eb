// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/auth/MailAuth.proto

package ehooray.fishing.dto.proto.request.auth;

public final class MailAuthProto {
  private MailAuthProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MailAuthOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.auth.MailAuth)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string mail = 1;</code>
     * @return The mail.
     */
    java.lang.String getMail();
    /**
     * <code>string mail = 1;</code>
     * @return The bytes for mail.
     */
    com.google.protobuf.ByteString
        getMailBytes();

    /**
     * <code>string password = 2;</code>
     * @return The password.
     */
    java.lang.String getPassword();
    /**
     * <code>string password = 2;</code>
     * @return The bytes for password.
     */
    com.google.protobuf.ByteString
        getPasswordBytes();

    /**
     * <code>int32 serverId = 3;</code>
     * @return The serverId.
     */
    int getServerId();
  }
  /**
   * Protobuf type {@code request.auth.MailAuth}
   */
  public static final class MailAuth extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.auth.MailAuth)
      MailAuthOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MailAuth.newBuilder() to construct.
    private MailAuth(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MailAuth() {
      mail_ = "";
      password_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MailAuth();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.auth.MailAuthProto.internal_static_request_auth_MailAuth_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.auth.MailAuthProto.internal_static_request_auth_MailAuth_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.class, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder.class);
    }

    public static final int MAIL_FIELD_NUMBER = 1;
    private volatile java.lang.Object mail_;
    /**
     * <code>string mail = 1;</code>
     * @return The mail.
     */
    @java.lang.Override
    public java.lang.String getMail() {
      java.lang.Object ref = mail_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mail_ = s;
        return s;
      }
    }
    /**
     * <code>string mail = 1;</code>
     * @return The bytes for mail.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMailBytes() {
      java.lang.Object ref = mail_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mail_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PASSWORD_FIELD_NUMBER = 2;
    private volatile java.lang.Object password_;
    /**
     * <code>string password = 2;</code>
     * @return The password.
     */
    @java.lang.Override
    public java.lang.String getPassword() {
      java.lang.Object ref = password_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        password_ = s;
        return s;
      }
    }
    /**
     * <code>string password = 2;</code>
     * @return The bytes for password.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPasswordBytes() {
      java.lang.Object ref = password_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        password_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SERVERID_FIELD_NUMBER = 3;
    private int serverId_;
    /**
     * <code>int32 serverId = 3;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.auth.MailAuth}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.auth.MailAuth)
        ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.auth.MailAuthProto.internal_static_request_auth_MailAuth_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.auth.MailAuthProto.internal_static_request_auth_MailAuth_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.class, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        mail_ = "";

        password_ = "";

        serverId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.auth.MailAuthProto.internal_static_request_auth_MailAuth_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth build() {
        ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth buildPartial() {
        ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth result = new ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth(this);
        result.mail_ = mail_;
        result.password_ = password_;
        result.serverId_ = serverId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private java.lang.Object mail_ = "";
      /**
       * <code>string mail = 1;</code>
       * @return The mail.
       */
      public java.lang.String getMail() {
        java.lang.Object ref = mail_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          mail_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string mail = 1;</code>
       * @return The bytes for mail.
       */
      public com.google.protobuf.ByteString
          getMailBytes() {
        java.lang.Object ref = mail_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          mail_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string mail = 1;</code>
       * @param value The mail to set.
       * @return This builder for chaining.
       */
      public Builder setMail(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        mail_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string mail = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMail() {
        
        mail_ = getDefaultInstance().getMail();
        onChanged();
        return this;
      }
      /**
       * <code>string mail = 1;</code>
       * @param value The bytes for mail to set.
       * @return This builder for chaining.
       */
      public Builder setMailBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        mail_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object password_ = "";
      /**
       * <code>string password = 2;</code>
       * @return The password.
       */
      public java.lang.String getPassword() {
        java.lang.Object ref = password_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          password_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string password = 2;</code>
       * @return The bytes for password.
       */
      public com.google.protobuf.ByteString
          getPasswordBytes() {
        java.lang.Object ref = password_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          password_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string password = 2;</code>
       * @param value The password to set.
       * @return This builder for chaining.
       */
      public Builder setPassword(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        password_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string password = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPassword() {
        
        password_ = getDefaultInstance().getPassword();
        onChanged();
        return this;
      }
      /**
       * <code>string password = 2;</code>
       * @param value The bytes for password to set.
       * @return This builder for chaining.
       */
      public Builder setPasswordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        password_ = value;
        onChanged();
        return this;
      }

      private int serverId_ ;
      /**
       * <code>int32 serverId = 3;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <code>int32 serverId = 3;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 serverId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.auth.MailAuth)
    }

    // @@protoc_insertion_point(class_scope:request.auth.MailAuth)
    private static final ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth();
    }

    public static ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailAuth>
        PARSER = new com.google.protobuf.AbstractParser<MailAuth>() {
      @java.lang.Override
      public MailAuth parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailAuth> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailAuth> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_auth_MailAuth_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_auth_MailAuth_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033request/auth/MailAuth.proto\022\014request.a" +
      "uth\"<\n\010MailAuth\022\014\n\004mail\030\001 \001(\t\022\020\n\010passwor" +
      "d\030\002 \001(\t\022\020\n\010serverId\030\003 \001(\005BQ\n&ehooray.fis" +
      "hing.dto.proto.request.authB\rMailAuthPro" +
      "toH\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_auth_MailAuth_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_auth_MailAuth_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_auth_MailAuth_descriptor,
        new java.lang.String[] { "Mail", "Password", "ServerId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
