package ehooray.fishing.component.logger;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.schedule.CheckMaintainSchedule;
import ehooray.fishing.component.utils.ProtoToStringConverter;
import ehooray.fishing.dto.proto.request.RequestProto;

@Component
@Aspect
public class CheckMaintainScheduleLogger
{
    @Autowired
    protected ProtoToStringConverter protoToStringConverter;

    @Pointcut("execution(* ehooray.fishing.component.schedule.CheckMaintainSchedule.check())")
    protected void check()
    {
    }

    @Before("check()")
    protected void before_check(JoinPoint joinPoint) throws Exception
    {
        LogManager.getLogger(this.getClass()).debug("start check");
    }

    @AfterReturning(value = "check()", returning = "state")
    protected void afterReturning_check(JoinPoint joinPoint, CheckMaintainSchedule.State state) throws Exception
    {
        LogManager.getLogger(this.getClass()).debug("end check, state = " + state);
    }

    @AfterThrowing(value = "check()", throwing = "exception")
    protected void afterThrowing_check(JoinPoint joinPoint, Exception exception) throws Exception
    {
        LogManager.getLogger(this.getClass()).error("check failed", exception);
    }

    @Before(value = "execution(* ehooray.fishing.component.WebSocketHandler.broadcastRequest(*, *, *))")
    public void broadcastRequest(JoinPoint joinPoint) throws Exception
    {
        String loggerName = (String) joinPoint.getArgs()[2];
        if(!loggerName.equals(this.getClass().getCanonicalName())) return;

        List<?> sessions = (List<?>) joinPoint.getArgs()[0];
        RequestProto.Request requestProto = (RequestProto.Request) joinPoint.getArgs()[1];
        LogManager.getLogger(this.getClass()).debug(String.format("sessions count = %d, request = %s",
                sessions.size(), protoToStringConverter.logRequestProto(requestProto)));
    }
}