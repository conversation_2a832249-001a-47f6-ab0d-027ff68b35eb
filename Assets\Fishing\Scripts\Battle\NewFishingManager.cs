using StylizedWater2;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Fishing.UI;
using Zenject;
using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.Network;

namespace Fishing
{
    /// <summary>
    /// 新釣魚遊戲的核心管理器
    /// 負責管理整個釣魚遊戲的流程和狀態
    /// 包含釣竿、魚線、魚類、相機等遊戲物件的控制
    /// 以及各種遊戲狀態的管理（拋餌、收線、結束等）
    /// 新版本改進了張力系統和加分區機制
    /// </summary>
    public class NewFishingManager : FishingManager
    {
        [Header("新釣魚系統組件")]
        // 移除Inspector引用，使用動態加載
        // public NewLineTensionManager newLineTensionManager;
        private NewLineTensionManager newLineTensionManager;
        
        // 覆蓋原有的ResetValue方法，確保正確清理事件和引用
        protected override void ResetValue()
        {
            fishRod.ResetValue();
            forceItem.ResetValue();
            fishLine.ResetValue();
            baitPath.ResetValue();
            baitLine.gameObject.SetActive(true);
            ResetCamera();

            fishingGamePanelManager.Close<CloseupTextManager>();
            
            // 關閉新的張力UI
            if (newLineTensionManager != null)
            {
                // 先移除事件監聽，避免因為事件引用而導致的問題
                if (cordReelManager != null && cordReelManager.pullButton != null)
                {
                    var trigger = FishingEventTrigger.GetTrigger(cordReelManager.pullButton.gameObject);
                    trigger.onPointerDown -= newLineTensionManager.OnPullButtonPressed;
                    trigger.onPointerUp -= newLineTensionManager.OnPullButtonUp;
                }
                
                newLineTensionManager.CloseRoot();
                fishingGamePanelManager.Close<NewLineTensionManager>();
                
                // 重置新張力管理器狀態
                newLineTensionManager.ResetStatus();
                newLineTensionManager = null; // 清空引用
            }
            
            fishingGamePanelManager.Close<FishInfoManager>();
            fishingGamePanelManager.Close<GameEndManager>();

            // 重新啟用捲線器UI
            fishingGamePanelManager.OpenAsync<CordReelManager>().Forget();
            if (cordReelManager != null)
            {
                cordReelManager.StartInteractive();
                cordReelManager.StopInteractive();
                cordReelManager.StartInteractive();
                
                if (cordReelManager.wheelRotate != null)
                    cordReelManager.wheelRotate.StopRotate();
            }

            fishingGamePanelManager.Close<ContinueManager>();
            throwBaitTime = 0f;
        }
    }
} 