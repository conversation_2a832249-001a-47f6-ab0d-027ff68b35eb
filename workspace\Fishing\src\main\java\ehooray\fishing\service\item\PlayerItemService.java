package ehooray.fishing.service.item;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv.Tag;
import ehooray.fishing.pojo.event.DiffItemEvent;
import ehooray.fishing.repository.PlayerItemRepository;

@Service
public class PlayerItemService
{
    @Autowired
    protected ApplicationEventPublisher publisher;

    @Autowired
    protected PlayerItemRepository playerItemRepository;

    @Autowired
    protected CsvContainer csvContainer;

    public List<PlayerItemEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getPlayerItems();
    }

    public Optional<PlayerItemEntity> findByCsvId(PlayerEntity player, int csvId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getCsvId() == csvId).findFirst();
    }

    protected PlayerItemEntity findOrAdd(PlayerEntity player, int csvId)
    {
        Optional<PlayerItemEntity> findItem = findByCsvId(player, csvId);
        if(findItem.isPresent()) return findItem.get();

        return createItem(player, csvId);
    }

    protected PlayerItemEntity createItem(PlayerEntity player, int csvId)
    {
        PlayerItemEntity newItem = new PlayerItemEntity();
        newItem.setPlayer(player);
        newItem.setCsvId(csvId);
        getAllByPlayer(player).add(newItem);
        return newItem;
    }

    public DiffItem consumeItem(PlayerEntity player, PlayerItemCsv.Row playerItemRow, int count) throws Exception
    {
        Optional<PlayerItemEntity> findItem = findByCsvId(player, playerItemRow.getId());
        if(findItem.isEmpty()) throw new ExceptionByCsvId("item not enough");
        if(findItem.get().getCount() < count) throw new ExceptionByCsvId("item not enough");
        
        PlayerItemEntity playerItem = findItem.get();
        playerItem.setCount(playerItem.getCount() - count);
        playerItemRepository.save(playerItem);

        DiffItem diffItem = new DiffItem(playerItem, PlayerItemCsv.Tag.valueOf(playerItemRow.getTag()).get(), -count);
        publisher.publishEvent(new DiffItemEvent(this, player, diffItem, false));
        return diffItem;
    }

    public DiffItem produceItem(PlayerEntity player, PlayerItemCsv.Row playerItemRow, int count)
    {
        PlayerItemEntity playerItem = findOrAdd(player, playerItemRow.getId());
        playerItem.setCount(playerItem.getCount() + count);
        playerItemRepository.save(playerItem);

        DiffItem diffItem = new DiffItem(playerItem, PlayerItemCsv.Tag.valueOf(playerItemRow.getTag()).get(), count);
        publisher.publishEvent(new DiffItemEvent(this, player, diffItem, true));
        return diffItem;
    }

    public ArrayList<PlayerItemService.DiffItem> consumeItems(PlayerEntity player, int[] csvIds, int[] counts) throws Exception
    {
        int[] consumeResourceIds = csvIds;
        int[] consumeResourceNums = counts;
        ArrayList<PlayerItemService.DiffItem> consumeItemResult = new ArrayList<>();
        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
        for (int i = 0; i < consumeResourceIds.length; i++)
        {
            int consumeResourceId = consumeResourceIds[i];
            int consumeResourceNum = consumeResourceNums[i];

            Optional<PlayerItemCsv.Row> playerItemRow = playerItemCsv.getOptionalById(consumeResourceId);
            if (playerItemRow.isEmpty()) continue;

            PlayerItemService.DiffItem result = consumeItem(player, playerItemRow.get(), consumeResourceNum);
            consumeItemResult.add(result);
        }

        return consumeItemResult;
    }

    public ArrayList<PlayerItemService.DiffItem> produceItems(PlayerEntity player, int[] csvIds, int[] counts) throws Exception
    {
        // produce item
        int[] itemIds = csvIds;
        int[] itemNums = counts;
        ArrayList<PlayerItemService.DiffItem> produceItemResult = new ArrayList<>();
        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
        for (int i = 0; i < itemIds.length; i++)
        {
            int itemId = itemIds[i];
            int itemNum = itemNums[i];

            Optional<PlayerItemCsv.Row> playerItemRow = playerItemCsv.getOptionalById(itemId);
            if (playerItemRow.isEmpty()) continue;

            PlayerItemService.DiffItem result = produceItem(player, playerItemRow.get(), itemNum);
            produceItemResult.add(result);
        }

        return produceItemResult;
    }

    public void validateItemLimit(PlayerEntity player, int[] csvIds) throws Exception
    {
        int[] itemIds = csvIds;
        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
        for (int itemId : itemIds)
        {
            Optional<PlayerItemCsv.Row> playerItemRow = playerItemCsv.getOptionalById(itemId);
            if (playerItemRow.isEmpty()) continue;

            validateSingleItemLimit(player, playerItemRow.get());
        }
    }

    protected void validateSingleItemLimit(PlayerEntity player, PlayerItemCsv.Row playerItemRow) throws Exception
    {
        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
        switch(playerItemRow.getTagEnum().get())
        {
//            case Resource:
//                PlayerItemCsv.Row limitRow = playerItemCsv.findByTag(Tag.WarehouseMaxLimit.getId());
//                PlayerItemEntity limitPlayerItem = findOrAdd(player, limitRow.getId());
//                PlayerItemEntity playerItem = findOrAdd(player, playerItemRow.getId());
//                if(playerItem.getCount() >= limitPlayerItem.getCount())
//                    throw new ExceptionByCsvId(String.format("over limit, %d > %d", playerItem.getCount(), limitPlayerItem.getCount()));
//                return;

            default:
                return;
        }
    }

    public class DiffItem
    {
        protected PlayerItemEntity playerItem;
        protected int diffCount;
        protected PlayerItemCsv.Tag itemTag;
        public DiffItem(PlayerItemEntity playerItem, PlayerItemCsv.Tag itemTag, int diffCount)
        {
            this.playerItem = playerItem;
            this.itemTag = itemTag;
            this.diffCount = diffCount;
        }
        public PlayerItemEntity getPlayerItem()
        {
            return playerItem;
        }
        public PlayerItemCsv.Tag getItemTag()
        {
            return itemTag;
        }
        public int getDiffCount()
        {
            return diffCount;
        }
    }
}
