package ehooray.fishing.pojo.csv;

import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("UpgradeTable.xlsx")
public class UpgradeCsv extends ACsv<UpgradeCsv.Row>
{

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        @ExcelCellName("Rarity")
        protected int rarity;
        @ExcelCellName("UpgradeLv")
        protected int upgradeLv;
        @ExcelCellName("UpgradeSuccessProb")
        protected int upgradeSuccessProb;
        @ExcelCellName("MaterialBonus")
        protected int materialBonus;
        @ExcelCellName("UpgradeCost")
        protected int upgradeCost;
        
        public int getRowIndex()
        {
            return rowIndex;
        }
        
        @Override
        public int getId()
        {
            return id;
        }

        public int getRarity()
        {
            return rarity;
        }

        public int getUpgradeLv()
        {
            return upgradeLv;
        }

        public int getUpgradeSuccessProb()
        {
            return upgradeSuccessProb;
        }

        public int getMaterialBonus()
        {
            return materialBonus;
        }

        public int getUpgradeCost()
        {
            return upgradeCost;
        }
    }

    public Optional<Row> findByRareAndLv(int rare, int lv)
    {
        return getRows().stream().filter(r -> r.getRarity() == rare && r.getUpgradeLv() == lv).findFirst();
    }
}
