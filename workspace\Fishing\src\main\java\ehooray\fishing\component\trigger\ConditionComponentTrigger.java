package ehooray.fishing.component.trigger;

import java.util.List;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.component.BasePassiveComponent;
import ehooray.fishing.pojo.csv.ComponentCsv;


@Component
public class ConditionComponentTrigger extends BaseTrigger
{
    @Autowired
    protected BeanFactory beanFactory;

    /**
     * Trigger all component rows with condition, all invoke result is boolean
     * implementation:(LuaComponent)
     * @return boolean or equal all componentRows
     */
    @Override
    public Object invoke(BaseTrigger.Input input, List<ComponentCsv.Row> componentRows) throws Exception
    {
        boolean results = false;
        for(ComponentCsv.Row componentRow : componentRows)
        {
            Class<?> invoker = ComponentCsv.Main.valueOf(componentRow.getMain()).get().getInvoker();
            BasePassiveComponent passiveComponent = (BasePassiveComponent)beanFactory.getBean(invoker);

            Object[] resultArray = (Object[])passiveComponent.invoke(input, componentRow);
            results |= (boolean)resultArray[0];
        }
        return results;
    }

}
