# Task ID: 1
# Title: Project Setup and Core Game Structure (Phaser 3)
# Status: pending
# Dependencies: None
# Priority: high
# Description: Initialize the Phaser 3 project, set up basic game states (e.g., TitleScreen, BoatMenuScene, FishingScene), and establish the main game loop structure as outlined in the PRD. Configure asset loading for initial sprites and sounds.
# Details:
Create a new Phaser 3 project. Define main game configuration (dimensions, renderer). Implement a SceneManager to handle transitions between scenes like TitleScreen, BoatMenuScene. Create placeholder scenes for core gameplay areas. Basic UI elements for the Title Screen (e.g., 'Start Game' button) and Boat Menu (placeholder for time, weather, location). Use JSON for configuration files where appropriate (e.g., scene keys, asset paths).

# Test Strategy:
Verify project compiles and runs. Test navigation between Title Screen and Boat Menu. Ensure basic UI elements are displayed correctly. Check console for errors during scene transitions.

# Subtasks:
## 1. Initialize Phaser 3 Project and Define Core Game Configuration [pending]
### Dependencies: None
### Description: Set up a new Phaser 3 project using a standard template or by manually including Phaser. Define the main game configuration object including game dimensions (e.g., 800x600), renderer type (e.g., Phaser.AUTO), and a default background color. This establishes the foundational canvas and game instance.
### Details:
Use npm/yarn to install Phaser 3. Create an `index.html` to host the game and a `main.js` (or `index.js`) to initialize the `Phaser.Game` instance. The configuration object should look similar to: `const config = { type: Phaser.AUTO, width: 800, height: 600, backgroundColor: '#1d212d', parent: 'game-container', physics: { default: 'arcade', arcade: { gravity: { y: 0 } } }, scene: [] }; new Phaser.Game(config);`. Ensure an HTML element with id `game-container` exists.

## 2. Implement Basic Scene Manager and Initial Scene Registration [pending]
### Dependencies: 1.1
### Description: Create a mechanism or utilize Phaser's built-in scene plugin for managing game scenes (TitleScreen, BoatMenuScene, FishingScene). Register these scenes with the game instance. For now, transitions can be simple `this.scene.start('SceneKey')` calls.
### Details:
Define basic scene classes (empty shells for now) for `TitleScreen`, `BoatMenuScene`, and `FishingScene`. Each class should extend `Phaser.Scene` and have a unique key. Update the main game configuration's `scene` array to include these scene classes, e.g., `scene: [BootScene, PreloadScene, TitleScreen, BoatMenuScene, FishingScene]`. Ensure one scene (e.g., a `BootScene` or `TitleScreen`) is set to start automatically. A `BootScene` could handle loading minimal assets before showing a `PreloadScene` or `TitleScreen`.

## 3. Create Placeholder Scenes with Basic Structure [pending]
### Dependencies: 1.2
### Description: Flesh out the placeholder scenes (TitleScreen, BoatMenuScene, FishingScene) defined in the previous step. Each scene should have `preload()`, `create()`, and `update()` methods. Add a simple text element to each scene's `create()` method to display its name, confirming it has loaded.
### Details:
For each scene file (e.g., `TitleScreen.js`): `class TitleScreen extends Phaser.Scene { constructor() { super({ key: 'TitleScreen' }); } preload() {} create() { this.add.text(this.cameras.main.centerX, this.cameras.main.centerY, 'Title Screen', { fontSize: '32px', fill: '#fff' }).setOrigin(0.5); } update() {} }`. Repeat for `BoatMenuScene` and `FishingScene` with their respective names.

## 4. Configure Asset Loading for Initial Sprites and Sounds [pending]
### Dependencies: 1.3
### Description: Set up an asset loading system. This includes creating an `assets` directory structure and potentially a JSON configuration file (e.g., `assets.json`) to manage asset paths and keys. Load a few placeholder sprites and a sound effect, typically within a dedicated `PreloadScene` or the `preload()` method of the `TitleScreen`.
### Details:
Create `assets/images/` and `assets/audio/` folders. Add a sample `logo.png` and `click.wav` (or `.mp3`). Create `PreloadScene.js`. In its `preload()` method, use `this.load.image('logo', 'assets/images/logo.png');` and `this.load.audio('clickSound', 'assets/audio/click.wav');`. In its `create()` method, transition to the `TitleScreen`: `this.scene.start('TitleScreen');`. Ensure `PreloadScene` is the first scene started by the game config.

## 5. Implement Basic UI Elements for Title Screen and Boat Menu [pending]
### Dependencies: 1.2, 1.3, 1.4
### Description: Add basic interactive UI elements. On the TitleScreen, implement a 'Start Game' button that transitions to the BoatMenuScene. On the BoatMenuScene, add placeholder text elements for Time, Weather, and Location as specified in the PRD.
### Details:
In `TitleScreen.js` (`create()`): Add a 'Start Game' button: `const startButton = this.add.text(this.cameras.main.centerX, this.cameras.main.centerY + 100, 'Start Game', { fontSize: '24px', fill: '#fff', backgroundColor: '#555' }).setPadding(10).setOrigin(0.5).setInteractive(); startButton.on('pointerdown', () => this.scene.start('BoatMenuScene'));`. In `BoatMenuScene.js` (`create()`): Add placeholder texts: `this.add.text(50, 50, 'Time: [Placeholder]', { fontSize: '20px', fill: '#fff' }); this.add.text(50, 80, 'Weather: [Placeholder]', { fontSize: '20px', fill: '#fff' }); this.add.text(50, 110, 'Location: [Placeholder]', { fontSize: '20px', fill: '#fff' });`.

