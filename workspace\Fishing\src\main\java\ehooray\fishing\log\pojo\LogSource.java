package ehooray.fishing.log.pojo;

import ehooray.fishing.log.config.SystemBehaviourEnum;

public class LogSource
{
    protected SystemBehaviourEnum behaviour;
    protected int[] behaviourInputs = { };


    public LogSource(SystemBehaviourEnum behaviour, int... behaviourInputs)
    {
        set(behaviour, behaviourInputs);
    }
    
    public LogSource(SystemBehaviourEnum behaviour, LogSource copySource)
    {
        set(behaviour, copySource.getBehaviourInputs());
    }
    
    protected void set(SystemBehaviourEnum behaviour, int[] behaviourInputs)
    {
        this.behaviour = behaviour;
        this.behaviourInputs = behaviourInputs;
    }

    public SystemBehaviourEnum getBehaviour()
    {
        return behaviour;
    }

    public int[] getBehaviourInputs()
    {
        return behaviourInputs;
    }
}
