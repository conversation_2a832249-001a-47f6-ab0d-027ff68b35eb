<!-- Generated by SpecStory -->

# 填寫專案描述檔案區塊 (2025-06-03 17:48:30)

_**User**_

@project_description.md 請幫我填寫這這幾個區塊的檔案腳本功能
@data @scenes @scripts @ui 

---

_**Assistant**_



---

好的，我將根據您提供的文件夾結構和文件名，來推測每個腳本的功能，並填充到 `project_description.md` 文件中。

我將按以下方式組織信息：

*   **data**: 存放遊戲數據和配置文件的目錄。
*   **scenes**: 存放遊戲各個場景邏輯的目錄。
*   **scripts**: 存放遊戲核心邏輯、管理器和工具類腳本的目錄。
*   **ui**: 存放遊戲用戶界面相關腳本的目錄。

現在，我將開始填充 `project_description.md` 文件。

---

```diff
  data:
- 
+   - TextConfig.js: 文本配置，可能包含游戏中各种文本字符串。
+   - equipment.json: 装备数据，定义各种装备的属性。
+   - inventorySchema.json: 库存模式，定义库存系统的数据结构。
+   - lures.json: 鱼饵数据，定义各种鱼饵的属性。
+   - fish.json: 鱼类数据，定义各种鱼类的属性和行为。
+   - sample-renjs-quest-dialog.yaml: RenJS 任务对话示例，用于对话系统。
+   - LocationData.js: 地点数据，定义游戏中不同地点的信息。
+   - gameConfig.json: 游戏配置，包含全局游戏设置。
+   - attributes.json: 属性数据，定义角色或物品的各种属性。
  
  scenes:
- 
+   - SettingsScene.js: 设置场景，处理游戏设置选项。
+   - BoatMenuScene.js: 船只菜单场景，可能用于选择船只或相关操作。
+   - MenuScene.js: 主菜单场景。
+   - GameScene.js: 核心游戏场景，主要的钓鱼活动发生在这里。
+   - ShopScene.js: 商店场景，用于购买物品。
+   - QuestScene.js: 任务场景，显示和管理任务。
+   - HUDScene.js: HUD（平视显示器）场景，显示游戏中的实时信息。
+   - CabinScene.js: 小屋场景，可能是玩家的基地或休息场所。
+   - DialogScene.js: 对话场景，处理游戏中的角色对话。
+   - AlbumScene.js: 图鉴场景，可能用于展示收集到的鱼类或其他物品。
+   - PreloadScene.js: 预加载场景，在游戏开始前加载资源。
+   - BootScene.js: 启动场景，游戏的初始入口点。
  
  scripts:
- 
- 
- ui:
- 
+   - WeatherManager.js: 天气管理器，控制游戏中的天气变化。
+   - SceneManager.js: 场景管理器，管理不同游戏场景的切换。
+   - ReelingMiniGame.js: 收线迷你游戏逻辑。
+   - PlayerController.js: 玩家控制器，处理玩家输入和行为。
+   - PlayerProgression.js: 玩家进程管理器，跟踪和管理玩家的成长和解锁内容。
+   - LuringMiniGame.js: 诱鱼迷你游戏逻辑。
+   - LocationManager.js: 地点管理器，管理游戏中的不同地点。
+   - InventoryManager.js: 库存管理器，处理玩家物品的存储和使用。
+   - HCGNotificationManager.js: HCG 通知管理器 (具体 HCG 指代不明，可能是某个框架或系统)。
+   - GameState.js: 游戏状态管理器，跟踪和管理游戏的整体状态。
+   - CraftingManager.js: 合成管理器，处理物品的制作。
+   - EquipmentEnhancer.js: 装备强化器，处理装备的升级和强化。
+   - TimeManager.js: 时间管理器，控制游戏中的时间流逝。
+   - TournamentManager.js: 锦标赛管理器，组织和管理钓鱼比赛。
+   - SaveUtility.js: 存档工具，处理游戏的保存和加载。
+   - RenJsSaveIntegration.js: RenJS 存档集成，将 RenJS 的存档功能整合到游戏中。
+   - RenJsContentValidator.js: RenJS 内容验证器，验证 RenJS 相关内容的正确性。
+   - RenJsLoader.js: RenJS 加载器，加载 RenJS 相关资源。
+   - QuestManager.js: 任务管理器，管理游戏中的任务。
+   - InputManager.js: 输入管理器，处理来自键盘、鼠标或触摸屏的输入。
+   - HCGUnlockSystem.js: HCG 解锁系统 (具体 HCG 指代不明)。
+   - FishSpawner.js: 鱼类生成器，控制鱼类在游戏世界的生成。
+   - FishDatabase.js: 鱼类数据库，存储和管理鱼类数据。
+   - EventEmitter.js: 事件发射器，用于实现事件驱动的编程模式。
+   - DialogManager.js: 对话管理器，管理游戏中的对话流程。
+   - EnhancedAudioManager.js: 增强型音频管理器，提供更高级的音频控制功能。
+   - AudioManager.js: 音频管理器，处理游戏中的声音和音乐。
+   - AchievementEnhancer.js: 成就增强器，可能与成就系统的扩展功能相关。
+   - AdvancedQuestSystem.js: 高级任务系统，提供更复杂的任务功能。
+   - InventoryValidator.js: 库存验证器，验证库存操作的有效性。
+   - CastingMiniGame.js: 抛竿迷你游戏逻辑。
+   - SettingsManager.js: 设置管理器，管理游戏的各项设置。
+   - GameLoop.js: 游戏循环，驱动游戏的核心逻辑。
+   - DataLoader.js: 数据加载器，负责加载游戏所需的数据文件。
+   - FishingMinigames.js: 钓鱼迷你游戏的主控脚本或集合。
+ 
+ ui:
+   - ShopUI.js: 商店的用户界面。
+   - PlayerProgressionUI.js: 玩家进程的用户界面，显示玩家等级、经验等。
+   - TimeWeatherUI.js: 时间和天气信息的用户界面。
+   - CraftingUI.js: 合成系统的用户界面。
+   - InventoryUI.js: 库存系统的用户界面。
+   - UITheme.js: UI 主题，定义界面元素的统一样式。
+   - EquipmentEnhancementUI.js: 装备强化系统的用户界面。
+   - RenJsDebugUI.js: RenJS 调试用户界面，用于开发和测试。
+   - LoadingStateManager.js: 加载状态管理器，可能负责显示加载进度条等。
+   - MapSelectionUI.js: 地图选择用户界面。
+   - AudioSettingsUI.js: 音频设置的用户界面。
+   - AchievementPopupSystem.js: 成就弹出提示系统。
+   - FishCollectionUI.js: 鱼类图鉴或收集册的用户界面。
  
```

---

`project_description.md` 文件已更新，其中包含了您指定的 `data`、`scenes`、`scripts` 和 `ui` 目錄下各個文件的推測功能描述。

請檢查文件內容是否符合您的預期。如果您需要進一步的修改或有其他問題，請隨時告訴我。

---

_**User**_

請幫我翻譯成繁體中文

---

_**Assistant**_



---

好的，我會將 `project_description.md` 的內容翻譯成繁體中文。

首先，我需要讀取檔案的目前內容。

---

Model provided invalid arguments to read file tool.

---

抱歉，讀取文件時發生了一點小問題。我再來試一次。

---

Read file: project_description.md

---

好的，我已經讀取到 `project_description.md` 的內容。現在我將其翻譯成繁體中文並更新檔案。

---

```diff
  data:
-   - TextConfig.js: 文本配置，可能包含游戏中各种文本字符串。
-   - equipment.json: 装备数据，定义各种装备的属性。
-   - inventorySchema.json: 库存模式，定义库存系统的数据结构。
-   - lures.json: 鱼饵数据，定义各种鱼饵的属性。
-   - fish.json: 鱼类数据，定义各种鱼类的属性和行为。
-   - sample-renjs-quest-dialog.yaml: RenJS 任务对话示例，用于对话系统。
-   - LocationData.js: 地点数据，定义游戏中不同地点的信息。
-   - gameConfig.json: 游戏配置，包含全局游戏设置。
-   - attributes.json: 属性数据，定义角色或物品的各种属性。
+   - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。
+   - equipment.json: 裝備數據，定義各種裝備的屬性。
+   - inventorySchema.json: 庫存模式，定義庫存系統的數據結構。
+   - lures.json: 魚餌數據，定義各種魚餌的屬性。
+   - fish.json: 魚類數據，定義各種魚類的屬性和行為。
+   - sample-renjs-quest-dialog.yaml: RenJS 任務對話範例，用於對話系統。
+   - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。
+   - gameConfig.json: 遊戲配置，包含全域遊戲設定。
+   - attributes.json: 屬性數據，定義角色或物品的各種屬性。
  
  scenes:
-   - SettingsScene.js: 设置场景，处理游戏设置选项。
-   - BoatMenuScene.js: 船只菜单场景，可能用于选择船只或相关操作。
-   - MenuScene.js: 主菜单场景。
-   - GameScene.js: 核心游戏场景，主要的钓鱼活动发生在这里。
-   - ShopScene.js: 商店场景，用于购买物品。
-   - QuestScene.js: 任务场景，显示和管理任务。
-   - HUDScene.js: HUD（平视显示器）场景，显示游戏中的实时信息。
-   - CabinScene.js: 小屋场景，可能是玩家的基地或休息场所。
-   - DialogScene.js: 对话场景，处理游戏中的角色对话。
-   - AlbumScene.js: 图鉴场景，可能用于展示收集到的鱼类或其他物品。
-   - PreloadScene.js: 预加载场景，在游戏开始前加载资源。
-   - BootScene.js: 启动场景，游戏的初始入口点。
+   - SettingsScene.js: 設定場景，處理遊戲設定選項。
+   - BoatMenuScene.js: 船隻選單場景，可能用於選擇船隻或相關操作。
+   - MenuScene.js: 主選單場景。
+   - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。
+   - ShopScene.js: 商店場景，用於購買物品。
+   - QuestScene.js: 任務場景，顯示和管理任務。
+   - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。
+   - CabinScene.js: 小屋場景，可能是玩家的基地或休息場所。
+   - DialogScene.js: 對話場景，處理遊戲中的角色對話。
+   - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。
+   - PreloadScene.js: 預載入場景，在遊戲開始前載入資源。
+   - BootScene.js: 啟動場景，遊戲的初始進入點。
  
  scripts:
-   - WeatherManager.js: 天气管理器，控制游戏中的天气变化。
-   - SceneManager.js: 场景管理器，管理不同游戏场景的切换。
-   - ReelingMiniGame.js: 收线迷你游戏逻辑。
-   - PlayerController.js: 玩家控制器，处理玩家输入和行为。
-   - PlayerProgression.js: 玩家进程管理器，跟踪和管理玩家的成长和解锁内容。
-   - LuringMiniGame.js: 诱鱼迷你游戏逻辑。
-   - LocationManager.js: 地点管理器，管理游戏中的不同地点。
-   - InventoryManager.js: 库存管理器，处理玩家物品的存储和使用。
-   - HCGNotificationManager.js: HCG 通知管理器 (具体 HCG 指代不明，可能是某个框架或系统)。
-   - GameState.js: 游戏状态管理器，跟踪和管理游戏的整体状态。
-   - CraftingManager.js: 合成管理器，处理物品的制作。
-   - EquipmentEnhancer.js: 装备强化器，处理装备的升级和强化。
-   - TimeManager.js: 时间管理器，控制游戏中的时间流逝。
-   - TournamentManager.js: 锦标赛管理器，组织和管理钓鱼比赛。
-   - SaveUtility.js: 存档工具，处理游戏的保存和加载。
-   - RenJsSaveIntegration.js: RenJS 存档集成，将 RenJS 的存档功能整合到游戏中。
-   - RenJsContentValidator.js: RenJS 内容验证器，验证 RenJS 相关内容的正确性。
-   - RenJsLoader.js: RenJS 加载器，加载 RenJS 相关资源。
-   - QuestManager.js: 任务管理器，管理游戏中的任务。
-   - InputManager.js: 输入管理器，处理来自键盘、鼠标或触摸屏的输入。
-   - HCGUnlockSystem.js: HCG 解锁系统 (具体 HCG 指代不明)。
-   - FishSpawner.js: 鱼类生成器，控制鱼类在游戏世界的生成。
-   - FishDatabase.js: 鱼类数据库，存储和管理鱼类数据。
-   - EventEmitter.js: 事件发射器，用于实现事件驱动的编程模式。
-   - DialogManager.js: 对话管理器，管理游戏中的对话流程。
-   - EnhancedAudioManager.js: 增强型音频管理器，提供更高级的音频控制功能。
-   - AudioManager.js: 音频管理器，处理游戏中的声音和音乐。
-   - AchievementEnhancer.js: 成就增强器，可能与成就系统的扩展功能相关。
-   - AdvancedQuestSystem.js: 高级任务系统，提供更复杂的任务功能。
-   - InventoryValidator.js: 库存验证器，验证库存操作的有效性。
-   - CastingMiniGame.js: 抛竿迷你游戏逻辑。
-   - SettingsManager.js: 设置管理器，管理游戏的各项设置。
-   - GameLoop.js: 游戏循环，驱动游戏的核心逻辑。
-   - DataLoader.js: 数据加载器，负责加载游戏所需的数据文件。
-   - FishingMinigames.js: 钓鱼迷你游戏的主控脚本或集合。
+   - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。
+   - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。
+   - ReelingMiniGame.js: 收線迷你遊戲邏輯。
+   - PlayerController.js: 玩家控制器，處理玩家輸入和行為。
+   - PlayerProgression.js: 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容。
+   - LuringMiniGame.js: 誘魚迷你遊戲邏輯。
+   - LocationManager.js: 地點管理器，管理遊戲中的不同地點。
+   - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。
+   - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。
+   - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。
+   - CraftingManager.js: 合成管理器，處理物品的製作。
+   - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。
+   - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。
+   - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。
+   - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。
+   - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。
+   - RenJsContentValidator.js: RenJS 內容驗證器，驗證 RenJS 相關內容的正確性。
+   - RenJsLoader.js: RenJS 載入器，載入 RenJS 相關資源。
+   - QuestManager.js: 任務管理器，管理遊戲中的任務。
+   - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。
+   - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。
+   - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。
+   - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。
+   - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。
+   - DialogManager.js: 對話管理器，管理遊戲中的對話流程。
+   - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。
+   - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。
+   - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。
+   - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。
+   - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。
+   - CastingMiniGame.js: 拋竿迷你遊戲邏輯。
+   - SettingsManager.js: 設定管理器，管理遊戲的各項設定。
+   - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。
+   - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。
+   - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。
  
  ui:
-   - ShopUI.js: 商店的用户界面。
-   - PlayerProgressionUI.js: 玩家进程的用户界面，显示玩家等级、经验等。
-   - TimeWeatherUI.js: 时间和天气信息的用户界面。
-   - CraftingUI.js: 合成系统的用户界面。
-   - InventoryUI.js: 库存系统的用户界面。
-   - UITheme.js: UI 主题，定义界面元素的统一样式。
-   - EquipmentEnhancementUI.js: 装备强化系统的用户界面。
-   - RenJsDebugUI.js: RenJS 调试用户界面，用于开发和测试。
-   - LoadingStateManager.js: 加载状态管理器，可能负责显示加载进度条等。
-   - MapSelectionUI.js: 地图选择用户界面。
-   - AudioSettingsUI.js: 音频设置的用户界面。
-   - AchievementPopupSystem.js: 成就弹出提示系统。
-   - FishCollectionUI.js: 鱼类图鉴或收集册的用户界面。
+   - ShopUI.js: 商店的使用者介面。
+   - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。
+   - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。
+   - CraftingUI.js: 合成系統的使用者介面。
+   - InventoryUI.js: 庫存系統的使用者介面。
+   - UITheme.js: UI 主題，定義介面元素的統一樣式。
+   - EquipmentEnhancementUI.js: 裝備強化系統的使用者介面。
+   - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。
+   - LoadingStateManager.js: 載入狀態管理器，可能負責顯示載入進度條等。
+   - MapSelectionUI.js: 地圖選擇使用者介面。
+   - AudioSettingsUI.js: 音訊設定的使用者介面。
+   - AchievementPopupSystem.js: 成就彈出提示系統。
+   - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。
  
```

---

`project_description.md` 檔案已成功翻譯成繁體中文。

請檢查更新後的檔案內容。如果您還有其他需要，請隨時提出。

---

_**User**_

這些檔案列表，請照字母順序 a~z升冪排序

---

_**Assistant**_



---

好的，我會將 `project_description.md` 中每個區塊的檔案列表按照字母順序 (a-z) 進行升冪排序。

首先，我需要讀取檔案的目前內容。

---

Read file: project_description.md

---



---

我已經讀取到檔案內容。現在我將對每個區塊的檔案列表進行排序，然後更新檔案。

---

```diff
  data:
-   - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。
+   - attributes.json: 屬性數據，定義角色或物品的各種屬性。
    - equipment.json: 裝備數據，定義各種裝備的屬性。
+   - fish.json: 魚類數據，定義各種魚類的屬性和行為。
+   - gameConfig.json: 遊戲配置，包含全域遊戲設定。
    - inventorySchema.json: 庫存模式，定義庫存系統的數據結構。
+   - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。
    - lures.json: 魚餌數據，定義各種魚餌的屬性。
-   - fish.json: 魚類數據，定義各種魚類的屬性和行為。
    - sample-renjs-quest-dialog.yaml: RenJS 任務對話範例，用於對話系統。
-   - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。
-   - gameConfig.json: 遊戲配置，包含全域遊戲設定。
-   - attributes.json: 屬性數據，定義角色或物品的各種屬性。
+   - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。
  
  scenes:
-   - SettingsScene.js: 設定場景，處理遊戲設定選項。
+   - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。
    - BoatMenuScene.js: 船隻選單場景，可能用於選擇船隻或相關操作。
-   - MenuScene.js: 主選單場景。
-   - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。
-   - ShopScene.js: 商店場景，用於購買物品。
-   - QuestScene.js: 任務場景，顯示和管理任務。
-   - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。
+   - BootScene.js: 啟動場景，遊戲的初始進入點。
    - CabinScene.js: 小屋場景，可能是玩家的基地或休息場所。
    - DialogScene.js: 對話場景，處理遊戲中的角色對話。
-   - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。
+   - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。
+   - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。
+   - MenuScene.js: 主選單場景。
    - PreloadScene.js: 預載入場景，在遊戲開始前載入資源。
-   - BootScene.js: 啟動場景，遊戲的初始進入點。
+   - QuestScene.js: 任務場景，顯示和管理任務。
+   - SettingsScene.js: 設定場景，處理遊戲設定選項。
+   - ShopScene.js: 商店場景，用於購買物品。
  
  scripts:
-   - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。
-   - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。
-   - ReelingMiniGame.js: 收線迷你遊戲邏輯。
+   - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。
+   - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。
+   - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。
+   - CastingMiniGame.js: 拋竿迷你遊戲邏輯。
+   - CraftingManager.js: 合成管理器，處理物品的製作。
+   - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。
+   - DialogManager.js: 對話管理器，管理遊戲中的對話流程。
+   - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。
+   - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。
+   - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。
+   - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。
+   - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。
+   - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。
+   - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。
+   - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。
+   - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。
+   - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。
+   - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。
+   - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。
+   - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。
+   - LocationManager.js: 地點管理器，管理遊戲中的不同地點。
+   - LuringMiniGame.js: 誘魚迷你遊戲邏輯。
    - PlayerController.js: 玩家控制器，處理玩家輸入和行為。
    - PlayerProgression.js: 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容。
-   - LuringMiniGame.js: 誘魚迷你遊戲邏輯。
-   - LocationManager.js: 地點管理器，管理遊戲中的不同地點。
-   - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。
-   - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。
-   - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。
-   - CraftingManager.js: 合成管理器，處理物品的製作。
-   - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。
-   - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。
-   - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。
-   - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。
-   - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。
+   - QuestManager.js: 任務管理器，管理遊戲中的任務。
+   - ReelingMiniGame.js: 收線迷你遊戲邏輯。
    - RenJsContentValidator.js: RenJS 內容驗證器，驗證 RenJS 相關內容的正確性。
    - RenJsLoader.js: RenJS 載入器，載入 RenJS 相關資源。
-   - QuestManager.js: 任務管理器，管理遊戲中的任務。
-   - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。
-   - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。
-   - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。
-   - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。
-   - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。
-   - DialogManager.js: 對話管理器，管理遊戲中的對話流程。
-   - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。
-   - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。
-   - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。
-   - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。
-   - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。
-   - CastingMiniGame.js: 拋竿迷你遊戲邏輯。
+   - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。
+   - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。
+   - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。
    - SettingsManager.js: 設定管理器，管理遊戲的各項設定。
-   - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。
-   - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。
-   - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。
+   - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。
+   - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。
+   - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。
  
  ui:
-   - ShopUI.js: 商店的使用者介面。
-   - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。
-   - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。
+   - AchievementPopupSystem.js: 成就彈出提示系統。
+   - AudioSettingsUI.js: 音訊設定的使用者介面。
    - CraftingUI.js: 合成系統的使用者介面。
-   - InventoryUI.js: 庫存系統的使用者介面。
-   - UITheme.js: UI 主題，定義介面元素的統一樣式。
    - EquipmentEnhancementUI.js: 裝備強化系統的使用者介面。
-   - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。
+   - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。
+   - InventoryUI.js: 庫存系統的使用者介面。
    - LoadingStateManager.js: 載入狀態管理器，可能負責顯示載入進度條等。
    - MapSelectionUI.js: 地圖選擇使用者介面。
-   - AudioSettingsUI.js: 音訊設定的使用者介面。
-   - AchievementPopupSystem.js: 成就彈出提示系統。
-   - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。
+   - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。
+   - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。
+   - ShopUI.js: 商店的使用者介面。
+   - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。
+   - UITheme.js: UI 主題，定義介面元素的統一樣式。
  
```

---

檔案 `project_description.md` 中各個區塊的檔案列表已經按照字母順序 (a-z) 重新排序。

請您檢查一下更新後的檔案內容，看看是否符合您的要求。如果還有其他需要調整的地方，請隨時告訴我。

---

_**User**_

請幫我在這個檔案加上 適合這份文件內容的標題

---

_**Assistant**_



---

好的，我將為 `project_description.md` 檔案添加一個合適的標題。

根據檔案內容，我建議使用「專案檔案結構與功能說明」作為標題。

現在我將更新檔案。

---

```diff
+ # 專案檔案結構與功能說明
+ 
  data:
    - attributes.json: 屬性數據，定義角色或物品的各種屬性。
```

---

已在 `project_description.md` 檔案的開頭添加了標題「# 專案檔案結構與功能說明」。

請查看更新後的檔案，看看是否滿意。

---

_**User**_

@Luxury_Angler_GDD(updated).txt 這份實際說明，我想和當初的GDD做關聯，該如何進行，請先不要修改

---

_**Assistant**_

好的，我們可以將 `project_description.md` 和 `Luxury_Angler_GDD(updated).txt` 進行關聯。以下是一些可以考慮的方向和步驟，在您確認之前，我不會修改任何檔案：

**目標：** 建立 `project_description.md`（檔案結構和功能）與 `Luxury_Angler_GDD(updated).txt`（遊戲設計細節）之間的清晰對應關係。

**如何進行關聯（建議方案）：**

1.  **在 `project_description.md` 中添加 GDD 參考：**
    *   可以在 `project_description.md` 的開頭或結尾部分，明確指出該文件是對應 `Luxury_Angler_GDD(updated).txt` 的技術實現概述。
    *   例如，添加一句話：「本文件概述了 `Luxury_Angler` 專案的檔案結構與各模組功能，詳細的遊戲設計請參考 `Luxury_Angler_GDD(updated).txt`。」

2.  **在 `project_description.md` 的各個檔案描述中添加 GDD 章節對應（可選，但更詳細）：**
    *   對於 `project_description.md` 中描述的每個（或重要的）檔案/腳本，可以嘗試找到 `Luxury_Angler_GDD(updated).txt` 中對應的章節或功能點。
    *   例如，在 `ReelingMiniGame.js` 的描述後面，可以加上類似 `(對應 GDD 4.1.3 Reel-In Minigame)` 的註釋。
    *   這樣做的好處是，開發者在查看某個具體檔案的功能時，可以快速定位到 GDD 中的詳細設計。

3.  **在 `Luxury_Angler_GDD(updated).txt` 中反向連結（可選，工作量較大）：**
    *   這一步相對複雜，可能需要手動編輯 GDD 文件。
    *   可以在 GDD 的相關章節（例如，描述收線小遊戲的章節）末尾，註明實現該功能的腳本是 `src/scripts/ReelingMiniGame.js`。
    *   這種雙向連結是最為完善的，但維護成本也較高。

4.  **使用標籤或關鍵字（如果未來有更進階的文檔管理系統）：**
    *   如果未來使用支援標籤或關鍵字索引的文檔系統，可以為 GDD 中的章節和 `project_description.md` 中的檔案功能描述打上相同的標籤，方便搜尋和關聯。

**初步建議：**

我建議先從方案 1 和方案 2 開始。

*   **方案 1 (添加 GDD 參考):** 實施起來最簡單快速，能提供基本的關聯性。
*   **方案 2 (添加 GDD 章節對應):** 會讓 `project_description.md` 更具參考價值，但需要花費一些時間去梳理 GDD 和檔案之間的對應關係。

**接下來的步驟：**

請您考慮以上方案，並告訴我您希望如何進行。例如：

*   您是希望我先在 `project_description.md` 開頭加上對 GDD 的參考說明？
*   還是您希望我嘗試找出一些核心腳本（例如各個小遊戲、管理器）在 GDD 中的對應章節，並在 `project_description.md` 中進行標註？
*   或者您有其他的想法？

請告訴我您的決定，我將按照您的指示操作。

---

_**User**_

我比較偏好在每個檔案打tag讓我知道這個腳本和哪個系統有關係

---

_**Assistant**_



---

好的，我理解您的偏好。我們可以在 `project_description.md` 中的每個檔案描述後方，加上標籤（tag）來註明它與 `Luxury_Angler_GDD(updated).txt` 中哪個系統或章節相關。

這樣做的好處是，當您查看某個檔案的功能時，可以快速對應到 GDD 中的詳細設計說明。

我將會：
1.  仔細閱讀 `Luxury_Angler_GDD(updated).txt` 的目錄和主要章節。
2.  逐一分析 `project_description.md` 中的每個檔案條目。
3.  根據檔案的功能描述，找到 GDD 中最相關的章節編號或系統名稱。
4.  在每個檔案描述的末尾，添加類似 `[GDD: X.Y]` 或 `[GDD: 系統名稱]` 的標籤。例如：
    *   `attributes.json: 屬性數據，定義角色或物品的各種屬性。 [GDD: 3]`
    *   `ReelingMiniGame.js: 收線迷你遊戲邏輯。 [GDD: 4.1.3]`

我現在開始處理，首先讀取 `project_description.md` 的現有內容。

---

Read file: project_description.md

---

好的，我已經讀取了 `project_description.md` 的內容。現在，我將根據 GDD (`Luxury_Angler_GDD(updated).txt`) 的內容，為每個檔案條目添加關聯標籤。

---

```diff
  
  data:
-   - attributes.json: 屬性數據，定義角色或物品的各種屬性。
-   - equipment.json: 裝備數據，定義各種裝備的屬性。
-   - fish.json: 魚類數據，定義各種魚類的屬性和行為。
-   - gameConfig.json: 遊戲配置，包含全域遊戲設定。
-   - inventorySchema.json: 庫存模式，定義庫存系統的數據結構。
-   - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。
-   - lures.json: 魚餌數據，定義各種魚餌的屬性。
-   - sample-renjs-quest-dialog.yaml: RenJS 任務對話範例，用於對話系統。
-   - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。
+   - attributes.json: 屬性數據，定義角色或物品的各種屬性。 [GDD: 3, 3.1, 3.2]
+   - equipment.json: 裝備數據，定義各種裝備的屬性。 [GDD: 7.2]
+   - fish.json: 魚類數據，定義各種魚類的屬性和行為。 [GDD: 3.2, 4.5]
+   - gameConfig.json: 遊戲配置，包含全域遊戲設定。 [GDD: 13]
+   - inventorySchema.json: 庫存模式，定義庫存系統的數據結構。 [GDD: 7.1]
+   - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。 [GDD: 2.1, 2.2, 4.4]
+   - lures.json: 魚餌數據，定義各種魚餌的屬性。 [GDD: 4.4, 7.2]
+   - sample-renjs-quest-dialog.yaml: RenJS 任務對話範例，用於對話系統。 [GDD: 8.2, 8.3]
+   - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。 [GDD: 12, 13]
  
  scenes:
-   - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。
-   - BoatMenuScene.js: 船隻選單場景，可能用於選擇船隻或相關操作。
-   - BootScene.js: 啟動場景，遊戲的初始進入點。
-   - CabinScene.js: 小屋場景，可能是玩家的基地或休息場所。
-   - DialogScene.js: 對話場景，處理遊戲中的角色對話。
-   - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。
-   - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。
-   - MenuScene.js: 主選單場景。
-   - PreloadScene.js: 預載入場景，在遊戲開始前載入資源。
-   - QuestScene.js: 任務場景，顯示和管理任務。
-   - SettingsScene.js: 設定場景，處理遊戲設定選項。
-   - ShopScene.js: 商店場景，用於購買物品。
+   - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。 [GDD: 8.1]
+   - BoatMenuScene.js: 船隻選單場景，可能用於選擇船隻或相關操作。 [GDD: 2.1, 2.2]
+   - BootScene.js: 啟動場景，遊戲的初始進入點。 [GDD: 13]
+   - CabinScene.js: 小屋場景，可能是玩家的基地或休息場所。 [GDD: 8.1]
+   - DialogScene.js: 對話場景，處理遊戲中的角色對話。 [GDD: 8.2]
+   - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。 [GDD: 2, 4]
+   - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。 [GDD: 2.2, 12]
+   - MenuScene.js: 主選單場景。 [GDD: 2.1]
+   - PreloadScene.js: 預載入場景，在遊戲開始前載入資源。 [GDD: 13]
+   - QuestScene.js: 任務場景，顯示和管理任務。 [GDD: 2.2, 8.3]
+   - SettingsScene.js: 設定場景，處理遊戲設定選項。 [GDD: 13]
+   - ShopScene.js: 商店場景，用於購買物品。 [GDD: 2, 9]
  
  scripts:
-   - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。
-   - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。
-   - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。
-   - CastingMiniGame.js: 拋竿迷你遊戲邏輯。
-   - CraftingManager.js: 合成管理器，處理物品的製作。
-   - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。
-   - DialogManager.js: 對話管理器，管理遊戲中的對話流程。
-   - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。
-   - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。
-   - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。
-   - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。
-   - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。
-   - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。
-   - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。
-   - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。
-   - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。
-   - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。
-   - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。
-   - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。
-   - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。
-   - LocationManager.js: 地點管理器，管理遊戲中的不同地點。
-   - LuringMiniGame.js: 誘魚迷你遊戲邏輯。
-   - PlayerController.js: 玩家控制器，處理玩家輸入和行為。
-   - PlayerProgression.js: 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容。
-   - QuestManager.js: 任務管理器，管理遊戲中的任務。
-   - ReelingMiniGame.js: 收線迷你遊戲邏輯。
-   - RenJsContentValidator.js: RenJS 內容驗證器，驗證 RenJS 相關內容的正確性。
-   - RenJsLoader.js: RenJS 載入器，載入 RenJS 相關資源。
-   - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。
-   - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。
-   - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。
-   - SettingsManager.js: 設定管理器，管理遊戲的各項設定。
-   - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。
-   - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。
-   - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。
+   - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。 [GDD: 10]
+   - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。 [GDD: 8.3]
+   - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。 [GDD: 12]
+   - CastingMiniGame.js: 拋竿迷你遊戲邏輯。 [GDD: 4.1.1]
+   - CraftingManager.js: 合成管理器，處理物品的製作。 [GDD: 5]
+   - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。 [GDD: 13]
+   - DialogManager.js: 對話管理器，管理遊戲中的對話流程。 [GDD: 8.2]
+   - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。 [GDD: 12]
+   - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。 [GDD: 7.2]
+   - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。 [GDD: 13]
+   - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。 [GDD: 3.2, 4.5, 13]
+   - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。 [GDD: 4, 4.5]
+   - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。 [GDD: 4.1]
+   - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。 [GDD: 2, 13]
+   - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。 [GDD: 2, 13]
+   - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。 [GDD: 8.1, 10]
+   - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。 [GDD: 8.1, 10]
+   - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。 [GDD: 4.1, 13]
+   - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。 [GDD: 7.1]
+   - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。 [GDD: 7.1, 13]
+   - LocationManager.js: 地點管理器，管理遊戲中的不同地點。 [GDD: 2, 4.4]
+   - LuringMiniGame.js: 誘魚迷你遊戲邏輯。 [GDD: 4.1.2]
+   - PlayerController.js: 玩家控制器，處理玩家輸入和行為。 [GDD: 2, 3, 4]
+   - PlayerProgression.js: 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容。 [GDD: 3.1.1, 6]
+   - QuestManager.js: 任務管理器，管理遊戲中的任務。 [GDD: 8.3]
+   - ReelingMiniGame.js: 收線迷你遊戲邏輯。 [GDD: 4.1.3]
+   - RenJsContentValidator.js: RenJS 內容驗證器，驗證 RenJS 相關內容的正確性。 [GDD: 8.2, 13]
+   - RenJsLoader.js: RenJS 載入器，載入 RenJS 相關資源。 [GDD: 8.2, 13]
+   - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。 [GDD: 8.2, 13]
+   - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。 [GDD: 13]
+   - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。 [GDD: 13]
+   - SettingsManager.js: 設定管理器，管理遊戲的各項設定。 [GDD: 13]
+   - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。 [GDD: 4.2]
+   - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。 [GDD: 6, specific events]
+   - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。 [GDD: 4.3]
  
  ui:
-   - AchievementPopupSystem.js: 成就彈出提示系統。
-   - AudioSettingsUI.js: 音訊設定的使用者介面。
-   - CraftingUI.js: 合成系統的使用者介面。
-   - EquipmentEnhancementUI.js: 裝備強化系統的使用者介面。
-   - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。
-   - InventoryUI.js: 庫存系統的使用者介面。
-   - LoadingStateManager.js: 載入狀態管理器，可能負責顯示載入進度條等。
-   - MapSelectionUI.js: 地圖選擇使用者介面。
-   - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。
-   - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。
-   - ShopUI.js: 商店的使用者介面。
-   - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。
-   - UITheme.js: UI 主題，定義介面元素的統一樣式。
+   - AchievementPopupSystem.js: 成就彈出提示系統。 [GDD: 10, 12]
+   - AudioSettingsUI.js: 音訊設定的使用者介面。 [GDD: 12, 13]
+   - CraftingUI.js: 合成系統的使用者介面。 [GDD: 5, 12]
+   - EquipmentEnhancementUI.js: 裝備強化系統的使用者介面。 [GDD: 7.2, 12]
+   - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。 [GDD: 7.1, 8.1, 12]
+   - InventoryUI.js: 庫存系統的使用者介面。 [GDD: 7.1, 12]
+   - LoadingStateManager.js: 載入狀態管理器，可能負責顯示載入進度條等。 [GDD: 12, 13]
+   - MapSelectionUI.js: 地圖選擇使用者介面。 [GDD: 2, 4.4, 12]
+   - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。 [GDD: 6, 12]
+   - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。 [GDD: 8.2, 13]
+   - ShopUI.js: 商店的使用者介面。 [GDD: 9, 12]
+   - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。 [GDD: 4.2, 4.3, 12]
+   - UITheme.js: UI 主題，定義介面元素的統一樣式。 [GDD: 12]
  
```

---

`project_description.md` 檔案已更新。我為每個檔案條目添加了 GDD（遊戲設計文件）的章節編號標籤，以便您能快速查閱相關的設計細節。

例如，您現在會看到類似以下的條目：
`  - attributes.json: 屬性數據，定義角色或物品的各種屬性。 [GDD: 3, 3.1, 3.2]`
`  - ReelingMiniGame.js: 收線迷你遊戲邏輯。 [GDD: 4.1.3]`

請您檢查更新後的檔案，看看這些標籤是否清晰且符合您的預期。如果需要任何調整，請隨時告訴我。

---

_**User**_

請幫我補上編號的簡易標題 比如從@Luxury_Angler_GDD(updated).txt 把目錄拷貝過來

---

_**Assistant**_



---

好的，我明白了。您希望將 `Luxury_Angler_GDD(updated).txt` 中的目錄（Table of Contents）作為一個帶編號的簡易標題列表，添加到 `project_description.md` 文件中，以便更好地將文件結構與GDD內容關聯起來。

我將執行以下操作：
1.  從 `Luxury_Angler_GDD(updated).txt` 中提取目錄部分。
2.  在 `project_description.md` 的主標題下方，現有 `data:` 區塊之前，插入一個新的區塊，例如命名為「GDD 章節參考」或類似的標題。
3.  將提取的GDD目錄內容放入這個新的區塊中。

首先，我需要讀取 `project_description.md` 的現有內容，以確定插入新內容的確切位置。

---

Read file: project_description.md

---



---

我已經讀取了 `project_description.md` 的內容。現在，我將從 `Luxury_Angler_GDD(updated).txt` 中提取目錄，並將其作為一個新的參考部分添加到 `project_description.md` 中。

我將添加一個二級標題，例如「## GDD 目錄參考」，然後列出GDD中的主要章節。

---

```diff
  # 專案檔案結構與功能說明
+ 
+ ## GDD 目錄參考
+ 1.  Game Overview
+ 2.  Core Gameplay Loop
+ 3.  Attributes System
+ 4.  Fishing System
+     *   4.1 Fishing Mechanics
+         *   4.1.1 Cast Minigame
+         *   4.1.2 Lure Minigame (Enhanced)
+         *   4.1.3 Reel-In Minigame
+     *   4.2 Time System
+     *   4.3 Weather System
+     *   4.4 Lure Types
+     *   4.5 Fish Struggle Styles
+     *   4.6 QTE Reaction Controls
+ 5.  Crafting System
+ 6.  Level Progression
+ 7.  Inventory and Equipment
+     *   7.1 Inventory System
+     *   7.2 Equipment System
+ 8.  Social System
+     *   8.1 Cruise Cabin
+     *   8.2 Story and Dialog System (RenJS)
+ 9.  Shop System
+ 10. Achievement System
+ 11. Economy System
+ 12. Art and Audio
+ 13. Technical Design
  
  data:
```

---

我已經將 `Luxury_Angler_GDD(updated).txt` 的目錄作為「GDD 目錄參考」部分添加到了 `project_description.md` 文件的標題下方。

這樣，您在查看 `project_description.md` 中各檔案的GDD標籤時，可以更方便地對照這個目錄來理解其在整體遊戲設計中的位置。

請檢查更新後的 `project_description.md` 文件，看看是否符合您的要求。

---

_**User**_

請幫我根據 @project_description.md 生成rule /Generate Cursor Rules 

---

_**Assistant**_



---

我將根據 `project_description.md` 的內容創建一個 Cursor Rules 文件，幫助開發者理解專案結構和各個檔案的功能。

---

```diff
- 
+ # 釣魚遊戲專案結構指南
+ 
+ 本專案是一個基於 Phaser 3 的 2D 釣魚模擬遊戲 "Luxury Angler"。
+ 
+ ## 專案檔案組織
+ 
+ ### 數據層 (Data Layer)
+ 數據檔案位於 `src/data/` 目錄，包含遊戲的核心配置和內容：
+ 
+ - [attributes.json](mdc:src/data/attributes.json): 屬性數據，定義角色或物品的各種屬性 [GDD: 3, 3.1, 3.2]
+ - [equipment.json](mdc:src/data/equipment.json): 裝備數據，定義各種裝備的屬性 [GDD: 7.2]
+ - [fish.json](mdc:src/data/fish.json): 魚類數據，定義各種魚類的屬性和行為 [GDD: 3.2, 4.5]
+ - [gameConfig.json](mdc:src/data/gameConfig.json): 遊戲配置，包含全域遊戲設定 [GDD: 13]
+ - [inventorySchema.json](mdc:src/data/inventorySchema.json): 庫存模式，定義庫存系統的數據結構 [GDD: 7.1]
+ - [LocationData.js](mdc:src/data/LocationData.js): 地點數據，定義遊戲中不同地點的資訊 [GDD: 2.1, 2.2, 4.4]
+ - [lures.json](mdc:src/data/lures.json): 魚餌數據，定義各種魚餌的屬性 [GDD: 4.4, 7.2]
+ - [TextConfig.js](mdc:src/data/TextConfig.js): 文字配置，可能包含遊戲中各種文字字串 [GDD: 12, 13]
+ 
+ ### 場景層 (Scene Layer)
+ 場景檔案位於 `src/scenes/` 目錄，管理遊戲的不同畫面和狀態：
+ 
+ **核心場景:**
+ - [GameScene.js](mdc:src/scenes/GameScene.js): 核心遊戲場景，主要的釣魚活動發生在此 [GDD: 2, 4]
+ - [BoatMenuScene.js](mdc:src/scenes/BoatMenuScene.js): 船隻選單場景，可能用於選擇船隻或相關操作 [GDD: 2.1, 2.2]
+ - [HUDScene.js](mdc:src/scenes/HUDScene.js): HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊 [GDD: 2.2, 12]
+ 
+ **社交場景:**
+ - [CabinScene.js](mdc:src/scenes/CabinScene.js): 小屋場景，可能是玩家的基地或休息場所 [GDD: 8.1]
+ - [DialogScene.js](mdc:src/scenes/DialogScene.js): 對話場景，處理遊戲中的角色對話 [GDD: 8.2]
+ 
+ **系統場景:**
+ - [ShopScene.js](mdc:src/scenes/ShopScene.js): 商店場景，用於購買物品 [GDD: 2, 9]
+ - [QuestScene.js](mdc:src/scenes/QuestScene.js): 任務場景，顯示和管理任務 [GDD: 2.2, 8.3]
+ - [AlbumScene.js](mdc:src/scenes/AlbumScene.js): 圖鑑場景，可能用於展示收集到的魚類或其他物品 [GDD: 8.1]
+ 
+ ### 腳本層 (Scripts Layer)
+ 核心邏輯腳本位於 `src/scripts/` 目錄：
+ 
+ **釣魚機制:**
+ - [FishingMinigames.js](mdc:src/scripts/FishingMinigames.js): 釣魚迷你遊戲的主控腳本或集合 [GDD: 4.1]
+ - [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿迷你遊戲邏輯 [GDD: 4.1.1]
+ - [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚迷你遊戲邏輯 [GDD: 4.1.2]
+ - [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線迷你遊戲邏輯 [GDD: 4.1.3]
+ 
+ **核心管理器:**
+ - [GameState.js](mdc:src/scripts/GameState.js): 遊戲狀態管理器，追蹤和管理遊戲的整體狀態 [GDD: 2, 13]
+ - [GameLoop.js](mdc:src/scripts/GameLoop.js): 遊戲循環，驅動遊戲的核心邏輯 [GDD: 2, 13]
+ - [PlayerController.js](mdc:src/scripts/PlayerController.js): 玩家控制器，處理玩家輸入和行為 [GDD: 2, 3, 4]
+ - [SceneManager.js](mdc:src/scripts/SceneManager.js): 場景管理器，管理不同遊戲場景的切換 [GDD: 13]
+ 
+ **系統管理器:**
+ - [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理器，處理玩家物品的儲存和使用 [GDD: 7.1]
+ - [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成管理器，處理物品的製作 [GDD: 5]
+ - [TimeManager.js](mdc:src/scripts/TimeManager.js): 時間管理器，控制遊戲中的時間流逝 [GDD: 4.2]
+ - [WeatherManager.js](mdc:src/scripts/WeatherManager.js): 天氣管理器，控制遊戲中的天氣變化 [GDD: 4.3]
+ 
+ ### UI層 (UI Layer)
+ 用戶界面檔案位於 `src/ui/` 目錄：
+ 
+ **核心UI:**
+ - [InventoryUI.js](mdc:src/ui/InventoryUI.js): 庫存系統的使用者介面 [GDD: 7.1, 12]
+ - [CraftingUI.js](mdc:src/ui/CraftingUI.js): 合成系統的使用者介面 [GDD: 5, 12]
+ - [ShopUI.js](mdc:src/ui/ShopUI.js): 商店的使用者介面 [GDD: 9, 12]
+ 
+ **資訊顯示UI:**
+ - [TimeWeatherUI.js](mdc:src/ui/TimeWeatherUI.js): 時間和天氣資訊的使用者介面 [GDD: 4.2, 4.3, 12]
+ - [PlayerProgressionUI.js](mdc:src/ui/PlayerProgressionUI.js): 玩家進程的使用者介面，顯示玩家等級、經驗等 [GDD: 6, 12]
+ - [MapSelectionUI.js](mdc:src/ui/MapSelectionUI.js): 地圖選擇使用者介面 [GDD: 2, 4.4, 12]
+ 
+ ## 開發指導原則
+ 
+ ### 文件命名規範
+ - **場景**: 以 `Scene.js` 結尾
+ - **管理器**: 以 `Manager.js` 結尾  
+ - **UI組件**: 以 `UI.js` 結尾
+ - **迷你遊戲**: 以 `MiniGame.js` 結尾
+ - **數據檔案**: 使用 `.json` 或 `.js` 格式
+ 
+ ### GDD對應關係
+ 每個檔案都標記了對應的 GDD 章節編號，請在修改前參考相關的遊戲設計文檔。
+ 
+ ### 核心架構
+ - 使用 Phaser 3 遊戲引擎
+ - 採用場景管理系統
+ - 數據驅動的設計模式
+ - 模組化的UI組件設計
+ 
+ ### 重要提醒
+ 修改任何核心系統檔案時，請確保：
+ 1. 檢查相關的GDD章節
+ 2. 考慮對其他系統的影響
+ 3. 更新相關的UI組件
+ 4. 測試所有相關的迷你遊戲
+ 
```

---

我已經成功為您創建了 `.cursor/rules/fishing-game-structure.mdc` 檔案。這個 Cursor Rules 檔案包含了：

## 主要內容：

1. **專案概述** - 說明這是一個基於 Phaser 3 的釣魚遊戲
2. **檔案組織結構** - 按照四個主要層級組織：
   - 數據層 (Data Layer)
   - 場景層 (Scene Layer) 
   - 腳本層 (Scripts Layer)
   - UI層 (UI Layer)

3. **詳細的檔案參考** - 每個檔案都包含：
   - 檔案路徑連結 (使用 `mdc:` 格式)
   - 功能描述
   - 對應的 GDD 章節標籤

4. **開發指導原則** - 包含：
   - 檔案命名規範
   - GDD 對應關係說明
   - 核心架構介紹
   - 開發時的重要提醒

這個 Rules 檔案將幫助開發者：
- 快速理解專案結構
- 找到需要的檔案
- 了解各檔案的職責
- 參考對應的遊戲設計文檔
- 遵循專案的開發規範

現在 AI 在處理這個專案時，會自動參考這個規則檔案來更好地理解代碼庫的結構和組織方式。

---

_**User**_

@fishing-game-structure.mdc 這個rule可以改成像 @project_description.md 一樣，詳盡的檔案列表說明嗎，不要遺漏 /Generate Cursor Rules 

---

_**Assistant**_



---

我將修改現有的 `fishing-game-structure.mdc` rule，讓它包含所有檔案的詳盡列表，就像 `project_description.md` 一樣完整。

---

```diff
  
  本專案是一個基於 Phaser 3 的 2D 釣魚模擬遊戲 "Luxury Angler"。
+ 
+ ## GDD 目錄參考
+ 1.  Game Overview
+ 2.  Core Gameplay Loop
+ 3.  Attributes System
+ 4.  Fishing System
+     *   4.1 Fishing Mechanics
+         *   4.1.1 Cast Minigame
+         *   4.1.2 Lure Minigame (Enhanced)
+         *   4.1.3 Reel-In Minigame
+     *   4.2 Time System
+     *   4.3 Weather System
+     *   4.4 Lure Types
+     *   4.5 Fish Struggle Styles
+     *   4.6 QTE Reaction Controls
+ 5.  Crafting System
+ 6.  Level Progression
+ 7.  Inventory and Equipment
+     *   7.1 Inventory System
+     *   7.2 Equipment System
+ 8.  Social System
+     *   8.1 Cruise Cabin
+     *   8.2 Story and Dialog System (RenJS)
+ 9.  Shop System
+ 10. Achievement System
+ 11. Economy System
+ 12. Art and Audio
+ 13. Technical Design
  
  ## 專案檔案組織
```

```diff
  - [LocationData.js](mdc:src/data/LocationData.js): 地點數據，定義遊戲中不同地點的資訊 [GDD: 2.1, 2.2, 4.4]
  - [lures.json](mdc:src/data/lures.json): 魚餌數據，定義各種魚餌的屬性 [GDD: 4.4, 7.2]
+ - [sample-renjs-quest-dialog.yaml](mdc:src/data/sample-renjs-quest-dialog.yaml): RenJS 任務對話範例，用於對話系統 [GDD: 8.2, 8.3]
  - [TextConfig.js](mdc:src/data/TextConfig.js): 文字配置，可能包含遊戲中各種文字字串 [GDD: 12, 13]
  
  ### 場景層 (Scene Layer)
  場景檔案位於 `src/scenes/` 目錄，管理遊戲的不同畫面和狀態：
  
- **核心場景:**
- - [GameScene.js](mdc:src/scenes/GameScene.js): 核心遊戲場景，主要的釣魚活動發生在此 [GDD: 2, 4]
+ - [AlbumScene.js](mdc:src/scenes/AlbumScene.js): 圖鑑場景，可能用於展示收集到的魚類或其他物品 [GDD: 8.1]
  - [BoatMenuScene.js](mdc:src/scenes/BoatMenuScene.js): 船隻選單場景，可能用於選擇船隻或相關操作 [GDD: 2.1, 2.2]
- - [HUDScene.js](mdc:src/scenes/HUDScene.js): HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊 [GDD: 2.2, 12]
- 
- **社交場景:**
+ - [BootScene.js](mdc:src/scenes/BootScene.js): 啟動場景，遊戲的初始進入點 [GDD: 13]
  - [CabinScene.js](mdc:src/scenes/CabinScene.js): 小屋場景，可能是玩家的基地或休息場所 [GDD: 8.1]
  - [DialogScene.js](mdc:src/scenes/DialogScene.js): 對話場景，處理遊戲中的角色對話 [GDD: 8.2]
- 
- **系統場景:**
- - [ShopScene.js](mdc:src/scenes/ShopScene.js): 商店場景，用於購買物品 [GDD: 2, 9]
+ - [GameScene.js](mdc:src/scenes/GameScene.js): 核心遊戲場景，主要的釣魚活動發生在此 [GDD: 2, 4]
+ - [HUDScene.js](mdc:src/scenes/HUDScene.js): HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊 [GDD: 2.2, 12]
+ - [MenuScene.js](mdc:src/scenes/MenuScene.js): 主選單場景 [GDD: 2.1]
+ - [PreloadScene.js](mdc:src/scenes/PreloadScene.js): 預載入場景，在遊戲開始前載入資源 [GDD: 13]
  - [QuestScene.js](mdc:src/scenes/QuestScene.js): 任務場景，顯示和管理任務 [GDD: 2.2, 8.3]
- - [AlbumScene.js](mdc:src/scenes/AlbumScene.js): 圖鑑場景，可能用於展示收集到的魚類或其他物品 [GDD: 8.1]
+ - [SettingsScene.js](mdc:src/scenes/SettingsScene.js): 設定場景，處理遊戲設定選項 [GDD: 13]
+ - [ShopScene.js](mdc:src/scenes/ShopScene.js): 商店場景，用於購買物品 [GDD: 2, 9]
  
  ### 腳本層 (Scripts Layer)
  核心邏輯腳本位於 `src/scripts/` 目錄：
  
- **釣魚機制:**
+ - [AchievementEnhancer.js](mdc:src/scripts/AchievementEnhancer.js): 成就增強器，可能與成就系統的擴展功能相關 [GDD: 10]
+ - [AdvancedQuestSystem.js](mdc:src/scripts/AdvancedQuestSystem.js): 進階任務系統，提供更複雜的任務功能 [GDD: 8.3]
+ - [AudioManager.js](mdc:src/scripts/AudioManager.js): 音訊管理器，處理遊戲中的聲音和音樂 [GDD: 12]
+ - [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿迷你遊戲邏輯 [GDD: 4.1.1]
+ - [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成管理器，處理物品的製作 [GDD: 5]
+ - [DataLoader.js](mdc:src/scripts/DataLoader.js): 數據載入器，負責載入遊戲所需的數據檔案 [GDD: 13]
+ - [DialogManager.js](mdc:src/scripts/DialogManager.js): 對話管理器，管理遊戲中的對話流程 [GDD: 8.2]
+ - [EnhancedAudioManager.js](mdc:src/scripts/EnhancedAudioManager.js): 增強型音訊管理器，提供更進階的音訊控制功能 [GDD: 12]
+ - [EquipmentEnhancer.js](mdc:src/scripts/EquipmentEnhancer.js): 裝備強化器，處理裝備的升級和強化 [GDD: 7.2]
+ - [EventEmitter.js](mdc:src/scripts/EventEmitter.js): 事件發射器，用於實現事件驅動的程式設計模式 [GDD: 13]
+ - [FishDatabase.js](mdc:src/scripts/FishDatabase.js): 魚類資料庫，儲存和管理魚類數據 [GDD: 3.2, 4.5, 13]
+ - [FishSpawner.js](mdc:src/scripts/FishSpawner.js): 魚類生成器，控制魚類在遊戲世界的生成 [GDD: 4, 4.5]
  - [FishingMinigames.js](mdc:src/scripts/FishingMinigames.js): 釣魚迷你遊戲的主控腳本或集合 [GDD: 4.1]
- - [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿迷你遊戲邏輯 [GDD: 4.1.1]
- - [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚迷你遊戲邏輯 [GDD: 4.1.2]
- - [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線迷你遊戲邏輯 [GDD: 4.1.3]
- 
- **核心管理器:**
+ - [GameLoop.js](mdc:src/scripts/GameLoop.js): 遊戲循環，驅動遊戲的核心邏輯 [GDD: 2, 13]
  - [GameState.js](mdc:src/scripts/GameState.js): 遊戲狀態管理器，追蹤和管理遊戲的整體狀態 [GDD: 2, 13]
- - [GameLoop.js](mdc:src/scripts/GameLoop.js): 遊戲循環，驅動遊戲的核心邏輯 [GDD: 2, 13]
+ - [HCGNotificationManager.js](mdc:src/scripts/HCGNotificationManager.js): HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統) [GDD: 8.1, 10]
+ - [HCGUnlockSystem.js](mdc:src/scripts/HCGUnlockSystem.js): HCG 解鎖系統 (具體 HCG 指代不明) [GDD: 8.1, 10]
+ - [InputManager.js](mdc:src/scripts/InputManager.js): 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入 [GDD: 4.1, 13]
+ - [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理器，處理玩家物品的儲存和使用 [GDD: 7.1]
+ - [InventoryValidator.js](mdc:src/scripts/InventoryValidator.js): 庫存驗證器，驗證庫存操作的有效性 [GDD: 7.1, 13]
+ - [LocationManager.js](mdc:src/scripts/LocationManager.js): 地點管理器，管理遊戲中的不同地點 [GDD: 2, 4.4]
+ - [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚迷你遊戲邏輯 [GDD: 4.1.2]
  - [PlayerController.js](mdc:src/scripts/PlayerController.js): 玩家控制器，處理玩家輸入和行為 [GDD: 2, 3, 4]
+ - [PlayerProgression.js](mdc:src/scripts/PlayerProgression.js): 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容 [GDD: 3.1.1, 6]
+ - [QuestManager.js](mdc:src/scripts/QuestManager.js): 任務管理器，管理遊戲中的任務 [GDD: 8.3]
+ - [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線迷你遊戲邏輯 [GDD: 4.1.3]
+ - [RenJsContentValidator.js](mdc:src/scripts/RenJsContentValidator.js): RenJS 內容驗證器，驗證 RenJS 相關內容的正確性 [GDD: 8.2, 13]
+ - [RenJsLoader.js](mdc:src/scripts/RenJsLoader.js): RenJS 載入器，載入 RenJS 相關資源 [GDD: 8.2, 13]
+ - [RenJsSaveIntegration.js](mdc:src/scripts/RenJsSaveIntegration.js): RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中 [GDD: 8.2, 13]
+ - [SaveUtility.js](mdc:src/scripts/SaveUtility.js): 存檔工具，處理遊戲的儲存和載入 [GDD: 13]
  - [SceneManager.js](mdc:src/scripts/SceneManager.js): 場景管理器，管理不同遊戲場景的切換 [GDD: 13]
- 
- **系統管理器:**
- - [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理器，處理玩家物品的儲存和使用 [GDD: 7.1]
- - [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成管理器，處理物品的製作 [GDD: 5]
+ - [SettingsManager.js](mdc:src/scripts/SettingsManager.js): 設定管理器，管理遊戲的各項設定 [GDD: 13]
  - [TimeManager.js](mdc:src/scripts/TimeManager.js): 時間管理器，控制遊戲中的時間流逝 [GDD: 4.2]
+ - [TournamentManager.js](mdc:src/scripts/TournamentManager.js): 錦標賽管理器，組織和管理釣魚比賽 [GDD: 6, specific events]
  - [WeatherManager.js](mdc:src/scripts/WeatherManager.js): 天氣管理器，控制遊戲中的天氣變化 [GDD: 4.3]
  
  ### UI層 (UI Layer)
  用戶界面檔案位於 `src/ui/` 目錄：
  
- **核心UI:**
+ - [AchievementPopupSystem.js](mdc:src/ui/AchievementPopupSystem.js): 成就彈出提示系統 [GDD: 10, 12]
+ - [AudioSettingsUI.js](mdc:src/ui/AudioSettingsUI.js): 音訊設定的使用者介面 [GDD: 12, 13]
+ - [CraftingUI.js](mdc:src/ui/CraftingUI.js): 合成系統的使用者介面 [GDD: 5, 12]
+ - [EquipmentEnhancementUI.js](mdc:src/ui/EquipmentEnhancementUI.js): 裝備強化系統的使用者介面 [GDD: 7.2, 12]
+ - [FishCollectionUI.js](mdc:src/ui/FishCollectionUI.js): 魚類圖鑑或收集冊的使用者介面 [GDD: 7.1, 8.1, 12]
  - [InventoryUI.js](mdc:src/ui/InventoryUI.js): 庫存系統的使用者介面 [GDD: 7.1, 12]
- - [CraftingUI.js](mdc:src/ui/CraftingUI.js): 合成系統的使用者介面 [GDD: 5, 12]
+ - [LoadingStateManager.js](mdc:src/ui/LoadingStateManager.js): 載入狀態管理器，可能負責顯示載入進度條等 [GDD: 12, 13]
+ - [MapSelectionUI.js](mdc:src/ui/MapSelectionUI.js): 地圖選擇使用者介面 [GDD: 2, 4.4, 12]
+ - [PlayerProgressionUI.js](mdc:src/ui/PlayerProgressionUI.js): 玩家進程的使用者介面，顯示玩家等級、經驗等 [GDD: 6, 12]
+ - [RenJsDebugUI.js](mdc:src/ui/RenJsDebugUI.js): RenJS 調試使用者介面，用於開發和測試 [GDD: 8.2, 13]
  - [ShopUI.js](mdc:src/ui/ShopUI.js): 商店的使用者介面 [GDD: 9, 12]
- 
- **資訊顯示UI:**
  - [TimeWeatherUI.js](mdc:src/ui/TimeWeatherUI.js): 時間和天氣資訊的使用者介面 [GDD: 4.2, 4.3, 12]
- - [PlayerProgressionUI.js](mdc:src/ui/PlayerProgressionUI.js): 玩家進程的使用者介面，顯示玩家等級、經驗等 [GDD: 6, 12]
- - [MapSelectionUI.js](mdc:src/ui/MapSelectionUI.js): 地圖選擇使用者介面 [GDD: 2, 4.4, 12]
+ - [UITheme.js](mdc:src/ui/UITheme.js): UI 主題，定義介面元素的統一樣式 [GDD: 12]
  
  ## 開發指導原則
```

```diff
  - 數據驅動的設計模式
  - 模組化的UI組件設計
+ 
+ ### 重要系統檔案
+ 
+ **釣魚核心系統:**
+ - [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿機制
+ - [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚機制  
+ - [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線機制
+ - [FishingMinigames.js](mdc:src/scripts/FishingMinigames.js): 迷你遊戲協調器
+ 
+ **管理系統:**
+ - [GameState.js](mdc:src/scripts/GameState.js): 遊戲狀態管理
+ - [PlayerController.js](mdc:src/scripts/PlayerController.js): 玩家控制
+ - [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理
+ - [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成系統
+ 
+ **環境系統:**
+ - [TimeManager.js](mdc:src/scripts/TimeManager.js): 時間系統
+ - [WeatherManager.js](mdc:src/scripts/WeatherManager.js): 天氣系統
+ - [LocationManager.js](mdc:src/scripts/LocationManager.js): 地點管理
  
  ### 重要提醒
```

```diff
  3. 更新相關的UI組件
  4. 測試所有相關的迷你遊戲
+ 
  
  
```

---

我已經成功將 `fishing-game-structure.mdc` 更新為包含所有檔案的詳盡版本。更新的 Cursor Rules 檔案現在包含：

## 主要改進：

1. **完整的檔案列表** - 包含了所有 65 個檔案（9個data + 12個scenes + 31個scripts + 13個ui）
2. **GDD目錄參考** - 添加了完整的遊戲設計文檔目錄結構
3. **按字母順序排列** - 所有檔案都按照字母順序組織
4. **完整的GDD標籤** - 每個檔案都有對應的遊戲設計文檔章節標籤
5. **新增重要系統檔案區塊** - 突出顯示核心系統檔案分組

## 新增功能：

- **GDD目錄參考**: 提供完整的遊戲設計文檔結構
- **重要系統檔案**: 按功能分組的核心系統檔案
  - 釣魚核心系統
  - 管理系統  
  - 環境系統

這個更新後的 Rules 檔案現在提供了：
- 完整的專案結構概覽
- 所有檔案的詳細功能說明
- 清晰的 GDD 對應關係
- 核心系統的快速參考
- 開發指導原則

現在 AI 將能夠更準確地理解整個專案的結構，並在開發過程中提供更好的協助。

---

_**User**_

請幫我根據@fishing-game-structure.mdc 及@project_description.md ，參考 @Luxury_Angler_GDD(updated).txt ，在@GameDesign 生成每個系統藉由程式碼和設計文件的差異性反推回去撰寫每個系統獨立的設計文件，每一章小節都要獨立檔案出來，格式用markdown

一個一個系統寫詳盡，不要跳過或是總結形式

---

_**Assistant**_



---

我將根據您的要求，為每個系統創建獨立的設計文件。首先讓我檢查 GameDesign 目錄並開始創建各個系統的詳細設計文檔。

---

<details>
            <summary>Listed directory GameDesign • **0** results</summary>
        
No results found
</details>

---

GameDesign 目錄是空的，我將開始創建每個系統的獨立設計文檔。讓我從第一個系統開始：

---

```diff
- 
+ # 1. 遊戲概覽系統設計文檔
+ 
+ ## 1.1 遊戲概念設計
+ 
+ ### 核心概念
+ Luxury Angler 是一個融合現實釣魚機制與奢華生活主題的 2D 釣魚模擬遊戲。玩家從新手釣魚者成長為大師級釣手，體驗 50 個等級的進程，探索 5 個異域地圖，每個地圖包含 10 個釣魚點。
+ 
+ ### 遊戲核心特色
+ - **奢華釣魚生活風格**: 高端船隻和異域釣魚地點
+ - **深度合成系統**: 透過魚卡合成機制製作釣竿、魚餌、船隻和服裝
+ - **動態環境系統**: 日夜循環和天氣系統影響魚類可得性
+ - **技能與休閒並重**: 技能導向和放置類遊戲玩法的結合
+ 
+ ### 技術實現架構
+ 相關檔案：
+ - [gameConfig.json](../src/data/gameConfig.json): 遊戲全域配置
+ - [TextConfig.js](../src/data/TextConfig.js): 文字配置系統
+ - [GameState.js](../src/scripts/GameState.js): 遊戲狀態管理器
+ - [GameLoop.js](../src/scripts/GameLoop.js): 遊戲主循環
+ 
+ ## 1.2 目標受眾分析
+ 
+ ### 主要目標群體
+ - 年齡：16-35 歲的休閒到中度玩家
+ - 偏好：模擬、生活模擬和 RPG 遊戲愛好者
+ - 參考遊戲：Stardew Valley、Dave the Diver
+ - 遊戲動機：享受進程、合成、社交元素與奢華體驗
+ 
+ ### 玩家類型適配
+ 1. **休閒玩家**: 
+    - 自動釣魚功能
+    - 簡化的UI操作
+    - 清晰的進度指引
+ 
+ 2. **中度玩家**: 
+    - 技能導向的迷你遊戲
+    - 複雜的合成系統
+    - 社交互動機制
+ 
+ ## 1.3 平台與技術規格
+ 
+ ### 支援平台
+ - **主要平台**: 行動裝置 (iOS, Android)
+ - **潛在平台**: PC (Steam), Nintendo Switch
+ 
+ ### 技術架構
+ - **遊戲引擎**: Phaser 3
+ - **圖形風格**: 手繪 2D 風格，鮮豔水域，奢華船隻，精細魚類
+ - **效能目標**: 60 FPS，輕量級時間/天氣模擬
+ 
+ 相關檔案：
+ - [BootScene.js](../src/scenes/BootScene.js): 遊戲啟動場景
+ - [PreloadScene.js](../src/scenes/PreloadScene.js): 資源預載入
+ - [MenuScene.js](../src/scenes/MenuScene.js): 主選單場景
+ 
+ ## 1.4 遊戲類型與機制
+ 
+ ### 核心類型
+ - **模擬遊戲**: 真實的釣魚體驗
+ - **釣魚遊戲**: 專業的釣魚機制
+ - **生活模擬**: 奢華生活風格
+ - **卡牌收集**: 50 張魚卡系統
+ - **社交遊戲**: 10 位比基尼女伴系統
+ - **放置遊戲**: 自動釣魚選項
+ 
+ ### 獨特賣點 (USP)
+ 1. **奢華釣魚生活方式**: 結合高端船隻和異域地點
+ 2. **深度合成系統**: 使用魚卡合成裝備的創新機制
+ 3. **動態環境**: 8 個時間段和天氣系統影響遊戲體驗
+ 4. **技能與休閒平衡**: 滿足不同類型玩家需求
+ 
+ ## 1.5 核心功能概述
+ 
+ ### 釣魚機制
+ 相關檔案：
+ - [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚迷你遊戲主控
+ - [CastingMiniGame.js](../src/scripts/CastingMiniGame.js): 拋竿遊戲
+ - [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 誘魚遊戲
+ - [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js): 收線遊戲
+ 
+ ### 合成系統
+ 相關檔案：
+ - [CraftingManager.js](../src/scripts/CraftingManager.js): 合成管理器
+ - [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面
+ 
+ ### 進程系統
+ 相關檔案：
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 玩家進程管理
+ - [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 進程介面
+ 
+ ### 社交系統
+ 相關檔案：
+ - [CabinScene.js](../src/scenes/CabinScene.js): 船艙場景
+ - [DialogScene.js](../src/scenes/DialogScene.js): 對話場景
+ - [DialogManager.js](../src/scripts/DialogManager.js): 對話管理器
+ 
+ ## 1.6 設計原則與限制
+ 
+ ### 設計原則
+ 1. **易於學習，難於精通**: 簡單入門，深度挑戰
+ 2. **視覺奢華感**: 高品質的視覺呈現
+ 3. **漸進式揭露**: 功能逐步解鎖
+ 4. **玩家選擇權**: 技能或休閒模式自由切換
+ 
+ ### 技術限制
+ 1. **行動裝置效能**: 優化記憶體使用
+ 2. **網路依賴**: 雲端存檔功能
+ 3. **本地化需求**: 多語言支援架構
+ 
+ 相關檔案：
+ - [DataLoader.js](../src/scripts/DataLoader.js): 數據載入器
+ - [SaveUtility.js](../src/scripts/SaveUtility.js): 存檔工具
+ - [SettingsManager.js](../src/scripts/SettingsManager.js): 設定管理
+ 
+ ## 1.7 系統間整合
+ 
+ ### 核心系統連接
+ ```
+ 遊戲狀態 (GameState) 
+     ↓
+ 場景管理 (SceneManager) 
+     ↓
+ 各個功能場景 (GameScene, BoatMenuScene, etc.)
+     ↓
+ UI系統 (各種UI組件)
+ ```
+ 
+ ### 數據流架構
+ ```
+ 配置數據 (gameConfig.json)
+     ↓
+ 遊戲狀態管理 (GameState.js)
+     ↓
+ 各系統管理器 (Manager classes)
+     ↓
+ 使用者介面 (UI classes)
+ ```
+ 
+ 相關檔案：
+ - [SceneManager.js](../src/scripts/SceneManager.js): 場景管理器
+ - [EventEmitter.js](../src/scripts/EventEmitter.js): 事件系統 
```

---

現在讓我繼續創建第二個系統文檔：

---

```diff
- 
+ # 2. 核心遊戲循環系統設計文檔
+ 
+ ## 2.1 主要遊戲循環流程圖
+ 
+ ### 遊戲流程架構
+ ```
+ 標題畫面 (MenuScene)
+     ↓
+ 船隻選單 (BoatMenuScene) - 時間、天氣、位置顯示
+     ↓
+ 玩家選擇行動：
+     ├── 旅行 → 選擇地圖 (1/5) → 選擇地點 (1/10) → 抵達 (時間+1小時, 能量-2)
+     ├── 釣魚 → 選擇模式：
+     │   ├── 故事模式 → 故事地圖
+     │   └── 任務模式 → 任務地圖 (排除故事基礎魚地圖)
+     │       └── 釣魚 (拋竿、誘魚、收線; 時間+30分, 能量-5)
+     │           ├── 成功 → 收集魚卡 (如果魚缸未滿) → 返回船隻選單
+     │           └── 失敗 → 返回船隻選單
+     ├── 船艱 → 巡航艦艙 (社交互動) → 返回船隻選單
+     ├── 商店 (僅限起始港口) → 商店選單 (購買物品) → 返回船隻選單
+     ├── 庫存 → 庫存管理 (管理卡片、裝備) → 返回船隻選單
+     └── 返回港口 (時間+1小時, 能量-2) → 出售魚卡 → 進入商店
+ ```
+ 
+ ### 技術實現架構
+ 相關檔案：
+ - [GameLoop.js](../src/scripts/GameLoop.js): 主遊戲循環
+ - [SceneManager.js](../src/scripts/SceneManager.js): 場景切換管理
+ - [BoatMenuScene.js](../src/scenes/BoatMenuScene.js): 船隻選單主場景
+ - [GameScene.js](../src/scenes/GameScene.js): 核心釣魚場景
+ 
+ ## 2.2 遊戲循環詳細描述
+ 
+ ### 入口點流程
+ 1. **標題畫面**: 
+    - 新遊戲或載入遊戲
+    - 教學或存檔載入
+    - 相關檔案: [MenuScene.js](../src/scenes/MenuScene.js)
+ 
+ 2. **船隻選單中心**:
+    - 顯示當前時間 (例如：黎明)
+    - 顯示天氣狀態 (例如：晴天)
+    - 顯示船隻位置 (例如：起始港口 或 地圖3地點7)
+    - 相關檔案: [BoatMenuScene.js](../src/scenes/BoatMenuScene.js)
+ 
+ ### 核心行動選項
+ 
+ #### 2.2.1 旅行系統
+ - **功能**: 在起始港口和5個釣魚地圖間移動
+ - **地圖結構**: 每個地圖包含10個獨特地點 (例如：珊瑚礁、深海溝渠)
+ - **消耗**: 旅行需要1小時和2點能量
+ - **相關檔案**:
+   - [LocationManager.js](../src/scripts/LocationManager.js): 地點管理
+   - [LocationData.js](../src/data/LocationData.js): 地點數據
+   - [MapSelectionUI.js](../src/ui/MapSelectionUI.js): 地圖選擇介面
+ 
+ #### 2.2.2 釣魚系統核心循環
+ **故事模式 vs 任務模式**:
+ - **故事模式**: 
+   - 存取與敘事相關的地圖
+   - 包含獨特魚類和挑戰
+   - 綁定主要故事線
+   
+ - **任務模式**: 
+   - 存取任務地圖 (排除故事基礎魚地圖)
+   - 用於技能磨練或捕捉特定魚類
+   - 不影響故事進程
+ 
+ **釣魚流程**:
+ 1. **選擇模式和地圖**
+ 2. **執行三階段釣魚**:
+    - 拋竿 ([CastingMiniGame.js](../src/scripts/CastingMiniGame.js))
+    - 誘魚 ([LuringMiniGame.js](../src/scripts/LuringMiniGame.js))
+    - 收線 ([ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js))
+ 3. **結果處理**: 成功收集魚卡或失敗
+ 4. **資源消耗**: 30分鐘和5點能量
+ 
+ 相關檔案：
+ - [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚遊戲協調
+ - [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成
+ - [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據庫
+ 
+ #### 2.2.3 社交互動系統
+ - **船艙活動**: 與伴侶進行社交互動
+ - **功能**: 
+   - 與10位比基尼女伴互動
+   - 增進關係
+   - 解鎖獎勵和HCG場景
+ - **相關檔案**:
+   - [CabinScene.js](../src/scenes/CabinScene.js): 船艙場景
+   - [DialogScene.js](../src/scenes/DialogScene.js): 對話系統
+   - [DialogManager.js](../src/scripts/DialogManager.js): 對話管理
+ 
+ #### 2.2.4 商店與庫存管理
+ **商店系統**:
+ - **限制**: 僅在停靠起始港口時可用
+ - **功能**: 購買魚卡、裝備、寶石
+ - **相關檔案**: 
+   - [ShopScene.js](../src/scenes/ShopScene.js): 商店場景
+   - [ShopUI.js](../src/ui/ShopUI.js): 商店介面
+ 
+ **庫存管理**:
+ - **功能**: 管理魚卡、裝備和其他物品
+ - **魚缸限制**: 當魚缸滿載時必須返回港口出售
+ - **相關檔案**:
+   - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理
+   - [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面
+ 
+ ## 2.3 時間與能量系統
+ 
+ ### 時間管理
+ - **時間週期**: 8個時間段 (例如：黎明、正午)，每段3遊戲小時
+ - **時間進行方式**:
+   - 釣魚: +30分鐘
+   - 旅行: +1小時
+ - **相關檔案**: [TimeManager.js](../src/scripts/TimeManager.js)
+ 
+ ### 能量系統
+ - **能量消耗**:
+   - 釣魚: -5點能量
+   - 旅行: -2點能量
+ - **能量管理**: 需要合理安排行動避免能量耗盡
+ - **相關檔案**: [PlayerController.js](../src/scripts/PlayerController.js)
+ 
+ ## 2.4 魚缸與經濟循環
+ 
+ ### 魚缸機制
+ - **容量限制**: 魚缸有固定容量
+ - **強制返回**: 魚缸滿載時必須返回起始港口
+ - **出售流程**: 
+   1. 返回港口 (消耗1小時和2能量)
+   2. 出售所有魚卡
+   3. 釋放魚缸空間
+   4. 可進入商店購買物品
+ 
+ ### 經濟循環
+ - **收入來源**: 出售捕獲的魚卡
+ - **支出項目**: 購買裝備、魚餌、船隻升級
+ - **循環模式**: 釣魚 → 收集 → 出售 → 購買 → 強化 → 更好釣魚
+ 
+ 相關檔案：
+ - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 玩家進程
+ 
+ ## 2.5 場景切換與狀態管理
+ 
+ ### 場景架構
+ ```
+ MenuScene (主選單)
+     ↓
+ BootScene → PreloadScene → BoatMenuScene
+     ↓
+ GameScene ← → CabinScene ← → ShopScene
+     ↓           ↓              ↓
+ HUDScene    DialogScene    QuestScene
+ ```
+ 
+ ### 狀態持續性
+ - **遊戲狀態**: 跨場景保持玩家數據
+ - **場景數據**: 場景間的數據傳遞
+ - **存檔系統**: 自動和手動存檔
+ 
+ 相關檔案：
+ - [GameState.js](../src/scripts/GameState.js): 遊戲狀態管理
+ - [SaveUtility.js](../src/scripts/SaveUtility.js): 存檔工具
+ - [SceneManager.js](../src/scripts/SceneManager.js): 場景管理
+ 
+ ## 2.6 循環驅動機制
+ 
+ ### 主循環結構
+ ```javascript
+ // 偽代碼示例
+ class GameLoop {
+     update(delta) {
+         // 更新時間系統
+         this.timeManager.update(delta);
+         
+         // 更新天氣系統
+         this.weatherManager.update(delta);
+         
+         // 更新當前場景
+         this.sceneManager.updateCurrentScene(delta);
+         
+         // 處理玩家輸入
+         this.inputManager.processInput();
+         
+         // 更新UI
+         this.uiManager.updateUI();
+     }
+ }
+ ```
+ 
+ ### 系統更新順序
+ 1. **時間系統更新**: 推進遊戲時間
+ 2. **天氣系統更新**: 檢查天氣變化
+ 3. **場景邏輯更新**: 當前場景的遊戲邏輯
+ 4. **輸入處理**: 玩家操作響應
+ 5. **UI更新**: 介面元素更新
+ 6. **渲染**: 畫面繪製
+ 
+ 相關檔案：
+ - [TimeManager.js](../src/scripts/TimeManager.js): 時間系統
+ - [WeatherManager.js](../src/scripts/WeatherManager.js): 天氣系統
+ - [InputManager.js](../src/scripts/InputManager.js): 輸入管理
+ - [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 時間天氣UI
+ 
+ ## 2.7 循環中的進程追蹤
+ 
+ ### 玩家進度指標
+ - **等級進程**: 50等級系統
+ - **地圖解鎖**: 逐步開放新地圖
+ - **魚類收集**: 50張魚卡收集進度
+ - **社交關係**: 與伴侶的關係值
+ 
+ ### 成就與里程碑
+ - **釣魚成就**: 捕獲特定魚類
+ - **收集成就**: 完成魚卡收集
+ - **社交成就**: 伴侶關係里程碑
+ - **探索成就**: 發現所有地點
+ 
+ 相關檔案：
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 進程管理
+ - [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
+ - [QuestManager.js](../src/scripts/QuestManager.js): 任務管理 
```

---

現在讓我繼續創建第三個系統文檔：

---

```diff
- 
+ # 3. 屬性系統設計文檔
+ 
+ ## 3.1 玩家屬性系統 (34項屬性)
+ 
+ ### 3.1.1 玩家升級屬性 (10項)
+ 
+ 這些屬性透過等級提升、裝備或技能來增強，影響釣魚、合成和社交互動。
+ 
+ #### 精準度與技能屬性
+ **拋竿精準度 (Cast Accuracy)**
+ - **功能**: 增加熱點目標區域大小
+ - **提升方式**: 每級+1% (最大20%)
+ - **影響系統**: 拋竿迷你遊戲成功率
+ - **技術實現**: [CastingMiniGame.js](../src/scripts/CastingMiniGame.js)
+ 
+ **咬餌率 (Bite Rate)**
+ - **功能**: 提高魚類咬餌的可能性
+ - **提升方式**: 每級+0.5%
+ - **影響系統**: 誘魚階段成功率
+ - **技術實現**: [LuringMiniGame.js](../src/scripts/LuringMiniGame.js)
+ 
+ **QTE精準度 (QTE Precision)**
+ - **功能**: 延長QTE反應時間窗口
+ - **提升方式**: 每級+0.5%
+ - **影響系統**: 收線迷你遊戲反應時間
+ - **技術實現**: [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js)
+ 
+ **誘魚成功率 (Lure Success)**
+ - **功能**: 誘魚輸入的成功率
+ - **提升方式**: 每級+0.5%
+ - **影響系統**: 魚餌控制成功率
+ - **技術實現**: [LuringMiniGame.js](../src/scripts/LuringMiniGame.js)
+ 
+ #### 資源與能力屬性
+ **能量 (Energy)**
+ - **功能**: 行動的體力值 (基礎最大值100)
+ - **提升方式**: 每級+2點
+ - **影響系統**: 釣魚、旅行活動消耗
+ - **技術實現**: [PlayerController.js](../src/scripts/PlayerController.js)
+ 
+ **稀有魚機率 (Rare Fish Chance)**
+ - **功能**: 遇到稀有魚類的機率
+ - **提升方式**: 每級+0.2%
+ - **影響系統**: 魚類生成系統
+ - **技術實現**: [FishSpawner.js](../src/scripts/FishSpawner.js)
+ 
+ **線強度 (Line Strength)**
+ - **功能**: 減少線斷裂機會
+ - **提升方式**: 每級+0.3%
+ - **影響系統**: 收線迷你遊戲張力控制
+ - **技術實現**: [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js)
+ 
+ **拋竿距離 (Casting Range)**
+ - **功能**: 增加拋竿射程
+ - **提升方式**: 每級+0.5%
+ - **影響系統**: 可達釣魚區域
+ - **技術實現**: [CastingMiniGame.js](../src/scripts/CastingMiniGame.js)
+ 
+ **船隻速度 (Boat Speed)**
+ - **功能**: 減少旅行時間
+ - **提升方式**: 每級+0.3%
+ - **影響系統**: 地點間移動效率
+ - **技術實現**: [LocationManager.js](../src/scripts/LocationManager.js)
+ 
+ **金幣獲得 (Coin Gain)**
+ - **功能**: 增加獲得的金幣
+ - **提升方式**: 每級+0.3%
+ - **影響系統**: 經濟收益
+ - **技術實現**: [PlayerProgression.js](../src/scripts/PlayerProgression.js)
+ 
+ ### 3.1.2 釣竿屬性 (6項)
+ 
+ 釣竿裝備提供的屬性加成，影響釣魚的各個階段。
+ 
+ **拋竿精準度加成 (Cast Accuracy Boost)**
+ - **功能**: 釣竿提供的精準度加成
+ - **範例**: 5星釣竿 +25%
+ - **數據來源**: [equipment.json](../src/data/equipment.json)
+ 
+ **張力穩定性 (Tension Stability)**
+ - **功能**: 擴大安全張力區域
+ - **範例**: 5星釣竿 +28%
+ - **影響**: 收線迷你遊戲容錯率
+ 
+ **稀有魚機率加成 (Rare Fish Chance Boost)**
+ - **功能**: 釣竿增加的稀有魚遇到率
+ - **範例**: 5星釣竿 +15%
+ - **影響**: 高價值魚類捕獲率
+ 
+ **拋竿距離加成 (Casting Range Boost)**
+ - **功能**: 釣竿延伸的拋竿射程
+ - **範例**: 5星釣竿 +35%
+ - **影響**: 更遠釣魚點存取
+ 
+ **收線速度 (Reel Speed)**
+ - **功能**: 更快的收線速度
+ - **範例**: 5星釣竿 +15%
+ - **影響**: 收線迷你遊戲效率
+ 
+ **掙扎抗性 (Struggle Resistance)**
+ - **功能**: 減少魚類掙扎強度
+ - **範例**: 5星釣竿 +15%
+ - **影響**: 減少收線難度
+ 
+ 技術實現：
+ - [equipment.json](../src/data/equipment.json): 裝備屬性數據
+ - [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化系統
+ 
+ ### 3.1.3 魚餌屬性 (4項)
+ 
+ 魚餌裝備影響誘魚階段的成功率和效果。
+ 
+ **咬餌率加成 (Bite Rate Boost)**
+ - **功能**: 魚餌提供的咬餌率增加
+ - **範例**: 5星魚餌 +60%
+ - **影響**: 魚類興趣度
+ 
+ **誘魚成功率加成 (Lure Success Boost)**
+ - **功能**: 魚餌改善誘魚輸入成功率
+ - **範例**: 3星魚餌 +40%
+ - **影響**: 誘魚迷你遊戲容錯
+ 
+ **魚餌耐久性 (Lure Durability)**
+ - **功能**: 減少魚餌失效機會
+ - **範例**: 5星魚餌 +10%
+ - **影響**: 魚餌使用壽命
+ 
+ **魚餌精準度 (Lure Precision)**
+ - **功能**: 緊縮輸入窗口要求
+ - **範例**: 5星魚餌 +10%
+ - **影響**: 誘魚技能需求
+ 
+ 技術實現：
+ - [lures.json](../src/data/lures.json): 魚餌屬性數據
+ - [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 魚餌效果應用
+ 
+ ### 3.1.4 比基尼助手屬性 (6項)
+ 
+ 伴侶系統提供的各種加成效果。
+ 
+ **浪漫效果 (Romance Effectiveness)**
+ - **功能**: 增加浪漫點數獲得
+ - **提升方式**: 透過技能改善
+ - **影響**: 社交互動效果
+ 
+ **船艙親和力 (Cabin Affinity)**
+ - **功能**: 增強船艙互動效果
+ - **提升方式**: 透過船隻改善
+ - **影響**: 伴侶互動品質
+ 
+ **娛樂心情加成 (Entertainment Mood Boost)**
+ - **功能**: 提升伴侶心情
+ - **提升方式**: 透過船隻改善
+ - **影響**: 長期關係發展
+ 
+ **魚類偵測 (Fish Detection)**
+ - **功能**: 發現高價值魚類
+ - **提升方式**: 透過伴侶改善
+ - **影響**: 稀有魚類發現率
+ 
+ **能量加成 (Energy Boost)**
+ - **功能**: 伴侶提供的能量增加
+ - **提升方式**: 透過服裝改善
+ - **影響**: 活動持續能力
+ 
+ **QTE精準度加成 (QTE Precision Boost)**
+ - **功能**: 伴侶延長QTE反應時間
+ - **提升方式**: 透過技能改善
+ - **影響**: 收線迷你遊戲表現
+ 
+ 技術實現：
+ - [CabinScene.js](../src/scenes/CabinScene.js): 伴侶互動
+ - [DialogManager.js](../src/scripts/DialogManager.js): 社交系統
+ 
+ ### 3.1.5 船隻屬性 (8項)
+ 
+ 船隻裝備提供的整體遊戲體驗改善。
+ 
+ **合成效率 (Crafting Efficiency)**
+ - **功能**: 減少合成時間/成本
+ - **提升方式**: 透過船隻改善
+ - **影響**: 合成系統效率
+ 
+ **自動釣魚產量 (Auto-Fishing Yield)**
+ - **功能**: 增加自動釣魚獎勵
+ - **提升方式**: 透過船隻改善
+ - **影響**: 放置遊戲收益
+ 
+ **魚類偵測增強 (Fish Detection Enhancement)**
+ - **功能**: 船隻提供的魚類偵測
+ - **範例**: 5星船隻 +10%
+ - **影響**: 魚類發現機率
+ 
+ **熱點穩定性 (Hotspot Stability)**
+ - **功能**: 增加熱點持續時間
+ - **提升方式**: 透過船隻改善
+ - **影響**: 釣魚點可用時間
+ 
+ **伴侶槽容量 (Companion Slot Capacity)**
+ - **功能**: 增加伴侶槽位
+ - **提升方式**: 透過船隻改善
+ - **影響**: 同時攜帶伴侶數量
+ 
+ **自動釣魚效率 (Auto-Fishing Efficiency)**
+ - **功能**: 減少自動釣魚時間
+ - **提升方式**: 透過船隻改善
+ - **影響**: 放置遊戲速度
+ 
+ **船隻耐久性 (Boat Durability)**
+ - **功能**: 減少惡劣條件懲罰
+ - **提升方式**: 透過船隻改善
+ - **影響**: 天氣影響抗性
+ 
+ **魚缸儲存 (Fishtank Storage)**
+ - **功能**: 擴展魚卡儲存空間
+ - **提升方式**: 透過船隻改善
+ - **影響**: 魚卡容量
+ 
+ 技術實現：
+ - [BoatMenuScene.js](../src/scenes/BoatMenuScene.js): 船隻介面
+ - [CraftingManager.js](../src/scripts/CraftingManager.js): 合成效率
+ 
+ ## 3.2 魚類屬性系統 (11項)
+ 
+ ### 3.2.1 物理屬性
+ 
+ **大小 (Size)**
+ - **範圍**: 1-10
+ - **範例**: 海妖 (Kraken): 10
+ - **影響**: 釣魚難度和視覺呈現
+ 
+ **重量 (Weight)**
+ - **範圍**: 0.1-500 公斤
+ - **範例**: 巨型烏賊: 500公斤
+ - **影響**: 收線力量需求
+ 
+ **速度 (Speed)**
+ - **範圍**: 1-10
+ - **範例**: 旗魚 (Marlin): 10
+ - **影響**: 誘魚和收線階段的反應時間
+ 
+ ### 3.2.2 行為屬性
+ 
+ **攻擊性 (Aggressiveness)**
+ - **範圍**: 1-10
+ - **範例**: 鯊魚: 10
+ - **影響**: 咬餌行為和掙扎模式
+ 
+ **狡猾度 (Elusiveness)**
+ - **範圍**: 1-10
+ - **範例**: 鮟鱇魚: 10
+ - **影響**: 誘魚難度和逃脫機率
+ 
+ **力量 (Strength)**
+ - **範圍**: 1-10
+ - **範例**: 鮪魚: 10
+ - **影響**: 收線張力和掙扎強度
+ 
+ **耐力 (Endurance)**
+ - **範圍**: 1-10
+ - **範例**: 海妖: 10
+ - **影響**: 收線持續時間
+ 
+ ### 3.2.3 遊戲機制屬性
+ 
+ **稀有度 (Rarity)**
+ - **範圍**: 1-10
+ - **範例**: 藍旗魚: 10
+ - **影響**: 出現機率和獎勵價值
+ 
+ **深度偏好 (Depth Preference)**
+ - **範圍**: 1-10
+ - **範例**: 蝰魚: 10 (深海)
+ - **影響**: 釣魚地點和時間限制
+ 
+ **魚餌偏好 (Bait Preference)**
+ - **範圍**: 1-10
+ - **範例**: 鱒魚: 10 (偏好飛釣餌)
+ - **影響**: 特定魚餌的效果加成
+ 
+ **活躍時間段/天氣偏好 (Active Time/Weather Preference)**
+ - **範例**: 月光鱸魚: 夜晚、雨天
+ - **影響**: 特定時間和天氣的出現機率
+ 
+ 技術實現：
+ - [fish.json](../src/data/fish.json): 魚類屬性數據
+ - [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據管理
+ - [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成邏輯
+ 
+ ## 3.3 屬性計算與平衡系統
+ 
+ ### 3.3.1 屬性疊加機制
+ 
+ **加法疊加**
+ ```javascript
+ // 範例：最終拋竿精準度
+ finalCastAccuracy = 
+     playerLevel.castAccuracy +     // 玩家等級加成
+     equipment.rod.castAccuracy +   // 釣竿加成
+     companion.skillBonus +         // 伴侶技能加成
+     temporaryBuffs                 // 臨時增益
+ ```
+ 
+ **乘法疊加**
+ ```javascript
+ // 範例：最終咬餌率
+ finalBiteRate = 
+     baseBiteRate * 
+     (1 + playerLevel.biteRateBonus/100) *
+     (1 + lure.biteRateBonus/100) *
+     (1 + weather.biteRateModifier/100)
+ ```
+ 
+ ### 3.3.2 屬性平衡考量
+ 
+ **早期遊戲平衡**
+ - 低等級屬性提供明顯改善
+ - 基礎裝備有顯著差異
+ - 容錯率較高
+ 
+ **中期遊戲平衡**
+ - 屬性增長趨緩
+ - 策略選擇重要性增加
+ - 專精化開始顯現
+ 
+ **後期遊戲平衡**
+ - 邊際效益遞減
+ - 最適化配置重要
+ - 技能表現決定性
+ 
+ 技術實現：
+ - [attributes.json](../src/data/attributes.json): 屬性平衡數據
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 屬性計算
+ - [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備屬性處理
+ 
+ ## 3.4 屬性UI與視覺反饋
+ 
+ ### 3.4.1 屬性顯示介面
+ 
+ **玩家屬性面板**
+ - 當前屬性值顯示
+ - 升級預覽
+ - 裝備影響顯示
+ - 相關檔案: [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js)
+ 
+ **裝備比較介面**
+ - 屬性差異對比
+ - 裝備前後預覽
+ - 推薦配置建議
+ - 相關檔案: [EquipmentEnhancementUI.js](../src/ui/EquipmentEnhancementUI.js)
+ 
+ **魚類圖鑑介面**
+ - 魚類屬性展示
+ - 捕獲難度指示
+ - 最佳釣魚條件
+ - 相關檔案: [FishCollectionUI.js](../src/ui/FishCollectionUI.js)
+ 
+ ### 3.4.2 即時反饋系統
+ 
+ **屬性變化通知**
+ - 升級獲得屬性提示
+ - 裝備效果即時顯示
+ - 臨時增益/減益提示
+ 
+ **遊戲中屬性影響**
+ - 迷你遊戲難度調整視覺化
+ - 成功率指示器
+ - 屬性加成效果顯示
+ 
+ 技術實現：
+ - [UITheme.js](../src/ui/UITheme.js): UI主題和樣式
+ - [LoadingStateManager.js](../src/ui/LoadingStateManager.js): 載入狀態顯示
+ 
+ ## 3.5 屬性系統與其他系統整合
+ 
+ ### 3.5.1 與釣魚系統整合
+ - 拋竿精準度影響熱點成功率
+ - 誘魚屬性影響魚類興趣
+ - 收線屬性影響張力控制
+ 
+ ### 3.5.2 與合成系統整合
+ - 裝備屬性決定合成結果
+ - 屬性需求限制合成配方
+ - 合成效率影響製作時間
+ 
+ ### 3.5.3 與社交系統整合
+ - 浪漫屬性影響伴侶關係
+ - 伴侶屬性提供遊戲加成
+ - 社交互動影響屬性獲得
+ 
+ ### 3.5.4 與進程系統整合
+ - 等級提升自動增加屬性
+ - 成就解鎖屬性加成
+ - 里程碑獎勵特殊屬性
+ 
+ 相關檔案：
+ - [PlayerController.js](../src/scripts/PlayerController.js): 玩家控制與屬性應用
+ - [GameState.js](../src/scripts/GameState.js): 屬性狀態管理
+ - [EventEmitter.js](../src/scripts/EventEmitter.js): 屬性變化事件 
```

---

現在讓我繼續創建第四個系統文檔 - 釣魚系統：

---

```diff
- 
+ # 4. 釣魚系統設計文檔
+ 
+ ## 4.1 釣魚機制總覽
+ 
+ ### 4.1.1 拋竿迷你遊戲 (Cast Minigame)
+ 
+ 拋竿迷你遊戲是釣魚流程的第一階段，決定魚餌投放的精準度。
+ 
+ #### 遊戲機制設計
+ **基本流程**:
+ 1. 顯示拋竿計量器
+ 2. 玩家在精準區域內點擊
+ 3. 成功：魚餌投放到熱點（大型或稀有魚類出現機率較高）
+ 4. 失敗：魚餌投放到普通區域
+ 
+ **視覺設計**:
+ - 2D釣竿拋竿動畫
+ - 魚餌帶釣線拋向水中
+ - 成功：魚餌落在熱點並有濺水音效
+ - 失敗：魚餌落在熱點外
+ 
+ **屬性影響**:
+ - **拋竿精準度**: 影響精準區域大小
+ - **魚類偵測**: 影響熱點可見度
+ - **拋竿距離**: 影響可達釣魚區域
+ 
+ **技術實現**:
+ ```javascript
+ // 拋竿精準度計算範例
+ const accuracyZoneSize = baseZoneSize * (1 + player.castAccuracy / 100);
+ const fishDetectionChance = baseFishDetection * (1 + player.fishDetection / 100);
+ ```
+ 
+ 相關檔案：
+ - [CastingMiniGame.js](../src/scripts/CastingMiniGame.js): 拋竿遊戲邏輯
+ 
+ ### 4.1.2 誘魚迷你遊戲 (Lure Minigame) - 增強版
+ 
+ 誘魚迷你遊戲是核心釣魚體驗，涉及複雜的魚類行為模擬和五種魚餌控制方式。
+ 
+ #### 遊戲介面設計
+ **誘魚模擬UI** (右上角):
+ - 顯示魚餌在水中的移動
+ - 附近魚類在UI底部游泳
+ - 感興趣的魚會停下、冒泡、觀察魚餌5秒
+ 
+ **基本控制方式**:
+ - **上 (W)**: 魚餌快速上游1秒
+ - **左/右 (A/D)**: 魚餌向船隻方向（螢幕右側）游動1秒
+ - **所有動作**: 1秒冷卻時間
+ 
+ #### 魚影機制
+ **魚影類型**:
+ - **小型魚影**: 普通魚類
+ - **中型魚影**: 中等稀有度
+ - **大型魚影**: 稀有或高價值魚類
+ 
+ **魚影行為**:
+ - 游向魚餌的方式反映魚類**攻擊性**和**狡猾度**
+ - 繞圈、衝刺等行為模式
+ 
+ #### 誘魚階段系統
+ **階段設計**:
+ - 玩家有**3-4次機會**（階段）來鉤住魚
+ - 每階段使用特定魚餌的節奏輸入
+ - **成功**: 正確輸入讓魚影保持接近
+ - **失敗**: 錯誤讓魚影游走；全部失敗則收回魚餌
+ 
+ **漸進難度**:
+ - 後期階段更困難（更緊的時機、複雜序列）
+ - 根據魚類**狡猾度**和玩家**誘魚成功率**調整
+ 
+ #### 五種魚餌類型與控制方式
+ 
+ **1. 旋轉餌 (Spinner) - 脈衝點擊**
+ - **控制方式**: 有節奏的點擊
+ - **效果**: 魚餌旋轉閃光
+ - **適用魚類**: 攻擊性魚類
+ 
+ **2. 軟塑膠餌 (Soft Plastic) - 拖拽暫停**
+ - **控制方式**: 拖拽後暫停
+ - **效果**: 模擬受傷小魚
+ - **適用魚類**: 掠食性魚類
+ 
+ **3. 飛釣餌 (Fly) - 滑動閃爍組合**
+ - **控制方式**: 快速滑動組合
+ - **效果**: 模擬昆蟲落水
+ - **適用魚類**: 表層魚類
+ 
+ **4. 波扒餌 (Popper) - 點擊長按爆發**
+ - **控制方式**: 點擊後長按
+ - **效果**: 表面水花爆發
+ - **適用魚類**: 大型攻擊性魚類
+ 
+ **5. 匙型餌 (Spoon) - 圓形軌跡**
+ - **控制方式**: 畫圓形軌跡
+ - **效果**: 閃爍擺動
+ - **適用魚類**: 深水魚類
+ 
+ #### 鉤魚最終階段
+ **鉤魚流程**:
+ 1. 成功完成所有階段後
+ 2. 最終定時輸入鉤住魚
+ 3. **反饋**: 濺水聲（成功）或撞擊聲（失敗）
+ 
+ #### 屬性整合系統
+ **玩家屬性影響**:
+ - **誘魚成功率**: 放寬輸入時機
+ - **咬餌率**: 可能減少所需階段數
+ 
+ **魚類屬性影響**:
+ - **攻擊性**: 更快的魚影接近速度
+ - **狡猾度**: 更高的失敗風險
+ 
+ **魚餌屬性加成**:
+ - **咬餌率**: 提高魚類興趣
+ - **誘魚成功率**: 改善輸入容錯
+ - **魚餌耐久性**: 減少失效機會
+ - **魚餌精準度**: 調整技能需求
+ 
+ 技術實現：
+ ```javascript
+ // 誘魚階段難度計算
+ const phaseDifficulty = baseDifficulty * 
+     (1 + fish.elusiveness / 10) * 
+     (1 - player.lureSuccess / 100) *
+     (1 - lure.lureSuccessBonus / 100);
+ ```
+ 
+ 相關檔案：
+ - [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 誘魚遊戲主邏輯
+ - [lures.json](../src/data/lures.json): 魚餌數據定義
+ 
+ ### 4.1.3 收線迷你遊戲 (Reel-In Minigame)
+ 
+ 收線迷你遊戲是釣魚的最後階段，需要管理張力並應對魚類掙扎。
+ 
+ #### 基本遊戲機制
+ **視覺元素**:
+ - 被鉤住掙扎的魚與釣線
+ - 濺水效果
+ - 魚快速左右游動
+ 
+ **張力計量器**:
+ - 顯示當前釣線張力
+ - 玩家必須防止張力達到最大值
+ - 張力過高會導致釣線斷裂
+ 
+ **收線控制**:
+ - **滑鼠按壓**: 收線並消耗魚的體力
+ - 消耗體力數值會從掙扎的魚身上彈出顯示
+ 
+ #### 魚類體力系統
+ **體力條**: 顯示在魚的頂部
+ **掙扎循環**: 魚會進行數次掙扎直到體力歸零
+ **勝利條件**: 魚體力耗盡後被成功拉起
+ 
+ #### QTE反應控制系統
+ **掙扎樣式觸發QTE**:
+ 魚類會隨機執行2-3種掙扎方式中的組合，每種掙扎需要特定的QTE回應。
+ 
+ **QTE失敗懲罰**:
+ 如果未能快速輸入正確的QTE，張力計量器會突然增加到90%最大值。
+ 
+ #### 十種魚類掙扎樣式
+ 
+ **1. 快速顫動 (Rapid Thrashing)**
+ - **行為**: 快速左右擺動
+ - **QTE**: 快速點擊序列
+ - **風險**: 高張力增加
+ 
+ **2. 深潜 (Deep Dive)**
+ - **行為**: 向深水游動
+ - **QTE**: 長按住控制
+ - **風險**: 持續張力增加
+ 
+ **3. 長距離衝刺 (Long Sprint)**
+ - **行為**: 直線高速游走
+ - **QTE**: 跟隨滑動
+ - **風險**: 突發高張力
+ 
+ **4. 圓形游動 (Circular Swimming)**
+ - **行為**: 繞圈游動
+ - **QTE**: 圓形軌跡追蹤
+ - **風險**: 持續中等張力
+ 
+ **5. 跳躍 (Jumping)**
+ - **行為**: 躍出水面
+ - **QTE**: 定時釋放
+ - **風險**: 瞬間極高張力
+ 
+ **6. 底部摩擦 (Bottom Scraping)**
+ - **行為**: 在海底摩擦
+ - **QTE**: 交替點擊
+ - **風險**: 線損傷風險
+ 
+ **7. 螺旋游動 (Spiral Swimming)**
+ - **行為**: 螺旋形移動
+ - **QTE**: 螺旋手勢
+ - **風險**: 複雜張力變化
+ 
+ **8. 突然停止 (Sudden Stop)**
+ - **行為**: 瞬間停止掙扎
+ - **QTE**: 立即停止輸入
+ - **風險**: 玩家過度收線
+ 
+ **9. 之字形游動 (Zigzag Swimming)**
+ - **行為**: Z字形快速移動
+ - **QTE**: 快速方向變化
+ - **風險**: 不可預測張力
+ 
+ **10. 死亡翻滾 (Death Roll)**
+ - **行為**: 身體翻滾旋轉
+ - **QTE**: 旋轉手勢配合
+ - **風險**: 極高斷線風險
+ 
+ #### QTE反應控制類型
+ 
+ **1. 快速點擊 (Quick Tap)**
+ - **操作**: 在時間窗口內快速點擊
+ - **適用掙扎**: 快速顫動、底部摩擦
+ 
+ **2. 長按釋放 (Hold and Release)**
+ - **操作**: 長按後在正確時機釋放
+ - **適用掙扎**: 深潛、跳躍
+ 
+ **3. 滑動鏈 (Swipe Chain)**
+ - **操作**: 連續方向滑動
+ - **適用掙扎**: 長距離衝刺、之字形游動
+ 
+ **4. 軌跡追蹤 (Trace Following)**
+ - **操作**: 跟隨螢幕軌跡
+ - **適用掙扎**: 圓形游動、螺旋游動
+ 
+ **5. 定時輸入 (Timed Input)**
+ - **操作**: 精確時機的單次輸入
+ - **適用掙扎**: 突然停止、死亡翻滾
+ 
+ #### 屬性影響系統
+ **玩家屬性**:
+ - **張力穩定性**: 擴大安全張力區域
+ - **收線速度**: 更快的收線效率
+ - **QTE精準度**: 延長反應時間窗口
+ - **線強度**: 減少斷線風險
+ 
+ **魚類屬性**:
+ - **力量**: 影響張力產生強度
+ - **耐力**: 決定掙扎持續時間
+ - **速度**: 影響掙扎頻率
+ - **大小**: 影響整體難度
+ 
+ **釣竿屬性**:
+ - **張力穩定性**: 減少張力波動
+ - **掙扎抗性**: 減少魚類掙扎效果
+ - **收線速度**: 提高收線效率
+ 
+ 技術實現：
+ ```javascript
+ // 張力計算範例
+ const tensionIncrease = 
+     fish.strength * struggleIntensity * 
+     (1 - rod.tensionStability / 100) *
+     (1 - player.lineStrength / 100);
+ 
+ // QTE時間窗口計算
+ const qteWindow = baseQTETime * (1 + player.qtePrecision / 100);
+ ```
+ 
+ 相關檔案：
+ - [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js): 收線遊戲邏輯
+ - [fish.json](../src/data/fish.json): 魚類掙扎樣式數據
+ 
+ ## 4.2 時間系統設計
+ 
+ ### 4.2.1 時間週期結構
+ 
+ **8個時間段設計**:
+ 1. **黎明** (05:00-08:00): 晨光魚類活躍
+ 2. **上午** (08:00-11:00): 穩定釣魚期
+ 3. **正午** (11:00-14:00): 陽光充足，部分魚類休息
+ 4. **下午** (14:00-17:00): 下午活動高峰
+ 5. **黃昏** (17:00-20:00): 覓食時間，高活動期
+ 6. **夜晚** (20:00-23:00): 夜行魚類出現
+ 7. **深夜** (23:00-02:00): 特殊夜行魚類
+ 8. **午夜** (02:00-05:00): 最稀有夜行魚類
+ 
+ ### 4.2.2 時間進程機制
+ 
+ **時間推進方式**:
+ - **釣魚活動**: +30分鐘
+ - **旅行活動**: +1小時
+ - **其他活動**: 視活動類型而定
+ 
+ **時間影響因素**:
+ - 魚類出現機率
+ - 特定魚種的活躍時段
+ - 釣魚效率加成/減益
+ - 特殊事件觸發
+ 
+ ### 4.2.3 魚類時間偏好系統
+ 
+ **範例時間偏好**:
+ - **月光鱸魚**: 夜晚時段 (+50% 出現機率)
+ - **日照金槍魚**: 正午時段 (+30% 出現機率)
+ - **黎明鯛魚**: 黎明時段 (+40% 出現機率)
+ 
+ 技術實現：
+ ```javascript
+ // 時間影響魚類出現機率
+ const timeBonus = fish.preferredTimeModifier[currentTimeSlot] || 1.0;
+ const spawnChance = baseFishSpawnChance * timeBonus;
+ ```
+ 
+ 相關檔案：
+ - [TimeManager.js](../src/scripts/TimeManager.js): 時間系統管理
+ - [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 時間顯示介面
+ 
+ ## 4.3 天氣系統設計
+ 
+ ### 4.3.1 天氣類型與效果
+ 
+ **晴天 (Sunny)**:
+ - **魚類偵測加成**: 提高發現魚類機率
+ - **視覺效果**: 明亮水面，良好能見度
+ - **特殊效果**: 表層魚類更活躍
+ 
+ **雨天 (Rainy)**:
+ - **咬餌率加成**: +10% 咬餌率
+ - **視覺效果**: 雨滴、較暗的環境
+ - **特殊效果**: 某些魚類在雨天更活躍
+ 
+ ### 4.3.2 天氣變化機制
+ 
+ **變化週期**: 每2遊戲小時檢查天氣變化
+ **變化機率**: 基於現實天氣模式的權重系統
+ **預報系統**: 玩家可查看未來天氣預報
+ 
+ ### 4.3.3 天氣與魚類行為
+ 
+ **晴天偏好魚類**:
+ - 表層活動魚類
+ - 視覺獵食者
+ - 光線敏感魚種
+ 
+ **雨天偏好魚類**:
+ - 深水魚類上浮
+ - 以昆蟲為食的魚類
+ - 低光環境適應魚種
+ 
+ 技術實現：
+ ```javascript
+ // 天氣影響咬餌率
+ const weatherBonus = weather === 'rainy' ? 1.1 : 1.0;
+ const finalBiteRate = baseBiteRate * weatherBonus;
+ ```
+ 
+ 相關檔案：
+ - [WeatherManager.js](../src/scripts/WeatherManager.js): 天氣系統管理
+ - [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 天氣顯示介面
+ 
+ ## 4.4 地圖選擇與釣魚地點
+ 
+ ### 4.4.1 地圖結構設計
+ 
+ **5個主要地圖**:
+ 1. **珊瑚灣 (Coral Cove)**: 熱帶淺水區域
+ 2. **深海深淵 (Deep Abyss)**: 深海釣魚區域
+ 3. **北極海域 (Arctic Waters)**: 冰冷海域
+ 4. **火山島嶼 (Volcanic Island)**: 地熱活動區域
+ 5. **神秘海溝 (Mysterious Trench)**: 未知深海區域
+ 
+ **每個地圖10個釣魚點**:
+ - 每個釣魚點有獨特的魚類組合
+ - 不同深度和環境特色
+ - 特殊釣魚條件或挑戰
+ 
+ ### 4.4.2 故事模式 vs 任務模式
+ 
+ **故事模式地圖**:
+ - 與主要敘事線綁定
+ - 包含獨特魚類和挑戰
+ - 解鎖新功能和劇情
+ - 有特殊的Boss魚類
+ 
+ **任務模式地圖**:
+ - 獨立於故事的訓練場
+ - 排除故事基礎魚地圖
+ - 專注技能練習和特定魚種捕獲
+ - 不影響故事進程
+ 
+ ### 4.4.3 釣魚點特色系統
+ 
+ **範例釣魚點**:
+ - **珊瑚礁 (Coral Reef)**: 彩色熱帶魚
+ - **深海溝渠 (Deep Trench)**: 深海巨型魚類
+ - **海底山丘 (Underwater Hills)**: 中型洄游魚
+ - **熱水口 (Thermal Vents)**: 耐熱特殊魚種
+ 
+ 技術實現：
+ - [LocationManager.js](../src/scripts/LocationManager.js): 地點管理系統
+ - [LocationData.js](../src/data/LocationData.js): 地點數據定義
+ - [MapSelectionUI.js](../src/ui/MapSelectionUI.js): 地圖選擇介面
+ 
+ ## 4.5 魚類系統設計
+ 
+ ### 4.5.1 魚類分類結構
+ 
+ **魚類總覽**:
+ - **總數**: 50種魚類（50張魚卡）
+ - **類型分組**: 10個魚類類型，每類型5種魚
+ - **Boss魚類**: 10條Boss魚
+ - **分布**: 5個地圖 × 10個地點，每地點5種不同魚類
+ 
+ ### 4.5.2 魚類類型分組範例
+ 
+ **1. 小型熱帶魚類型**:
+ - 小丑魚、天使魚、蝴蝶魚、刺尾魚、六線魚
+ 
+ **2. 中型珊瑚魚類型**:
+ - 石斑魚、鸚鵡魚、麥鯛、金鯧、海鱺
+ 
+ **3. 大型洄游魚類型**:
+ - 鮪魚、旗魚、鯊魚、海豚魚、舵魚
+ 
+ **4. 深海魚類型**:
+ - 鮟鱇魚、蝰魚、燈籠魚、琵琶魚、深海鯊
+ 
+ **5. 極地魚類型**:
+ - 北極鱈魚、冰魚、海豹魚、極地鯊、冰川鱒
+ 
+ ### 4.5.3 Boss魚類設計
+ 
+ **Boss魚特色**:
+ - 每5等級出現一條Boss魚
+ - 特殊的掙扎模式組合
+ - 需要特定裝備或策略
+ - 豐厚的獎勵和成就
+ 
+ **範例Boss魚**:
+ - **等級5**: 巨型鱸魚 - 多階段掙扎
+ - **等級10**: 電鰻王 - 電擊QTE挑戰
+ - **等級50**: 海妖 - 終極Boss戰
+ 
+ ### 4.5.4 魚類生成邏輯
+ 
+ **基本生成規則**:
+ ```javascript
+ // 魚類生成機率計算
+ const spawnChance = 
+     fish.baseSpawnRate *
+     timeMultiplier *
+     weatherMultiplier *
+     locationMultiplier *
+     playerLevelMultiplier *
+     equipmentBonuses;
+ ```
+ 
+ **稀有度影響**:
+ - 稀有度1-3：常見魚類，高生成率
+ - 稀有度4-6：中等稀有，正常生成率
+ - 稀有度7-10：極稀有，低生成率
+ 
+ 技術實現：
+ - [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據庫
+ - [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成邏輯
+ - [fish.json](../src/data/fish.json): 魚類屬性數據
+ 
+ ## 4.6 整合系統設計
+ 
+ ### 4.6.1 釣魚系統與屬性系統整合
+ 
+ **屬性對釣魚的影響**:
+ - 拋竿階段：精準度、距離影響成功率
+ - 誘魚階段：咬餌率、成功率影響魚類興趣
+ - 收線階段：張力穩定、QTE精準度影響難度
+ 
+ ### 4.6.2 釣魚系統與進程系統整合
+ 
+ **經驗值獲得**:
+ - 成功釣魚：基礎經驗值
+ - 稀有魚類：額外經驗加成
+ - 完美釣魚：技能表現獎勵
+ 
+ **等級影響**:
+ - 解鎖新地圖和釣魚點
+ - 提高基礎屬性
+ - 獲得新能力和特權
+ 
+ ### 4.6.3 釣魚系統與經濟系統整合
+ 
+ **魚卡價值**:
+ - 基於稀有度的價格差異
+ - 時間和天氣加成影響價值
+ - 完美捕獲的品質獎勵
+ 
+ **裝備需求**:
+ - 更好裝備提高成功率
+ - 專用裝備對抗特定魚類
+ - 升級路徑的經濟投資
+ 
+ 相關檔案：
+ - [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚遊戲主控制器
+ - [GameScene.js](../src/scenes/GameScene.js): 核心釣魚場景
+ - [HUDScene.js](../src/scenes/HUDScene.js): 釣魚過程中的UI顯示
+ - [InputManager.js](../src/scripts/InputManager.js): 輸入處理系統 
```

---

現在讓我繼續創建第五個系統文檔 - 合成系統：

---

```diff
- 
+ # 5. 合成系統設計文檔
+ 
+ ## 5.1 合成機制總覽
+ 
+ ### 5.1.1 船隻工作坊合成系統
+ 
+ 合成系統是《Luxury Angler》的核心進程機制，讓玩家將捕獲的魚卡轉化為更強力的裝備。
+ 
+ #### 基本合成流程
+ 1. **進入船隻工作坊**: 從船隻選單存取合成介面
+ 2. **選擇合成配方**: 從60個可用配方中選擇
+ 3. **放入材料**: 使用魚卡（1-5星）作為主要材料
+ 4. **支付成本**: 消耗金幣（200-16,000）
+ 5. **等待完成**: 合成時間（2-30分鐘）
+ 6. **獲得物品**: 產出釣竿、魚餌、船隻或服裝
+ 
+ #### 合成材料系統
+ **魚卡分級**:
+ - **1星魚卡**: 基礎材料，用於初級合成
+ - **2星魚卡**: 普通材料，中級合成主料
+ - **3星魚卡**: 優質材料，高級合成配方
+ - **4星魚卡**: 稀有材料，精英級裝備
+ - **5星魚卡**: 傳說材料，大師級裝備
+ 
+ **材料消耗機制**:
+ - 合成完成後魚卡被消耗
+ - 某些配方需要特定種類的魚卡
+ - 高級合成可能需要多種不同魚卡
+ 
+ 技術實現：
+ - [CraftingManager.js](../src/scripts/CraftingManager.js): 合成系統核心邏輯
+ - [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面實現
+ 
+ ### 5.1.2 升級合成機制
+ 
+ **裝備升級路徑**:
+ - **基礎級** → **精英級** → **大師級**
+ - 每級需要合成2個低級物品
+ - 升級成本和時間遞增
+ - 屬性大幅提升
+ 
+ **升級配方範例**:
+ ```
+ 精英竹製釣竿 = 2 × 竹製釣竿 + 1,000金幣 + 12分鐘
+ 大師竹製釣竿 = 2 × 精英竹製釣竿 + 8,000金幣 + 22分鐘
+ ```
+ 
+ ## 5.2 詳細合成配方系統 (60個配方)
+ 
+ ### 5.2.1 釣竿系列 (15個配方)
+ 
+ 釣竿是主要戰鬥裝備，提供拋竿精準度、張力穩定性等關鍵屬性。
+ 
+ #### 基礎釣竿配方 (5個)
+ **1. 竹製釣竿**
+ - **材料**: 2張鯡魚卡
+ - **成本**: 200金幣
+ - **時間**: 2分鐘
+ - **屬性**: +5% 拋竿精準度
+ - **描述**: 傳統竹製，輕巧耐用
+ 
+ **2. 玻璃纖維釣竿**
+ - **材料**: 2張鱸魚卡
+ - **成本**: 400金幣
+ - **時間**: 4分鐘
+ - **屬性**: +8% 拋竿精準度, +5% 張力穩定性
+ - **描述**: 現代材料，彈性佳
+ 
+ **3. 碳纖維釣竿**
+ - **材料**: 2張鱒魚卡
+ - **成本**: 800金幣
+ - **時間**: 6分鐘
+ - **屬性**: +12% 拋竿精準度, +8% 張力穩定性
+ - **描述**: 高科技材料，極輕且強韌
+ 
+ **4. 鋼製釣竿**
+ - **材料**: 2張梭魚卡
+ - **成本**: 1,200金幣
+ - **時間**: 8分鐘
+ - **屬性**: +15% 拋竿精準度, +12% 張力穩定性, +5% 線強度
+ - **描述**: 重型釣竿，適合大型魚類
+ 
+ **5. 鈦合金釣竿**
+ - **材料**: 2張鮭魚卡
+ - **成本**: 2,000金幣
+ - **時間**: 10分鐘
+ - **屬性**: +20% 拋竿精準度, +18% 張力穩定性, +10% 稀有魚機率
+ - **描述**: 頂級材料，完美平衡
+ 
+ #### 精英釣竿配方 (5個)
+ 升級合成，需要2個基礎釣竿 + 額外成本
+ 
+ **6. 精英竹製釣竿**
+ - **材料**: 2支竹製釣竿
+ - **成本**: 1,000金幣
+ - **時間**: 12分鐘
+ - **屬性**: +12% 拋竿精準度, +8% 張力穩定性
+ 
+ **7. 精英玻璃纖維釣竿**
+ - **材料**: 2支玻璃纖維釣竿
+ - **成本**: 2,000金幣
+ - **時間**: 14分鐘
+ - **屬性**: +18% 拋竿精準度, +15% 張力穩定性
+ 
+ **8. 精英碳纖維釣竿**
+ - **材料**: 2支碳纖維釣竿
+ - **成本**: 3,000金幣
+ - **時間**: 16分鐘
+ - **屬性**: +22% 拋竿精準度, +18% 張力穩定性, +8% 拋竿距離
+ 
+ **9. 精英鋼製釣竿**
+ - **材料**: 2支鋼製釣竿
+ - **成本**: 4,000金幣
+ - **時間**: 18分鐘
+ - **屬性**: +25% 拋竿精準度, +22% 張力穩定性, +12% 線強度
+ 
+ **10. 精英鈦合金釣竿**
+ - **材料**: 2支鈦合金釣竿
+ - **成本**: 6,000金幣
+ - **時間**: 20分鐘
+ - **屬性**: +28% 拋竿精準度, +25% 張力穩定性, +15% 稀有魚機率
+ 
+ #### 大師釣竿配方 (5個)
+ 終極級釣竿，需要2個精英釣竿
+ 
+ **11. 大師竹製釣竿**
+ - **材料**: 2支精英竹製釣竿
+ - **成本**: 8,000金幣
+ - **時間**: 22分鐘
+ - **屬性**: +20% 拋竿精準度, +15% 張力穩定性, +10% 收線速度
+ 
+ **12. 大師玻璃纖維釣竿**
+ - **材料**: 2支精英玻璃纖維釣竿
+ - **成本**: 10,000金幣
+ - **時間**: 24分鐘
+ - **屬性**: +25% 拋竿精準度, +20% 張力穩定性, +12% 收線速度
+ 
+ **13. 大師碳纖維釣竿**
+ - **材料**: 2支精英碳纖維釣竿
+ - **成本**: 12,000金幣
+ - **時間**: 26分鐘
+ - **屬性**: +30% 拋竿精準度, +25% 張力穩定性, +15% 拋竿距離
+ 
+ **14. 大師鋼製釣竿**
+ - **材料**: 2支精英鋼製釣竿
+ - **成本**: 14,000金幣
+ - **時間**: 28分鐘
+ - **屬性**: +32% 拋竿精準度, +28% 張力穩定性, +18% 線強度
+ 
+ **15. 大師鈦合金釣竿**
+ - **材料**: 2支精英鈦合金釣竿
+ - **成本**: 16,000金幣
+ - **時間**: 30分鐘
+ - **屬性**: +35% 拋竿精準度, +30% 張力穩定性, +20% 稀有魚機率, +15% 收線速度
+ 
+ ### 5.2.2 魚餌系列 (15個配方)
+ 
+ 魚餌系列專注於提升咬餌率和誘魚成功率，對應五種魚餌類型。
+ 
+ #### 基礎魚餌配方 (5個)
+ **16. 基礎旋轉餌**
+ - **材料**: 2張鱸魚卡
+ - **成本**: 300金幣
+ - **時間**: 2分鐘
+ - **屬性**: +20% 咬餌率
+ - **適用**: 攻擊性魚類
+ 
+ **17. 軟蟲餌**
+ - **材料**: 2張鯰魚卡
+ - **成本**: 500金幣
+ - **時間**: 4分鐘
+ - **屬性**: +25% 咬餌率, +10% 誘魚成功率
+ - **適用**: 掠食性魚類
+ 
+ **18. 飛釣餌**
+ - **材料**: 2張鱒魚卡
+ - **成本**: 700金幣
+ - **時間**: 6分鐘
+ - **屬性**: +30% 咬餌率, +15% 誘魚成功率
+ - **適用**: 表層魚類
+ 
+ **19. 波扒餌**
+ - **材料**: 2張梭魚卡
+ - **成本**: 900金幣
+ - **時間**: 8分鐘
+ - **屬性**: +35% 咬餌率, +20% 誘魚成功率
+ - **適用**: 大型攻擊性魚類
+ 
+ **20. 匙型餌**
+ - **材料**: 2張鮭魚卡
+ - **成本**: 1,100金幣
+ - **時間**: 10分鐘
+ - **屬性**: +40% 咬餌率, +25% 誘魚成功率
+ - **適用**: 深水魚類
+ 
+ #### 精英魚餌配方 (5個)
+ **21-25. 精英系列魚餌**
+ - 需要2個對應基礎魚餌
+ - 成本範圍: 1,500-3,500金幣
+ - 時間範圍: 12-20分鐘
+ - 屬性提升: +15-25% 各項數值
+ 
+ #### 大師魚餌配方 (5個)
+ **26-30. 大師系列魚餌**
+ - 需要2個對應精英魚餌
+ - 成本範圍: 4,000-6,000金幣
+ - 時間範圍: 22-30分鐘
+ - 屬性提升: +25-40% 各項數值
+ - 額外效果: 魚餌耐久性 +10-15%
+ 
+ ### 5.2.3 船隻系列 (15個配方)
+ 
+ 船隻提供整體遊戲體驗提升，包括合成效率、自動釣魚和儲存空間。
+ 
+ #### 基礎船隻配方 (5個)
+ **31. 划艇**
+ - **材料**: 3張沙丁魚卡
+ - **成本**: 500金幣
+ - **時間**: 5分鐘
+ - **屬性**: +3% 船隻速度, +5格魚缸儲存
+ - **描述**: 小型個人船隻
+ 
+ **32. 小艇**
+ - **材料**: 3張鱸魚卡
+ - **成本**: 1,000金幣
+ - **時間**: 7分鐘
+ - **屬性**: +5% 船隻速度, +8% 合成效率, +10格魚缸儲存
+ - **描述**: 實用小型船隻
+ 
+ **33. 快艇**
+ - **材料**: 3張鮪魚卡
+ - **成本**: 2,000金幣
+ - **時間**: 9分鐘
+ - **屬性**: +8% 船隻速度, +12% 合成效率, +15格魚缸儲存
+ - **描述**: 高速移動船隻
+ 
+ **34. 遊艇**
+ - **材料**: 3張旗魚卡
+ - **成本**: 4,000金幣
+ - **時間**: 11分鐘
+ - **屬性**: +12% 船隻速度, +15% 合成效率, +20格魚缸儲存, +1伴侶槽
+ - **描述**: 奢華休閒船隻
+ 
+ **35. 豪華郵輪**
+ - **材料**: 3張鯊魚卡
+ - **成本**: 6,000金幣
+ - **時間**: 13分鐘
+ - **屬性**: +15% 船隻速度, +20% 合成效率, +25格魚缸儲存, +2伴侶槽
+ - **描述**: 頂級奢華船隻
+ 
+ #### 精英船隻配方 (5個)
+ **36-40. 精英系列船隻**
+ - 需要2艘對應基礎船隻
+ - 成本範圍: 2,000-12,000金幣
+ - 時間範圍: 15-23分鐘
+ - 新增屬性: 自動釣魚效率、熱點穩定性
+ 
+ #### 大師船隻配方 (5個)
+ **41-45. 大師系列船隻**
+ - 需要2艘對應精英船隻
+ - 成本範圍: 10,000-16,000金幣
+ - 時間範圍: 25-33分鐘
+ - 頂級屬性: 最大自動釣魚產量、完美合成效率
+ 
+ ### 5.2.4 服裝系列 (15個配方)
+ 
+ 服裝提供各種輔助加成，包括能量、社交和特殊能力。
+ 
+ #### 基礎服裝配方 (5個)
+ **46. 漁夫帽**
+ - **材料**: 2張鱒魚卡
+ - **成本**: 400金幣
+ - **時間**: 3分鐘
+ - **屬性**: +5% 能量, +3% 咬餌率
+ - **描述**: 經典漁夫裝備
+ 
+ **47. 太陽眼鏡**
+ - **材料**: 2張梭魚卡
+ - **成本**: 600金幣
+ - **時間**: 5分鐘
+ - **屬性**: +8% 魚類偵測, +5% 拋竿精準度
+ - **描述**: 時尚實用配件
+ 
+ **48. 釣魚背心**
+ - **材料**: 2張鮭魚卡
+ - **成本**: 800金幣
+ - **時間**: 7分鐘
+ - **屬性**: +10% 能量, +5格庫存空間
+ - **描述**: 多功能釣魚背心
+ 
+ **49. 比基尼上衣**
+ - **材料**: 2張鱸魚卡
+ - **成本**: 1,000金幣
+ - **時間**: 9分鐘
+ - **屬性**: +12% 浪漫效果, +8% 娛樂心情加成
+ - **描述**: 度假風格服裝
+ 
+ **50. 涼鞋**
+ - **材料**: 2張鱸魚卡
+ - **成本**: 1,200金幣
+ - **時間**: 11分鐘
+ - **屬性**: +5% 船隻速度, +8% 能量
+ - **描述**: 舒適輕便鞋履
+ 
+ #### 精英服裝配方 (5個)
+ **51-55. 精英系列服裝**
+ - 需要2件對應基礎服裝
+ - 成本範圍: 1,500-3,500金幣
+ - 時間範圍: 13-21分鐘
+ - 增強社交和實用屬性
+ 
+ #### 大師服裝配方 (5個)
+ **56-60. 大師系列服裝**
+ - 需要2件對應精英服裝
+ - 成本範圍: 4,000-6,000金幣
+ - 時間範圍: 23-31分鐘
+ - 頂級時尚和功能性
+ 
+ ## 5.3 合成系統機制設計
+ 
+ ### 5.3.1 時間管理系統
+ 
+ **合成時間計算**:
+ ```javascript
+ // 基礎合成時間修正
+ const craftingTime = baseCraftingTime * 
+     (1 - player.craftingEfficiency / 100) *
+     (1 - boat.craftingBonus / 100) *
+     (1 - companion.craftingAssist / 100);
+ ```
+ 
+ **時間階層**:
+ - **快速合成** (2-5分鐘): 基礎裝備
+ - **標準合成** (6-15分鐘): 中級裝備
+ - **長時間合成** (16-33分鐘): 高級裝備
+ 
+ ### 5.3.2 成本平衡系統
+ 
+ **金幣成本設計**:
+ - **早期** (200-1,200金幣): 容易獲得，快速進步
+ - **中期** (1,500-6,000金幣): 需要策略規劃
+ - **後期** (8,000-16,000金幣): 重大投資決策
+ 
+ **材料稀缺性**:
+ - 普通魚卡: 容易獲得，大量需求
+ - 稀有魚卡: 限量供應，珍貴材料
+ 
+ ### 5.3.3 配方解鎖系統
+ 
+ **解鎖條件**:
+ - **玩家等級**: 達到特定等級解鎖新配方
+ - **前置裝備**: 需要製作基礎版本才能升級
+ - **故事進程**: 某些配方綁定劇情進展
+ - **成就獎勵**: 完成特定成就解鎖特殊配方
+ 
+ ### 5.3.4 品質控制系統
+ 
+ **合成成功率**:
+ - 基礎配方: 100% 成功率
+ - 精英配方: 95% 成功率，5% 機率額外屬性
+ - 大師配方: 90% 成功率，10% 機率完美品質
+ 
+ **失敗處理**:
+ - 材料損失50%
+ - 金幣退回75%
+ - 獲得"合成經驗"減少未來失敗率
+ 
+ ## 5.4 合成UI與使用者體驗
+ 
+ ### 5.4.1 合成介面設計
+ 
+ **主要元素**:
+ - **配方列表**: 可用配方分類顯示
+ - **材料槽位**: 拖放魚卡到對應位置
+ - **成本顯示**: 金幣和時間需求
+ - **進度條**: 合成進行中的視覺反饋
+ - **完成通知**: 合成結束的提醒系統
+ 
+ **篩選與搜尋**:
+ - 按裝備類型篩選
+ - 按材料需求篩選
+ - 按解鎖狀態篩選
+ - 搜尋特定配方名稱
+ 
+ ### 5.4.2 合成預覽系統
+ 
+ **屬性比較**:
+ - 顯示當前裝備 vs 合成結果
+ - 屬性變化以顏色標示
+ - 總體戰力評估
+ 
+ **材料檢查**:
+ - 自動檢查所需材料是否充足
+ - 缺少材料以紅色標示
+ - 推薦獲得途徑提示
+ 
+ ### 5.4.3 批量合成功能
+ 
+ **快速合成**:
+ - 一鍵合成多個同類物品
+ - 自動選擇最佳材料組合
+ - 批量完成通知
+ 
+ **合成佇列**:
+ - 安排多個合成任務
+ - 自動依序執行
+ - 佇列管理介面
+ 
+ 技術實現：
+ - [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面實現
+ - [InventoryUI.js](../src/ui/InventoryUI.js): 材料選擇介面
+ 
+ ## 5.5 合成系統平衡與經濟整合
+ 
+ ### 5.5.1 經濟循環設計
+ 
+ **資源流動**:
+ ```
+ 釣魚獲得魚卡 → 出售部分魚卡獲得金幣 → 保留魚卡用於合成 → 
+ 合成裝備提升釣魚效率 → 捕獲更好魚類 → 更高經濟收益
+ ```
+ 
+ **投資回報**:
+ - 短期投資：基礎裝備，快速見效
+ - 中期投資：精英裝備，穩定收益
+ - 長期投資：大師裝備，最大化效益
+ 
+ ### 5.5.2 進程驅動設計
+ 
+ **能力解鎖曲線**:
+ - 等級1-10：學習基本合成
+ - 等級11-25：掌握精英合成
+ - 等級26-50：精通大師合成
+ 
+ **挑戰適配**:
+ - 新手友善：簡單配方，明確指引
+ - 中級挑戰：資源管理，選擇策略
+ - 高級精通：最適化配置，完美時機
+ 
+ ### 5.5.3 社交整合
+ 
+ **伴侶協助**:
+ - 特定伴侶提供合成加成
+ - 關係等級影響協助效果
+ - 伴侶專長對應不同裝備類型
+ 
+ **競爭元素**:
+ - 合成排行榜
+ - 限時合成挑戰
+ - 稀有配方競逐
+ 
+ 相關檔案：
+ - [CraftingManager.js](../src/scripts/CraftingManager.js): 合成系統核心
+ - [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面
+ - [InventoryManager.js](../src/scripts/InventoryManager.js): 材料管理
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 配方解鎖
+ - [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備屬性計算 
```

---

現在讓我建立一個目錄文件來總結所有已建立的設計文檔：

---

```diff
- 
+ # Luxury Angler 遊戲設計文檔總覽
+ 
+ 本目錄包含《Luxury Angler》遊戲的完整系統設計文檔，每個文檔詳述特定系統的設計理念、技術實現和整合方式。
+ 
+ ## 📂 文檔結構
+ 
+ ### 已完成文檔
+ 
+ 1. **[01-game-overview.md](./01-game-overview.md)** - 遊戲概覽系統設計文檔
+    - 遊戲概念與核心特色
+    - 目標受眾分析
+    - 平台與技術規格
+    - 遊戲類型與獨特賣點
+    - 核心功能概述
+    - 設計原則與技術限制
+    - 系統間整合架構
+ 
+ 2. **[02-core-gameplay-loop.md](./02-core-gameplay-loop.md)** - 核心遊戲循環系統設計文檔
+    - 主要遊戲循環流程圖
+    - 船隻選單中心設計
+    - 旅行系統
+    - 釣魚系統核心循環
+    - 社交互動系統
+    - 商店與庫存管理
+    - 時間與能量系統
+    - 場景切換與狀態管理
+ 
+ 3. **[03-attributes-system.md](./03-attributes-system.md)** - 屬性系統設計文檔
+    - 玩家屬性系統 (34項屬性)
+      - 玩家升級屬性 (10項)
+      - 釣竿屬性 (6項)
+      - 魚餌屬性 (4項)
+      - 比基尼助手屬性 (6項)
+      - 船隻屬性 (8項)
+    - 魚類屬性系統 (11項)
+    - 屬性計算與平衡系統
+    - 屬性UI與視覺反饋
+    - 與其他系統整合
+ 
+ 4. **[04-fishing-system.md](./04-fishing-system.md)** - 釣魚系統設計文檔
+    - 拋竿迷你遊戲設計
+    - 誘魚迷你遊戲 (增強版)
+      - 五種魚餌類型與控制方式
+      - 魚影機制與誘魚階段
+      - 屬性整合系統
+    - 收線迷你遊戲
+      - 十種魚類掙扎樣式
+      - QTE反應控制系統
+    - 時間系統設計 (8個時間段)
+    - 天氣系統設計
+    - 地圖選擇與釣魚地點
+    - 魚類系統設計 (50種魚類)
+ 
+ 5. **[05-crafting-system.md](./05-crafting-system.md)** - 合成系統設計文檔
+    - 船隻工作坊合成系統
+    - 詳細合成配方系統 (60個配方)
+      - 釣竿系列 (15個配方)
+      - 魚餌系列 (15個配方)
+      - 船隻系列 (15個配方)
+      - 服裝系列 (15個配方)
+    - 合成系統機制設計
+    - 合成UI與使用者體驗
+    - 合成系統平衡與經濟整合
+ 
+ ### 待建立文檔
+ 
+ 6. **06-level-progression.md** - 等級進程系統設計文檔
+ 7. **07-inventory-equipment.md** - 庫存與裝備系統設計文檔
+ 8. **08-social-system.md** - 社交系統設計文檔
+ 9. **09-shop-system.md** - 商店系統設計文檔
+ 10. **10-achievement-system.md** - 成就系統設計文檔
+ 11. **11-economy-system.md** - 經濟系統設計文檔
+ 12. **12-art-audio.md** - 美術與音效系統設計文檔
+ 13. **13-technical-design.md** - 技術設計文檔
+ 
+ ## 🔗 系統整合關係圖
+ 
+ ```
+ 遊戲概覽 (01)
+     ↓
+ 核心循環 (02) ←→ 屬性系統 (03)
+     ↓              ↓
+ 釣魚系統 (04) ←→ 合成系統 (05)
+     ↓              ↓
+ 進程系統 (06) ←→ 庫存裝備 (07)
+     ↓              ↓
+ 社交系統 (08) ←→ 商店系統 (09)
+     ↓              ↓
+ 成就系統 (10) ←→ 經濟系統 (11)
+     ↓              ↓
+ 美術音效 (12) ←→ 技術設計 (13)
+ ```
+ 
+ ## 📋 使用指南
+ 
+ ### 設計師
+ - 查閱特定系統設計理念和平衡考量
+ - 了解系統間的整合關係
+ - 參考屬性計算公式和數值設定
+ 
+ ### 程式設計師
+ - 查看技術實現架構和相關檔案
+ - 了解資料結構和API設計
+ - 參考程式碼範例和計算邏輯
+ 
+ ### 產品經理
+ - 理解功能範圍和優先級
+ - 評估開發複雜度和時程
+ - 了解使用者體驗設計
+ 
+ ## 🎯 設計原則
+ 
+ ### 一致性原則
+ - 所有系統遵循相同的設計模式
+ - UI/UX 保持一致的風格
+ - 數值平衡採用統一標準
+ 
+ ### 擴展性原則
+ - 模組化設計便於功能擴展
+ - 資料驅動架構支援內容更新
+ - API設計考慮未來需求
+ 
+ ### 可維護性原則
+ - 詳細的技術文檔和程式碼註釋
+ - 清晰的檔案結構和命名規範
+ - 完整的測試覆蓋和錯誤處理
+ 
+ ## 📄 相關參考文檔
+ 
+ - **[project_description.md](../project_description.md)** - 專案檔案結構與功能說明
+ - **[Luxury_Angler_GDD(updated).txt](../Luxury_Angler_GDD(updated).txt)** - 遊戲設計總文檔
+ - **[.cursor/rules/fishing-game-structure.mdc](../.cursor/rules/fishing-game-structure.mdc)** - 專案結構規則
+ 
+ ## 🚀 更新說明
+ 
+ - **2025-01-XX**: 創建前5個系統設計文檔
+ - **待定**: 完成其餘8個系統設計文檔
+ - **待定**: 系統整合測試和平衡調整
+ 
+ ---
+ 
+ *注意：本文檔持續更新中，請定期查看最新版本。* 
```

---

我已經為您成功創建了前5個系統的詳細設計文檔，包括：

## 已完成的設計文檔：

1. **[01-game-overview.md](GameDesign/01-game-overview.md)** - 遊戲概覽系統
   - 涵蓋遊戲概念、目標受眾、平台技術、核心特色等

2. **[02-core-gameplay-loop.md](GameDesign/02-core-gameplay-loop.md)** - 核心遊戲循環
   - 詳細的遊戲流程圖、船隻選單、旅行系統、釣魚循環等

3. **[03-attributes-system.md](GameDesign/03-attributes-system.md)** - 屬性系統
   - 完整的34項玩家屬性和11項魚類屬性詳述

4. **[04-fishing-system.md](GameDesign/04-fishing-system.md)** - 釣魚系統
   - 三階段釣魚迷你遊戲、5種魚餌類型、10種掙扎樣式、時間天氣系統

5. **[05-crafting-system.md](GameDesign/05-crafting-system.md)** - 合成系統
   - 60個詳細合成配方、材料系統、升級路徑

6. **[README.md](GameDesign/README.md)** - 總覽目錄文件

## 特色亮點：

### 📋 詳盡程度
- 每個系統都有完整的技術實現細節
- 包含程式碼範例和計算公式
- 明確標註對應的檔案路徑

### 🔗 系統整合
- 清楚描述各系統間的相互關係
- 屬性如何影響遊戲機制
- 經濟循環和進程設計

### 🎯 實用性
- 提供具體的數值設定和平衡考量
- UI/UX設計指導
- 技術架構建議

要繼續建立其餘8個系統的設計文檔嗎？我可以接著創建：
- 等級進程系統
- 庫存與裝備系統  
- 社交系統
- 商店系統
- 成就系統
- 經濟系統
- 美術與音效系統
- 技術設計文檔

---

_**User**_

好的 繼續建立

---

_**Assistant**_



---

我將繼續建立下一個系統設計文檔。按照順序，接下來是等級進程系統設計文檔。

---

```diff
- 
+ # 6. 等級進程系統設計文檔
+ 
+ ## 6.1 等級系統總覽
+ 
+ ### 6.1.1 等級結構設計
+ 
+ 《Luxury Angler》採用50等級的進程系統，為玩家提供長期的成長目標和成就感。
+ 
+ #### 等級分段設計
+ **新手期 (1-10級)**:
+ - 學習基本釣魚機制
+ - 解鎖核心功能
+ - 快速等級提升體驗
+ - 引導式進程設計
+ 
+ **發展期 (11-25級)**:
+ - 掌握進階技巧
+ - 解鎖新地圖和內容
+ - 中等等級提升速度
+ - 策略選擇重要性增加
+ 
+ **成熟期 (26-40級)**:
+ - 精通各種釣魚技巧
+ - 解鎖高級裝備和配方
+ - 較慢等級提升速度
+ - 深度遊戲體驗
+ 
+ **大師期 (41-50級)**:
+ - 挑戰最困難內容
+ - 解鎖終極裝備
+ - 最慢等級提升速度
+ - 完美主義者目標
+ 
+ #### 等級需求曲線
+ ```javascript
+ // 等級經驗需求計算
+ const getRequiredXP = (level) => {
+     if (level <= 10) return 100 * level * level;           // 新手期：快速增長
+     if (level <= 25) return 1000 + 150 * (level - 10) * (level - 10);  // 發展期：中等增長
+     if (level <= 40) return 4000 + 200 * (level - 25) * (level - 25);  // 成熟期：較慢增長
+     return 8000 + 300 * (level - 40) * (level - 40);      // 大師期：最慢增長
+ };
+ ```
+ 
+ 技術實現：
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 等級進程核心邏輯
+ - [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 等級介面顯示
+ 
+ ### 6.1.2 經驗值獲得機制
+ 
+ #### 主要經驗值來源
+ 
+ **釣魚活動經驗值**:
+ - **成功釣魚**: 50-200 XP (基於魚類稀有度)
+ - **完美釣魚**: 額外 25% XP 加成
+ - **首次捕獲**: 額外 50% XP 加成
+ - **Boss魚類**: 500-1000 XP
+ 
+ **合成活動經驗值**:
+ - **基礎合成**: 20-50 XP
+ - **精英合成**: 100-200 XP  
+ - **大師合成**: 300-500 XP
+ - **首次製作**: 額外 30% XP 加成
+ 
+ **探索活動經驗值**:
+ - **發現新地點**: 100-300 XP
+ - **解鎖新地圖**: 500-1000 XP
+ - **完成地圖探索**: 200-600 XP
+ 
+ **社交活動經驗值**:
+ - **伴侶互動**: 10-30 XP
+ - **關係提升**: 100-500 XP
+ - **解鎖HCG**: 200-800 XP
+ 
+ #### 經驗值計算公式
+ ```javascript
+ // 釣魚經驗值計算
+ const fishingXP = baseFishXP * 
+     rarityMultiplier * 
+     (isFirstCatch ? 1.5 : 1.0) * 
+     (isPerfectCatch ? 1.25 : 1.0) * 
+     playerXPBonus;
+ 
+ // 合成經驗值計算  
+ const craftingXP = baseCraftingXP * 
+     itemTierMultiplier * 
+     (isFirstCraft ? 1.3 : 1.0) * 
+     craftingXPBonus;
+ ```
+ 
+ ### 6.1.3 等級獎勵系統
+ 
+ #### 每級固定獎勵
+ - **屬性點**: 每級獲得 3 點屬性分配點
+ - **金幣獎勵**: 等級 × 100 金幣
+ - **能量上限**: 每級 +2 點最大能量
+ 
+ #### 特殊等級里程碑
+ **等級5里程碑**:
+ - 解鎖第二個地圖
+ - 獲得Boss魚挑戰權限
+ - 解鎖基礎合成配方
+ 
+ **等級10里程碑**:
+ - 解鎖第三個地圖
+ - 解鎖精英合成配方
+ - 獲得第一位伴侶
+ 
+ **等級15里程碑**:
+ - 解鎖第四個地圖
+ - 解鎖船隻升級系統
+ - 獲得自動釣魚功能
+ 
+ **等級20里程碑**:
+ - 解鎖第五個地圖
+ - 解鎖大師合成配方
+ - 獲得第二位伴侶
+ 
+ **等級25里程碑**:
+ - 解鎖神秘海溝
+ - 解鎖傳奇裝備
+ - 獲得船隊管理系統
+ 
+ **等級30里程碑**:
+ - 解鎖PvP釣魚比賽
+ - 解鎖限定Boss魚
+ - 獲得第三位伴侶
+ 
+ **等級35里程碑**:
+ - 解鎖深海探險模式
+ - 解鎖神話級裝備
+ - 獲得天氣控制能力
+ 
+ **等級40里程碑**:
+ - 解鎖時空釣魚
+ - 解鎖終極Boss戰
+ - 獲得第四位伴侶
+ 
+ **等級45里程碑**:
+ - 解鎖創世級裝備
+ - 解鎖無限模式
+ - 獲得第五位伴侶
+ 
+ **等級50里程碑**:
+ - 獲得大師稱號
+ - 解鎖所有內容
+ - 獲得完美獎勵
+ 
+ ## 6.2 技能樹系統設計
+ 
+ ### 6.2.1 技能樹結構
+ 
+ 技能樹分為4個主要分支，每個分支專注不同的遊戲領域。
+ 
+ #### 釣魚專精分支 (Fishing Mastery)
+ **核心技能路線**:
+ 1. **精準拋竿** (等級1): 拋竿精準度 +10%
+ 2. **敏銳感知** (等級3): 魚類偵測 +15%  
+ 3. **完美時機** (等級5): QTE精準度 +20%
+ 4. **線控大師** (等級8): 張力穩定性 +25%
+ 5. **釣魚宗師** (等級12): 所有釣魚屬性 +10%
+ 
+ **分支技能**:
+ - **誘魚專家** (等級7): 咬餌率 +25%
+ - **收線高手** (等級10): 收線速度 +30%
+ - **稀有獵人** (等級15): 稀有魚機率 +20%
+ 
+ #### 合成專精分支 (Crafting Mastery)
+ **核心技能路線**:
+ 1. **基礎工藝** (等級2): 合成效率 +15%
+ 2. **材料學** (等級4): 材料節省機率 10%
+ 3. **精湛工藝** (等級7): 合成品質 +20%
+ 4. **大師工匠** (等級11): 合成失敗率 -50%
+ 5. **傳奇匠人** (等級16): 完美品質機率 +25%
+ 
+ **分支技能**:
+ - **快速製作** (等級6): 合成時間 -30%
+ - **資源回收** (等級9): 失敗時材料回收 75%
+ - **批量生產** (等級13): 同時進行3個合成
+ 
+ #### 探索專精分支 (Exploration Mastery)
+ **核心技能路線**:
+ 1. **航海知識** (等級1): 船隻速度 +15%
+ 2. **地理學** (等級3): 發現隱藏地點機率 +20%
+ 3. **天氣預測** (等級6): 提前3小時天氣預報
+ 4. **深海探險** (等級10): 解鎖深海專用地點
+ 5. **時空導航** (等級20): 時間旅行能力
+ 
+ **分支技能**:
+ - **能量管理** (等級4): 行動能量消耗 -25%
+ - **熱點感知** (等級8): 熱點持續時間 +50%
+ - **傳送門** (等級14): 瞬間傳送到已探索地點
+ 
+ #### 社交專精分支 (Social Mastery)
+ **核心技能路線**:
+ 1. **魅力** (等級1): 浪漫效果 +20%
+ 2. **交際能力** (等級3): 伴侶關係提升速度 +30%
+ 3. **領導力** (等級6): 同時攜帶伴侶 +1
+ 4. **心理學** (等級9): 伴侶特殊能力觸發率 +25%
+ 5. **完美伴侶** (等級18): 所有伴侶同時獲得最大加成
+ 
+ **分支技能**:
+ - **溝通專家** (等級5): 對話選項增加
+ - **禮物達人** (等級7): 禮物效果 +50%
+ - **關係大師** (等級12): 維持多重高級關係
+ 
+ ### 6.2.2 技能點分配系統
+ 
+ #### 技能點獲得
+ - **等級提升**: 每級獲得 1 技能點
+ - **成就完成**: 特定成就獎勵 1-3 技能點
+ - **里程碑獎勵**: 重要里程碑額外獎勵技能點
+ 
+ #### 技能重置機制
+ - **免費重置**: 等級15前可免費重置
+ - **付費重置**: 等級15後需要支付金幣
+ - **完全重置**: 消耗稀有材料重置所有技能
+ 
+ #### 技能前置需求
+ ```javascript
+ // 技能解鎖條件範例
+ const skillRequirements = {
+     "線控大師": {
+         level: 8,
+         prerequisite: ["精準拋竿", "完美時機"],
+         fishingXP: 1000
+     },
+     "大師工匠": {
+         level: 11,
+         prerequisite: ["材料學", "精湛工藝"],
+         craftingXP: 800
+     }
+ };
+ ```
+ 
+ 技術實現：
+ - [SkillTree.js](../src/scripts/SkillTree.js): 技能樹邏輯
+ - [SkillTreeUI.js](../src/ui/SkillTreeUI.js): 技能樹介面
+ 
+ ## 6.3 內容解鎖系統
+ 
+ ### 6.3.1 地圖解鎖順序
+ 
+ **等級基礎解鎖**:
+ - **等級1**: 珊瑚灣 (新手地圖)
+ - **等級5**: 深海深淵 (中級地圖)  
+ - **等級10**: 北極海域 (進階地圖)
+ - **等級15**: 火山島嶼 (高級地圖)
+ - **等級20**: 神秘海溝 (終極地圖)
+ 
+ **額外解鎖條件**:
+ - 完成前一地圖的所有Boss魚
+ - 達到指定的釣魚熟練度
+ - 擁有對應的裝備等級
+ 
+ ### 6.3.2 功能解鎖時程
+ 
+ **基礎功能 (1-10級)**:
+ - 等級1: 基礎釣魚、庫存管理
+ - 等級2: 合成系統
+ - 等級3: 商店系統
+ - 等級5: 社交系統
+ - 等級8: 成就系統
+ - 等級10: 自動釣魚
+ 
+ **進階功能 (11-25級)**:
+ - 等級12: 船隻升級
+ - 等級15: 競技模式
+ - 等級18: 公會系統
+ - 等級20: 拍賣行
+ - 等級22: 寵物系統
+ - 等級25: 飛行船隻
+ 
+ **高級功能 (26-50級)**:
+ - 等級30: PvP釣魚
+ - 等級35: 時空釣魚
+ - 等級40: 維度探索
+ - 等級45: 創造模式
+ - 等級50: 無限挑戰
+ 
+ ### 6.3.3 裝備解鎖機制
+ 
+ **釣竿解鎖等級**:
+ - 等級1: 竹製釣竿
+ - 等級5: 玻璃纖維釣竿
+ - 等級10: 碳纖維釣竿
+ - 等級15: 鋼製釣竿
+ - 等級20: 鈦合金釣竿
+ - 等級30: 神話釣竿
+ - 等級40: 傳奇釣竿
+ - 等級50: 創世釣竿
+ 
+ **船隻解鎖等級**:
+ - 等級1: 划艇
+ - 等級8: 小艇
+ - 等級15: 快艇
+ - 等級25: 遊艇
+ - 等級35: 豪華郵輪
+ - 等級45: 星際戰艦
+ 
+ ## 6.4 成就與里程碑系統
+ 
+ ### 6.4.1 成就分類
+ 
+ #### 釣魚成就
+ **捕獲成就**:
+ - "初次下鉤": 捕獲第一條魚
+ - "百魚大師": 捕獲100條不同魚類
+ - "稀有獵人": 捕獲10條稀有魚
+ - "Boss終結者": 擊敗所有Boss魚
+ - "完美釣手": 連續20次完美釣魚
+ 
+ **技巧成就**:
+ - "精準射手": 拋竿命中熱點100次
+ - "誘魚專家": 完成1000次誘魚挑戰
+ - "收線大師": 零失誤收線50次
+ 
+ #### 收集成就
+ **圖鑑成就**:
+ - "魚類學者": 完成25%魚類圖鑑
+ - "海洋專家": 完成50%魚類圖鑑
+ - "海洋之王": 完成100%魚類圖鑑
+ 
+ **裝備成就**:
+ - "初級工匠": 製作第一件裝備
+ - "裝備收藏家": 收集50件不同裝備
+ - "傳奇裝備師": 製作傳奇級裝備
+ 
+ #### 探索成就
+ **地圖成就**:
+ - "探險家": 發現所有地圖
+ - "熱點獵人": 發現所有熱點
+ - "秘境探索者": 發現隱藏地點
+ 
+ #### 社交成就
+ **關係成就**:
+ - "初戀": 第一次與伴侶達到親密關係
+ - "萬人迷": 與所有伴侶建立良好關係
+ - "完美戀人": 與一位伴侶達到最高關係等級
+ 
+ ### 6.4.2 里程碑獎勵系統
+ 
+ #### 獎勵類型
+ **屬性獎勵**:
+ - 永久屬性加成
+ - 特殊能力解鎖
+ - 獨特稱號獲得
+ 
+ **物質獎勵**:
+ - 稀有材料
+ - 限定裝備
+ - 大量金幣
+ 
+ **功能獎勵**:
+ - 新功能解鎖
+ - 介面自定義選項
+ - 特殊權限
+ 
+ #### 里程碑進度追蹤
+ ```javascript
+ // 里程碑進度檢查
+ const checkMilestone = (milestone) => {
+     const progress = player.getMilestoneProgress(milestone);
+     if (progress >= milestone.requirement) {
+         milestone.unlock();
+         player.addReward(milestone.rewards);
+         displayMilestoneNotification(milestone);
+     }
+ };
+ ```
+ 
+ 技術實現：
+ - [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
+ - [MilestoneManager.js](../src/scripts/MilestoneManager.js): 里程碑管理
+ 
+ ## 6.5 等級平衡與設計考量
+ 
+ ### 6.5.1 等級曲線設計
+ 
+ #### 早期遊戲 (1-15級)
+ **設計目標**:
+ - 快速獲得成就感
+ - 學習遊戲機制
+ - 建立遊戲習慣
+ 
+ **平衡要點**:
+ - 經驗需求低，升級快速
+ - 頻繁的新內容解鎖
+ - 寬鬆的失敗懲罰
+ 
+ #### 中期遊戲 (16-35級)
+ **設計目標**:
+ - 深化策略思考
+ - 建立長期目標
+ - 培養專精領域
+ 
+ **平衡要點**:
+ - 適中的經驗需求
+ - 有意義的選擇
+ - 技能特化重要性
+ 
+ #### 後期遊戲 (36-50級)
+ **設計目標**:
+ - 挑戰硬核玩家
+ - 完美主義追求
+ - 社區地位象徵
+ 
+ **平衡要點**:
+ - 高經驗需求
+ - 極具挑戰性內容
+ - 精確的操作要求
+ 
+ ### 6.5.2 進程驅動設計
+ 
+ #### 短期目標 (每次遊戲session)
+ - 完成日常釣魚目標
+ - 推進當前等級經驗
+ - 完成特定成就
+ 
+ #### 中期目標 (每週)
+ - 達到下一個里程碑
+ - 解鎖新內容
+ - 提升技能分支
+ 
+ #### 長期目標 (每月)
+ - 達到新的等級段落
+ - 完成重大成就
+ - 精通特定系統
+ 
+ ### 6.5.3 防止等級通膨設計
+ 
+ #### 經驗來源限制
+ - 每日活動經驗上限
+ - 重複活動遞減獎勵
+ - 高級內容門檻要求
+ 
+ #### 內容深度確保
+ - 每個等級段有意義的新內容
+ - 避免純數值提升
+ - 質量導向的進程設計
+ 
+ ## 6.6 等級系統與其他系統整合
+ 
+ ### 6.6.1 與屬性系統整合
+ - 等級提升自動增加基礎屬性
+ - 技能點分配影響專精屬性
+ - 里程碑獎勵提供獨特屬性加成
+ 
+ ### 6.6.2 與釣魚系統整合
+ - 等級解鎖新釣魚地點
+ - 技能影響釣魚機制表現
+ - 高級內容需要對應等級
+ 
+ ### 6.6.3 與合成系統整合
+ - 等級解鎖新合成配方
+ - 技能提供合成加成
+ - 高級裝備需要等級前置
+ 
+ ### 6.6.4 與社交系統整合
+ - 等級影響伴侶解鎖
+ - 社交技能提升關係效率
+ - 高等級解鎖特殊互動
+ 
+ ### 6.6.5 與經濟系統整合
+ - 等級獎勵提供金幣收入
+ - 高等級解鎖昂貴內容
+ - 技能影響經濟效率
+ 
+ 相關檔案：
+ - [PlayerProgression.js](../src/scripts/PlayerProgression.js): 等級進程核心
+ - [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 等級介面
+ - [SkillTree.js](../src/scripts/SkillTree.js): 技能樹系統
+ - [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
+ - [MilestoneManager.js](../src/scripts/MilestoneManager.js): 里程碑管理
+ - [GameState.js](../src/scripts/GameState.js): 進程狀態管理 
```

---

現在繼續建立下一個系統設計文檔：

---

```diff
- 
+ # 7. 庫存與裝備系統設計文檔
+ 
+ ## 7.1 庫存系統總覽
+ 
+ ### 7.1.1 庫存架構設計
+ 
+ 《Luxury Angler》的庫存系統採用多分類管理架構，將不同類型的物品分別儲存和管理。
+ 
+ #### 庫存分類結構
+ **魚類收藏區 (Fish Collection)**:
+ - **魚缸系統**: 當前釣魚 session 的暫存區域
+ - **魚類圖鑑**: 已收集魚類的永久記錄
+ - **魚卡庫存**: 用於合成的魚卡材料儲存
+ 
+ **裝備欄位 (Equipment Slots)**:
+ - **釣竿欄位**: 當前裝備的釣竿
+ - **魚餌欄位**: 當前使用的魚餌
+ - **船隻欄位**: 當前的船隻
+ - **服裝欄位**: 當前穿著的服裝套裝
+ 
+ **道具庫存 (Item Inventory)**:
+ - **合成材料**: 用於製作的各種材料
+ - **消耗品**: 臨時增益道具
+ - **禮物道具**: 用於社交互動的禮品
+ 
+ **特殊收藏 (Special Collections)**:
+ - **成就獎品**: 成就解鎖的特殊物品
+ - **限定道具**: 活動或里程碑獲得的獨特物品
+ - **紀念品**: 探索和冒險的收集品
+ 
+ #### 容量管理系統
+ **動態容量設計**:
+ ```javascript
+ // 庫存容量計算
+ const inventoryCapacity = {
+     fishTank: baseCapacity + boatStorage + skillBonus,
+     equipment: fixedSlots + unlockedSlots,
+     items: baseItemSlots + bagUpgrades,
+     special: unlimitedStorage
+ };
+ ```
+ 
+ **容量擴展方式**:
+ - **船隻升級**: 增加魚缸和物品儲存空間
+ - **技能投資**: 庫存管理技能提供額外容量
+ - **背包購買**: 商店購買的儲存擴展
+ - **成就獎勵**: 特定成就解鎖額外空間
+ 
+ 技術實現：
+ - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
+ - [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面系統
+ 
+ ### 7.1.2 魚缸系統詳細設計
+ 
+ #### 魚缸機制核心
+ **基本功能**:
+ - 暫存當次釣魚 session 捕獲的魚類
+ - 提供魚類狀態保持功能
+ - 支援魚類品質顯示和分類
+ 
+ **容量限制機制**:
+ - **基礎容量**: 10條魚 (1級船隻)
+ - **擴展容量**: 最高50條魚 (頂級船隻)
+ - **滿載處理**: 魚缸滿載時必須返回港口出售
+ 
+ #### 魚缸狀態管理
+ **魚類狀態指標**:
+ - **新鮮度**: 影響出售價格 (100% → 80% 隨時間遞減)
+ - **品質等級**: 完美、優良、普通、劣質
+ - **稀有度標記**: 1-10星稀有度視覺顯示
+ - **首次捕獲**: 特殊標記和獎勵加成
+ 
+ **新鮮度系統**:
+ ```javascript
+ // 新鮮度衰減計算
+ const freshnessDecay = (fish, timeElapsed) => {
+     const decayRate = 0.02; // 每小時衰減2%
+     const preservationBonus = boat.preservationSystem || 0;
+     const finalFreshness = Math.max(
+         0.8, // 最低保持80%新鮮度
+         fish.freshness - (timeElapsed * decayRate * (1 - preservationBonus))
+     );
+     return finalFreshness;
+ };
+ ```
+ 
+ #### 魚缸管理介面
+ **視覺設計元素**:
+ - 2D魚缸視圖，魚類在水中游動
+ - 容量指示器 (目前數量/最大容量)
+ - 魚類縮圖和詳細資訊
+ - 新鮮度和品質標記
+ 
+ **互動功能**:
+ - 點選魚類查看詳細資訊
+ - 拖拽整理魚類順序
+ - 批量選擇和處理
+ - 快速出售和保留選項
+ 
+ ### 7.1.3 物品分類與管理
+ 
+ #### 合成材料管理
+ **魚卡分類系統**:
+ - **1星魚卡**: 基礎材料，大量儲存
+ - **2星魚卡**: 常用材料，中等數量
+ - **3星魚卡**: 優質材料，有限儲存
+ - **4星魚卡**: 稀有材料，精心保管
+ - **5星魚卡**: 傳說材料，極珍貴
+ 
+ **材料堆疊規則**:
+ - 同種魚卡自動堆疊 (最多99張)
+ - 不同星級分別計算
+ - 特殊魚卡 (Boss魚) 單獨顯示
+ 
+ #### 消耗品系統
+ **增益道具類型**:
+ - **經驗增益藥水**: +50% 經驗獲得 (1小時)
+ - **幸運護符**: +25% 稀有魚機率 (30分鐘)
+ - **能量飲料**: 恢復50點能量
+ - **完美魚餌**: 下次釣魚100%成功率
+ - **時間加速器**: 合成時間減半
+ 
+ **道具使用機制**:
+ - 即時使用或預約使用
+ - 效果堆疊限制 (同類型不可疊加)
+ - 持續時間顯示和提醒
+ - 自動使用功能 (高級功能)
+ 
+ 技術實現：
+ - [ItemManager.js](../src/scripts/ItemManager.js): 物品管理
+ - [ConsumableEffects.js](../src/scripts/ConsumableEffects.js): 消耗品效果
+ 
+ ## 7.2 裝備系統設計
+ 
+ ### 7.2.1 裝備欄位架構
+ 
+ #### 主要裝備欄位
+ **釣竿欄位**:
+ - **功能**: 核心釣魚工具
+ - **屬性影響**: 拋竿精準度、張力穩定性、稀有魚機率
+ - **升級路徑**: 基礎 → 精英 → 大師級
+ - **特殊效果**: 某些釣竿有獨特被動技能
+ 
+ **魚餌欄位**:
+ - **功能**: 誘魚階段核心道具
+ - **類型區分**: 5種魚餌類型，各有特色
+ - **屬性影響**: 咬餌率、誘魚成功率、魚餌耐久性
+ - **消耗機制**: 使用後有機率損壞或消失
+ 
+ **船隻欄位**:
+ - **功能**: 整體遊戲體驗提升
+ - **屬性影響**: 船隻速度、合成效率、儲存容量
+ - **特殊功能**: 自動釣魚、伴侶槽、特殊能力
+ - **升級機制**: 透過合成系統獲得更好船隻
+ 
+ **服裝欄位**:
+ - **頭部裝備**: 帽子、頭飾 (能量、偵測加成)
+ - **身體裝備**: 上衣、背心 (能量、儲存加成)
+ - **腿部裝備**: 褲子、裙子 (移動、社交加成)
+ - **足部裝備**: 鞋子、涼鞋 (速度、舒適度)
+ - **飾品欄位**: 太陽眼鏡、手錶 (特殊效果)
+ 
+ #### 裝備切換系統
+ **快速切換功能**:
+ - 保存多套裝備組合
+ - 一鍵切換到預設配置
+ - 情境推薦系統 (釣魚、探索、社交)
+ 
+ **裝備預設組合**:
+ ```javascript
+ // 裝備預設範例
+ const equipmentPresets = {
+     "釣魚專精": {
+         rod: "大師鈦合金釣竿",
+         lure: "大師旋轉餌",
+         boat: "精英遊艇",
+         clothing: "釣魚專業套裝"
+     },
+     "探索模式": {
+         rod: "輕量碳纖維釣竿", 
+         boat: "精英快艇",
+         clothing: "探險者套裝"
+     },
+     "社交活動": {
+         boat: "豪華郵輪",
+         clothing: "時尚比基尼套裝"
+     }
+ };
+ ```
+ 
+ ### 7.2.2 裝備屬性系統
+ 
+ #### 屬性計算機制
+ **屬性疊加邏輯**:
+ ```javascript
+ // 最終屬性計算
+ const calculateFinalStats = (player, equipment) => {
+     let stats = {...player.baseStats};
+     
+     // 裝備屬性疊加
+     equipment.forEach(item => {
+         if (item.statBonus) {
+             Object.keys(item.statBonus).forEach(stat => {
+                 stats[stat] += item.statBonus[stat];
+             });
+         }
+     });
+     
+     // 套裝效果
+     const setBonus = calculateSetBonus(equipment);
+     return applySetBonus(stats, setBonus);
+ };
+ ```
+ 
+ #### 套裝效果系統
+ **套裝設計理念**:
+ - 2件套：小型屬性加成
+ - 3件套：中型屬性加成 + 特殊效果
+ - 4件套：大型屬性加成 + 強力特殊效果
+ - 5件套：最強屬性加成 + 獨特能力
+ 
+ **範例套裝效果**:
+ - **釣魚大師套裝** (4件): 所有釣魚屬性 +15%, 完美釣魚機率 +20%
+ - **探險家套裝** (3件): 移動速度 +25%, 發現隱藏地點機率 +30%
+ - **社交名媛套裝** (5件): 所有社交屬性 +20%, 同時與所有伴侶互動
+ 
+ ### 7.2.3 裝備強化系統
+ 
+ #### 強化機制設計
+ **強化等級**:
+ - 每件裝備可強化至 +10
+ - 強化成功率隨等級遞減
+ - 失敗可能降級或損壞裝備
+ 
+ **強化材料需求**:
+ - **基礎強化石** (+1 到 +3): 基礎魚卡製作
+ - **中級強化石** (+4 到 +6): 稀有魚卡製作  
+ - **高級強化石** (+7 到 +9): Boss魚卡製作
+ - **完美強化石** (+10): 傳說魚卡製作
+ 
+ #### 強化成功率表
+ ```javascript
+ // 強化成功率計算
+ const enhancementRates = {
+     1: 0.95,  // +1: 95%
+     2: 0.90,  // +2: 90%
+     3: 0.85,  // +3: 85%
+     4: 0.75,  // +4: 75%
+     5: 0.65,  // +5: 65%
+     6: 0.55,  // +6: 55%
+     7: 0.40,  // +7: 40%
+     8: 0.25,  // +8: 25%
+     9: 0.15,  // +9: 15%
+     10: 0.05  // +10: 5%
+ };
+ ```
+ 
+ #### 強化保護機制
+ **安全強化**:
+ - +1 到 +3：100% 安全，失敗不損壞
+ - +4 到 +6：失敗降級1，但不損壞
+ - +7 到 +10：失敗可能損壞裝備
+ 
+ **保護道具**:
+ - **保護符**: 防止裝備損壞
+ - **幸運石**: 提高成功率 +10%
+ - **完美催化劑**: 100% 成功，限量獲得
+ 
+ 技術實現：
+ - [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化
+ - [EquipmentEnhancementUI.js](../src/ui/EquipmentEnhancementUI.js): 強化介面
+ 
+ ## 7.3 庫存管理功能
+ 
+ ### 7.3.1 物品整理系統
+ 
+ #### 自動整理功能
+ **智能分類**:
+ - 同類物品自動群組
+ - 稀有度由高到低排列
+ - 新獲得物品優先顯示
+ - 收藏物品固定位置
+ 
+ **整理規則設定**:
+ ```javascript
+ // 物品排序規則
+ const sortingRules = {
+     byRarity: (a, b) => b.rarity - a.rarity,
+     byType: (a, b) => a.type.localeCompare(b.type),
+     byValue: (a, b) => b.value - a.value,
+     byQuantity: (a, b) => b.quantity - a.quantity,
+     custom: (a, b) => player.customSortFunction(a, b)
+ };
+ ```
+ 
+ #### 搜尋與篩選
+ **搜尋功能**:
+ - 物品名稱模糊搜尋
+ - 屬性關鍵字搜尋
+ - 稀有度範圍篩選
+ - 物品類型篩選
+ 
+ **進階篩選**:
+ - 可用於合成的材料
+ - 符合當前等級的裝備
+ - 有套裝效果的物品
+ - 可強化的裝備
+ 
+ ### 7.3.2 物品比較系統
+ 
+ #### 裝備比較介面
+ **比較功能**:
+ - 並排顯示裝備屬性
+ - 數值差異用顏色標示
+ - 綜合戰力評估
+ - 套裝效果影響分析
+ 
+ **比較指標**:
+ - 基礎屬性對比
+ - 特殊效果差異
+ - 適用場景推薦
+ - 升級潛力評估
+ 
+ #### 價值評估系統
+ **物品價值計算**:
+ ```javascript
+ // 物品價值評估
+ const calculateItemValue = (item) => {
+     let value = item.baseValue;
+     
+     // 稀有度加成
+     value *= (1 + item.rarity * 0.2);
+     
+     // 強化等級加成
+     value *= (1 + item.enhanceLevel * 0.1);
+     
+     // 市場需求調整
+     value *= marketDemand[item.type] || 1.0;
+     
+     return Math.floor(value);
+ };
+ ```
+ 
+ ### 7.3.3 批量操作功能
+ 
+ #### 批量處理操作
+ **多選操作**:
+ - 批量出售低價值物品
+ - 批量使用同類消耗品
+ - 批量分解不需要的裝備
+ - 批量移動到特定分類
+ 
+ **智能建議**:
+ - 建議出售的過時裝備
+ - 推薦保留的稀有材料
+ - 提醒即將過期的消耗品
+ - 標記可合成的材料組合
+ 
+ #### 庫存優化建議
+ **空間管理提醒**:
+ - 庫存接近滿載警告
+ - 建議釋放空間的方法
+ - 推薦購買的擴容方案
+ - 分析庫存使用效率
+ 
+ 技術實現：
+ - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
+ - [ItemSorter.js](../src/scripts/ItemSorter.js): 物品排序
+ - [ItemComparator.js](../src/scripts/ItemComparator.js): 物品比較
+ 
+ ## 7.4 特殊庫存功能
+ 
+ ### 7.4.1 魚類圖鑑系統
+ 
+ #### 圖鑑收集機制
+ **收集進度追蹤**:
+ - 50種魚類完整記錄
+ - 捕獲次數統計
+ - 最大尺寸記錄
+ - 捕獲地點和時間
+ - 使用魚餌類型記錄
+ 
+ **圖鑑獎勵系統**:
+ - 收集里程碑獎勵
+ - 完成度成就解鎖
+ - 特殊稱號獲得
+ - 隱藏內容解鎖
+ 
+ #### 圖鑑展示功能
+ **詳細資訊展示**:
+ - 3D魚類模型展示
+ - 詳細屬性和行為描述
+ - 棲息地和習性介紹
+ - 捕獲技巧和建議
+ - 歷史捕獲記錄
+ 
+ **成就展示**:
+ - 首次捕獲時間戳
+ - 個人最佳記錄
+ - 稀有度排名
+ - 收集完成度徽章
+ 
+ ### 7.4.2 紀念品收藏系統
+ 
+ #### 紀念品類型
+ **探索紀念品**:
+ - 地圖探索紀念幣
+ - 特殊地點發現證書
+ - 第一次到達記錄
+ - 隱藏秘境紀念品
+ 
+ **成就紀念品**:
+ - 重要里程碑獎盃
+ - 特殊技藝證書
+ - 競賽參與證明
+ - 稀有魚類捕獲證書
+ 
+ **社交紀念品**:
+ - 伴侶關係紀念品
+ - 特殊事件參與證明
+ - 社交里程碑紀念
+ - 限時活動獎品
+ 
+ #### 紀念品展示系統
+ **個人博物館**:
+ - 3D展示空間
+ - 可自定義擺放位置
+ - 紀念品故事介紹
+ - 訪客觀賞功能
+ 
+ ### 7.4.3 限定收藏系統
+ 
+ #### 限定物品管理
+ **活動限定物品**:
+ - 季節活動專屬道具
+ - 節日慶典紀念品
+ - 限時挑戰獎勵
+ - 開發者特別贈品
+ 
+ **收藏價值系統**:
+ ```javascript
+ // 限定物品價值評估
+ const limitedItemValue = (item) => {
+     const baseValue = item.originalValue;
+     const timeMultiplier = calculateTimeScarcity(item.obtainDate);
+     const rarityMultiplier = item.isEventExclusive ? 3.0 : 1.5;
+     
+     return baseValue * timeMultiplier * rarityMultiplier;
+ };
+ ```
+ 
+ 技術實現：
+ - [CollectionManager.js](../src/scripts/CollectionManager.js): 收藏管理
+ - [MuseumDisplay.js](../src/scripts/MuseumDisplay.js): 博物館展示
+ - [LimitedItemTracker.js](../src/scripts/LimitedItemTracker.js): 限定物品追蹤
+ 
+ ## 7.5 庫存與其他系統整合
+ 
+ ### 7.5.1 與合成系統整合
+ 
+ #### 材料需求預覽
+ **合成材料檢查**:
+ - 實時檢查所需材料庫存
+ - 缺少材料高亮顯示
+ - 推薦獲得途徑提示
+ - 預估合成成本計算
+ 
+ **材料預留功能**:
+ - 為特定配方預留材料
+ - 防止意外使用或出售
+ - 批量預留多個配方材料
+ - 預留狀態視覺提示
+ 
+ ### 7.5.2 與經濟系統整合
+ 
+ #### 價值評估與出售
+ **智能定價系統**:
+ - 基於市場供需的動態定價
+ - 考慮物品稀有度和需求
+ - 時間因素影響價格波動
+ - 批量出售折扣機制
+ 
+ **出售建議系統**:
+ - 推薦出售時機
+ - 價格趨勢預測
+ - 庫存過剩警告
+ - 利潤最大化建議
+ 
+ ### 7.5.3 與進程系統整合
+ 
+ #### 等級解鎖內容
+ **庫存擴展**:
+ - 等級提升自動增加基礎容量
+ - 技能投資解鎖特殊功能
+ - 成就獎勵額外儲存空間
+ - VIP功能高級庫存管理
+ 
+ **功能進程解鎖**:
+ - 等級1: 基礎庫存功能
+ - 等級5: 自動整理功能
+ - 等級10: 批量操作功能
+ - 等級15: 智能建議系統
+ - 等級20: 高級搜尋篩選
+ - 等級25: 個人博物館
+ - 等級30: 市場價值分析
+ - 等級35: 自動庫存優化
+ - 等級40: 預測性庫存管理
+ - 等級50: 完全自訂化庫存
+ 
+ ### 7.5.4 與社交系統整合
+ 
+ #### 物品分享與贈送
+ **社交功能**:
+ - 向伴侶贈送禮物
+ - 展示稀有收藏品
+ - 交換特殊材料
+ - 共享庫存資源 (高級功能)
+ 
+ **競爭元素**:
+ - 收藏品排行榜
+ - 稀有物品炫耀系統
+ - 收集成就比較
+ - 限定物品珍藏度
+ 
+ 相關檔案：
+ - [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
+ - [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面
+ - [EquipmentManager.js](../src/scripts/EquipmentManager.js): 裝備管理
+ - [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化
+ - [ItemManager.js](../src/scripts/ItemManager.js): 物品管理
+ - [CollectionManager.js](../src/scripts/CollectionManager.js): 收藏管理
+ - [GameState.js](../src/scripts/GameState.js): 庫存狀態管理 
```

---

現在更新README.md文檔，將新完成的文檔加入列表：

---

```diff
     - 合成系統平衡與經濟整合
  
+ 6. **[06-level-progression.md](./06-level-progression.md)** - 等級進程系統設計文檔
+    - 等級系統總覽 (50等級結構)
+    - 經驗值獲得機制
+    - 等級獎勵與里程碑系統
+    - 技能樹系統設計 (4個專精分支)
+    - 內容解鎖系統
+    - 成就與里程碑系統
+    - 等級平衡與設計考量
+    - 與其他系統整合
+ 
+ 7. **[07-inventory-equipment.md](./07-inventory-equipment.md)** - 庫存與裝備系統設計文檔
+    - 庫存系統總覽 (多分類管理)
+    - 魚缸系統詳細設計
+    - 物品分類與管理
+    - 裝備系統設計 (裝備欄位、屬性、強化)
+    - 庫存管理功能 (整理、比較、批量操作)
+    - 特殊庫存功能 (圖鑑、紀念品、限定收藏)
+    - 與其他系統整合
+ 
  ### 待建立文檔
  
- 6. **06-level-progression.md** - 等級進程系統設計文檔
- 7. **07-inventory-equipment.md** - 庫存與裝備系統設計文檔
  8. **08-social-system.md** - 社交系統設計文檔
  9. **09-shop-system.md** - 商店系統設計文檔
```

```diff
  ## 🚀 更新說明
  
- - **2025-01-XX**: 創建前5個系統設計文檔
- - **待定**: 完成其餘8個系統設計文檔
+ - **2025-01-XX**: 創建前7個系統設計文檔
+   - 完成核心系統：遊戲概覽、循環、屬性、釣魚、合成
+   - 完成進程系統：等級、庫存裝備
+ - **待定**: 完成其餘6個系統設計文檔
  - **待定**: 系統整合測試和平衡調整
  
```

---

完成！我已經成功建立了2個新的系統設計文檔：

## 📋 新完成的文檔

### **06-level-progression.md** - 等級進程系統設計文檔
詳細內容包括：
- **50等級系統結構**：新手期(1-10)、發展期(11-25)、成熟期(26-40)、大師期(41-50)
- **經驗值機制**：釣魚、合成、探索、社交等多種獲得途徑
- **技能樹系統**：4個專精分支(釣魚、合成、探索、社交)
- **內容解鎖系統**：地圖、功能、裝備的等級解鎖
- **成就與里程碑**：詳細的獎勵和進程追蹤
- **平衡設計**：防止等級通膨的機制

### **07-inventory-equipment.md** - 庫存與裝備系統設計文檔
詳細內容包括：
- **庫存架構**：魚類收藏、裝備欄位、道具庫存、特殊收藏
- **魚缸系統**：容量管理、新鮮度機制、狀態追蹤
- **裝備系統**：欄位設計、屬性計算、套裝效果、強化機制
- **庫存管理**：自動整理、搜尋篩選、批量操作
- **特殊功能**：魚類圖鑑、紀念品收藏、限定物品管理
- **系統整合**：與合成、經濟、進程、社交系統的深度整合

## 📊 目前進度
- ✅ **已完成**：7個系統設計文檔 (共13個)
- 🔄 **剩餘**：6個系統設計文檔
- 📈 **完成率**：54% (7/13)

下一步將繼續建立社交系統設計文檔。每個文檔都遵循統一的結構和風格，包含詳細的技術實現說明、程式碼範例、檔案路徑引用，以及與其他系統的整合關係。

是否需要我繼續建立下一個系統設計文檔？

---

_**User**_

好的 繼續

---

_**Assistant**_



---

