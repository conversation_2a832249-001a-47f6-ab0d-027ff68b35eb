// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/Shop.proto

package ehooray.fishing.dto.proto.model;

public final class ShopProto {
  private ShopProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ShopOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.Shop)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>int32 commodityCsvId = 3;</code>
     * @return The commodityCsvId.
     */
    int getCommodityCsvId();

    /**
     * <code>int32 buyCount = 4;</code>
     * @return The buyCount.
     */
    int getBuyCount();

    /**
     * <code>int32 questState = 5;</code>
     * @return The questState.
     */
    int getQuestState();

    /**
     * <code>int64 questFinishTime = 6;</code>
     * @return The questFinishTime.
     */
    long getQuestFinishTime();

    /**
     * <code>int64 characterEntityId = 7;</code>
     * @return The characterEntityId.
     */
    long getCharacterEntityId();

    /**
     * <code>int64 nextStartTime = 8;</code>
     * @return The nextStartTime.
     */
    long getNextStartTime();

    /**
     * <code>int64 startTime = 9;</code>
     * @return The startTime.
     */
    long getStartTime();
  }
  /**
   * Protobuf type {@code model.Shop}
   */
  public static final class Shop extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.Shop)
      ShopOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Shop.newBuilder() to construct.
    private Shop(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Shop() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Shop();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.ShopProto.internal_static_model_Shop_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.ShopProto.internal_static_model_Shop_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.ShopProto.Shop.class, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int COMMODITYCSVID_FIELD_NUMBER = 3;
    private int commodityCsvId_;
    /**
     * <code>int32 commodityCsvId = 3;</code>
     * @return The commodityCsvId.
     */
    @java.lang.Override
    public int getCommodityCsvId() {
      return commodityCsvId_;
    }

    public static final int BUYCOUNT_FIELD_NUMBER = 4;
    private int buyCount_;
    /**
     * <code>int32 buyCount = 4;</code>
     * @return The buyCount.
     */
    @java.lang.Override
    public int getBuyCount() {
      return buyCount_;
    }

    public static final int QUESTSTATE_FIELD_NUMBER = 5;
    private int questState_;
    /**
     * <code>int32 questState = 5;</code>
     * @return The questState.
     */
    @java.lang.Override
    public int getQuestState() {
      return questState_;
    }

    public static final int QUESTFINISHTIME_FIELD_NUMBER = 6;
    private long questFinishTime_;
    /**
     * <code>int64 questFinishTime = 6;</code>
     * @return The questFinishTime.
     */
    @java.lang.Override
    public long getQuestFinishTime() {
      return questFinishTime_;
    }

    public static final int CHARACTERENTITYID_FIELD_NUMBER = 7;
    private long characterEntityId_;
    /**
     * <code>int64 characterEntityId = 7;</code>
     * @return The characterEntityId.
     */
    @java.lang.Override
    public long getCharacterEntityId() {
      return characterEntityId_;
    }

    public static final int NEXTSTARTTIME_FIELD_NUMBER = 8;
    private long nextStartTime_;
    /**
     * <code>int64 nextStartTime = 8;</code>
     * @return The nextStartTime.
     */
    @java.lang.Override
    public long getNextStartTime() {
      return nextStartTime_;
    }

    public static final int STARTTIME_FIELD_NUMBER = 9;
    private long startTime_;
    /**
     * <code>int64 startTime = 9;</code>
     * @return The startTime.
     */
    @java.lang.Override
    public long getStartTime() {
      return startTime_;
    }

    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.ShopProto.Shop parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.ShopProto.Shop prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.Shop}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.Shop)
        ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.ShopProto.internal_static_model_Shop_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.ShopProto.internal_static_model_Shop_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.ShopProto.Shop.class, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.ShopProto.Shop.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        csvId_ = 0;

        commodityCsvId_ = 0;

        buyCount_ = 0;

        questState_ = 0;

        questFinishTime_ = 0L;

        characterEntityId_ = 0L;

        nextStartTime_ = 0L;

        startTime_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.ShopProto.internal_static_model_Shop_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.ShopProto.Shop getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.ShopProto.Shop build() {
        ehooray.fishing.dto.proto.model.ShopProto.Shop result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.ShopProto.Shop buildPartial() {
        ehooray.fishing.dto.proto.model.ShopProto.Shop result = new ehooray.fishing.dto.proto.model.ShopProto.Shop(this);
        result.entityId_ = entityId_;
        result.csvId_ = csvId_;
        result.commodityCsvId_ = commodityCsvId_;
        result.buyCount_ = buyCount_;
        result.questState_ = questState_;
        result.questFinishTime_ = questFinishTime_;
        result.characterEntityId_ = characterEntityId_;
        result.nextStartTime_ = nextStartTime_;
        result.startTime_ = startTime_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private int commodityCsvId_ ;
      /**
       * <code>int32 commodityCsvId = 3;</code>
       * @return The commodityCsvId.
       */
      @java.lang.Override
      public int getCommodityCsvId() {
        return commodityCsvId_;
      }
      /**
       * <code>int32 commodityCsvId = 3;</code>
       * @param value The commodityCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setCommodityCsvId(int value) {
        
        commodityCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 commodityCsvId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommodityCsvId() {
        
        commodityCsvId_ = 0;
        onChanged();
        return this;
      }

      private int buyCount_ ;
      /**
       * <code>int32 buyCount = 4;</code>
       * @return The buyCount.
       */
      @java.lang.Override
      public int getBuyCount() {
        return buyCount_;
      }
      /**
       * <code>int32 buyCount = 4;</code>
       * @param value The buyCount to set.
       * @return This builder for chaining.
       */
      public Builder setBuyCount(int value) {
        
        buyCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 buyCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyCount() {
        
        buyCount_ = 0;
        onChanged();
        return this;
      }

      private int questState_ ;
      /**
       * <code>int32 questState = 5;</code>
       * @return The questState.
       */
      @java.lang.Override
      public int getQuestState() {
        return questState_;
      }
      /**
       * <code>int32 questState = 5;</code>
       * @param value The questState to set.
       * @return This builder for chaining.
       */
      public Builder setQuestState(int value) {
        
        questState_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 questState = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestState() {
        
        questState_ = 0;
        onChanged();
        return this;
      }

      private long questFinishTime_ ;
      /**
       * <code>int64 questFinishTime = 6;</code>
       * @return The questFinishTime.
       */
      @java.lang.Override
      public long getQuestFinishTime() {
        return questFinishTime_;
      }
      /**
       * <code>int64 questFinishTime = 6;</code>
       * @param value The questFinishTime to set.
       * @return This builder for chaining.
       */
      public Builder setQuestFinishTime(long value) {
        
        questFinishTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 questFinishTime = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestFinishTime() {
        
        questFinishTime_ = 0L;
        onChanged();
        return this;
      }

      private long characterEntityId_ ;
      /**
       * <code>int64 characterEntityId = 7;</code>
       * @return The characterEntityId.
       */
      @java.lang.Override
      public long getCharacterEntityId() {
        return characterEntityId_;
      }
      /**
       * <code>int64 characterEntityId = 7;</code>
       * @param value The characterEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setCharacterEntityId(long value) {
        
        characterEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 characterEntityId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharacterEntityId() {
        
        characterEntityId_ = 0L;
        onChanged();
        return this;
      }

      private long nextStartTime_ ;
      /**
       * <code>int64 nextStartTime = 8;</code>
       * @return The nextStartTime.
       */
      @java.lang.Override
      public long getNextStartTime() {
        return nextStartTime_;
      }
      /**
       * <code>int64 nextStartTime = 8;</code>
       * @param value The nextStartTime to set.
       * @return This builder for chaining.
       */
      public Builder setNextStartTime(long value) {
        
        nextStartTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 nextStartTime = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextStartTime() {
        
        nextStartTime_ = 0L;
        onChanged();
        return this;
      }

      private long startTime_ ;
      /**
       * <code>int64 startTime = 9;</code>
       * @return The startTime.
       */
      @java.lang.Override
      public long getStartTime() {
        return startTime_;
      }
      /**
       * <code>int64 startTime = 9;</code>
       * @param value The startTime to set.
       * @return This builder for chaining.
       */
      public Builder setStartTime(long value) {
        
        startTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 startTime = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTime() {
        
        startTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.Shop)
    }

    // @@protoc_insertion_point(class_scope:model.Shop)
    private static final ehooray.fishing.dto.proto.model.ShopProto.Shop DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.ShopProto.Shop();
    }

    public static ehooray.fishing.dto.proto.model.ShopProto.Shop getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Shop>
        PARSER = new com.google.protobuf.AbstractParser<Shop>() {
      @java.lang.Override
      public Shop parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Shop> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Shop> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ShopProto.Shop getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Shop_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Shop_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020model/Shop.proto\022\005model\"\303\001\n\004Shop\022\020\n\010en" +
      "tityId\030\001 \001(\003\022\r\n\005csvId\030\002 \001(\005\022\026\n\016commodity" +
      "CsvId\030\003 \001(\005\022\020\n\010buyCount\030\004 \001(\005\022\022\n\nquestSt" +
      "ate\030\005 \001(\005\022\027\n\017questFinishTime\030\006 \001(\003\022\031\n\021ch" +
      "aracterEntityId\030\007 \001(\003\022\025\n\rnextStartTime\030\010" +
      " \001(\003\022\021\n\tstartTime\030\t \001(\003BF\n\037ehooray.fishi" +
      "ng.dto.proto.modelB\tShopProtoH\002\252\002\025Fishin" +
      "g.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_Shop_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_Shop_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Shop_descriptor,
        new java.lang.String[] { "EntityId", "CsvId", "CommodityCsvId", "BuyCount", "QuestState", "QuestFinishTime", "CharacterEntityId", "NextStartTime", "StartTime", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
