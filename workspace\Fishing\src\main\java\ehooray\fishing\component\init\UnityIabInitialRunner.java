package ehooray.fishing.component.init;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.iab.unity.AndroidIabReceiptVerifier;
import ehooray.fishing.component.iab.unity.IosIabReceiptVerifier_v7;
import ehooray.fishing.component.iab.unity.UnityEditorIabReceiptVerifier;

@Component
@Order(RunnerOrder.UnityIab)
public class UnityIabInitialRunner implements ApplicationRunner
{
    @Value("${game.iab-unity.editor-md5-key}")
    protected String unityEditorMd5Key;

    @Value("${game.iab-unity.client-app-id}")
    protected String appId;

    @Value("${game.iab-unity.android-public-key}")
    protected String androidPublicKey;



    @Autowired
    protected UnityEditorIabReceiptVerifier unityEditorIabReceiptVerifier;

    @Autowired
    protected AndroidIabReceiptVerifier androidIabReceiptVerifier;

    @Autowired
    protected IosIabReceiptVerifier_v7 iosIabReceiptVerifier;



    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        unityEditorIabReceiptVerifier.initial(appId, unityEditorMd5Key);
        androidIabReceiptVerifier.initial(androidPublicKey);
        iosIabReceiptVerifier.initial(appId);
    }

}
