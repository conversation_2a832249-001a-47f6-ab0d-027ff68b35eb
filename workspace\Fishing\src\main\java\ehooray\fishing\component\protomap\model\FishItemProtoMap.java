package ehooray.fishing.component.protomap.model;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.FishItemProto;
import ehooray.fishing.dto.proto.model.PlayerItemProto;
import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.service.item.PlayerItemService;

@Component
public class FishItemProtoMap
{
    public FishItemProto.FishItem.Builder toBuilderByEntity(FishItemEntity fishItem)
    {
        var builder = FishItemProto.FishItem.newBuilder();
        builder.setEntityId(fishItem.getId());
        builder.setCsvId(fishItem.getCsvId());
        builder.setSize(fishItem.getSize());
        if (fishItem.getExpireTime().isPresent())
            builder.setExpireTime(fishItem.getExpireTime().get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        
        return builder;
    }

    public Any toAnyByEntity(FishItemEntity fishItem)
    {
        return Any.pack(toBuilderByEntity(fishItem).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<FishItemEntity> fishItems)
    {
        ArrayList<Any> fishItemProtos = new ArrayList<Any>(fishItems.size());
        for(FishItemEntity fishItem : fishItems)
            fishItemProtos.add(toAnyByEntity(fishItem));
        
        return fishItemProtos;
    }
    
    public FishItemProto.FishItem toProtoByEntity(FishItemEntity fishItem)
    {
        return toBuilderByEntity(fishItem).build();
    }
    
    public ArrayList<FishItemProto.FishItem> toProtoByEntities(List<FishItemEntity> fishEntites)
    {
        ArrayList<FishItemProto.FishItem> playerItemProtos = new ArrayList<FishItemProto.FishItem>(fishEntites.size());
        for(FishItemEntity fish : fishEntites)
            playerItemProtos.add(toProtoByEntity(fish));
        
        return playerItemProtos;
    }
}
