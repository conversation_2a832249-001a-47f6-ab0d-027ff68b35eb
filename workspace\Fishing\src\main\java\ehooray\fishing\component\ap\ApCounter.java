package ehooray.fishing.component.ap;

import java.time.LocalDateTime;
import java.time.ZoneId;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.pojo.csv.LevelCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.VariableCsv;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;
import ehooray.fishing.service.player.PlayerService;

@Component
public class ApCounter
{
    protected final CsvContainer csvContainer;
    protected final PlayerService playerService;
    protected final PlayerItemService playerItemService;
    
    @Autowired
    public ApCounter(CsvContainer csvContainer, PlayerService playerService, PlayerItemService playerItemService)
    {
        this.csvContainer = csvContainer;
        this.playerService = playerService;
        this.playerItemService = playerItemService;
    }
    
    public CountApResult countAp(PlayerEntity player, LocalDateTime currentDate) throws Exception
    {
        if (player.getApLastCountDate().isEmpty())
        {
            playerService.setApLastCountDate(player, currentDate);
        }
            
        
        // check should update reset or not
        long countApOnceTime = csvContainer.getCsv(VariableCsv.class).getCountApMilliseconds();
        long timeDiff = toEpochMilli(currentDate) - toEpochMilli(player.getApLastCountDate().get());

        // if not over countApOnceTime, just pass remain time
        CountApResult result = new CountApResult();
        if (timeDiff < countApOnceTime)
        {
            result.apCountRemainMilliseconds = countApOnceTime - timeDiff;
            return result;
        }
        
        // enter here mean need do count ap
        
        int apCount = (int)(timeDiff / countApOnceTime);
        // check how much ap can count
        PlayerItemCsv.Row apCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.Ap.getId());
        PlayerItemEntity apItem = playerItemService.findByCsvId(player, apCsvRow.getId()).get();
        LevelCsv levelCsv = csvContainer.getCsv(LevelCsv.class);
        int apLimit = levelCsv.findRowByLevel(player.getLevel()).get().getAp();
        int currentApCount = (apItem != null) ? apItem.getCount() : 0;
        int apCountCapacity = apLimit - currentApCount;
        if(apCountCapacity < apCount)
        {
            if(apCountCapacity < 0)
                apCount = 0;
            else
                apCount = apCountCapacity; 
        }
        

        result.apCountResult = playerItemService.produceItem(player, apCsvRow, apCount);
        result.apCountRemainMilliseconds = countApOnceTime - (timeDiff % countApOnceTime);
        
        player.setApLastCountDate(currentDate);
        playerService.setApLastCountDate(player, currentDate);
        
        return result;
    }
    
    private long toEpochMilli(LocalDateTime dateTime)
    {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
    
    public static class CountApResult
    {
        public DiffItem apCountResult;
        public long apCountRemainMilliseconds;
    }
}
