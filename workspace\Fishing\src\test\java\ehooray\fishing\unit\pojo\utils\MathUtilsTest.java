package ehooray.fishing.unit.pojo.utils;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import ehooray.fishing.pojo.utils.MathUtils;

public class MathUtilsTest
{
    @ParameterizedTest
    @ValueSource(ints = {0, 20, 90, 100})
    public void testRandomSum(int expect)
    {
        int expectN = 4;

        int sum = 0;
        List<Integer> result = MathUtils.numbersWithSum(expectN, expect);
        for (var v : result)
        {
            System.out.println(v);
            sum += v;
        }
        
        
        assertThat(result.size()).isEqualTo(expectN);
        assertThat(sum).isEqualTo(expect);
    }
}
