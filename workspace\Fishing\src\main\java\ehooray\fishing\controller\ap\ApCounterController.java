package ehooray.fishing.controller.ap;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.ap.ApCounter;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.dto.proto.response.ap.CountApResultProto;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;

@Controller("ApCounterController")
public class ApCounterController
{
    @Autowired
    protected ApCounter apCounter;
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    
    @TransactionMethod(Method.Write)
    public void count(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var player = handleMediator.getPlayer().get();
        
        // action
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.ApCounterController_count));
        var result = apCounter.countAp(player, LocalDateTime.now());
        
        // respone
        var resultBuilder = CountApResultProto.CountApResult.newBuilder();
        if (result.apCountResult != null)
            resultBuilder.setApItem(playerItemProtoMap.toProtoByDiffItem(result.apCountResult));
        resultBuilder.setApCountRemainTime(result.apCountRemainMilliseconds);
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
