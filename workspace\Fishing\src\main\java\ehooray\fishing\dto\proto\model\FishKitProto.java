// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/FishKit.proto

package ehooray.fishing.dto.proto.model;

public final class FishKitProto {
  private FishKitProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FishKitOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.FishKit)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int64 fishRodEntityId = 2;</code>
     * @return The fishRodEntityId.
     */
    long getFishRodEntityId();

    /**
     * <code>int32 fishLineCsvId = 3;</code>
     * @return The fishLineCsvId.
     */
    int getFishLineCsvId();

    /**
     * <code>int32 baitCsvId = 4;</code>
     * @return The baitCsvId.
     */
    int getBaitCsvId();
  }
  /**
   * Protobuf type {@code model.FishKit}
   */
  public static final class FishKit extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.FishKit)
      FishKitOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishKit.newBuilder() to construct.
    private FishKit(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishKit() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishKit();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.FishKitProto.internal_static_model_FishKit_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.FishKitProto.internal_static_model_FishKit_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.FishKitProto.FishKit.class, ehooray.fishing.dto.proto.model.FishKitProto.FishKit.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int FISHRODENTITYID_FIELD_NUMBER = 2;
    private long fishRodEntityId_;
    /**
     * <code>int64 fishRodEntityId = 2;</code>
     * @return The fishRodEntityId.
     */
    @java.lang.Override
    public long getFishRodEntityId() {
      return fishRodEntityId_;
    }

    public static final int FISHLINECSVID_FIELD_NUMBER = 3;
    private int fishLineCsvId_;
    /**
     * <code>int32 fishLineCsvId = 3;</code>
     * @return The fishLineCsvId.
     */
    @java.lang.Override
    public int getFishLineCsvId() {
      return fishLineCsvId_;
    }

    public static final int BAITCSVID_FIELD_NUMBER = 4;
    private int baitCsvId_;
    /**
     * <code>int32 baitCsvId = 4;</code>
     * @return The baitCsvId.
     */
    @java.lang.Override
    public int getBaitCsvId() {
      return baitCsvId_;
    }

    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.FishKitProto.FishKit prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.FishKit}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.FishKit)
        ehooray.fishing.dto.proto.model.FishKitProto.FishKitOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.FishKitProto.internal_static_model_FishKit_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.FishKitProto.internal_static_model_FishKit_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.FishKitProto.FishKit.class, ehooray.fishing.dto.proto.model.FishKitProto.FishKit.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.FishKitProto.FishKit.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        fishRodEntityId_ = 0L;

        fishLineCsvId_ = 0;

        baitCsvId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.FishKitProto.internal_static_model_FishKit_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishKitProto.FishKit getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.FishKitProto.FishKit.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishKitProto.FishKit build() {
        ehooray.fishing.dto.proto.model.FishKitProto.FishKit result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishKitProto.FishKit buildPartial() {
        ehooray.fishing.dto.proto.model.FishKitProto.FishKit result = new ehooray.fishing.dto.proto.model.FishKitProto.FishKit(this);
        result.entityId_ = entityId_;
        result.fishRodEntityId_ = fishRodEntityId_;
        result.fishLineCsvId_ = fishLineCsvId_;
        result.baitCsvId_ = baitCsvId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private long fishRodEntityId_ ;
      /**
       * <code>int64 fishRodEntityId = 2;</code>
       * @return The fishRodEntityId.
       */
      @java.lang.Override
      public long getFishRodEntityId() {
        return fishRodEntityId_;
      }
      /**
       * <code>int64 fishRodEntityId = 2;</code>
       * @param value The fishRodEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setFishRodEntityId(long value) {
        
        fishRodEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 fishRodEntityId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishRodEntityId() {
        
        fishRodEntityId_ = 0L;
        onChanged();
        return this;
      }

      private int fishLineCsvId_ ;
      /**
       * <code>int32 fishLineCsvId = 3;</code>
       * @return The fishLineCsvId.
       */
      @java.lang.Override
      public int getFishLineCsvId() {
        return fishLineCsvId_;
      }
      /**
       * <code>int32 fishLineCsvId = 3;</code>
       * @param value The fishLineCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setFishLineCsvId(int value) {
        
        fishLineCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 fishLineCsvId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishLineCsvId() {
        
        fishLineCsvId_ = 0;
        onChanged();
        return this;
      }

      private int baitCsvId_ ;
      /**
       * <code>int32 baitCsvId = 4;</code>
       * @return The baitCsvId.
       */
      @java.lang.Override
      public int getBaitCsvId() {
        return baitCsvId_;
      }
      /**
       * <code>int32 baitCsvId = 4;</code>
       * @param value The baitCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setBaitCsvId(int value) {
        
        baitCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 baitCsvId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBaitCsvId() {
        
        baitCsvId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.FishKit)
    }

    // @@protoc_insertion_point(class_scope:model.FishKit)
    private static final ehooray.fishing.dto.proto.model.FishKitProto.FishKit DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.FishKitProto.FishKit();
    }

    public static ehooray.fishing.dto.proto.model.FishKitProto.FishKit getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishKit>
        PARSER = new com.google.protobuf.AbstractParser<FishKit>() {
      @java.lang.Override
      public FishKit parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FishKit> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishKit> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishKitProto.FishKit getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_FishKit_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_FishKit_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023model/FishKit.proto\022\005model\"^\n\007FishKit\022" +
      "\020\n\010entityId\030\001 \001(\003\022\027\n\017fishRodEntityId\030\002 \001" +
      "(\003\022\025\n\rfishLineCsvId\030\003 \001(\005\022\021\n\tbaitCsvId\030\004" +
      " \001(\005BI\n\037ehooray.fishing.dto.proto.modelB" +
      "\014FishKitProtoH\002\252\002\025Fishing.Network.Protob" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_FishKit_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_FishKit_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_FishKit_descriptor,
        new java.lang.String[] { "EntityId", "FishRodEntityId", "FishLineCsvId", "BaitCsvId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
