package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("CommodityTable.xlsx")
public class CommodityCsv extends ACsv<CommodityCsv.Row>
{
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Type")
        protected int type;

        @ExcelCellName("CommodityGroup")
        protected int commodityGroup;

        @ExcelCellName("ItemID")
        protected int itemId;

        @ExcelCellName("ItemAmount")
        protected int itemAmount;

        @ExcelCellName("BuyLimit")
        protected int buyLimit;

        @ExcelCellName("RequireID")
        protected int requireId;

        @ExcelCellName("RequireAmount")
        protected int requireAmount;

        @ExcelCellName("DiscountRequireAmount")
        protected int discountRequireAmount;

        @ExcelCellName("Weight")
        protected int weight;

        @ExcelCellName("EventID")
        protected int eventId;
        
        @ExcelCellName("IabID")
        protected int iabId;

        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }

        public int getType()
        {
            return type;
        }

        public int getCommodityGroup()
        {
            return commodityGroup;
        }

        public int getItemId()
        {
            return itemId;
        }

        public int getItemAmount()
        {
            return itemAmount;
        }

        public int getBuyLimit()
        {
            return buyLimit;
        }

        public int getRequireId()
        {
            return requireId;
        }

        public int getRequireAmount()
        {
            return requireAmount;
        }

        public int getDiscountRequireAmount()
        {
            return discountRequireAmount;
        }

        public int getWeight()
        {
            return weight;
        }

        public int getEventId()
        {
            return eventId;
        }

        public boolean ignoreBuyLimit()
        {
            return getBuyLimit() <= 0;
        }
        
        public boolean isDiscount()
        {
            return getDiscountRequireAmount() > 0;
        }

        public int getIabId()
        {
            return iabId;
        }
    }

    // another utility function at here
    protected HashMap<Integer, List<Row>> groupMap;

    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();

        Collection<Row> rows = getRows();
        groupMap = new HashMap<>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsInGroup = groupMap.get(row.getCommodityGroup());
            if(rowsInGroup == null)
            {
                rowsInGroup = new ArrayList<>();
                groupMap.put(row.getCommodityGroup(), rowsInGroup);
            }
            rowsInGroup.add(row);
        }

        for(List<Row> groupRows : groupMap.values())
            groupRows.sort((row1, row2) -> Integer.compare(row1.getId(), row2.getId()));
    }

    public List<Row> findAllByGroup(int group)
    {
        return groupMap.get(group);
    }

}
