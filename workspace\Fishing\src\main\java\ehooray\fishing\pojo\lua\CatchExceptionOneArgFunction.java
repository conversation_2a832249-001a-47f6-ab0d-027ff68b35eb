package ehooray.fishing.pojo.lua;

import org.apache.logging.log4j.LogManager;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.lib.OneArgFunction;

public abstract class CatchExceptionOneArgFunction extends OneArgFunction
{
    public abstract LuaValue doCall(LuaValue arg) throws Exception;
    @Override
    public LuaValue call(LuaValue arg)
    {
        try
        {
            return doCall(arg);
        }
        catch(Exception e)
        {
            LogManager.getLogger(this.getClass()).error(e);
            
            return LuaValue.NIL;
        }
    }

}
