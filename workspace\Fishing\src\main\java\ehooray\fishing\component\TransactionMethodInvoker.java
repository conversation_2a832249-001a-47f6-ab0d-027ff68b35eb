package ehooray.fishing.component;

import java.lang.reflect.Method;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import ehooray.fishing.pojo.ProtoHandleMediator;
import ehooray.fishing.service.player.PlayerService;

// Note: @Transactional method, must be public, and call by outside
@Component
public class TransactionMethodInvoker
{

	@Autowired
	protected PlayerService playerService;
	
	@Autowired
	protected ProtoHandleMediatorContainer protoHandleMediatorContainer;

	@Transactional(readOnly = true, timeoutString = "${game.transactional.timeout}")
	public ProtoHandleMediator invokeReadMethod(Object controller, Method method, ProtoHandleMediator.Builder handleMediatorBuilder) throws Exception
	{
	    ProtoHandleMediator mediator = buildMediator(handleMediatorBuilder);
        method.invoke(controller, mediator);
        return mediator;
	}

	@Transactional(rollbackFor = Throwable.class, timeoutString = "${game.transactional.timeout}")
	public ProtoHandleMediator invokeWriteMethod(Object controller, Method method, ProtoHandleMediator.Builder handleMediatorBuilder) throws Exception
	{
	    ProtoHandleMediator mediator = buildMediator(handleMediatorBuilder);
        method.invoke(controller, mediator);
        return mediator;
	}

	protected ProtoHandleMediator buildMediator(ProtoHandleMediator.Builder handleMediatorBuilder) throws Exception
	{
	    if(handleMediatorBuilder.haveSetPlayerId())
	    {
	        handleMediatorBuilder.setPlayer(playerService.findByEntityId(handleMediatorBuilder.getPlayerId()));
	    }
	    ProtoHandleMediator mediator = handleMediatorBuilder.build();
	    protoHandleMediatorContainer.addMediator(mediator);
	    return mediator;
	}
}
