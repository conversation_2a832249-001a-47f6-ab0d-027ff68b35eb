// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/shop/BuyRegularItem.proto

package ehooray.fishing.dto.proto.request.shop;

public final class BuyRegularItemProto {
  private BuyRegularItemProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BuyRegularItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.shop.BuyRegularItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 shopEntity = 1;</code>
     * @return The shopEntity.
     */
    long getShopEntity();

    /**
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * Protobuf type {@code request.shop.BuyRegularItem}
   */
  public static final class BuyRegularItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.shop.BuyRegularItem)
      BuyRegularItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BuyRegularItem.newBuilder() to construct.
    private BuyRegularItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BuyRegularItem() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BuyRegularItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.internal_static_request_shop_BuyRegularItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.internal_static_request_shop_BuyRegularItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.class, ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.Builder.class);
    }

    public static final int SHOPENTITY_FIELD_NUMBER = 1;
    private long shopEntity_;
    /**
     * <code>int64 shopEntity = 1;</code>
     * @return The shopEntity.
     */
    @java.lang.Override
    public long getShopEntity() {
      return shopEntity_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.shop.BuyRegularItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.shop.BuyRegularItem)
        ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.internal_static_request_shop_BuyRegularItem_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.internal_static_request_shop_BuyRegularItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.class, ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        shopEntity_ = 0L;

        count_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.internal_static_request_shop_BuyRegularItem_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem build() {
        ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem buildPartial() {
        ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem result = new ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem(this);
        result.shopEntity_ = shopEntity_;
        result.count_ = count_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long shopEntity_ ;
      /**
       * <code>int64 shopEntity = 1;</code>
       * @return The shopEntity.
       */
      @java.lang.Override
      public long getShopEntity() {
        return shopEntity_;
      }
      /**
       * <code>int64 shopEntity = 1;</code>
       * @param value The shopEntity to set.
       * @return This builder for chaining.
       */
      public Builder setShopEntity(long value) {
        
        shopEntity_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 shopEntity = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShopEntity() {
        
        shopEntity_ = 0L;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.shop.BuyRegularItem)
    }

    // @@protoc_insertion_point(class_scope:request.shop.BuyRegularItem)
    private static final ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem();
    }

    public static ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BuyRegularItem>
        PARSER = new com.google.protobuf.AbstractParser<BuyRegularItem>() {
      @java.lang.Override
      public BuyRegularItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<BuyRegularItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BuyRegularItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto.BuyRegularItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_shop_BuyRegularItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_shop_BuyRegularItem_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!request/shop/BuyRegularItem.proto\022\014req" +
      "uest.shop\"3\n\016BuyRegularItem\022\022\n\nshopEntit" +
      "y\030\001 \001(\003\022\r\n\005count\030\002 \001(\005BW\n&ehooray.fishin" +
      "g.dto.proto.request.shopB\023BuyRegularItem" +
      "ProtoH\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_shop_BuyRegularItem_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_shop_BuyRegularItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_shop_BuyRegularItem_descriptor,
        new java.lang.String[] { "ShopEntity", "Count", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
