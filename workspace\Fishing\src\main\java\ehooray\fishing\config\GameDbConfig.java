package ehooray.fishing.config;

import java.util.HashMap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jta.atomikos.AtomikosDataSourceBean;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy;
import org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
//@formatter:off
@EnableJpaRepositories
(
   basePackages = "${spring.datasource.game.repository-class-package}",
   entityManagerFactoryRef = GameDbConfig.EntityManagerFactoryRef,
   transactionManagerRef = JTAConfig.JTATransactionManagerRef
)
//@formatter:on
@EnableTransactionManagement
public class GameDbConfig
{
    public static final String EntityManagerFactoryRef = "gameEntityManagerFactory";
    //public static final String TransactionManagerRef = "gameTransactionManager";

    @Autowired
    protected Environment envioEnvironment;

    @Value("${spring.datasource.game.entity-class-package}")
    protected String entityPackage;

    @Bean
    @ConfigurationProperties("spring.datasource.game")
    @Primary // must set to default, or EntityManagerFactoryBuilder won't assign success
    public DataSource gameDataSource()
    {
        AtomikosDataSourceBean dataSource = new AtomikosDataSourceBean();
        return dataSource;
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean gameEntityManagerFactory(@Qualifier("gameDataSource")
    DataSource dataSource, EntityManagerFactoryBuilder builder)
    {
        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", envioEnvironment.getProperty("spring.jpa.hibernate.ddl-auto"));
        // set db field name to use lower case and "_"
        properties.put("hibernate.physical_naming_strategy", SpringPhysicalNamingStrategy.class.getName());
        properties.put("hibernate.implicit_naming_strategy", SpringImplicitNamingStrategy.class.getName());
        properties.put("hibernate.dialect", envioEnvironment.getProperty("spring.datasource.game.hibernate-dialect"));
        // jta
        properties.put("hibernate.current_session_context_class", "jta");
        properties.put("javax.persistence.transactionType", "jta");

        LocalContainerEntityManagerFactoryBean entityManagerFactoryBean =
                builder.dataSource(dataSource)
                       .properties(properties)
                       .packages(entityPackage)
                       .persistenceUnit(EntityManagerFactoryRef).build();

        return entityManagerFactoryBean;
    }

}
