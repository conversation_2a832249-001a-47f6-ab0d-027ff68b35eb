using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.NewStrategies
{
    /// <summary>
    /// 新釣魚捕獲狀態策略
    /// 實現新的捕獲效果和轉場，處理新UI元素
    /// </summary>
    public class NewCatchStrategy : FishingStrategyBase
    {
        private QTECutinManager qteCutinManager;
        
        public NewCatchStrategy() : base(FishingState.Catch) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 基本線和釣竿處理
            context.fishLine.SetTopPointToRod();
            context.cordReelManager.ResetCordReel();
            
            // 關閉UI面板
            context.fishingGamePanelManager.Close<CordReelManager>();
            context.fishingGamePanelManager.Close<PausePanelManager>();
            
            // 關閉新的張力UI
            if (context.fishingGamePanelManager.TryGetGamePanel(out NewLineTensionManager newLineTensionManager))
            {
                newLineTensionManager.CloseRoot();
                context.fishingGamePanelManager.Close<NewLineTensionManager>();
            }
            
            context.fishingGamePanelManager.Close<FishInfoManager>();
            
            // 相機震動和特效
            ShakeCamera.ShakeOnce(context.mainCamera).Forget();
            context.forceItem.CloseWaterEffect();
            context.forceItem.SetForce(0);
            
            // 隱藏釣竿和釣線
            context.fishRod.rodModel.gameObject.SetActive(false);
            context.fishLine.gameObject.SetActive(false);
            
            // 顯示魚
            context.fish.transform.gameObject.SetActive(true);
            context.fish.PullUpAnimation();
            context.fish.PlayWaterEffect();
            
            // 相機動畫和屏幕效果
            context.mainCamera.GetComponent<Animator>().enabled = true;
            context.mainCamera.GetComponent<Animator>().Play("Show", 0, 0);
            context.SetBlankScreen(true);
            
            // 播放捕獲音效
            // 可在此添加新的捕獲音效或特效
            
            // 顯示捕獲成功的立繪動畫
            await ShowCatchCutinAnimation(context);

            return;
        }
        
        /// <summary>
        /// 顯示捕獲成功的立繪動畫
        /// </summary>
        private async UniTask ShowCatchCutinAnimation(FishingManager context)
        {
            // 等待一小段時間，讓相機動畫開始播放
            await UniTask.Delay(500);
            
            // 加載QTECutinManager
            if (qteCutinManager == null)
            {
                qteCutinManager = await context.fishingGamePanelManager.OpenAsync<QTECutinManager>();
            }
            
            if (qteCutinManager == null)
            {
                Debug.LogError("[Catch] Failed to load QTECutinManager");
                return;
            }
            
            // 根據魚的大小或稀有度選擇不同的角色立繪和成功等級
            string characterName = "C101_Demo"; // 預設角色
            int successLevel = 2; // 預設為完美成功
            
            // 可以根據魚的數據來決定顯示哪個角色和成功等級
            if (context.fish != null)
            {
                // 根據魚的稀有度決定成功等級
                // 使用Fish類的公開方法或屬性來獲取魚的數據
                // 由於fishData是私有的，我們需要通過其他方式獲取魚的稀有度
                
                // 預設為最高等級
                successLevel = 2; // 完美
                
                // 可以根據魚的其他公開屬性來判斷稀有度
                // 例如，根據魚的生命值或掙扎力度等
                if (context.fish.life < 50)
                {
                    successLevel = 0; // 普通
                }
                else if (context.fish.life < 100)
                {
                    successLevel = 1; // 良好
                }
                else
                {
                    successLevel = 2; // 完美
                }
                
                // 可以根據魚的ID或其他屬性選擇不同的角色立繪
                // 這裡只是示例，實際邏輯可能需要根據項目需求調整
                characterName = "C101_Demo";
            }
            
            // 顯示立繪動畫
            await qteCutinManager.ShowCutinAnimation(characterName, successLevel);
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 捕獲狀態無需特殊更新邏輯，依賴動畫事件驅動
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 