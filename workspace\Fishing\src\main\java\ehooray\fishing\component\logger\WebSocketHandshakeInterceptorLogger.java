package ehooray.fishing.component.logger;

import org.apache.logging.log4j.LogManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class WebSocketHandshakeInterceptorLogger
{
    @AfterThrowing(value = "execution(* org.springframework.web.socket.server.HandshakeInterceptor.beforeHandshake(..))", throwing="exception")
    public void afterThrowing_beforeHandshake(JoinPoint joinPoint, Exception exception)
    {
        ServerHttpRequest request = (ServerHttpRequest)joinPoint.getArgs()[0];
        
        LogManager.getLogger(this.getClass()).error("beforeHandshake request uri = " + request.getURI(), exception);
    }
}