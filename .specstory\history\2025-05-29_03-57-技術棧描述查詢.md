<!-- Generated by SpecStory -->

# 技術棧描述查詢 (2025-05-29 11:57:30)

_**User**_

@Luxury_Angler_GDD_Unity.txt 這份描述技術棧是使用那些

---

_**Assistant**_



---

根据这份游戏设计文档，技术栈主要包括以下几个部分：

## 主要技术栈

### 游戏引擎
- **Phaser 3** - 针对2D移动端优化的游戏引擎

### 故事和对话系统
- **RenJS** - 视觉小说引擎，用于创建和管理故事情节、角色互动和对话系统

### 数据管理
- **云端存档** - 用于保存游戏进度
- **JSON格式** - 用于存储鱼卡和玩家进度数据

### 性能目标
- **60 FPS** - 目标帧率
- **轻量级时间/天气模拟系统**

### 平台支持
- **主要平台**: 移动端 (iOS, Android)
- **潜在平台**: PC (Steam), Nintendo Switch

这个技术栈选择很适合开发2D移动端钓鱼模拟游戏，Phaser 3提供了优秀的2D渲染能力，RenJS处理复杂的社交和故事系统，而JSON数据格式确保了数据的灵活性和可维护性。整体架构注重移动端性能优化。

---

_**User**_

把遊戲引擎改成Unity，故事系統改成Naninovel

---

_**Assistant**_



---

我来帮您修改游戏设计文档，将游戏引擎改为Unity，故事系统改为Naninovel。

---

```diff
  8. Social System
     8.1 Cruise Chatroom
-    8.2 Story and Dialog System (RenJS)
+    8.2 Story and Dialog System (Naninovel)
  9. Shop System
  10. Achievement System
```

```diff
  
  2.1 Main Game Loop Flowchart
- The gameplay loop starts at the Title Screen and centers on the Boat Menu, where players choose actions: traveling to fishing spots, fishing, chatting, shopping, or managing inventory. Time and energy consumption tie into the day-night and weather systems. Players must return to the starting game port to access the Shop or sell Fish Cards from their boat’s fishtank.
+ The gameplay loop starts at the Title Screen and centers on the Boat Menu, where players choose actions: traveling to fishing spots, fishing, chatting, shopping, or managing inventory. Time and energy consumption tie into the day-night and weather systems. Players must return to the starting game port to access the Shop or sell Fish Cards from their boat's fishtank.
  
  Flowchart (simplified):
```

```diff
  
  2.2 Gameplay Loop Description
- Players begin at the Title Screen, entering the Boat Menu after a tutorial or save load. The Boat Menu displays time (e.g., Dawn), weather (e.g., Sunny), and the boat’s current location (e.g., Starting Port or Map 3, Spot 7). From here, players can:
+ Players begin at the Title Screen, entering the Boat Menu after a tutorial or save load. The Boat Menu displays time (e.g., Dawn), weather (e.g., Sunny), and the boat's current location (e.g., Starting Port or Map 3, Spot 7). From here, players can:
  - **Travel**: Move between the starting port and 5 fishing maps, each with 10 unique spots (e.g., Coral Reef, Deep Trench). Travel takes 1 hour and 2 Energy.
  - **Fish**: Choose between **Story Mode** or **Practice Mode**:
```

```diff
  - **Description**: A timing-based minigame 
  - **Gameplay**: A cast meter showing up and if player click inside a Accurate Section on the cast meter, the lure will be casted to a hotspot(large or rare fishes have higher chance to appear), but if player missed the accurate section, the lure will be casted to a normal spot.
- - **Modifiers**: Player’s Cast Accuracy and Fish Detection attributes affect Accurate Section size and visibility.
+ - **Modifiers**: Player's Cast Accuracy and Fish Detection attributes affect Accurate Section size and visibility.
  - **Outcome**: Success proceeds to the **Lure** minigame; failure ends the attempt.
  - **Visual**: Add the visual of a 2D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Section on the cast meter, The lure with fishing line will land in the hotspot in the water with a splash sound, otherwise if player failed to click within Accurate Section, the lure with fishing line will land outside hotspot.
```

```diff
  4.1.3 Reel-In Minigame
  - **Description**: Tension-based minigame with QTEs to reel in the fish.
- - **Modifiers**: Player’s Tension Stability and Reel Speed affect difficulty.
+ - **Modifiers**: Player's Tension Stability and Reel Speed affect difficulty.
  - **Outcome**: Success yields rewards; failure loses the fish.
  - **Visual**: A hooked struggling fish with fishing line tied to it with splashing water. The fish will quickly swim left and right randomly.
```

```diff
  
  Clothing (15):
- 1. Fisher’s Cap: 2 Trout Cards, 400 coins, 3 min.
+ 1. Fisher's Cap: 2 Trout Cards, 400 coins, 3 min.
  2. Sunglasses: 2 Pike Cards, 600 coins, 5 min.
  3. Fishing Vest: 2 Salmon Cards, 800 coins, 7 min.
  4. Bikini Top: 2 Bass Cards, 1,000 coins, 9 min.
  5. Sandals: 2 Perch Cards, 1,200 coins, 11 min.
- 6. Elite Fisher’s Cap: 2 Fisher’s Caps, 1,500 coins, 13 min.
+ 6. Elite Fisher's Cap: 2 Fisher's Caps, 1,500 coins, 13 min.
  7. Elite Sunglasses: 2 Sunglasses, 2,000 coins, 15 min.
  8. Elite Fishing Vest: 2 Fishing Vests, 2,500 coins, 17 min.
  9. Elite Bikini Top: 2 Bikini Tops, 3,000 coins, 19 min.
  10. Elite Sandals: 2 Sandals, 3,500 coins, 21 min.
- 11. Master Fisher’s Cap: 2 Elite Fisher’s Caps, 4,000 coins, 23 min.
+ 11. Master Fisher's Cap: 2 Elite Fisher's Caps, 4,000 coins, 23 min.
  12. Master Sunglasses: 2 Elite Sunglasses, 4,500 coins, 25 min.
  13. Master Fishing Vest: 2 Elite Fishing Vests, 5,000 coins, 27 min.
```

```diff
  - **Organization**: Items are categorized into Fish Cards, Equipment (Rods, Lures, Boats, Clothing), and Consumables (e.g., Energy Potions).
  - **Management**: Players can sort items by type, rarity, or acquisition time. Bulk actions (e.g., sell all duplicates) are available.
- - **Fish Cards**: Collected via fishing or auto-fishing, used for crafting or gifting. Each card displays the fish’s name, rarity, and attributes.
+ - **Fish Cards**: Collected via fishing or auto-fishing, used for crafting or gifting. Each card displays the fish's name, rarity, and attributes.
  - **Equipment**: Includes rods, lures, boats, and clothing, each with unique stats and effects.
  - **Actions**: Equip, merge, sell, or gift items to companions.
```

```diff
  - **Lures**: Improve Bite Rate, Lure Success, etc. (e.g., Basic Spinner: +20% Bite Rate).
  - **Boats**: Enhance Boat Speed, Fish Detection, etc. (e.g., Rowboat: +3% Boat Speed).
- - **Clothing**: Provide various buffs (e.g., Fisher’s Cap: +5% Energy).
+ - **Clothing**: Provide various buffs (e.g., Fisher's Cap: +5% Energy).
  
  **Equipment Slots**: 7 slots (1 Rod, 1 Lure, 1 Boat, 3 Clothing, 1-3 Companions).
```

```diff
  - **Romance Meter**: 0-100 points, increased by interactions. Milestones (e.g., 50, 100) unlock rewards like HCG scenes.
  
- 8.2 Story and Dialog System (RenJS)
- - **RenJS**: A visual novel engine used for creating and managing storylines, character interactions, and dialogue.
- - **Integration**: RenJS handles all narrative content, including companion backstories, romance arcs, and event dialogues.
+ 8.2 Story and Dialog System (Naninovel)
+ - **Naninovel**: A visual novel framework for Unity used for creating and managing storylines, character interactions, and dialogue.
+ - **Integration**: Naninovel handles all narrative content, including companion backstories, romance arcs, and event dialogues.
  - **Features**: Branching dialogue choices, affection-based responses, and scripted events tied to player progression.
  - **Cross-System Integration**: Dialogue choices can influence gameplay (e.g., unlocking new fishing spots or temporary buffs).
```

---

```diff
  --------------------
  
- - Engine: Phaser 3 (optimized for 2D mobile).
+ - Engine: Unity (cross-platform support for mobile, PC, and console).
  - Data: Cloud saves, JSON for Fish Cards and player progress.
- - Performance: 60 FPS target, lightweight time/weather simulation.
+ - Performance: 60 FPS target, optimized 2D rendering and lightweight time/weather simulation.
+ - Visual Novel Framework: Naninovel for Unity handles all narrative content and dialogue systems.
```

---

修改完成！我已经成功将游戏设计文档中的技术栈更新为：

## 主要更改内容：

### 1. 游戏引擎更改
- **原始**: Phaser 3 (针对2D移动端优化)
- **更新**: Unity (跨平台支持移动端、PC和主机)

### 2. 故事系统更改
- **原始**: RenJS - 视觉小说引擎
- **更新**: Naninovel - Unity的视觉小说框架

### 3. 技术设计部分完整更新
现在的技术栈包括：
- **引擎**: Unity (跨平台支持移动端、PC和主机)
- **数据**: 云端存档，JSON格式存储鱼卡和玩家进度
- **性能**: 60 FPS目标，优化的2D渲染和轻量级时间/天气模拟
- **视觉小说框架**: Naninovel for Unity处理所有叙述内容和对话系统

这个修改使得技术栈更加统一，Unity作为主引擎可以更好地集成Naninovel，并且Unity的跨平台能力也更适合多平台发布的需求。

---

_**User**_

2d遊戲拿掉，改成3d

---

_**Assistant**_



---

我来帮您将游戏从2D改为3D。让我先查找文档中所有提到2D的地方并进行修改。

---

<details>
            <summary>Grep search for "2D" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L38 | `Luxury Angler is a 2D fishing simulation game that blends realistic fishing mechanics with a luxurious lifestyle theme. Players progress from novice to master angler across 50 levels, fishing in 5 exotic maps with 10 sp` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L168 | `- **Visual**: Add the visual of a 2D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Secti` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L391 | `- Art Style: Hand-painted 2D, vibrant waters, luxurious boats, detailed fish.` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L399 | `- Performance: 60 FPS target, optimized 2D rendering and lightweight time/weather simulation.` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD%28updated%29.txt` | L38 | `Luxury Angler is a 2D fishing simulation game that blends realistic fishing mechanics with a luxurious lifestyle theme. Players progress from novice to master angler across 50 levels, fishing in 5 exotic maps with 10 sp` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD%28updated%29.txt` | L168 | `- **Visual**: Add the visual of a 2D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Secti` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD%28updated%29.txt` | L391 | `- Art Style: Hand-painted 2D, vibrant waters, luxurious boats, detailed fish.` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD%28updated%29.txt` | L397 | `- Engine: Phaser 3 (optimized for 2D mobile).` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L38 | `Luxury Angler is a 2D fishing simulation game that blends realistic fishing mechanics with a luxurious lifestyle theme. Players progress from novice to master angler across 50 levels, fishing in 5 exotic maps with 10 sp` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L168 | `- **Visual**: Add the visual of a 2D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Secti` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L391 | `- Art Style: Hand-painted 2D, vibrant waters, luxurious boats, detailed fish.` |
| `file:///d%3A/dev/Fishing/FishingPhaser/Luxury_Angler_GDD_Unity.txt` | L399 | `- Performance: 60 FPS target, optimized 2D rendering and lightweight time/weather simulation.` |

</details>

---

现在我来逐一修改这些2D的描述为3D：

---

```diff
  
  1.1 Game Concept
- Luxury Angler is a 2D fishing simulation game that blends realistic fishing mechanics with a luxurious lifestyle theme. Players progress from novice to master angler across 50 levels, fishing in 5 exotic maps with 10 spots each, crafting elite gear via a merge system, competing in tournaments, and building relationships with 10 bikini girl companions. The game features 50 Fish Cards, a day-night cycle with 8 time periods, a weather system (Sunny/Rainy), and skill-based minigames (casting, luring, reeling), alongside idle auto-fishing options, appealing to both casual and mid-core players.
+ Luxury Angler is a 3D fishing simulation game that blends realistic fishing mechanics with a luxurious lifestyle theme. Players progress from novice to master angler across 50 levels, fishing in 5 exotic maps with 10 spots each, crafting elite gear via a merge system, competing in tournaments, and building relationships with 10 bikini girl companions. The game features 50 Fish Cards, a day-night cycle with 8 time periods, a weather system (Sunny/Rainy), and skill-based minigames (casting, luring, reeling), alongside idle auto-fishing options, appealing to both casual and mid-core players.
  
  1.2 Target Audience
```

---

```diff
  - **Modifiers**: Player's Cast Accuracy and Fish Detection attributes affect Accurate Section size and visibility.
  - **Outcome**: Success proceeds to the **Lure** minigame; failure ends the attempt.
- - **Visual**: Add the visual of a 2D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Section on the cast meter, The lure with fishing line will land in the hotspot in the water with a splash sound, otherwise if player failed to click within Accurate Section, the lure with fishing line will land outside hotspot.
+ - **Visual**: Add the visual of a 3D fishing rod casting animation and after player click in the cast meter, a lure with fishing line is being thrown out toward the hotspot in the water. If the player clicked within the Accurate Section on the cast meter, The lure with fishing line will land in the hotspot in the water with a splash sound, otherwise if player failed to click within Accurate Section, the lure with fishing line will land outside hotspot.
  
  4.1.2 Lure Minigame (Enhanced)
```

---

```diff
  -----------------
  
- - Art Style: Hand-painted 2D, vibrant waters, luxurious boats, detailed fish.
+ - Art Style: High-quality 3D models with realistic textures, vibrant waters, luxurious boats, detailed fish.
  - Audio: Relaxing ocean music, water splashes, triumphant catch fanfares.
  
```

---

```diff
  - Engine: Unity (cross-platform support for mobile, PC, and console).
  - Data: Cloud saves, JSON for Fish Cards and player progress.
- - Performance: 60 FPS target, optimized 2D rendering and lightweight time/weather simulation.
+ - Performance: 60 FPS target, optimized 3D rendering and lightweight time/weather simulation.
  - Visual Novel Framework: Naninovel for Unity handles all narrative content and dialogue systems.
```

---

修改完成！我已经成功将游戏从2D改为3D。以下是所有的更改：

## 主要更改内容：

### 1. 游戏概念 (1.1节)
- **原始**: "Luxury Angler is a 2D fishing simulation game..."
- **更新**: "Luxury Angler is a 3D fishing simulation game..."

### 2. 钓鱼机制视觉描述 (4.1.1节)
- **原始**: "Add the visual of a 2D fishing rod casting animation..."
- **更新**: "Add the visual of a 3D fishing rod casting animation..."

### 3. 艺术风格 (12节)
- **原始**: "Hand-painted 2D, vibrant waters, luxurious boats, detailed fish"
- **更新**: "High-quality 3D models with realistic textures, vibrant waters, luxurious boats, detailed fish"

### 4. 技术性能 (13节)
- **原始**: "optimized 2D rendering and lightweight time/weather simulation"
- **更新**: "optimized 3D rendering and lightweight time/weather simulation"

现在游戏设计文档已经完全更新为3D游戏。这将为玩家提供更加沉浸式的钓鱼体验，包括：

- **更真实的3D钓鱼竿和鱼线动画**
- **立体的水面和鱼类模型**
- **更丰富的视觉层次和深度感**
- **更逼真的豪华游艇和海洋环境**

Unity引擎非常适合3D游戏开发，可以充分发挥其在3D渲染方面的优势。

---

_**User**_

請幫我把 @Luxury_Angler_GDD_Unity.txt 翻譯成繁體中文到@Luxury_Angler_GDD_Unity_zhtw.txt 
遊戲名稱、技術專有名詞等等不用翻譯，維持英文

---

_**Assistant**_



---

我来帮您将游戏设计文档翻译成繁体中文。让我先读取原文件内容，然后进行翻译。

---

Read file: Luxury_Angler_GDD_Unity.txt

---

Read file: Luxury_Angler_GDD_Unity.txt

---

現在我來將完整的文档翻译成繁体中文：

---



---



---

