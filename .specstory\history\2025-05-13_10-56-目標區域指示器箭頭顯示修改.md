<!-- Generated by SpecStory -->

# 目標區域指示器箭頭顯示修改 (2025-05-13 18:56:04)

_**User**_

targetAreaIndicator的顯示改成一個箭頭，圖檔的ImageType是Simple，且中心點在中央，根據判定區域，旋轉Z軸做指向

---

_**Assistant**_



---




---



---

好的，我将修改 `LureActionSystem.cs` 文件中的 `StartNextAttempt` 方法，使 `targetAreaIndicator` 显示为一个箭头，并根据目标角度旋转。

主要的改动包括：
1.  移除对 `indicatorImage.fillAmount` 的设置，因为箭头将使用 `Simple` ImageType。
2.  修改 `targetAreaIndicator.rectTransform.localEulerAngles` 的Z轴旋转，使其直接指向 `currentTargetAngle`。这里假设您使用的箭头 Sprite 资源在默认状态下（0度旋转）是指向右方的（即Transform的X轴正方向）。

以下是具体的代码修改：
我将修改 `Assets/Fishing/Scripts/Fish/LureActionSystem.cs` 文件。


---

```diff
          if (indicatorImage != null)
          {
-             indicatorImage.sprite = targetAreaNormalSprite; // 初始化为普通图片
-             indicatorImage.fillAmount = targetAreaAngleWidth / 360f;
+             // 假设用户已在编辑器中将 targetAreaIndicator 的 Image Type 设置为 Simple，
+             // 并将 Pivot 设置为 Center。
+             // targetAreaNormalSprite 应被设置为箭头的Sprite。
+             indicatorImage.sprite = targetAreaNormalSprite; // 初始化为普通状态的箭头图片
+ 
+             // 由于 targetAreaIndicator 现在是箭头，不再需要设置 fillAmount。
+             // indicatorImage.fillAmount = targetAreaAngleWidth / 360f; // 旧代码
  
-             // 计算旋转角度，使填充区域的中心对准目标角度
-             float rotationAngle = currentTargetAngle - (targetAreaAngleWidth / 2f);
-             // 如果fillOrigin不是Top(0)，可能需要额外调整rotationAngle以匹配起始位置
-             targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, rotationAngle);
+             // 设置箭头的旋转，使其直接指向目标角度 currentTargetAngle。
+             // 假设箭头Sprite资源默认朝向是X轴正方向（0度）。
+             // currentTargetAngle 的值: 0 代表右, 90 代表上, 180 代表左, 270 代表下。
+             targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, currentTargetAngle);
          }
          else
          {
              Debug.LogError("Target Area Indicator 没有 Image 组件!");
-             // 如果没有Image组件，则按旧方式设置角度以作备用，或者抛出错误
-             targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, currentTargetAngle);
+             // 如果没有Image组件，指示器将无法正确显示。
+             // 旧的备用逻辑: targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, currentTargetAngle);
+             // 这个逻辑与新逻辑一致，但前提是Image组件存在以显示Sprite。
          }
  
-         targetAreaIndicator.gameObject.SetActive(true); // 显示判定区域UI
+         targetAreaIndicator.gameObject.SetActive(true); // 显示判定区域UI (现在是箭头)
          Debug.Log($"尝试 {attemptsTotal - attemptsRemaining + 1}: 目标角度设置为 {currentTargetAngle}");
  
```

---

代码已按您的要求修改完毕。

请确保在 Unity 编辑器中：
1.  将 `targetAreaIndicator` 对象的 `Image` 组件的 `Image Type` 设置为 `Simple`。
2.  为其分配的 Sprite （即 `targetAreaNormalSprite`，以及可能的 `targetAreaHighlightSprite`）的 Pivot 设置在中心。
3.  确保您用作箭头的 Sprite 资源，在未旋转时（即 Z 轴旋转为0），其视觉上是指向右方的。如果它指向其他方向（例如向上），那么代码中的旋转角度 `currentTargetAngle` 可能需要加上或减去相应的偏移量（例如 `currentTargetAngle - 90f`）。

这些更改应该可以让 `targetAreaIndicator` 作为箭头正确指向目标方向了。

---

