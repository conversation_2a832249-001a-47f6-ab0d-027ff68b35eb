package ehooray.fishing.controller.fishkit;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.protomap.model.FishKitProtoMap;
import ehooray.fishing.dto.proto.request.fishkit.ActiveFishKitProto;
import ehooray.fishing.dto.proto.request.fishkit.UpdateFishKitProto;
import ehooray.fishing.entity.FishKitEntity;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.service.fishkit.FishKitService;
import ehooray.fishing.service.fishrod.FishRodService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.player.PlayerService;

@Controller("FishKitController")
public class FishKitController
{
    @Autowired
    protected FishKitService fishKitService;
    @Autowired
    protected FishRodService fishRodService;
    @Autowired
    protected PlayerItemService playerItemService;
    @Autowired
    protected PlayerService playerService;
    
    @Autowired
    protected FishKitProtoMap fishKitMap;
    
    @TransactionMethod(Method.Read)
    public void getFishKit(WebSocketHandleMediator handleMediator) throws Exception
    {
        // response
        var player = handleMediator.getPlayer().get();
        handleMediator.addResult(fishKitMap.toAnyByEntities(player.getFishKits()));
    }
    
    @TransactionMethod(Method.Read)
    public void getActiveFishKit(WebSocketHandleMediator handleMediator) throws Exception
    {
        // response
        var player = handleMediator.getPlayer().get();
        var activeFishKit = new ArrayList<FishKitEntity>();
        activeFishKit.add(player.getActiveFishKit());
        handleMediator.addResult(fishKitMap.toAnyByEntities(activeFishKit));
    }
   
    
    @TransactionMethod(Method.Write)
    public void updateFishKit(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var updateFishKit = handleMediator.getRequest().getDataList(0).unpack(UpdateFishKitProto.UpdateFishKit.class);
        var player = handleMediator.getPlayer().get();
        var fishKit = fishKitService.findByEntityId(player, updateFishKit.getFishKitId()).get();
        var fishRod = fishRodService.getAllByPlayer(player).stream().filter(rod->rod.getId() == updateFishKit.getFishRodEntityId()).findFirst();
        var fishLine = playerItemService.findByCsvId(player, updateFishKit.getFishLineCsvId());
        var bait = playerItemService.findByCsvId(player, updateFishKit.getBaitCsvId());
        
        // action
        fishKitService.updateFishKit(fishKit, fishRod.get(), fishLine.get(), bait.get());
        
        // response
        handleMediator.addResult(fishKitMap.toAnyByEntities(player.getFishKits()));
    }
    
    @TransactionMethod(Method.Write)
    public void activeFishKit(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var activeFishKit = handleMediator.getRequest().getDataList(0).unpack(ActiveFishKitProto.ActiveFishKit.class);
        var player = handleMediator.getPlayer().get();
        var fishKit = fishKitService.findByEntityId(player, activeFishKit.getFishKit()).get();
        
        // action
        playerService.activeFishKit(player, fishKit);
        
        // response
        var activeFishKits = new ArrayList<FishKitEntity>();
        activeFishKits.add(player.getActiveFishKit());
        handleMediator.addResult(fishKitMap.toAnyByEntities(activeFishKits));
    }
}
