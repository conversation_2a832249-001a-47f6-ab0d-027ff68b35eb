---
description: Detailed explanation of core business logic and functional systems for the fishing game, including game loops, battle systems, economic systems, gacha mechanisms, quest systems, resource management, and the operational mechanisms and interactions between various game modules.
globs: 
alwaysApply: false
---
# 钓鱼游戏业务逻辑指南

## 核心游戏循环

### 玩家进度系统
- **玩家等级**: 通过钓鱼经验提升等级
- **技能系统**: 不同类型的钓鱼技能
- **成就系统**: 完成特定条件获得奖励

### 钓鱼战斗系统
**位置**: `src/main/java/ehooray/fishing/controller/battle/` 和 `src/main/java/ehooray/fishing/service/battle/`

战斗流程：
1. 选择钓鱼地点
2. 装备鱼竿和鱼套装
3. 消耗 AP (Action Points)
4. 进行钓鱼战斗
5. 获得鱼类和奖励

### 装备系统

#### 鱼竿系统 (FishRod)
**位置**: `src/main/java/ehooray/fishing/controller/fishrod/` 和 `src/main/java/ehooray/fishing/service/fishrod/`
- 不同等级和属性的鱼竿
- 鱼竿升级和强化
- 特殊效果和加成

#### 鱼套装系统 (FishKit)
**位置**: `src/main/java/ehooray/fishing/controller/fishkit/` 和 `src/main/java/ehooray/fishing/service/fishkit/`
- 套装组合效果
- 套装收集和合成
- 属性加成计算

## 经济系统

### 物品管理 (Item)
**位置**: `src/main/java/ehooray/fishing/controller/item/` 和 `src/main/java/ehooray/fishing/service/item/`
- 鱼类物品
- 消耗品和材料
- 装备物品
- 物品堆叠和使用

### 商店系统 (Shop)
**位置**: `src/main/java/ehooray/fishing/controller/shop/` 和 `src/main/java/ehooray/fishing/service/shop/`
- 固定商品销售
- 限时商品
- 特殊货币购买
- 商品刷新机制

### 兑换系统 (Exchange)
**位置**: `src/main/java/ehooray/fishing/controller/exchange/` 和 `src/main/java/ehooray/fishing/service/exchange/`
- 物品间的兑换
- 特殊材料合成
- 兑换比率计算
- 兑换历史记录

## 抽奖系统 (Gacha)

**位置**: `src/main/java/ehooray/fishing/controller/gacha/` 和 `src/main/java/ehooray/fishing/service/gacha/`

### 抽奖机制
- 概率池设计
- 保底机制
- 稀有度分级
- 抽奖历史统计

### 奖励类型
- 鱼竿和装备
- 消耗品
- 游戏货币
- 特殊道具

## 任务系统 (Quest)

**位置**: `src/main/java/ehooray/fishing/controller/quest/` 和 `src/main/java/ehooray/fishing/service/quest/`

### 任务类型
- **日常任务**: 每日重置的简单任务
- **成就任务**: 长期目标和里程碑
- **活动任务**: 限时特殊任务
- **主线任务**: 游戏进度相关任务

### 任务条件
- 钓到特定数量的鱼
- 达到某个等级
- 使用特定装备
- 完成特定次数的战斗

## 资源管理

### AP 系统 (Action Points)
**位置**: `src/main/java/ehooray/fishing/controller/ap/` 和 `src/main/java/ehooray/fishing/service/ap/`
- AP 消耗机制
- AP 自动恢复
- AP 购买和补充
- AP 效率优化

### 时间系统 (Time)
**位置**: `src/main/java/ehooray/fishing/controller/time/` 和 `src/main/java/ehooray/fishing/service/time/`
- 游戏内时间管理
- 事件定时触发
- 冷却时间计算
- 时区处理

## 社交和竞技

### 排行榜系统
- 钓鱼数量排行
- 玩家等级排行
- 特殊成就排行

### 好友系统
- 好友添加和管理
- 好友互动功能
- 礼品赠送系统

## 支付系统 (IAB - In-App Billing)

**位置**: `src/main/java/ehooray/fishing/service/iab/`
- 应用内购买处理
- 支付验证
- 商品发放
- 支付记录管理

## 数据分析和统计

### 玩家行为分析
- 登录频率统计
- 游戏时长追踪
- 功能使用情况
- 消费行为分析

### 游戏平衡调整
- 奖励概率调整
- 难度平衡
- 经济系统监控
- 用户反馈收集

## 服务器管理

**位置**: `src/main/java/ehooray/fishing/service/server/`
- 服务器状态监控
- 维护模式管理
- 公告系统
- 版本控制

