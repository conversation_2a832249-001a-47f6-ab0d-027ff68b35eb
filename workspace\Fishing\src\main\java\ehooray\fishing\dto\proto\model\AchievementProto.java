// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/Achievement.proto

package ehooray.fishing.dto.proto.model;

public final class AchievementProto {
  private AchievementProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AchievementOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.Achievement)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 questType = 2;</code>
     * @return The questType.
     */
    int getQuestType();

    /**
     * <code>repeated int32 parameters = 3;</code>
     * @return A list containing the parameters.
     */
    java.util.List<java.lang.Integer> getParametersList();
    /**
     * <code>repeated int32 parameters = 3;</code>
     * @return The count of parameters.
     */
    int getParametersCount();
    /**
     * <code>repeated int32 parameters = 3;</code>
     * @param index The index of the element to return.
     * @return The parameters at the given index.
     */
    int getParameters(int index);

    /**
     * <code>int32 targetCount = 4;</code>
     * @return The targetCount.
     */
    int getTargetCount();
  }
  /**
   * Protobuf type {@code model.Achievement}
   */
  public static final class Achievement extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.Achievement)
      AchievementOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Achievement.newBuilder() to construct.
    private Achievement(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Achievement() {
      parameters_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Achievement();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.AchievementProto.internal_static_model_Achievement_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.AchievementProto.internal_static_model_Achievement_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.AchievementProto.Achievement.class, ehooray.fishing.dto.proto.model.AchievementProto.Achievement.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int QUESTTYPE_FIELD_NUMBER = 2;
    private int questType_;
    /**
     * <code>int32 questType = 2;</code>
     * @return The questType.
     */
    @java.lang.Override
    public int getQuestType() {
      return questType_;
    }

    public static final int PARAMETERS_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.IntList parameters_;
    /**
     * <code>repeated int32 parameters = 3;</code>
     * @return A list containing the parameters.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getParametersList() {
      return parameters_;
    }
    /**
     * <code>repeated int32 parameters = 3;</code>
     * @return The count of parameters.
     */
    public int getParametersCount() {
      return parameters_.size();
    }
    /**
     * <code>repeated int32 parameters = 3;</code>
     * @param index The index of the element to return.
     * @return The parameters at the given index.
     */
    public int getParameters(int index) {
      return parameters_.getInt(index);
    }
    private int parametersMemoizedSerializedSize = -1;

    public static final int TARGETCOUNT_FIELD_NUMBER = 4;
    private int targetCount_;
    /**
     * <code>int32 targetCount = 4;</code>
     * @return The targetCount.
     */
    @java.lang.Override
    public int getTargetCount() {
      return targetCount_;
    }

    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.AchievementProto.Achievement prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.Achievement}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.Achievement)
        ehooray.fishing.dto.proto.model.AchievementProto.AchievementOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.AchievementProto.internal_static_model_Achievement_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.AchievementProto.internal_static_model_Achievement_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.AchievementProto.Achievement.class, ehooray.fishing.dto.proto.model.AchievementProto.Achievement.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.AchievementProto.Achievement.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        questType_ = 0;

        parameters_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        targetCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.AchievementProto.internal_static_model_Achievement_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.AchievementProto.Achievement getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.AchievementProto.Achievement.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.AchievementProto.Achievement build() {
        ehooray.fishing.dto.proto.model.AchievementProto.Achievement result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.AchievementProto.Achievement buildPartial() {
        ehooray.fishing.dto.proto.model.AchievementProto.Achievement result = new ehooray.fishing.dto.proto.model.AchievementProto.Achievement(this);
        int from_bitField0_ = bitField0_;
        result.entityId_ = entityId_;
        result.questType_ = questType_;
        if (((bitField0_ & 0x00000001) != 0)) {
          parameters_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.parameters_ = parameters_;
        result.targetCount_ = targetCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int questType_ ;
      /**
       * <code>int32 questType = 2;</code>
       * @return The questType.
       */
      @java.lang.Override
      public int getQuestType() {
        return questType_;
      }
      /**
       * <code>int32 questType = 2;</code>
       * @param value The questType to set.
       * @return This builder for chaining.
       */
      public Builder setQuestType(int value) {
        
        questType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 questType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestType() {
        
        questType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList parameters_ = emptyIntList();
      private void ensureParametersIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          parameters_ = mutableCopy(parameters_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @return A list containing the parameters.
       */
      public java.util.List<java.lang.Integer>
          getParametersList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(parameters_) : parameters_;
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @return The count of parameters.
       */
      public int getParametersCount() {
        return parameters_.size();
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @param index The index of the element to return.
       * @return The parameters at the given index.
       */
      public int getParameters(int index) {
        return parameters_.getInt(index);
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @param index The index to set the value at.
       * @param value The parameters to set.
       * @return This builder for chaining.
       */
      public Builder setParameters(
          int index, int value) {
        ensureParametersIsMutable();
        parameters_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @param value The parameters to add.
       * @return This builder for chaining.
       */
      public Builder addParameters(int value) {
        ensureParametersIsMutable();
        parameters_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @param values The parameters to add.
       * @return This builder for chaining.
       */
      public Builder addAllParameters(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureParametersIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, parameters_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 parameters = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearParameters() {
        parameters_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int targetCount_ ;
      /**
       * <code>int32 targetCount = 4;</code>
       * @return The targetCount.
       */
      @java.lang.Override
      public int getTargetCount() {
        return targetCount_;
      }
      /**
       * <code>int32 targetCount = 4;</code>
       * @param value The targetCount to set.
       * @return This builder for chaining.
       */
      public Builder setTargetCount(int value) {
        
        targetCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 targetCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetCount() {
        
        targetCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.Achievement)
    }

    // @@protoc_insertion_point(class_scope:model.Achievement)
    private static final ehooray.fishing.dto.proto.model.AchievementProto.Achievement DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.AchievementProto.Achievement();
    }

    public static ehooray.fishing.dto.proto.model.AchievementProto.Achievement getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Achievement>
        PARSER = new com.google.protobuf.AbstractParser<Achievement>() {
      @java.lang.Override
      public Achievement parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Achievement> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Achievement> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.AchievementProto.Achievement getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Achievement_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Achievement_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027model/Achievement.proto\022\005model\"[\n\013Achi" +
      "evement\022\020\n\010entityId\030\001 \001(\003\022\021\n\tquestType\030\002" +
      " \001(\005\022\022\n\nparameters\030\003 \003(\005\022\023\n\013targetCount\030" +
      "\004 \001(\005BM\n\037ehooray.fishing.dto.proto.model" +
      "B\020AchievementProtoH\002\252\002\025Fishing.Network.P" +
      "rotob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_Achievement_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_Achievement_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Achievement_descriptor,
        new java.lang.String[] { "EntityId", "QuestType", "Parameters", "TargetCount", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
