package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class GachaCountEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final int count;

    public GachaCountEvent(Object source, PlayerEntity player, int count)
    {
        super(source);
        this.player = player;
        this.count = count;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
    
    public int getCount()
    {
        return count;
    }

}
