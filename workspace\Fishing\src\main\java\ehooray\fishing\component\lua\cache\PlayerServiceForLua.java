package ehooray.fishing.component.lua.cache;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.lua.ILuaCacheComponent;
import ehooray.fishing.pojo.lua.CatchExceptionOneArgFunction;
import ehooray.fishing.service.player.PlayerService;

@Component
public class PlayerServiceForLua implements ILuaCacheComponent
{
    @Autowired
    protected PlayerService playerService;

    protected LuaTable cache;

    @Override
    public void initial() throws Exception
    {
        cache = LuaValue.tableOf();
        // put new function for lua callback at here
        cache.set(FindByEntityId.class.getSimpleName(), new FindByEntityId(playerService));
    }

    @Override
    public LuaTable toLua()
    {
        return cache;
    }

    public class FindByEntityId extends CatchExceptionOneArgFunction
    {
        protected PlayerService playerService;
        protected FindByEntityId(PlayerService playerService) { this.playerService = playerService; }
        @Override
        public LuaValue doCall(LuaValue arg) throws Exception
        {
            return playerService.findByEntityId(arg.tolong()).get().toLua();
        }
    }

}
