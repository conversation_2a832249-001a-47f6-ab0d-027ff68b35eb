<!-- Generated by SpecStory -->

# 釣魚遊戲架構規劃 (2025-05-26 18:13:31)

_**User**_

這是一個phaser的prototype釣魚遊戲，我現在要轉移到正式項目，正式項目是使用spring boot+unity製作，請幫我看看有哪些適合放在java server，有哪些適合放在unity client，我初步看起來是inventory 玩家身上的equipment需要放在server使用database儲存，我們正式專案是使用mysql


你可以參考FishingServer/workspace/Fishing是spring boot專案
fishing-client是unity專案
這兩個專案可以使用mcp filesystem查看

phaser專案請根據目前cursor工作目錄直接查看

請不要實作任何功能，先規劃

---

_**Assistant**_



---

我來幫您分析這個Phaser釣魚遊戲的架構轉移。讓我先查看各個專案的結構。

---

<details>
            <summary>Listed current directory • **15** results</summary>
        
| Name |
|-------|
| 📁 `.cursor` |
| 📁 `.git` |
| 📄 `.gitignore` |
| 📄 `.cursorindexingignore` |
| 📁 `.specstory` |
| 📄 `test-simple.html` |
| 📄 `vite.config.js` |
| 📁 `src` |
| 📄 `package-lock.json` |
| 📄 `package.json` |
| 📁 `public` |
| 📄 `index.html` |
| 📄 `README.md` |
| 📄 `Editor _ Mermaid Chart-2025-05-24-214451.png` |
| 📄 `Luxury_Angler_GDD(updated).txt` |

</details>

---

<details>
            <summary>Listed directory src • **5** results</summary>
        
| Name |
|-------|
| 📁 `scripts` |
| 📁 `ui` |
| 📁 `scenes` |
| 📁 `data` |
| 📄 `main.js` |

</details>

---

<details>
            <summary>Listed directory src/scripts • **15** results</summary>
        
| Name |
|-------|
| 📄 `SettingsManager.js` |
| 📄 `ReelingMiniGame.js` |
| 📄 `SceneManager.js` |
| 📄 `LuringMiniGame.js` |
| 📄 `PlayerController.js` |
| 📄 `InputManager.js` |
| 📄 `InventoryManager.js` |
| 📄 `InventoryValidator.js` |
| 📄 `GameLoop.js` |
| 📄 `GameState.js` |
| 📄 `CraftingManager.js` |
| 📄 `DataLoader.js` |
| 📄 `FishingMinigames.js` |
| 📄 `AudioManager.js` |
| 📄 `CastingMiniGame.js` |

</details>

---

<details>
            <summary>Listed directory src/data • **6** results</summary>
        
| Name |
|-------|
| 📄 `lures.json` |
| 📄 `fish.json` |
| 📄 `gameConfig.json` |
| 📄 `inventorySchema.json` |
| 📄 `attributes.json` |
| 📄 `equipment.json` |

</details>

---

現在讓我查看Spring Boot和Unity專案的結構：

---



---



---



---



---

我將幫助您繼續分析您的項目架構並提供從Phaser到Spring Boot + Unity遷移的規劃建議。讓我先查看當前的工作目錄結構。

---



---

很好，我可以看到您有三個項目：FishingPhaser（原型）、FishingServer（Spring Boot）和fishing-client（Unity）。讓我先檢查一下Spring Boot和Unity項目的結構。

---



---



---

我來繼續完成之前的分析，查看Spring Boot和Unity專案結構，然後為您提供完整的架構規劃建議。

---



---



---



---



---



---



---

現在讓我查看Unity客戶端專案結構：

---



---



---

基於對話摘要，我來繼續完成架構規劃建議。讓我先獲取項目概覽規則來更好地理解整體架構。

---

### Rule: project-overview

**project overview**

```markdown
# Luxury Angler 项目总览

## 项目简介
Luxury Angler 是一个2D钓鱼模拟游戏，使用Phaser 3引擎开发。游戏混合了现实钓鱼机制和奢华生活主题，玩家从新手成长为钓鱼大师。

## 项目结构
- **主入口**: [main.js](mdc:src/main.js) - Phaser游戏配置和场景注册
- **场景系统**: [src/scenes/](mdc:src/scenes/) - 所有游戏场景
- **核心脚本**: [src/scripts/](mdc:src/scripts/) - 游戏逻辑和管理器
- **UI组件**: [src/ui/](mdc:src/ui/) - 用户界面组件
- **数据配置**: [src/data/](mdc:src/data/) - 游戏数据和配置文件

## 核心游戏循环
1. 标题画面 → 船只菜单（时间、天气、位置显示）
2. 旅行 → 选择地图（5个中的1个）→ 选择钓点（10个中的1个）
3. 钓鱼 → 选择模式：故事模式或练习模式
4. 钓鱼流程：抛竿 → 诱饵 → 收线
5. 社交互动、商店购买、库存管理

## 技术架构
- **引擎**: Phaser 3
- **分辨率**: 1280x720
- **物理引擎**: Arcade Physics
- **缩放模式**: FIT with CENTER_BOTH

## 游戏特色
- 50个等级，5个地图，每个地图10个钓点
- 50种鱼卡收集系统
- 合成系统：使用鱼卡制作装备
- 时间和天气系统影响鱼类出现

- 10个比基尼女孩同伴社交系统```

---

現在讓我獲取核心系統和數據系統的規則來更好地理解架構分配原則。

---

### Rule: core-systems

**core systems**

```markdown
# 核心游戏系统

## 主要管理器
- **[GameState.js](mdc:src/scripts/GameState.js)** - 游戏状态管理，包含玩家属性、进度、时间和天气系统
- **[GameLoop.js](mdc:src/sameLoop.js)** - 核心游戏循环逻辑
- **[Sjs](mdc:src/scripts/Sc)** - 场景切换和管理
- **[Playeler.js](mdc:src/scriptntroller.js)** - 玩家控制器和属性管理

## 钓鱼系统（三个迷你游戏）
1. **[CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js)** - 抛竿迷你游戏
   - 时机为基础的游戏
   - 抛竿计量表，准确区域内点击可到达热点
   - 视觉效果：2D钓竿抛竿动画，鱼线和诱饵投向水中
   
2. **[LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js)** - 诱饵迷你游戏  
   - 鱼影接近诱饵，玩家有3-4次机会钩鱼
   - 5种诱饵类型，各有不同的节奏输入控制
   - 诱饵模拟UI显示诱饵在水中的移动
   - 鱼类兴趣计量表和输入提示
   
3. **[ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js)** - 收线迷你游戏
   - 基于张力的游戏，包含QTE反应控制
   - 鱼类挣扎风格和体力系统
   - 张力计量表，防止鱼线断裂
   - 10种鱼类挣扎方式和QTE控制

## 属性系统（34个属性）
- **玩家升级属性**（10个）：抛竿精度、能量、咬钩率、稀有鱼机会、QTE精度等
- **钓竿属性**（6个）：抛竿精度、张力稳定性、稀有鱼机会、抛竿范围、收线速度、挣扎抗性
- **诱饵属性**（4个）：咬钩率、诱饵成功率、诱饵耐久度、诱饵精度
- **比基尼助手属性**（6个）：浪漫效果、聊天室亲和力、娱乐情绪提升等
- **船只属性**（8个）：制作效率、自动钓鱼产量、鱼类探测、热点稳定性等

## 时间和天气系统
- **时间循环**：8个时段（如黎明、正午），每个3小时
- **天气类型**：晴天（提升鱼类探测）、雨天（咬钩率+10%）
- **时间进展**：钓鱼+30分钟，旅行+1小时
- **鱼类活跃时间**：不同鱼类在特定时间段和天气条件下更活跃

## 鱼类系统
- **鱼类属性**（11个）：大小、侵略性、难以捕捉性、力量、稀有度、重量、速度、深度偏好、饵料偏好、耐力、活跃时间偏好
- **50种鱼卡**：从小鱼（0.1kg）到巨型鱼类（500kg）
- **挣扎风格**：快速冲撞、深潜、长距离冲刺等```
### Rule: data-systems

**data systems**

```markdown
# 数据和配置系统

## 数据文件
- **[gameConfig.json](mdc:src/data/gameConfig.json)** - 游戏基础配置
- **[fish.json](mdc:src/data/fish.json)** - 鱼类数据（50种鱼卡）
- **[lures.json](mdc:src/data/lures.json)** - 诱饵类型和配置
- **[equipment.json](mdc:src/data/equipment.json)** - 装备数据（钓竿、诱饵、船只、服装）
- **[attributes.json](mdc:src/data/attributes.json)** - 属性系统配置
- **[inventorySchema.json](mdc:src/data/inventorySchema.json)** - 库存系统架构

## 数据管理
- **[DataLoader.js](mdc:src/scripts/DataLoader.js)** - 数据加载和初始化
- **[InventoryManager.js](mdc:src/scripts/InventoryManager.js)** - 库存管理系统
- **[InventoryValidator.js](mdc:src/scripts/InventoryValidator.js)** - 库存验证逻辑

## 鱼类数据结构
- **基本属性**：大小（1-10）、侵略性（1-10）、难以捕捉性（1-10）
- **能力属性**：力量（1-10）、稀有度（1-10）、重量（0.1-500kg）
- **行为属性**：速度（1-10）、深度偏好（1-10）、饵料偏好（1-10）
- **特殊属性**：耐力（1-10）、活跃时间段/天气偏好

## 制作系统
- **[CraftingManager.js](mdc:src/scripts/CraftingManager.js)** - 合成系统管理
- **60个合成配方**：
  - 钓竿（15个）：竹竿 → 玻璃纤维竿 → 碳纤维竿 → 钢制竿 → 钛合金竿
  - 诱饵（15个）：基础旋转器 → 软虫 → 飞钓诱饵 → 波普诱饵 → 勺形诱饵
  - 船只（15个）：划艇 → 小艇 → 快艇 → 游艇 → 豪华邮轮
  - 服装（15个）：渔夫帽 → 太阳镜 → 钓鱼背心 → 比基尼上衣 → 凉鞋

## 制作成本
- **输入**：鱼卡（1-5星）
- **费用**：200-16,000金币
- **时间**：2-30分钟
- **升级**：合并低级装备创造高级版本

## 库存系统
- **容量**：50个槽位（可通过船只或技能扩展到100）
- **分类**：鱼卡、装备（钓竿、诱饵、船只、服装）、消耗品
- **管理**：按类型、稀有度或获取时间排序
- **批量操作**：出售重复物品等功能

## 装备系统
- **装备槽位**：7个槽位（1钓竿、1诱饵、1船只、3服装、1-3同伴）
- **属性加成**：装备属性与玩家属性叠加
- **获取方式**：通过合成制作或商店购买

- **升级机制**：合并低级装备创造高级版本```

---

基於Phaser項目的詳細分析和Spring Boot項目的架構，我現在來提供完整的架構分配建議：

## 釣魚遊戲架構分配建議

### 🎯 **伺服器端（Spring Boot + MySQL）**

#### **🗄️ 核心數據管理**
- **玩家系統**：帳號、等級、經驗值、金幣、統計數據
- **背包系統**：物品數據、魚卡收集、裝備庫存
- **裝備系統**：已裝備的釣竿、魚餌、船隻、服裝
- **製作系統**：配方進度、製作佇列、材料消耗記錄
- **成就系統**：釣魚成就、收集成就、等級里程碑

#### **🎮 遊戲邏輯**
- **遊戲狀態管理**：當前時間、天氣、位置、釣魚進度
- **鑽魚驗證**：防作弊驗證、鑽魚結果計算、稀有度判定
- **商店系統**：物品購買、價格管理、限時優惠
- **社交系統**：比基尼女孩互動記錄、聊天歷史
- **排行榜**：全域玩家排名、競賽結果

#### **⚙️ 業務服務**
- **數據同步**：跨設備進度同步、雲端存檔
- **活動管理**：限時活動、獎勵發放、事件觸發
- **支付系統**：內購驗證、虛擬貨幣管理
- **反作弊系統**：異常檢測、數據驗證

---

### 📱 **客戶端（Unity）**

#### **🎨 遊戲展現**
- **場景渲染**：地圖、水面、天空、天氣效果
- **3D模型**：玩家角色、船隻、魚類、NPC
- **動畫系統**：釣魚動作、水面波動、魚類游泳
- **特效系統**：魚咬餌特效、成功釣魚慶祝、升級特效

#### **🕹️ 小遊戲系統**
- **拋竿小遊戲**：力度控制、軌跡預測、時機判定
- **誘餌小遊戲**：魚餌移動控制、魚影追蹤、節奏輸入
- **收線小遊戲**：張力控制、QTE反應、魚類掙扎
- **輸入處理**：觸控、手勢識別、力度感測

#### **🖥️ UI系統**
- **界面渲染**：背包UI、商店界面、社交面板
- **即時反饋**：血條、經驗條、計時器、提示訊息
- **設定管理**：音效、畫質、控制設定（本地存儲）
- **導航系統**：場景切換、菜單管理

#### **📊 本地數據**
- **快取機制**：遊戲設定、音效偏好、UI狀態
- **離線模式**：基礎練習模式、教學關卡
- **性能優化**：資源預加載、紋理壓縮

---

### 🔄 **通訊協議設計**

#### **即時同步**
```
客戶端 → 伺服器：釣魚結果、位置變更、購買請求
伺服器 → 客戶端：數據更新、活動通知、好友狀態
```

#### **批次同步**
```
遊戲開始：下載玩家數據、背包狀態、好友列表
遊戲結束：上傳遊戲進度、統計數據、成就解鎖
```

---

### 🎯 **核心優勢**

#### **⚡ 性能優化**
- **客戶端**：流暢的3D渲染和即時小遊戲體驗
- **伺服器**：處理大量玩家的數據邏輯和驗證

#### **🛡️ 安全性**
- **防作弊**：關鍵遊戲結果在伺服器驗證
- **數據保護**：玩家進度和虛擬資產安全存儲

#### **📈 擴展性**
- **伺服器**：支援更多玩家、新功能、活動系統
- **客戶端**：專注於遊戲體驗和視覺效果

這個架構分配能夠充分發揮Spring Boot的企業級後端能力和Unity的3D遊戲渲染優勢，同時確保數據安全和遊戲體驗的流暢性。

---

_**User**_

我已經把 server用workspace索引進來，在 @src ；Unity client是@Assets  ，請幫我在實際的分析一下

---

_**Assistant**_



---

我來詳細分析您的Spring Boot server和Unity client的實際架構。讓我先查看這兩個專案的具體結構。

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main • **2** results</summary>
        
| Name |
|-------|
| 📁 `resources` |
| 📁 `java` |

</details>

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main/java • **1** results</summary>
        
| Name |
|-------|
| 📁 `ehooray` |

</details>

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main/java/ehooray • **1** results</summary>
        
| Name |
|-------|
| 📁 `fishing` |

</details>

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main/java/ehooray/fishing • **12** results</summary>
        
| Name |
|-------|
| 📁 `controller` |
| 📁 `service` |
| 📁 `repository` |
| 📁 `entity` |
| 📁 `config` |
| 📁 `log` |
| 📁 `pojo` |
| 📁 `dto` |
| 📁 `component` |
| 📁 `annotation` |
| 📄 `ServletInitializer.java` |
| 📄 `ApplicationMain.java` |

</details>

---

<details>
            <summary>Listed directory ../fishing-client/Assets/Fishing • **20** results</summary>
        
| Name |
|-------|
| 📁 `Tables` |
| 📁 `Resources` |
| 📁 `AAS` |
| 📁 `Scenes` |
| 📁 `Scripts` |
| 📄 `Prefabs.meta` |
| 📄 `Document.meta` |
| 📁 `Document` |
| 📁 `Prefabs` |
| 📁 `Editor` |
| 📁 `Models` |
| 📄 `Tables.meta` |
| 📄 `Scripts.meta` |
| 📄 `Scenes.meta` |
| 📄 `Resources.meta` |
| 📁 `Font` |
| 📄 `Models.meta` |
| 📄 `Font.meta` |
| 📄 `Editor.meta` |
| 📄 `AAS.meta` |

</details>

---

<details>
            <summary>Listed directory ../fishing-client/Assets/Fishing/Scripts • **44** results</summary>
        
| Name |
|-------|
| 📁 `Fish` |
| 📄 `NewFishingGameBootstrap.cs` |
| 📄 `FishingGameBootstrap.cs` |
| 📁 `Battle` |
| 📄 `NewFishingGameBootstrap.cs.meta` |
| 📁 `Installers` |
| 📁 `Utilities` |
| 📄 `Utilities.meta` |
| 📁 `UI` |
| 📄 `UI.meta` |
| 📁 `Tables` |
| 📁 `System` |
| 📄 `Tables.meta` |
| 📁 `SceneLoading` |
| 📄 `System.meta` |
| 📄 `SceneLoading.meta` |
| 📁 `Repository` |
| 📁 `Network` |
| 📄 `Repository.meta` |
| 📁 `NaniCommands` |
| 📄 `Network.meta` |
| 📄 `NaniCommands.meta` |
| 📁 `Lua` |
| 📄 `Lua.meta` |
| 📁 `LocalCache` |
| 📄 `LobbyBootstrap.cs` |
| 📄 `LobbyBootstrap.cs.meta` |
| 📄 `LocalCache.meta` |
| 📁 `LifeCycle` |
| 📄 `LifeCycle.meta` |
| 📄 `Installers.meta` |
| 📁 `General` |
| 📄 `General.meta` |
| 📄 `FishingGameBootstrap.cs.meta` |
| 📁 `Config` |
| 📄 `Fish.meta` |
| 📄 `Config.meta` |
| 📁 `Character` |
| 📁 `Camera` |
| 📄 `Character.meta` |
| 📄 `Camera.meta` |
| 📄 `Battle.meta` |
| 📁 `Ap` |
| 📄 `Ap.meta` |

</details>

---

Could not find file '../FishingServer/workspace/Fishing/src/main/java/ehooray/fishing/entity' in the workspace.

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main/java/ehooray/fishing/entity • **16** results</summary>
        
| Name |
|-------|
| 📄 `PlayerEntity.java` |
| 📄 `ExchangeEntity.java` |
| 📄 `FishItemEntity.java` |
| 📄 `SpotEntity.java` |
| 📄 `ServerInfoEntity.java` |
| 📄 `ShopEntity.java` |
| 📄 `QuestEntity.java` |
| 📄 `QuestTargetEntity.java` |
| 📄 `PlayerItemEntity.java` |
| 📄 `IabTransactionEntity.java` |
| 📄 `PlayerAuthEntity.java` |
| 📄 `FishRodEntity.java` |
| 📄 `GachaEntity.java` |
| 📄 `FishKitEntity.java` |
| 📄 `AchievementEntity.java` |
| 📄 `AEntity.java` |

</details>

---

<details>
            <summary>Listed directory ../FishingServer/workspace/Fishing/src/main/java/ehooray/fishing/controller • **14** results</summary>
        
| Name |
|-------|
| 📁 `quest` |
| 📁 `exchange` |
| 📁 `time` |
| 📁 `shop` |
| 📁 `item` |
| 📁 `player` |
| 📁 `gacha` |
| 📁 `fishkit` |
| 📁 `fishrod` |
| 📁 `auth` |
| 📁 `battle` |
| 📁 `ap` |
| 📄 `HttpProtoHandler.java` |
| 📄 `AuthProtoHandler.java` |

</details>

---

<details>
            <summary>Listed directory ../fishing-client/Assets/Fishing/Scripts/Network • **6** results</summary>
        
| Name |
|-------|
| 📄 `ServiceContainer.cs.meta` |
| 📁 `Service` |
| 📄 `ServiceContainer.cs` |
| 📄 `Service.meta` |
| 📁 `Proto` |
| 📄 `Proto.meta` |

</details>

---

<details>
            <summary>Listed directory ../fishing-client/Assets/Fishing/Scripts/Battle • **42** results</summary>
        
| Name |
|-------|
| 📁 `NewStrategies` |
| 📄 `FishingManager.cs` |
| 📄 `NewFishingStrategyInstaller.cs` |
| 📄 `Fish.cs` |
| 📄 `README.md` |
| 📄 `ThrowGestureDetector.cs` |
| 📄 `NewFishingManager.cs` |
| 📁 `Strategies` |
| 📄 `BattleEquipeData.cs` |
| 📄 `FishingStrategyBase.cs` |
| 📄 `Strategies.meta` |
| 📄 `README.md.meta` |
| 📄 `NewStrategies.meta` |
| 📄 `NewFishingStrategyInstaller.cs.meta` |
| 📄 `IFishingStrategy.cs.meta` |
| 📄 `IFishingStrategy.cs` |
| 📄 `FishingStrategyInstaller.cs.meta` |
| 📄 `FishingStrategyBase.cs.meta` |
| 📄 `FishingStrategyInstaller.cs` |
| 📄 `ThrowGestureDetector.cs.meta` |
| 📄 `NewFishingManager.cs.meta` |
| 📄 `FishRodForceItem.cs` |
| 📄 `ThrowBaitTwoPoint.cs` |
| 📄 `ThrowBaitTwoPoint.cs.meta` |
| 📄 `FishingManager.cs.meta` |
| 📄 `FishingSingleton.cs` |
| 📄 `FishingSingleton.cs.meta` |
| 📄 `FishRodPoint.cs.meta` |
| 📄 `FishRodForceItem.cs.meta` |
| 📄 `FishRodPoint.cs` |
| 📄 `FishLineDummy.cs.meta` |
| 📄 `FishRod.cs` |
| 📄 `FishRod.cs.meta` |
| 📄 `FishLine.cs` |
| 📄 `FishLine.cs.meta` |
| 📄 `FishLineDummy.cs` |
| 📄 `Fish.cs.meta` |
| 📄 `FishData.cs` |
| 📄 `FishData.cs.meta` |
| 📄 `BattleEquipeData.cs.meta` |
| 📄 `BaitLine.cs` |
| 📄 `BaitLine.cs.meta` |

</details>

---

現在讓我查看一些關鍵文件來理解架構：

---

Read file: ../FishingServer/workspace/Fishing/src/main/java/ehooray/fishing/entity/PlayerEntity.java

---

Read file: ../fishing-client/Assets/Fishing/Scripts/Battle/FishingManager.cs

---

Could not find file 'src/scripts/Fishing.js' in the workspace.

---

Read file: src/scripts/FishingMinigames.js

---

Read file: src/scripts/ReelingMiniGame.js

---

Could not find file 'src/data/Equipment.js' in the workspace.

---

Read file: src/data/equipment.json

---

基於對實際專案架構的深入分析，我現在為您提供詳細的**從Phaser釣魚遊戲轉移到Spring Boot + Unity架構**的規劃建議：

## 🎯 釣魚遊戲架構分配詳細規劃

### 📊 **伺服器端（Spring Boot + MySQL）**

#### **🗃️ 核心持久化數據**
```java
// 已經建立的實體架構
PlayerEntity - 玩家基本資料（等級、經驗、金幣）
PlayerItemEntity - 玩家物品背包
FishRodEntity - 釣竿裝備
FishKitEntity - 釣魚套裝
FishItemEntity - 釣到的魚類收藏
SpotEntity - 釣點數據
QuestEntity - 任務系統
AchievementEntity - 成就系統
ShopEntity - 商店交易記錄
GachaEntity - 抽獎記錄
```

#### **🎮 需要新增的Server端邏輯**
1. **戰鬥結算系統**
   - 釣魚結果驗證（防作弊）
   - 經驗值計算與發放
   - 戰利品生成與獎勵
   - 統計數據更新

2. **裝備升級系統**
   - 裝備強化邏輯
   - 材料消耗驗證
   - 屬性數值計算

3. **經濟系統**
   - 物品交易驗證
   - 價格動態調整
   - 金幣流水記錄

4. **排行榜系統**
   - 全服排名計算
   - 季度重置機制

---

### 🎮 **客戶端（Unity）**

#### **🎨 遊戲玩法與表現**
```csharp
// 已建立的Unity架構
FishingManager - 釣魚流程管理
Battle系統 - 戰鬥狀態機
Fish類別 - 魚類行為邏輯
UI管理器群 - 介面控制
網路服務 - 與Server通訊
```

#### **🔄 從Phaser移植的核心系統**
1. **釣魚小遊戲機制**
   ```javascript
   // Phaser -> Unity 移植重點
   CastingMiniGame → Unity投餌小遊戲
   LuringMiniGame → Unity誘餌小遊戲  
   ReelingMiniGame → Unity收線QTE系統
   ```

2. **釣魚狀態管理**
   ```csharp
   // 已有的FishingState枚舉需擴展
   enum FishingState {
       Prepare, BeforeStart, Start, Wait, Fight, Catch, Fail, Win
       // 新增: Casting, Luring, Reeling, QTE
   }
   ```

3. **裝備效果計算**
   ```csharp
   // 參考Phaser的equipment.json結構
   BattleEquipeData.cs 需要擴展:
   - castAccuracy, tensionStability
   - rareFishChance, castingRange
   - reelSpeed, struggleResistance
   ```

---

### 🔄 **關鍵數據流轉設計**

#### **釣魚開始流程**
```
Client → Server: 請求開始釣魚（釣點ID、裝備配置）
Server → Client: 返回戰鬥配置（魚類數據、環境參數）
Client: 執行釣魚小遊戲
Client → Server: 上傳戰鬥結果（QTE成功率、時間等）
Server: 驗證結果 + 計算獎勵
Server → Client: 返回最終獎勵
```

#### **裝備系統流轉**
```
Client: 展示裝備列表（從本地快取）
Client → Server: 裝備強化請求
Server: 驗證材料 + 執行強化
Server → Client: 返回新裝備屬性
Client: 更新本地快取 + UI
```

---

### 📦 **具體移植建議**

#### **🎯 第一階段：核心戰鬥系統**
1. **移植Phaser的ReelingMiniGame.js到Unity**
   - `tension`張力系統
   - `fishStamina`魚類耐力
   - `QTE系統`（tap, hold, sequence, timing）
   - `鬼線斷裂`機制

2. **整合Server端戰鬥驗證**
   ```java
   // 新增BattleController
   @PostMapping("/battle/start")
   @PostMapping("/battle/result") 
   ```

#### **🎯 第二階段：裝備與進度系統**
1. **移植equipment.json到MySQL**
   ```sql
   -- 新增表格
   CREATE TABLE equipment_templates;
   CREATE TABLE player_equipment;
   CREATE TABLE equipment_upgrades;
   ```

2. **建立Unity裝備管理器**
   ```csharp
   // 參考Phaser的裝備效果計算
   public class EquipmentEffectsCalculator
   ```

#### **🎯 第三階段：經濟與社交系統**
1. **商店系統**（Server主導）
2. **排行榜系統**（Server計算）
3. **好友系統**（Server管理）

---

### 🚀 **推薦開發順序**

1. **先完善Spring Boot的Entity層** - 補齊missing的表格結構
2. **建立基礎的戰鬥API** - start/result端點
3. **移植核心釣魚邏輯到Unity** - 重點是ReelingMiniGame
4. **整合網路通訊** - 使用既有的Network/Service架構
5. **逐步添加進階功能** - 裝備、任務、成就等

這樣的架構分配既能發揮Server端數據安全性的優勢，又能保持Unity客戶端的豐富遊戲體驗！

---

