// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/Request.proto

package ehooray.fishing.dto.proto.request;

public final class RequestProto {
  private RequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.Request)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string controller = 1;</code>
     * @return The controller.
     */
    java.lang.String getController();
    /**
     * <code>string controller = 1;</code>
     * @return The bytes for controller.
     */
    com.google.protobuf.ByteString
        getControllerBytes();

    /**
     * <code>string method = 2;</code>
     * @return The method.
     */
    java.lang.String getMethod();
    /**
     * <code>string method = 2;</code>
     * @return The bytes for method.
     */
    com.google.protobuf.ByteString
        getMethodBytes();

    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    java.util.List<com.google.protobuf.Any> 
        getDataListList();
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    com.google.protobuf.Any getDataList(int index);
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    int getDataListCount();
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getDataListOrBuilderList();
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
        int index);

    /**
     * <code>int64 requestId = 4;</code>
     * @return The requestId.
     */
    long getRequestId();
  }
  /**
   * Protobuf type {@code request.Request}
   */
  public static final class Request extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.Request)
      RequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Request.newBuilder() to construct.
    private Request(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Request() {
      controller_ = "";
      method_ = "";
      dataList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Request();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.RequestProto.internal_static_request_Request_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.RequestProto.internal_static_request_Request_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.RequestProto.Request.class, ehooray.fishing.dto.proto.request.RequestProto.Request.Builder.class);
    }

    public static final int CONTROLLER_FIELD_NUMBER = 1;
    private volatile java.lang.Object controller_;
    /**
     * <code>string controller = 1;</code>
     * @return The controller.
     */
    @java.lang.Override
    public java.lang.String getController() {
      java.lang.Object ref = controller_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        controller_ = s;
        return s;
      }
    }
    /**
     * <code>string controller = 1;</code>
     * @return The bytes for controller.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getControllerBytes() {
      java.lang.Object ref = controller_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        controller_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int METHOD_FIELD_NUMBER = 2;
    private volatile java.lang.Object method_;
    /**
     * <code>string method = 2;</code>
     * @return The method.
     */
    @java.lang.Override
    public java.lang.String getMethod() {
      java.lang.Object ref = method_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        method_ = s;
        return s;
      }
    }
    /**
     * <code>string method = 2;</code>
     * @return The bytes for method.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMethodBytes() {
      java.lang.Object ref = method_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        method_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATALIST_FIELD_NUMBER = 3;
    private java.util.List<com.google.protobuf.Any> dataList_;
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.Any> getDataListList() {
      return dataList_;
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getDataListOrBuilderList() {
      return dataList_;
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    @java.lang.Override
    public int getDataListCount() {
      return dataList_.size();
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    @java.lang.Override
    public com.google.protobuf.Any getDataList(int index) {
      return dataList_.get(index);
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 3;</code>
     */
    @java.lang.Override
    public com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
        int index) {
      return dataList_.get(index);
    }

    public static final int REQUESTID_FIELD_NUMBER = 4;
    private long requestId_;
    /**
     * <code>int64 requestId = 4;</code>
     * @return The requestId.
     */
    @java.lang.Override
    public long getRequestId() {
      return requestId_;
    }

    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.RequestProto.Request parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.RequestProto.Request prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.Request}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.Request)
        ehooray.fishing.dto.proto.request.RequestProto.RequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.RequestProto.internal_static_request_Request_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.RequestProto.internal_static_request_Request_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.RequestProto.Request.class, ehooray.fishing.dto.proto.request.RequestProto.Request.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.RequestProto.Request.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        controller_ = "";

        method_ = "";

        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dataListBuilder_.clear();
        }
        requestId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.RequestProto.internal_static_request_Request_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.RequestProto.Request getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.RequestProto.Request.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.RequestProto.Request build() {
        ehooray.fishing.dto.proto.request.RequestProto.Request result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.RequestProto.Request buildPartial() {
        ehooray.fishing.dto.proto.request.RequestProto.Request result = new ehooray.fishing.dto.proto.request.RequestProto.Request(this);
        int from_bitField0_ = bitField0_;
        result.controller_ = controller_;
        result.method_ = method_;
        if (dataListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            dataList_ = java.util.Collections.unmodifiableList(dataList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.dataList_ = dataList_;
        } else {
          result.dataList_ = dataListBuilder_.build();
        }
        result.requestId_ = requestId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private java.lang.Object controller_ = "";
      /**
       * <code>string controller = 1;</code>
       * @return The controller.
       */
      public java.lang.String getController() {
        java.lang.Object ref = controller_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          controller_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string controller = 1;</code>
       * @return The bytes for controller.
       */
      public com.google.protobuf.ByteString
          getControllerBytes() {
        java.lang.Object ref = controller_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          controller_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string controller = 1;</code>
       * @param value The controller to set.
       * @return This builder for chaining.
       */
      public Builder setController(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        controller_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string controller = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearController() {
        
        controller_ = getDefaultInstance().getController();
        onChanged();
        return this;
      }
      /**
       * <code>string controller = 1;</code>
       * @param value The bytes for controller to set.
       * @return This builder for chaining.
       */
      public Builder setControllerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        controller_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object method_ = "";
      /**
       * <code>string method = 2;</code>
       * @return The method.
       */
      public java.lang.String getMethod() {
        java.lang.Object ref = method_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          method_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string method = 2;</code>
       * @return The bytes for method.
       */
      public com.google.protobuf.ByteString
          getMethodBytes() {
        java.lang.Object ref = method_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          method_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string method = 2;</code>
       * @param value The method to set.
       * @return This builder for chaining.
       */
      public Builder setMethod(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        method_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string method = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMethod() {
        
        method_ = getDefaultInstance().getMethod();
        onChanged();
        return this;
      }
      /**
       * <code>string method = 2;</code>
       * @param value The bytes for method to set.
       * @return This builder for chaining.
       */
      public Builder setMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        method_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.Any> dataList_ =
        java.util.Collections.emptyList();
      private void ensureDataListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dataList_ = new java.util.ArrayList<com.google.protobuf.Any>(dataList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> dataListBuilder_;

      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public java.util.List<com.google.protobuf.Any> getDataListList() {
        if (dataListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dataList_);
        } else {
          return dataListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public int getDataListCount() {
        if (dataListBuilder_ == null) {
          return dataList_.size();
        } else {
          return dataListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public com.google.protobuf.Any getDataList(int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);
        } else {
          return dataListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder setDataList(
          int index, com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.set(index, value);
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder setDataList(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder addDataList(com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder addDataList(
          int index, com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(index, value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder addDataList(
          com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder addDataList(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder addAllDataList(
          java.lang.Iterable<? extends com.google.protobuf.Any> values) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dataList_);
          onChanged();
        } else {
          dataListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder clearDataList() {
        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dataListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public Builder removeDataList(int index) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.remove(index);
          onChanged();
        } else {
          dataListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public com.google.protobuf.Any.Builder getDataListBuilder(
          int index) {
        return getDataListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
          int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);  } else {
          return dataListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
           getDataListOrBuilderList() {
        if (dataListBuilder_ != null) {
          return dataListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dataList_);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public com.google.protobuf.Any.Builder addDataListBuilder() {
        return getDataListFieldBuilder().addBuilder(
            com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public com.google.protobuf.Any.Builder addDataListBuilder(
          int index) {
        return getDataListFieldBuilder().addBuilder(
            index, com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 3;</code>
       */
      public java.util.List<com.google.protobuf.Any.Builder> 
           getDataListBuilderList() {
        return getDataListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getDataListFieldBuilder() {
        if (dataListBuilder_ == null) {
          dataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  dataList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          dataList_ = null;
        }
        return dataListBuilder_;
      }

      private long requestId_ ;
      /**
       * <code>int64 requestId = 4;</code>
       * @return The requestId.
       */
      @java.lang.Override
      public long getRequestId() {
        return requestId_;
      }
      /**
       * <code>int64 requestId = 4;</code>
       * @param value The requestId to set.
       * @return This builder for chaining.
       */
      public Builder setRequestId(long value) {
        
        requestId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 requestId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestId() {
        
        requestId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.Request)
    }

    // @@protoc_insertion_point(class_scope:request.Request)
    private static final ehooray.fishing.dto.proto.request.RequestProto.Request DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.RequestProto.Request();
    }

    public static ehooray.fishing.dto.proto.request.RequestProto.Request getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Request>
        PARSER = new com.google.protobuf.AbstractParser<Request>() {
      @java.lang.Override
      public Request parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Request> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Request> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.RequestProto.Request getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_Request_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_Request_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025request/Request.proto\022\007request\032\031google" +
      "/protobuf/any.proto\"h\n\007Request\022\022\n\ncontro" +
      "ller\030\001 \001(\t\022\016\n\006method\030\002 \001(\t\022&\n\010dataList\030\003" +
      " \003(\0132\024.google.protobuf.Any\022\021\n\trequestId\030" +
      "\004 \001(\003BK\n!ehooray.fishing.dto.proto.reque" +
      "stB\014RequestProtoH\002\252\002\025Ehooray.Network.Pro" +
      "tob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
        });
    internal_static_request_Request_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_Request_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_Request_descriptor,
        new java.lang.String[] { "Controller", "Method", "DataList", "RequestId", });
    com.google.protobuf.AnyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
