// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/FishRod.proto

package ehooray.fishing.dto.proto.model;

public final class FishRodProto {
  private FishRodProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FishRodOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.FishRod)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>int32 strength = 3;</code>
     * @return The strength.
     */
    int getStrength();

    /**
     * <code>int32 control = 4;</code>
     * @return The control.
     */
    int getControl();

    /**
     * <code>int32 lucky = 5;</code>
     * @return The lucky.
     */
    int getLucky();

    /**
     * <code>int32 dqr = 6;</code>
     * @return The dqr.
     */
    int getDqr();

    /**
     * <code>int32 times = 7;</code>
     * @return The times.
     */
    int getTimes();

    /**
     * <code>bool isLock = 8;</code>
     * @return The isLock.
     */
    boolean getIsLock();

    /**
     * <code>int32 level = 9;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code model.FishRod}
   */
  public static final class FishRod extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.FishRod)
      FishRodOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishRod.newBuilder() to construct.
    private FishRod(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishRod() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishRod();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.FishRodProto.internal_static_model_FishRod_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.FishRodProto.internal_static_model_FishRod_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.class, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int STRENGTH_FIELD_NUMBER = 3;
    private int strength_;
    /**
     * <code>int32 strength = 3;</code>
     * @return The strength.
     */
    @java.lang.Override
    public int getStrength() {
      return strength_;
    }

    public static final int CONTROL_FIELD_NUMBER = 4;
    private int control_;
    /**
     * <code>int32 control = 4;</code>
     * @return The control.
     */
    @java.lang.Override
    public int getControl() {
      return control_;
    }

    public static final int LUCKY_FIELD_NUMBER = 5;
    private int lucky_;
    /**
     * <code>int32 lucky = 5;</code>
     * @return The lucky.
     */
    @java.lang.Override
    public int getLucky() {
      return lucky_;
    }

    public static final int DQR_FIELD_NUMBER = 6;
    private int dqr_;
    /**
     * <code>int32 dqr = 6;</code>
     * @return The dqr.
     */
    @java.lang.Override
    public int getDqr() {
      return dqr_;
    }

    public static final int TIMES_FIELD_NUMBER = 7;
    private int times_;
    /**
     * <code>int32 times = 7;</code>
     * @return The times.
     */
    @java.lang.Override
    public int getTimes() {
      return times_;
    }

    public static final int ISLOCK_FIELD_NUMBER = 8;
    private boolean isLock_;
    /**
     * <code>bool isLock = 8;</code>
     * @return The isLock.
     */
    @java.lang.Override
    public boolean getIsLock() {
      return isLock_;
    }

    public static final int LEVEL_FIELD_NUMBER = 9;
    private int level_;
    /**
     * <code>int32 level = 9;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.FishRodProto.FishRod prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.FishRod}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.FishRod)
        ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.FishRodProto.internal_static_model_FishRod_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.FishRodProto.internal_static_model_FishRod_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.FishRodProto.FishRod.class, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.FishRodProto.FishRod.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        csvId_ = 0;

        strength_ = 0;

        control_ = 0;

        lucky_ = 0;

        dqr_ = 0;

        times_ = 0;

        isLock_ = false;

        level_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.FishRodProto.internal_static_model_FishRod_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod build() {
        ehooray.fishing.dto.proto.model.FishRodProto.FishRod result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod buildPartial() {
        ehooray.fishing.dto.proto.model.FishRodProto.FishRod result = new ehooray.fishing.dto.proto.model.FishRodProto.FishRod(this);
        result.entityId_ = entityId_;
        result.csvId_ = csvId_;
        result.strength_ = strength_;
        result.control_ = control_;
        result.lucky_ = lucky_;
        result.dqr_ = dqr_;
        result.times_ = times_;
        result.isLock_ = isLock_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private int strength_ ;
      /**
       * <code>int32 strength = 3;</code>
       * @return The strength.
       */
      @java.lang.Override
      public int getStrength() {
        return strength_;
      }
      /**
       * <code>int32 strength = 3;</code>
       * @param value The strength to set.
       * @return This builder for chaining.
       */
      public Builder setStrength(int value) {
        
        strength_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 strength = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStrength() {
        
        strength_ = 0;
        onChanged();
        return this;
      }

      private int control_ ;
      /**
       * <code>int32 control = 4;</code>
       * @return The control.
       */
      @java.lang.Override
      public int getControl() {
        return control_;
      }
      /**
       * <code>int32 control = 4;</code>
       * @param value The control to set.
       * @return This builder for chaining.
       */
      public Builder setControl(int value) {
        
        control_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 control = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearControl() {
        
        control_ = 0;
        onChanged();
        return this;
      }

      private int lucky_ ;
      /**
       * <code>int32 lucky = 5;</code>
       * @return The lucky.
       */
      @java.lang.Override
      public int getLucky() {
        return lucky_;
      }
      /**
       * <code>int32 lucky = 5;</code>
       * @param value The lucky to set.
       * @return This builder for chaining.
       */
      public Builder setLucky(int value) {
        
        lucky_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 lucky = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLucky() {
        
        lucky_ = 0;
        onChanged();
        return this;
      }

      private int dqr_ ;
      /**
       * <code>int32 dqr = 6;</code>
       * @return The dqr.
       */
      @java.lang.Override
      public int getDqr() {
        return dqr_;
      }
      /**
       * <code>int32 dqr = 6;</code>
       * @param value The dqr to set.
       * @return This builder for chaining.
       */
      public Builder setDqr(int value) {
        
        dqr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 dqr = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDqr() {
        
        dqr_ = 0;
        onChanged();
        return this;
      }

      private int times_ ;
      /**
       * <code>int32 times = 7;</code>
       * @return The times.
       */
      @java.lang.Override
      public int getTimes() {
        return times_;
      }
      /**
       * <code>int32 times = 7;</code>
       * @param value The times to set.
       * @return This builder for chaining.
       */
      public Builder setTimes(int value) {
        
        times_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 times = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimes() {
        
        times_ = 0;
        onChanged();
        return this;
      }

      private boolean isLock_ ;
      /**
       * <code>bool isLock = 8;</code>
       * @return The isLock.
       */
      @java.lang.Override
      public boolean getIsLock() {
        return isLock_;
      }
      /**
       * <code>bool isLock = 8;</code>
       * @param value The isLock to set.
       * @return This builder for chaining.
       */
      public Builder setIsLock(boolean value) {
        
        isLock_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isLock = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsLock() {
        
        isLock_ = false;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>int32 level = 9;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>int32 level = 9;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 level = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.FishRod)
    }

    // @@protoc_insertion_point(class_scope:model.FishRod)
    private static final ehooray.fishing.dto.proto.model.FishRodProto.FishRod DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.FishRodProto.FishRod();
    }

    public static ehooray.fishing.dto.proto.model.FishRodProto.FishRod getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishRod>
        PARSER = new com.google.protobuf.AbstractParser<FishRod>() {
      @java.lang.Override
      public FishRod parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FishRod> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishRod> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_FishRod_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_FishRod_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023model/FishRod.proto\022\005model\"\227\001\n\007FishRod" +
      "\022\020\n\010entityId\030\001 \001(\003\022\r\n\005csvId\030\002 \001(\005\022\020\n\010str" +
      "ength\030\003 \001(\005\022\017\n\007control\030\004 \001(\005\022\r\n\005lucky\030\005 " +
      "\001(\005\022\013\n\003dqr\030\006 \001(\005\022\r\n\005times\030\007 \001(\005\022\016\n\006isLoc" +
      "k\030\010 \001(\010\022\r\n\005level\030\t \001(\005BI\n\037ehooray.fishin" +
      "g.dto.proto.modelB\014FishRodProtoH\002\252\002\025Fish" +
      "ing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_FishRod_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_FishRod_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_FishRod_descriptor,
        new java.lang.String[] { "EntityId", "CsvId", "Strength", "Control", "Lucky", "Dqr", "Times", "IsLock", "Level", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
