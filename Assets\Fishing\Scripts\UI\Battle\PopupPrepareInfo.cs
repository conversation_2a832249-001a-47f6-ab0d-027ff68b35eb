using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Fishing
{
    public class PopupPrepareInfo : MonoBehaviour
    {
        public Text NameText;
        public Text TimesText;

        public GameObject Strength;
        public GameObject Control;
        public GameObject Lucky;
        public GameObject Dqr;
        public GameObject Size;
        public GameObject Rare;
        public GameObject Length;
        public GameObject Skill;

        [Header("Active Rod Value")]
        public Text ActiveStrengthText;
        public Text ActiveControlText;
        public Text ActiveLuckyText;
        public Text ActiveDqrText;

        [Header("Current Rod Value")]
        public Text StrengthText;
        public Text ControlText;
        public Text LuckyText;
        public Text DqrText;

        [Header("Active Item Value")]
        public Text ActiveSizeText;
        public Text ActiveRareText;
        public Text ActiveLengthText;
        public Text ActiveSkillText;

        [Header("Current Item Value")]
        public Text SizeText;
        public Text RareText;
        public Text LengthText;
        public Text SkillText;

        [Header("Buttons")]
        public Button RepairBtn;
        public Button ActiveKitBtn;

        [Header("Color")]
        public Color red;
        public Color green;
        public Color whitle;


        private FishRodData nowRod;
        private FishRodData activeRod;
        private PlayerItem nowItem;
        private PlayerItem activeItem;

        #region Zenject Inject
        FishingRepositories fishingRepositories;
        FishingContainer container;
        FishingGamePanelManager fishingGamePanelManager;
        BattleEquipeData battleEquipeData;
        TextIDConfig textIDConfig;
        #endregion

        [Inject]
        public void Construct(FishingRepositories fishingRepositories,
                                FishingContainer container,
                                FishingGamePanelManager fishingGamePanelManager,
                                BattleEquipeData battleEquipeData,
                                TextIDConfig textIDConfig)
        {
            this.fishingRepositories = fishingRepositories;
            this.container = container;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.battleEquipeData = battleEquipeData;
            this.textIDConfig = textIDConfig;
        }
        public void RefreshRodInfo(FishRodData data)
        {
            Debug.Log($"Select Rod Item.");
            RemovedActiveButtonEvent();
            ResetNowSelect();
            nowRod = data;
            SetActiveRodInfo();
            var rodName = container.tableContainer.LanguageTable.GetData(data.CsvData.Name).Content;
            var levelString = data.level == 0 ? "" : $" +{data.level}";
            NameText.text = $"{rodName}{levelString}";
            TimesText.text = $"{data.times}/{data.CsvData.Times}";

            StrengthText.text = data.strength.ToString();
            ControlText.text = data.control.ToString();
            LuckyText.text = data.lucky.ToString();
            DqrText.text = data.dqr.ToString();
            CheckRodValue(data);
            ActiveKitBtn.onClick.AddListener(ActiveRod);
            RepairBtn.onClick.AddListener(Repair);

            var activeRod = fishingRepositories.fishRodRepository.GetFishRod(GetActiveKit().fishRodEntityId);
            if (activeRod.entityId == data.entityId)
            {
                ActiveKitBtn.interactable = false;
                ActiveKitBtn.image.color = new Color(1,1,1, 150f / 255f);
                var activeEquipment = container.tableContainer.LanguageTable.GetData(textIDConfig.ActiveEquipment).Content;
                ActiveKitBtn.GetComponentInChildren<Text>().text = activeEquipment;
            }
            else
            {
                ActiveKitBtn.interactable = true;
                ActiveKitBtn.image.color = new Color(1, 1, 1, 1);
                var kit = container.tableContainer.LanguageTable.GetData(textIDConfig.Kit).Content;
                ActiveKitBtn.GetComponentInChildren<Text>().text = kit;
            }
        }
        public void RefreshLineInfo(PlayerItem data)
        {
            RefreshItemInfo(ItemTableDataExtension.ItemTag.Fishline, data);

        }
        public void RefreshBaitInfo(PlayerItem data)
        {
            RefreshItemInfo(ItemTableDataExtension.ItemTag.Bait, data);
        }

        public void RefreshItemInfo(ItemTableDataExtension.ItemTag type, PlayerItem data)
        {
            Debug.Log($"Select {type.ToString()} Item.");
            RemovedActiveButtonEvent();
            ResetNowSelect();
            nowItem = data;
            if (type == ItemTableDataExtension.ItemTag.Bait)
            {
                SetActiveBait();
                SizeText.text = $"+{(float)data.CsvData.Parameter_1 / (float)10}%";
                RareText.text = $"+{(float)data.CsvData.Parameter_2 / (float)10}%";
                LengthText.text = $"0M";
                SkillText.text = $"+0%";
                CheckBaitValue(data);
                var activeBait = fishingRepositories.playerItemRepository.GetPlayerItem(GetActiveKit().baitCsvId);
                if (activeBait.csvId == data.csvId)
                {
                    ActiveKitBtn.interactable = false;
                    ActiveKitBtn.image.color = new Color(1,1,1, 150f / 255f);
                    var activeEquipment = container.tableContainer.LanguageTable.GetData(textIDConfig.ActiveEquipment).Content;
                    ActiveKitBtn.GetComponentInChildren<Text>().text = activeEquipment;
                }
                else
                {
                    ActiveKitBtn.interactable = true;
                    ActiveKitBtn.image.color = new Color(1, 1, 1, 1);
                    var kit = container.tableContainer.LanguageTable.GetData(textIDConfig.Kit).Content;
                    ActiveKitBtn.GetComponentInChildren<Text>().text = kit;
                }
            }
            else if (type == ItemTableDataExtension.ItemTag.Fishline)
            {
                SetActiveFishLine();
                LengthText.text = $"{data.CsvData.Parameter_1}M";
                SkillText.text = $"+{(float)data.CsvData.Parameter_2 / (float)10}%";
                SizeText.text = $"+0%";
                RareText.text = $"+0%";
                CheckFishlineValue(data);
                var activeLine = fishingRepositories.playerItemRepository.GetPlayerItem(GetActiveKit().fishLineCsvId);
                if (activeLine.csvId == data.csvId)
                {
                    ActiveKitBtn.interactable = false;
                    ActiveKitBtn.image.color = new Color(1,1,1, 150f / 255f);
                    var activeEquipment = container.tableContainer.LanguageTable.GetData(textIDConfig.ActiveEquipment).Content;
                    ActiveKitBtn.GetComponentInChildren<Text>().text = activeEquipment;
                }
                else
                {
                    ActiveKitBtn.interactable = true;
                    ActiveKitBtn.image.color = new Color(1, 1, 1, 1);
                    var kit = container.tableContainer.LanguageTable.GetData(textIDConfig.Kit).Content;
                    ActiveKitBtn.GetComponentInChildren<Text>().text = kit;
                }
            }
            var rodName = container.tableContainer.LanguageTable.GetData(data.CsvData.Name).Content;
            NameText.text = rodName;
            TimesText.text = data.finalCount.ToString();
            ActiveKitBtn.onClick.AddListener(ActiveItem);
        }

        private void CheckRodValue(FishRodData currentRod)
        {
            StrengthText.color = whitle;
            ControlText.color = whitle;
            LuckyText.color = whitle;
            DqrText.color = whitle;

            if (activeRod.strength > currentRod.strength)
                StrengthText.color = red;
            else if (activeRod.strength < currentRod.strength)
                StrengthText.color = green;

            if (activeRod.control > currentRod.control)
                ControlText.color = red;
            else if (activeRod.control < currentRod.control)
                ControlText.color = green;

            if (activeRod.lucky > currentRod.lucky)
                LuckyText.color = red;
            else if (activeRod.lucky < currentRod.lucky)
                LuckyText.color = green;

            if (activeRod.dqr > currentRod.dqr)
                DqrText.color = red;
            else if (activeRod.dqr < currentRod.dqr)
                DqrText.color = green;
        }
        private void CheckFishlineValue(PlayerItem currentItem)
        {
            SizeText.color = whitle;
            RareText.color = whitle;
            LengthText.color = whitle;
            SkillText.color = whitle;


            if (activeItem.CsvData.Parameter_1 > currentItem.CsvData.Parameter_1)
                LengthText.color = red;
            else if (activeItem.CsvData.Parameter_1 < currentItem.CsvData.Parameter_1)
                LengthText.color = green;

            if (activeItem.CsvData.Parameter_2 > currentItem.CsvData.Parameter_2)
                SkillText.color = red;
            else if (activeItem.CsvData.Parameter_2 < currentItem.CsvData.Parameter_2)
                SkillText.color = green;
        }
        private void CheckBaitValue(PlayerItem currentItem)
        {
            SizeText.color = whitle;
            RareText.color = whitle;
            LengthText.color = whitle;
            SkillText.color = whitle;

            if (activeItem.CsvData.Parameter_1 > currentItem.CsvData.Parameter_1)
                SizeText.color = red;
            else if (activeItem.CsvData.Parameter_1 < currentItem.CsvData.Parameter_1)
                SizeText.color = green;

            if (activeItem.CsvData.Parameter_2 > currentItem.CsvData.Parameter_2)
                RareText.color = red;
            else if (activeItem.CsvData.Parameter_2 < currentItem.CsvData.Parameter_2)
                RareText.color = green;
        }
        private void SetActiveRodInfo()
        {
            FishKitData activeKit = GetActiveKit();
            activeRod = fishingRepositories.fishRodRepository.GetFishRod(activeKit.fishRodEntityId);
            if (activeRod == null)
            {
                Debug.Log("active rod is null");
                return;
            }
            ActiveStrengthText.text = activeRod.strength.ToString();
            ActiveControlText.text = activeRod.control.ToString();
            ActiveLuckyText.text = activeRod.lucky.ToString();
            ActiveDqrText.text = activeRod.dqr.ToString();

            Strength.SetActive(true);
            Control.SetActive(true);
            Lucky.SetActive(true);
            Dqr.SetActive(true);
            Size.SetActive(false);
            Rare.SetActive(false);
            Length.SetActive(false);
            Skill.SetActive(false);
        }
        private void SetActiveFishLine()
        {
            FishKitData activeKit = GetActiveKit();
            activeItem = fishingRepositories.playerItemRepository.GetPlayerItem(activeKit.fishLineCsvId);
            if (activeItem == null)
            {
                Debug.Log("active rod is null");
                return;
            }
            ActiveLengthText.text = $"{activeItem.CsvData.Parameter_1}M";
            ActiveSkillText.text = $"+{(float)activeItem.CsvData.Parameter_2 / (float)10}%";

            Strength.SetActive(false);
            Control.SetActive(false);
            Lucky.SetActive(false);
            Dqr.SetActive(false);
            Size.SetActive(false);
            Rare.SetActive(false);
            Length.SetActive(true);
            Skill.SetActive(true);
        }
        private void SetActiveBait()
        {
            FishKitData activeKit = GetActiveKit();
            activeItem = fishingRepositories.playerItemRepository.GetPlayerItem(activeKit.baitCsvId);
            if (activeItem == null)
            {
                Debug.Log("active rod is null");
                return;
            }
            ActiveSizeText.text = $"+{(float)activeItem.CsvData.Parameter_1 / (float)10}%";
            ActiveRareText.text = $"+{(float)activeItem.CsvData.Parameter_2 / (float)10}%";

            Strength.SetActive(false);
            Control.SetActive(false);
            Lucky.SetActive(false);
            Dqr.SetActive(false);
            Size.SetActive(true);
            Rare.SetActive(true);
            Length.SetActive(false);
            Skill.SetActive(false);
        }
        public void ActiveRod()
        {
            //Debug.Log("Active rod");
            var runner = UpdateFishRodRunner();
            runner.RunAsync(showMask: true).ContinueWith(() =>
            {
                //bagPanel.IsShowInfo(false);
            });
        }

        private AsyncProgressRunner UpdateFishRodRunner()
        {
            var runner = container.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                await UpdateFishRodAsync();
                //bagPanel.SetActiveKit();
            });
            return runner;
        }

        private async UniTask UpdateFishRodAsync()
        {
            var updateFishKit = new Network.Proto.UpdateFishKit();
            updateFishKit.FishKitId = GetActiveKit().entityId;
            updateFishKit.FishRodEntityId = nowRod.entityId;
            updateFishKit.FishLineCsvId = GetActiveKit().fishLineCsvId;
            updateFishKit.BaitCsvId = GetActiveKit().baitCsvId;
            await container.networkService.fishKitService.UpdateFishKit(updateFishKit);
            RefreshRodInfo(nowRod);
            RefreshPopupPrepareCanvas(true);
            RefreshPopupPrepareBtnList();
        }
        public void ActiveItem()
        {
            Debug.Log("Active item");
            var runner = UpdateFishItemRunner();
            runner.RunAsync(showMask: true).ContinueWith(() =>
            {
                //bagPanel.IsShowInfo(false);
            });
        }

        private AsyncProgressRunner UpdateFishItemRunner()
        {
            var runner = container.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                await UpdateFishItemAsync();
                //bagPanel.SetActiveKit();
            });
            return runner;
        }

        private async UniTask UpdateFishItemAsync()
        {
            var updateFishKit = new Network.Proto.UpdateFishKit();
            updateFishKit.FishKitId = GetActiveKit().entityId;
            updateFishKit.FishRodEntityId = GetActiveKit().fishRodEntityId;

            if ((ItemTableDataExtension.ItemTag)activeItem.CsvData.Tag == ItemTableDataExtension.ItemTag.Fishline)
                updateFishKit.FishLineCsvId = nowItem.csvId;
            else
                updateFishKit.FishLineCsvId = GetActiveKit().fishLineCsvId;

            if ((ItemTableDataExtension.ItemTag)activeItem.CsvData.Tag == ItemTableDataExtension.ItemTag.Bait)
                updateFishKit.BaitCsvId = nowItem.csvId;
            else
                updateFishKit.BaitCsvId = GetActiveKit().baitCsvId;
            await container.networkService.fishKitService.UpdateFishKit(updateFishKit);
            RefreshItemInfo((ItemTableDataExtension.ItemTag)activeItem.CsvData.Tag, nowItem);
            RefreshPopupPrepareCanvas(false);
            RefreshPopupPrepareBtnList();
        }
        public void Repair()
        {
            var repairTimes = nowRod.CsvData.Times - nowRod.times;
            if (repairTimes <= 0)
            {
                var maxTimes = container.tableContainer.LanguageTable.GetData(textIDConfig.MaxTimes).Content;
                fishingGamePanelManager.Remind(maxTimes);
                return;
            }
            var money = fishingRepositories.playerItemRepository.GetGameCoin();
            if (money.finalCount < repairTimes * nowRod.CsvData.Perfee)
            {
                var notEnoughGameCoin = container.tableContainer.LanguageTable.GetData(textIDConfig.NotEnoughGameCoin).Content;
                fishingGamePanelManager.Remind(notEnoughGameCoin);
                return;
            }
            Debug.Log("Repair rod");
            var runner = RepairFishRodRunner();
            runner.RunAsync(showMask: true).ContinueWith(() =>
            {
                //bagPanel.IsShowInfo(false);
            });
        }

        private AsyncProgressRunner RepairFishRodRunner()
        {
            var runner = container.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                await RepairFishRodAsync();
                //bagPanel.SetActiveKit();
            });
            return runner;
        }

        private async UniTask RepairFishRodAsync()
        {
            var repairRod = new Network.Proto.RepairFishRod();
            repairRod.EntityId = nowRod.entityId;
            await container.networkService.fishRodService.RepairFishRod(repairRod);
            RefreshRodInfo(nowRod);
            RefreshPopupPrepareCanvas(false);
        }
        private void ResetNowSelect()
        {
            nowRod = null;
            nowItem = null;
        }
        public void RefreshPopupPrepareCanvas(bool refreshModel)
        {
            if (fishingGamePanelManager.TryGetGamePanel(out PrepareManager panel))
            {
                battleEquipeData.SetEquipData();
                if (refreshModel) battleEquipeData.LoadRodModel();
                panel.RefreshIcon();
            }
        }
        public void RefreshPopupPrepareBtnList()
        {
            if (fishingGamePanelManager.TryGetGamePanel(out PopupPrepare panel))
            {
                panel.Refresh();
            }
        }
        public void RemovedActiveButtonEvent()
        {
            ActiveKitBtn.onClick.RemoveAllListeners();
            RepairBtn.onClick.RemoveAllListeners();
        }
        public FishKitData GetActiveKit()
        {
            var fishKits = fishingRepositories.fishKitRepository.GetFishKits();
            foreach (var kit in fishKits.Values)
            {
                if (kit.isActive)
                    return kit;
            }
            Debug.Log("no active Kit");
            return null;
        }
    }
}

