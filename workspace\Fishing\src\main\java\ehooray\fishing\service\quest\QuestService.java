package ehooray.fishing.service.quest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.ProtoHandleMediatorContainer;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.trigger.ConditionComponentTrigger;
import ehooray.fishing.config.DirtyFlagEnum;
import ehooray.fishing.entity.AchievementEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.QuestEntity;
import ehooray.fishing.entity.QuestTargetEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.QuestCsv;
import ehooray.fishing.pojo.csv.QuestTargetCsv;
import ehooray.fishing.pojo.event.AwardQuestEvent;
import ehooray.fishing.pojo.event.BuyShopItemEvent;
import ehooray.fishing.pojo.event.DiffItemEvent;
import ehooray.fishing.pojo.event.FishItemEvent;
import ehooray.fishing.pojo.event.GachaCountEvent;
import ehooray.fishing.pojo.event.PlayerLevelUpEvent;
import ehooray.fishing.pojo.event.UpgradeRodEvent;
import ehooray.fishing.repository.AchievementRepository;
import ehooray.fishing.repository.QuestRepository;
import ehooray.fishing.repository.QuestTargetRepository;
import ehooray.fishing.service.item.PlayerItemService;

@Service
public class QuestService
{
    public enum OperatorLogic
    {
        Assign,
        Plus,
        Minus
    }
    
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected ConditionComponentTrigger conditionTrigger;
    
    @Autowired
    protected ProtoHandleMediatorContainer mediatorContainer;
    
    @Autowired
    protected QuestRepository questRepository;
    
    @Autowired
    protected QuestTargetRepository questTargetRepository;
    
    @Autowired
    protected AchievementRepository achievementRepository;
    
    @Autowired
    protected PlayerItemService playerItemService;
    
    @Autowired
    protected ApplicationEventPublisher publisher;
    
    public List<QuestEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getQuests();
    }
    
    public Optional<QuestEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }
    
    public Optional<QuestEntity> findByCsvId(PlayerEntity player, int csvId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getCsvId() == csvId).findFirst();
    }

    public void newQuest(PlayerEntity player, QuestCsv.Row questRow, LocalDateTime now) throws Exception
    {
        List<QuestTargetEntity> targets = new ArrayList<QuestTargetEntity>();
        
        QuestEntity quest = new QuestEntity();
        quest.setCsvId(questRow.getId());
        quest.setIsAward(false);
        quest.setTargets(targets);
        quest.setStartTime(now);
        quest.setPlayer(player);
        
        QuestTargetCsv targetCsv = csvContainer.getCsv(QuestTargetCsv.class);
        List<QuestTargetCsv.Row> targeRows = targetCsv.findAllByGroup(questRow.getQuestTarget());
        for (QuestTargetCsv.Row targetRow : targeRows)
        {
            QuestTargetEntity targetEntity = new QuestTargetEntity();
            targetEntity.setQuest(quest);
            targetEntity.setCsvId(targetRow.getId());
            targetEntity.setTargetCount(getNewTargetCount(player, targetRow));
            quest.getTargets().add(targetEntity);
        }
        
        getAllByPlayer(player).add(quest);
        questRepository.save(quest);
        for(QuestTargetEntity targetEnitity : quest.getTargets())
            questTargetRepository.save(targetEnitity);
    }
    
    public void deleteAll(PlayerEntity player, List<QuestEntity> questEntities)
    {
        for(var questEntity : questEntities)
        {
            var targets = questEntity.getTargets();
            questTargetRepository.deleteAll(targets);
        }
        
        getAllByPlayer(player).removeAll(questEntities);
        questRepository.deleteAll(questEntities);
    }
    
    public void awardQuest(PlayerEntity player, QuestEntity quest) throws Exception
    {
        checkAwardQuest(player, quest);
        
        quest.setIsAward(true);
        questRepository.save(quest);
        
        publisher.publishEvent(new AwardQuestEvent(this, player, quest));
    }
    
    public List<PlayerItemService.DiffItem> consumeHaveItemTargets(PlayerEntity player, QuestEntity quest) throws Exception
    {
    	var result = new ArrayList<PlayerItemService.DiffItem>();
    	
    	for (var target : quest.getTargets())
    	{
    		QuestTargetCsv.Row targetCsvRow = csvContainer.getCsv(QuestTargetCsv.class).getById(target.getCsvId());
    		if (targetCsvRow.getQuestType() != QuestTargetCsv.QuestType.HaveItem.getId())
    			continue;
    		PlayerItemCsv.Row playerItemRow = csvContainer.getCsv(PlayerItemCsv.class).getById(targetCsvRow.getParameter1());
    		var diffItem = playerItemService.consumeItem(player, playerItemRow, targetCsvRow.getParameter2());
    		result.add(diffItem);
    	}
    	
    	return result;
    }
    
    
    
    @EventListener
    public void doDiffItemEvent(DiffItemEvent diffItemEvent)
    {
//        var itemTag = diffItemEvent.getDiffItem().getItemTag();
//        if (itemTag == PlayerItemCsv.Tag.Population ||
//            itemTag == PlayerItemCsv.Tag.WarehouseMaxLimit || itemTag == PlayerItemCsv.Tag.BoatLimit)
//            return;
        
        if (diffItemEvent.getIsProduce())
            doProduceItemEvent(diffItemEvent);
        else
            doConsumeItemEvent(diffItemEvent);
    }
    
    @EventListener
    public void doPlayerLevelUpEvent(PlayerLevelUpEvent playerLevelUpEvent)
    {
        doChangeTargetCount(
                playerLevelUpEvent.getPlayer(),
                target -> target.getQuestType() == QuestTargetCsv.QuestType.PlayerLevel.getId(),
                OperatorLogic.Assign,
                (target, targetRow) -> playerLevelUpEvent.getPlayer().getLevel());
        doChangeAchievementCount(
                playerLevelUpEvent.getPlayer(),
                achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.PlayerLevel.getId(),
                ()-> {
                    var achievement = new AchievementEntity();
                    achievement.setQuestType(QuestTargetCsv.QuestType.PlayerLevel.getId());
                    achievement.setTargetCount(playerLevelUpEvent.getPlayer().getLevel());
                    return Optional.of(achievement);
                },
                OperatorLogic.Assign,
                achievement -> playerLevelUpEvent.getPlayer().getLevel());
    }
    
    @EventListener
    public void doUpgradeRodEvent(UpgradeRodEvent upgradeRodEvent)
    {
        doChangeTargetCount(
                upgradeRodEvent.getPlayer(),
                target -> target.getQuestType() == QuestTargetCsv.QuestType.UpgradeRod.getId(),
                OperatorLogic.Plus,
                (target, targetRow) -> 1); // plus 1 upgrade rod count
        doChangeAchievementCount(
                upgradeRodEvent.getPlayer(),
                achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.UpgradeRod.getId(),
                ()-> {
                    var achievement = new AchievementEntity();
                    achievement.setQuestType(QuestTargetCsv.QuestType.UpgradeRod.getId());
                    achievement.setTargetCount(1); // new achievement 1 upgrade rod count
                    return Optional.of(achievement);
                },
                OperatorLogic.Plus,
                achievement -> 1); // plus 1 upgrade rod count
    }
      
    @EventListener
    public void doGachaCountEvent(GachaCountEvent gachaCountEvent)
    {
    	doChangeTargetCount(
    			gachaCountEvent.getPlayer(),
    			target -> target.getQuestType() == QuestTargetCsv.QuestType.GachaCount.getId(),
                OperatorLogic.Plus,
                (target, targetRow) -> gachaCountEvent.getCount()); // add GachaCount count
        doChangeAchievementCount(
        		gachaCountEvent.getPlayer(),
        		achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.GachaCount.getId(),
				()-> {
	            	var achievement = new AchievementEntity();
	            	achievement.setQuestType(QuestTargetCsv.QuestType.GachaCount.getId());
	            	achievement.setTargetCount(gachaCountEvent.getCount());
	            	return Optional.of(achievement);
	            },
                OperatorLogic.Plus,
                achievement -> gachaCountEvent.getCount()); // add GachaCount count
    }
    
    
    @EventListener
    public void doBuyShopItemEvent(BuyShopItemEvent buyShopItemEvent)
    {
    	doChangeTargetCount(
    			buyShopItemEvent.getPlayer(),
    			target -> target.getQuestType() == QuestTargetCsv.QuestType.BuyShopItem.getId(),
                OperatorLogic.Plus,
                (target, targetRow) -> 1); // add one BuyShopItemEvent count
        doChangeAchievementCount(
        		buyShopItemEvent.getPlayer(),
        		achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.BuyShopItem.getId(),
				()-> {
	            	var achievement = new AchievementEntity();
	            	achievement.setQuestType(QuestTargetCsv.QuestType.BuyShopItem.getId());
	            	achievement.setTargetCount(1);
	            	return Optional.of(achievement);
	            },
                OperatorLogic.Plus,
                achievement -> 1); // add one BuyShopItemEvent count
    }
    
    @EventListener
    public void doAwardQuestEvent(AwardQuestEvent awardQuestEvent)
    {
    	QuestCsv questCsv = csvContainer.getCsv(QuestCsv.class);
    	QuestCsv.Row questRow = questCsv.getById(awardQuestEvent.getQuest().getCsvId());
    	doChangeTargetCount(
    			awardQuestEvent.getPlayer(),
    			target -> target.getQuestType() == QuestTargetCsv.QuestType.AwardQuest.getId() &&
        			target.getParameter1() == questRow.getQuestTypeId(),
                OperatorLogic.Plus,
                (target, targetRow) -> 1); // add one AwardQuestEvent count
        doChangeAchievementCount(
        		awardQuestEvent.getPlayer(),
        		achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.AwardQuest.getId() &&
        			achievement.getParameter_1() == questRow.getQuestTypeId(),
				()-> {
	            	var achievement = new AchievementEntity();
	            	achievement.setQuestType(QuestTargetCsv.QuestType.AwardQuest.getId());
	            	achievement.setParameter_1(questRow.getQuestTypeId());
	            	achievement.setTargetCount(1);
	            	return Optional.of(achievement);
	            },
                OperatorLogic.Plus,
                achievement -> 1); // add one AwardQuestEvent count
    }
    
    @EventListener
    public void doFishItemEvent(FishItemEvent fishItemEvent)
    {
        doChangeTargetCount(
                fishItemEvent.getPlayer(),
                target -> target.getQuestType() == QuestTargetCsv.QuestType.GainFishItem.getId() &&
                    target.getParameter1() == fishItemEvent.getFish().getCsvId(), // param_1 is fish csv id
                OperatorLogic.Plus,
                (target, targetRow) -> 1); // add one fish
        doChangeAchievementCount(
                fishItemEvent.getPlayer(),
                achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.GainFishItem.getId() &&
                    achievement.getParameter_1() == fishItemEvent.getFish().getCsvId(), // param_1 is fish csv id
                ()-> {
                    var achievement = new AchievementEntity();
                    achievement.setQuestType(QuestTargetCsv.QuestType.GainFishItem.getId());
                    achievement.setParameter_1(fishItemEvent.getFish().getCsvId());
                    achievement.setTargetCount(1); // default 1 fish
                    return Optional.of(achievement);
                },
                OperatorLogic.Plus,
                achievement -> 1); // add one fish
        
        doChangeTargetCount(
                fishItemEvent.getPlayer(),
                target -> target.getQuestType() == QuestTargetCsv.QuestType.FishSize.getId() &&
                    target.getParameter1() == fishItemEvent.getFish().getCsvId(), // param_1 is fish csv id
                OperatorLogic.Assign,
                (target, targetRow) -> Math.max(fishItemEvent.getFish().getSize(), target.getTargetCount())); // update with max size
        doChangeAchievementCount(
                fishItemEvent.getPlayer(),
                achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.FishSize.getId() &&
                    achievement.getParameter_1() == fishItemEvent.getFish().getCsvId(), // param_1 is fish csv id
                ()-> {
                    var achievement = new AchievementEntity();
                    achievement.setQuestType(QuestTargetCsv.QuestType.FishSize.getId());
                    achievement.setParameter_1(fishItemEvent.getFish().getCsvId());
                    achievement.setTargetCount(fishItemEvent.getFish().getSize()); // default fish size
                    return Optional.of(achievement);
                },
                OperatorLogic.Assign,
                achievement -> Math.max(fishItemEvent.getFish().getSize(), achievement.getTargetCount())); // update with max size
    }
    
    private void doConsumeItemEvent(DiffItemEvent diffItemEvent)
    {
    	doChangeTargetCount(
			diffItemEvent.getPlayer(),
			target -> target.getQuestType() == QuestTargetCsv.QuestType.HaveItem.getId() &&
        		target.getParameter1() == diffItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
            OperatorLogic.Plus, // diffCount is negative,  5+(-3) = 2
            (target, targetRow) -> diffItemEvent.getDiffItem().getDiffCount());
            
        doChangeAchievementCount(
    		diffItemEvent.getPlayer(),
    		achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.HaveItem.getId() &&
        		achievement.getParameter_1() == diffItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
			()-> {
            	var achievement = new AchievementEntity();
            	achievement.setQuestType(QuestTargetCsv.QuestType.HaveItem.getId());
            	achievement.setParameter_1(diffItemEvent.getDiffItem().getPlayerItem().getCsvId());
            	achievement.setTargetCount(diffItemEvent.getDiffItem().getDiffCount());
            	return Optional.of(achievement);
            },
            OperatorLogic.Plus, // diffCount is negative,  5+(-3) = 2
            achievement -> diffItemEvent.getDiffItem().getDiffCount());
    }
    
    private void doProduceItemEvent(DiffItemEvent produceItemEvent)
    {
        doChangeTargetCount(
            produceItemEvent.getPlayer(),
            target -> target.getQuestType() == QuestTargetCsv.QuestType.GainItem.getId() &&
            	target.getParameter1() == produceItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
            OperatorLogic.Plus,
            (target, targetRow) -> produceItemEvent.getDiffItem().getDiffCount());
        
        doChangeAchievementCount(
            produceItemEvent.getPlayer(),
            achievement -> achievement.getQuestType() == QuestTargetCsv.QuestType.GainItem.getId() &&
            	achievement.getParameter_1() == produceItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
            ()-> {
            	var achievement = new AchievementEntity();
            	achievement.setQuestType(QuestTargetCsv.QuestType.GainItem.getId());
            	achievement.setParameter_1(produceItemEvent.getDiffItem().getPlayerItem().getCsvId());
            	achievement.setTargetCount(produceItemEvent.getDiffItem().getDiffCount());
            	return Optional.of(achievement);
            },
            OperatorLogic.Plus,
            achievement -> produceItemEvent.getDiffItem().getDiffCount());
        
        doChangeTargetCount(
            produceItemEvent.getPlayer(),
            target -> target.getQuestType() == QuestTargetCsv.QuestType.HaveItem.getId() &&
        		target.getParameter1() == produceItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
            OperatorLogic.Plus,
            (target, targetRow) -> produceItemEvent.getDiffItem().getDiffCount());
        
        doChangeAchievementCount(
            produceItemEvent.getPlayer(),
            achievement ->achievement.getQuestType() == QuestTargetCsv.QuestType.HaveItem.getId() &&
        		achievement.getParameter_1() == produceItemEvent.getDiffItem().getPlayerItem().getCsvId(), // param_1 is Item csv id
			()-> {
            	var achievement = new AchievementEntity();
            	achievement.setQuestType(QuestTargetCsv.QuestType.HaveItem.getId());
            	achievement.setParameter_1(produceItemEvent.getDiffItem().getPlayerItem().getCsvId());
            	achievement.setTargetCount(produceItemEvent.getDiffItem().getDiffCount());
            	return Optional.of(achievement);
            },
            OperatorLogic.Plus,
            achievement -> produceItemEvent.getDiffItem().getDiffCount());
    }
    
    
    private int getNewTargetCount(PlayerEntity player, QuestTargetCsv.Row row)
    {
    	if (row.getQuestType() == QuestTargetCsv.QuestType.HaveItem.getId())
    	{
    		var findPlayerItem = playerItemService.findByCsvId(player, row.getParameter1());
    		return findPlayerItem.isPresent() ? findPlayerItem.get().getCount() : 0;
    	}
    	else if (row.getQuestType() == QuestTargetCsv.QuestType.PlayerLevel.getId())
        {
            return player.getLevel();
        }
        
        return 0;
    }
   
    private void checkAwardQuest(PlayerEntity player, QuestEntity quest) throws Exception
    {
        if (quest.getIsAward())
            throw new ExceptionByCsvId("it's awarded! csvid:" + quest.getCsvId());
        
        checkEnoughAwardConsumeItem(player, quest);
        
        QuestCsv.Row questCsvRow = csvContainer.getCsv(QuestCsv.class).getById(quest.getCsvId());
        QuestTargetCsv questTargetCsv = csvContainer.getCsv(QuestTargetCsv.class);
        Map<Long, Boolean> logicMap = new HashMap<Long, Boolean>();

        for(QuestTargetEntity target : quest.getTargets())
        {
            QuestTargetCsv.Row targetRow = questTargetCsv.getById(target.getCsvId());
            logicMap.put(target.getId(), Boolean.FALSE);
            
            if (questCsvRow.getQuestTypeId() == QuestCsv.QuestType.Acheivement.getId())
            {
                var achievementOptional = findAchievementsByTargetFirst(player, target);
                if (achievementOptional.isPresent())
                {
                    var achievement = achievementOptional.get();
                    if (QuestTargetCsv.QuestType.CheckFinishTarget(targetRow, achievement.getTargetCount()))
                        logicMap.replace(target.getId(), Boolean.TRUE);
                }
                else
                    logicMap.replace(target.getId(), Boolean.FALSE);
            }
            else
            {
                if (QuestTargetCsv.QuestType.CheckFinishTarget(targetRow, target.getTargetCount()))
                    logicMap.replace(target.getId(), Boolean.TRUE);
            }
        }

        boolean isAnyOk = false;
        for(Map.Entry<Long, Boolean> logic : logicMap.entrySet())
        {
            Optional<QuestTargetEntity> targetOption = quest.getTargets().stream().filter(t->t.getId() == logic.getKey().longValue()).findFirst();
            if (targetOption.isEmpty())
                throw new ExceptionByCsvId("targetOption.isEmpty()");

            QuestTargetEntity target = targetOption.get();
            QuestTargetCsv.Row targetRow = questTargetCsv.getById(target.getCsvId());

            if (logic.getValue().booleanValue())
                isAnyOk = true;

            if(targetRow.getLogic() == QuestTargetCsv.LogicType.And.getId() && !logic.getValue().booleanValue())
                throw new ExceptionByCsvId("it's not enough to award quest:" + quest.getCsvId());
        }   
        
        if (!isAnyOk)
            throw new ExceptionByCsvId("it's not enough to award quest:" + quest.getCsvId());
    }
    
    private void checkEnoughAwardConsumeItem(PlayerEntity player, QuestEntity quest) throws Exception
    {
    	QuestCsv.Row questCsvRow = csvContainer.getCsv(QuestCsv.class).getById(quest.getCsvId());
    	for (int i = 0; i < questCsvRow.getConsumeIds().length; i++)
    	{
    		var itemId = questCsvRow.getConsumeIds()[i];
    		var itemValue = questCsvRow.getConsumeValues()[i];
    		var item = playerItemService.findByCsvId(player, itemId);
    		if (item.isEmpty()) continue;
    		if (item.get().getCount() < itemValue)
    			throw new ExceptionByCsvId("quest:" + quest.getCsvId() + "item:" + itemId + " is not enough");
    	}
    }
   
    private Optional<AchievementEntity> findAchievementsByTargetFirst(PlayerEntity player, QuestTargetEntity target)
    {
        QuestTargetCsv questTargetCsv = csvContainer.getCsv(QuestTargetCsv.class);
        QuestTargetCsv.Row targetRow = questTargetCsv.getById(target.getCsvId());
        
        var achievementFilter = QuestTargetCsv.QuestType.GetAchievementFilter(targetRow);
        return player.getAchievements().stream().filter(achievementFilter).findFirst();
    }
    
    /**
     * chang target Count
     * @param player
     * @param questTargetFilter questTarget to find
     * @param opLogic +,-,=
     * @param countSupplier operator value
     */
    private void doChangeTargetCount(PlayerEntity player,
    		Predicate<QuestTargetCsv.Row> questTargetFilter,
    		OperatorLogic opLogic,
    		BiFunction<QuestTargetEntity, QuestTargetCsv.Row, Integer> countSupplier)
    {
        QuestTargetCsv questTargetCsv = csvContainer.getCsv(QuestTargetCsv.class);
        for(QuestEntity quest : player.getQuests())
        {
            if (quest.getIsAward())
                continue;
            QuestCsv.Row questCsvRow = csvContainer.getCsv(QuestCsv.class).getById(quest.getCsvId());
            if (questCsvRow.getQuestTypeId() == QuestCsv.QuestType.Acheivement.getId())
                continue;
            for(QuestTargetEntity target : quest.getTargets())
            {
                QuestTargetCsv.Row targetRow = questTargetCsv.getById(target.getCsvId());
                if (questTargetFilter.test(targetRow))
                {
                	int count = countSupplier.apply(target, targetRow);
                	int newTargetCount = count; // default logic set
                    if (opLogic == OperatorLogic.Plus)
                        newTargetCount = target.getTargetCount() + count;
                    else if (opLogic == OperatorLogic.Minus)
                        newTargetCount = target.getTargetCount() - count;
                    target.setTargetCount(newTargetCount);
                    questTargetRepository.save(target);
                }   
            }
        }
        
        mediatorContainer.getMediator().addDirtyFlag(DirtyFlagEnum.Quest.getId());
    }  
    
    /**
     * change achievement count
     * @param player
     * @param achievementFilter achievement to find
     * @param newAchievementSupplier new achievement to supply
     * @param opLogic +,-,=
     * @param countSupplier operator value
     */
    private void doChangeAchievementCount(PlayerEntity player,
    		Predicate<AchievementEntity> achievementFilter,
    		Supplier<Optional<AchievementEntity>> newAchievementSupplier,
            OperatorLogic opLogic,
            Function<AchievementEntity, Integer> countSupplier)
    {
        var findAchievement =  player.getAchievements().stream().filter(achievementFilter).collect(Collectors.toList());
        
        if (findAchievement.isEmpty())
        {
        	var supplyAchievementOpt = newAchievementSupplier.get();
        	if (supplyAchievementOpt.isEmpty())
        		return ;
        	var supplyAchievement = supplyAchievementOpt.get();
            var newAchievement = new AchievementEntity();
            newAchievement.setQuestType(supplyAchievement.getQuestType());
            newAchievement.setParameter_1(supplyAchievement.getParameter_1());
            newAchievement.setParameter_2(supplyAchievement.getParameter_2());
            newAchievement.setParameter_3(supplyAchievement.getParameter_3());
            newAchievement.setParameter_4(supplyAchievement.getParameter_4());
            newAchievement.setParameter_5(supplyAchievement.getParameter_5());
            newAchievement.setTargetCount(supplyAchievement.getTargetCount());
            newAchievement.setPlayer(player);
            player.getAchievements().add(newAchievement);
            achievementRepository.save(newAchievement);
            return;
        }
        
        for (var achievement : findAchievement)
        {
        	int count = countSupplier.apply(achievement);
        	int newTargetCount = count;
        	if (opLogic == OperatorLogic.Plus)
                newTargetCount = achievement.getTargetCount() + count;
            else if (opLogic == OperatorLogic.Minus)
                newTargetCount = achievement.getTargetCount() - count;
            achievement.setTargetCount(newTargetCount);
            achievementRepository.save(achievement);
        }
    }
}
