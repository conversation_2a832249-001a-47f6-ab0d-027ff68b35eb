using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Cysharp.Threading.Tasks;
using System.Threading;
using DG.Tweening;
using Zenject;
using Ehooray.UI;
using Fishing.UI;
using Ehooray;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/Battle/NewFishingCanvas")]
    public class NewLineTensionManager : GamePanel
    {
        enum TensionState
        {
            BeforeStart = 0,
            Pull,
            Release,
            Pause,
            End
        }
        TensionState tensionState;

        public PlayerHpCircleManager playerHpCircleManager;
        public FishingSkillBarManager fishingSkillBarManager;

        // 張力條相關
        [Header("張力條相關")]
        [SerializeField]
        Image tensionBarImage;
        [SerializeField]
        Image warningImage;
        
        // 加分區與必殺技
        [Header("加分區與必殺技")]
        [SerializeField]
        Image bonusAreaImage; // 新增加分區圖像
        [SerializeField]
        Image skillFillImage; // 新增必殺技填充圖像
        [SerializeField]
        Image glowEffectImage; // 新增加分區發光效果
        [SerializeField]
        Button skillButton; // 新增必殺技按鈕
        
        // 距離顯示
        [Header("距離顯示")]
        [SerializeField]
        Image distanceFillImage; // 新增距離填充圖像
        [SerializeField]
        Text distanceText; // 新增距離文字
        
        // 圖示
        [Header("圖示")]
        [SerializeField]
        GameObject fishIcon; // 新增魚圖示
        [SerializeField]
        GameObject rodIcon; // 新增釣竿圖示
        
        // 其他按鈕
        [Header("其他按鈕")]
        [SerializeField]
        Button pullButton;
        [SerializeField]
        Button pauseButton;
        
        // 基礎張力參數
        [Header("基礎張力參數")]
        [SerializeField]
        float defaultTension = 10;
        [SerializeField]
        float currentTension = 0;
        [SerializeField]
        int minTension = 0;
        [SerializeField]
        int maxTension = 1000;

        // 加分區系統
        [Header("加分區系統")]
        [SerializeField]
        float bonusAreaWidth = 100f;
        [SerializeField]
        float bonusAreaMinPosition = -300f;
        [SerializeField]
        float bonusAreaMaxPosition = 300f;
        [SerializeField]
        float bonusAreaMoveSpeed = 0.5f;
        [SerializeField]
        float bonusAreaMoveDirection = 1f;
        [SerializeField]
        float bonusAreaCurrentPosition = 0f;

        // 必殺技系統
        [Header("必殺技系統")]
        [SerializeField]
        float skillChargeRate = 0.01f;
        [SerializeField]
        float currentSkillCharge = 0f;
        [SerializeField]
        bool isSkillReady = false;
        [SerializeField]
        float skillEffectDuration = 3f;
        [SerializeField]
        bool isSkillActive = false;

        // 張力變化參數
        [Header("張力變化參數")]
        [SerializeField]
        float raiseTensionPerTime = 10;
        [SerializeField]
        float reduceTensionPerTime = 5;
        [Range(0, 1)]
        public float lowAlertThreshold = 0.2f;
        [Range(0, 1)]
        public float highAlertThreshold = 0.8f;
        public float raiseWarningDuration = 0.2f;
        public float dropWarningDuration = 0.4f;

        // 警告系統
        [Header("警告系統")]
        [SerializeField]
        AnimationCurve warningCurve;
        [SerializeField]
        float fillPercentage;

        // 距離系統
        [Header("距離系統")]
        [SerializeField]
        float reduceDistanceSpeed = 50f;
        [SerializeField]
        float increaseDistanceSpeed = 100f; // 新增：增加距離的速度
        [SerializeField]
        float fishDistance;
        [SerializeField]
        float maxDistance = 400f;
        [SerializeField]
        float minStartDistance = 50f; // 新增：最小初始距離

        // 魚的掙扎參數
        float struggleValue;
        float StunMF;
        float SStruggleMF;
        float MStruggleMF;
        float LStruggleMF;

        CancellationTokenSource cancellationTokenSource;
        Sequence bonusAreaGlowSequence;

        #region Zenject Inject
        FishingManager fishingManager;
        FishingGamePanelManager fishingGamePanelManager;
        TableContainer tableContainer;
        FishingRepositories fishingRepositories;
        FishingContainer fishingContainer;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
                                TableContainer tableContainer,
                                FishingGamePanelManager fishingGamePanelManager,
                                FishingRepositories fishingRepositories,
                                FishingContainer fishingContainer)
        {
            this.fishingManager = fishingManager;
            this.tableContainer = tableContainer;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.fishingRepositories = fishingRepositories;
            this.fishingContainer = fishingContainer;
        }

        private void Awake()
        {
            if (pauseButton != null)
                pauseButton.onClick.AddListener(OpenPausePanel);
                
            if (skillButton != null)
                skillButton.onClick.AddListener(ActivateSkill);
                
            // 初始化技能按鈕狀態為不可用
            SetSkillButtonState(false);
        }
        
        void Start()
        {
            LoadParameter();
            
            // 設置初始魚距離
            fishDistance = fishingManager.fish.distance;
            UpdateDistanceDisplay();
        }

        public void CloseRoot()
        {
            if (cancellationTokenSource != null)
                cancellationTokenSource.Cancel();

            ResetWarningImage();
            StopAllCoroutines();
            StopBonusAreaGlow();
            
            if (fishingSkillBarManager != null)
                fishingSkillBarManager.CancelGetStunPower();
                
            if (playerHpCircleManager != null)
                playerHpCircleManager.CloseRoot();
        }

        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            StopBonusAreaGlow();
        }

        public async void Play()
        {
            // 確保 fishingManager 和 fish 已初始化
            if (fishingManager == null || fishingManager.fish == null)
            {
                Debug.LogError("FishingManager or Fish is not initialized!");
                return;
            }

            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Dispose();
            }
            cancellationTokenSource = new CancellationTokenSource();

            ShakeCamera.ShakeOnce(Camera.main).Forget();
            
            if (fishingSkillBarManager != null)
                fishingSkillBarManager.SetSkillBarValue(0);

            if (playerHpCircleManager != null)
                playerHpCircleManager.Play().Forget();

            var closeupText = await fishingGamePanelManager.OpenAsync<CloseupTextManager>();
            closeupText.ShowCloseupText().Forget();

            defaultTension = maxTension / 2;
            currentTension = defaultTension;
            currentSkillCharge = 0f;

            // 設置並確保最小初始距離
            fishDistance = fishingManager.fish.distance;
            fishDistance = Mathf.Max(fishDistance, minStartDistance);
            fishingManager.fish.distance = fishDistance; // 更新回管理器中的值

            isSkillReady = false;
            isSkillActive = false;
            
            RefreshTensionBar();
            LetFishGo();

            try
            {
                while (!cancellationTokenSource.IsCancellationRequested)
                {
                    switch (tensionState)
                    {
                        case TensionState.BeforeStart:
                            break;
                        case TensionState.Pull:
                            RaiseTensionBar();
                            break;
                        case TensionState.Release:
                            ReduceTensionBar();
                            break;
                        case TensionState.Pause:
                            await UniTask.Delay(16, delayTiming: PlayerLoopTiming.FixedUpdate, cancellationToken: cancellationTokenSource.Token);
                            continue;
                        case TensionState.End:
                            break;
                    }

                    // 移動加分區
                    MoveBonusArea();
                    
                    // 更新張力條顯示
                    RefreshTensionBar();
                    
                    // 檢查是否在加分區內
                    CheckBonusAreaHit();
                    
                    // 警告張力危險
                    AlertTensionDanger();
                    
                    // 更新距離顯示
                    UpdateDistanceDisplay();
                    
                    await UniTask.Delay(16, delayTiming: PlayerLoopTiming.FixedUpdate, cancellationToken: cancellationTokenSource.Token);
                }
            }
            catch
            {
                // 處理取消操作的異常
            }
        }

        // 移動加分區
        private void MoveBonusArea()
        {
            bonusAreaCurrentPosition += bonusAreaMoveSpeed * bonusAreaMoveDirection;
            
            // 到達邊界時改變方向
            if (bonusAreaCurrentPosition >= bonusAreaMaxPosition)
            {
                bonusAreaCurrentPosition = bonusAreaMaxPosition;
                bonusAreaMoveDirection = -1;
            }
            else if (bonusAreaCurrentPosition <= bonusAreaMinPosition)
            {
                bonusAreaCurrentPosition = bonusAreaMinPosition;
                bonusAreaMoveDirection = 1;
            }
            
            // 更新加分區位置
            if (bonusAreaImage != null)
            {
                RectTransform rt = bonusAreaImage.rectTransform;
                rt.anchoredPosition = new Vector2(bonusAreaCurrentPosition, rt.anchoredPosition.y);
            }
        }
        
        // 檢查張力是否在加分區內
        private void CheckBonusAreaHit()
        {
            if (bonusAreaImage == null)
                return;
                
            // 計算張力條的實際位置
            float tensionPosition = Mathf.Lerp(-400, 400, fillPercentage);
            
            // 檢查是否在加分區範圍內
            float bonusAreaLeft = bonusAreaCurrentPosition - bonusAreaWidth / 2;
            float bonusAreaRight = bonusAreaCurrentPosition + bonusAreaWidth / 2;
            
            bool isInBonusArea = tensionPosition >= bonusAreaLeft && tensionPosition <= bonusAreaRight;
            
            // 當在加分區內時
            if (isInBonusArea) // 移除 tensionState == TensionState.Pull 判斷
            {
                // 減少魚的距離
                ReduceFishDistance();

                // 增加必殺技充能 (僅在拉線時充能)
                if (tensionState == TensionState.Pull)
                {
                    ChargeSkill();
                }

                // 顯示加分區發光效果
                ShowBonusAreaGlow();
            }
            else
            {
                // 增加魚的距離
                IncreaseFishDistance();
                // 關閉加分區發光效果
                HideBonusAreaGlow();
            }
        }

        // 減少魚的距離
        private void ReduceFishDistance()
        {
            if (fishingManager == null || fishingManager.fish == null)
                return;
                
            // 根據魚的掙扎狀態和釣竿參數減少距離
            fishDistance -= Time.deltaTime * struggleValue * reduceDistanceSpeed;
            
            // 確保距離不小於最小捕獲距離
            if (fishDistance <= fishingManager.fish.catchDistance)
            {
                fishDistance = fishingManager.fish.catchDistance;
                fishingManager.TransitionToState(FishingState.Catch).Forget();
            }
            
            // 更新魚的實際距離
            fishingManager.fish.distance = fishDistance;
        }
        
        // 新增：增加魚的距離
        private void IncreaseFishDistance()
        {
            if (fishingManager == null || fishingManager.fish == null || isSkillActive) // 技能期間不增加距離
                return;

            // 根據魚的掙扎狀態和參數增加距離
            fishDistance += Time.deltaTime * struggleValue * increaseDistanceSpeed;

            // 確保距離不超過最大值
            if (fishDistance > maxDistance)
            {
                fishDistance = maxDistance;
                // 可以在這裡添加距離達到最大值的處理，例如增加失敗計數或直接失敗
                 fishingManager.TransitionToState(FishingState.Fail).Forget(); // 暫定為直接失敗
            }

            // 更新魚的實際距離
            fishingManager.fish.distance = fishDistance;
        }
        
        // 增加必殺技充能
        private void ChargeSkill()
        {
            if (isSkillReady || isSkillActive)
                return;
                
            currentSkillCharge += skillChargeRate;
            
            // 更新必殺技填充圖像
            if (skillFillImage != null)
            {
                skillFillImage.fillAmount = currentSkillCharge;
            }
            
            // 檢查是否已充滿
            if (currentSkillCharge >= 1.0f)
            {
                isSkillReady = true;
                ShowSkillReadyEffect();
            }
        }
        
        // 顯示必殺技準備好的效果
        private void ShowSkillReadyEffect()
        {
            // 更新按鈕狀態
            SetSkillButtonState(true);
            
            // 播放特效
            if (skillButton != null)
            {
                Transform glowTransform = skillButton.transform.Find("Glow");
                if (glowTransform != null)
                {
                    Image glowImage = glowTransform.GetComponent<Image>();
                    if (glowImage != null)
                    {
                        // 播放閃爍效果
                        Sequence seq = DOTween.Sequence();
                        seq.Append(glowImage.DOFade(0.7f, 0.5f));
                        seq.Append(glowImage.DOFade(0f, 0.5f));
                        seq.SetLoops(-1);
                    }
                }
            }
        }
        
        // 顯示加分區發光效果
        private void ShowBonusAreaGlow()
        {
            if (glowEffectImage == null)
                return;
                
            if (bonusAreaGlowSequence == null || !bonusAreaGlowSequence.IsActive())
            {
                StopBonusAreaGlow();
                
                bonusAreaGlowSequence = DOTween.Sequence();
                bonusAreaGlowSequence.Append(glowEffectImage.DOFade(0.5f, 0.3f));
                bonusAreaGlowSequence.Append(glowEffectImage.DOFade(0.2f, 0.3f));
                bonusAreaGlowSequence.SetLoops(-1);
            }
        }
        
        // 隱藏加分區發光效果
        private void HideBonusAreaGlow()
        {
            StopBonusAreaGlow();
            
            if (glowEffectImage != null)
            {
                glowEffectImage.color = new Color(glowEffectImage.color.r, glowEffectImage.color.g, glowEffectImage.color.b, 0);
            }
        }
        
        // 停止加分區發光效果
        private void StopBonusAreaGlow()
        {
            if (bonusAreaGlowSequence != null && bonusAreaGlowSequence.IsActive())
            {
                bonusAreaGlowSequence.Kill();
                bonusAreaGlowSequence = null;
            }
        }
        
        // 激活必殺技
        private void ActivateSkill()
        {
            if (!isSkillReady || isSkillActive)
                return;
                
            isSkillReady = false;
            isSkillActive = true;
            currentSkillCharge = 0;
            
            // 更新UI
            if (skillFillImage != null)
            {
                skillFillImage.fillAmount = 0;
            }
            
            // 設置按鈕狀態
            SetSkillButtonState(false);
            
            // 停止閃爍效果
            if (skillButton != null)
            {
                Transform glowTransform = skillButton.transform.Find("Glow");
                if (glowTransform != null)
                {
                    Image glowImage = glowTransform.GetComponent<Image>();
                    if (glowImage != null)
                    {
                        DOTween.Kill(glowImage);
                        glowImage.color = new Color(glowImage.color.r, glowImage.color.g, glowImage.color.b, 0);
                    }
                }
            }
            
            // 執行必殺技效果
            UseSkillEffect().Forget();
        }
        
        // 必殺技效果
        private async UniTaskVoid UseSkillEffect()
        {
            // 執行魚的必殺技
            if (fishingManager != null && fishingManager.fish != null)
            {
                fishingManager.fish.CheckSkill();
                
                // 捲線器特效動畫
                if (fishingManager.cordReelManager != null)
                {
                    fishingManager.cordReelManager.SkillShake();
                }
                
                // 等待技能效果結束
                await UniTask.Delay((int)(skillEffectDuration * 1000), cancellationToken: cancellationTokenSource.Token);
                
                // 結束技能效果
                isSkillActive = false;
            }
        }
        
        // 設置必殺技按鈕狀態
        private void SetSkillButtonState(bool enabled)
        {
            if (skillButton == null)
                return;
                
            skillButton.interactable = enabled;
            
            // 更新按鈕視覺效果
            ColorBlock colors = skillButton.colors;
            colors.disabledColor = new Color(0.5f, 0.5f, 0.5f, 0.5f);
            skillButton.colors = colors;
        }

        // 更新距離顯示
        private void UpdateDistanceDisplay()
        {
            if (fishingManager == null || fishingManager.fish == null)
                return;
                
            // 更新距離文字
            if (distanceText != null)
            {
                distanceText.text = $"{Mathf.CeilToInt(fishDistance)}m / {Mathf.CeilToInt(maxDistance)}m";
            }
            
            // 更新距離條填充
            if (distanceFillImage != null)
            {
                // 確保 fishDistance 在 0 和 maxDistance 之間
                float clampedDistance = Mathf.Clamp(fishDistance, 0, maxDistance);
                float fillAmount = 1.0f - (clampedDistance / maxDistance);
                distanceFillImage.fillAmount = fillAmount;
                
                // 更新魚圖標位置
                if (fishIcon != null)
                {
                    RectTransform fishRt = fishIcon.GetComponent<RectTransform>();
                    float xPos = Mathf.Lerp(-250, 250, 1.0f - fillAmount);
                    fishRt.anchoredPosition = new Vector2(xPos, fishRt.anchoredPosition.y);
                }
            }
        }

        public void ResetStatus()
        {
            tensionState = TensionState.BeforeStart;
            currentSkillCharge = 0;
            isSkillReady = false;
            isSkillActive = false;
            
            if (skillFillImage != null)
            {
                skillFillImage.fillAmount = 0;
            }
            
            SetSkillButtonState(false);
        }

        // 添加是否按下按鈕的屬性
        private bool _isButtonPressed = false;
        public bool IsButtonPressed => _isButtonPressed;

        public void OnPullButtonPressed(PointerEventData data)
        {
            _isButtonPressed = true;
            PullFish();
            if (fishingManager != null && fishingManager.cordReelManager != null && fishingManager.cordReelManager.wheelRotate != null)
                fishingManager.cordReelManager.wheelRotate.SetPullFishSpeed();
        }

        public void PullFish()
        {
            if (tensionState == TensionState.Pause)
                return;
                
            tensionState = TensionState.Pull;
        }

        public void OnPullButtonUp(PointerEventData data)
        {
            _isButtonPressed = false;
            
            if (tensionState == TensionState.Pause)
                return;
                
            LetFishGo();
            
            if (fishingManager?.cordReelManager?.wheelRotate != null)
                fishingManager.cordReelManager.wheelRotate.SetReleaseFishSpeed();
        }

        public void LetFishGo()
        {
            tensionState = TensionState.Release;
        }

        public void RefreshTensionBar()
        {
            fillPercentage = currentTension / maxTension;
            
            if (tensionBarImage != null)
            {
                tensionBarImage.fillAmount = fillPercentage;
                
                // 根據張力值變化顏色
                tensionBarImage.color = GetTensionColorGradient(fillPercentage);
            }
        }
        
        // 獲取張力顏色漸變
        private Color GetTensionColorGradient(float value)
        {
            if (value < 0.3f)
                return Color.green; // 低張力 - 綠色
            else if (value < 0.6f)
                return Color.yellow; // 中張力 - 黃色
            else if (value < 0.8f)
                return new Color(1, 0.5f, 0); // 高張力 - 橙色
            else
                return Color.red; // 危險張力 - 紅色
        }

        public void RaiseTensionBar()
        {
            currentTension += raiseTensionPerTime;
            if (currentTension >= maxTension)
            {
                currentTension = maxTension;
                fishingManager.TransitionToState(FishingState.Fail).Forget();
            }
        }

        public void ReduceTensionBar()
        {
            currentTension -= reduceTensionPerTime / 60f;
            if (currentTension <= minTension)
            {
                currentTension = minTension;
                fishingManager.TransitionToState(FishingState.Fail).Forget();
            }
        }

        public bool IsValidFight()
        {
            // 判斷張力是否在加分區內
            float tensionPosition = Mathf.Lerp(-400, 400, fillPercentage);
            float bonusAreaLeft = bonusAreaCurrentPosition - bonusAreaWidth / 2;
            float bonusAreaRight = bonusAreaCurrentPosition + bonusAreaWidth / 2;
            
            bool isInBonusArea = tensionPosition >= bonusAreaLeft && tensionPosition <= bonusAreaRight;
            return isInBonusArea && tensionState == TensionState.Pull;
        }

        void AlertTensionDanger()
        {
            if (tensionState != TensionState.Pull && tensionState != TensionState.Release)
            {
                return;
            }

            if (cancellationTokenSource.IsCancellationRequested)
            {
                return;
            }

            if (fillPercentage <= lowAlertThreshold || fillPercentage >= highAlertThreshold)
            {
                DoWarningTween();
            }
            else
            {
                CloseWarning();
            }
        }

        void DoWarningTween()
        {
            if (warningImage == null)
                return;
                
            bool tweening = DOTween.IsTweening(warningImage);
            if (warningImage.color.a == 0)
            {
                DOTween.Kill(warningImage);
            }
            if (tweening)
            {
                return;
            }
            
            ShakeCamera.ShakeOnce(Camera.main, 0.01f).Forget();
            warningImage.DOColor(new Color(1, 1, 1, 0.9f), raiseWarningDuration).SetEase(warningCurve);
        }

        void CloseWarning()
        {
            if (warningImage == null)
                return;
                
            bool tweening = DOTween.IsTweening(warningImage);
            if (warningImage.color.a == 0)
            {
                return;
            }
            if (tweening)
            {
                DOTween.Kill(warningImage);
            }
            warningImage.DOColor(new Color(1, 1, 1, 0), dropWarningDuration);
        }

        public void ResetWarningImage()
        {
            if (warningImage == null)
                return;
                
            DOTween.Kill(warningImage);
            warningImage.color = new Color(1, 1, 1, 0);
        }

        void LoadParameter()
        {
            if (tableContainer == null)
                return;
                
            StunMF = tableContainer.VariableTable.StunMF / 1000f;
            SStruggleMF = tableContainer.VariableTable.SStruggleMF / 1000f;
            MStruggleMF = tableContainer.VariableTable.MStruggleMF / 1000f;
            LStruggleMF = tableContainer.VariableTable.LStruggleMF / 1000f;
            
            struggleValue = GetStruggleValue();
        }
        
        float GetStruggleValue()
        {
            if (fishingManager == null || fishingManager.fish == null)
                return SStruggleMF;
                
            float value;
            switch (fishingManager.fish.state)
            {
                case StruggleState.Big:
                    value = LStruggleMF;
                    break;
                case StruggleState.Mid:
                    value = MStruggleMF;
                    break;
                case StruggleState.Small:
                    value = SStruggleMF;
                    break;
                case StruggleState.Stun:
                    value = StunMF;
                    break;
                default:
                    value = SStruggleMF;
                    break;

            }
            return value;
        }
        
        public void OpenPausePanel()
        {
            var runner = OpenBagPanelRunner();
            runner.RunAsync(showMask: true).Forget();
        }

        public AsyncProgressRunner OpenBagPanelRunner()
        {
            var runner = fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                fishingManager.pausePanelManager = await fishingGamePanelManager.OpenAsync<PausePanelManager>();
            });
            return runner;
        }

        public void SetFishHPBar(float hpPersent)
        {
            // 可選擇是否保留生命值條顯示
        }

        // 添加暫停方法，用於QTE開始時暫停張力系統
        public void Pause()
        {
            if (tensionState != TensionState.Pause)
            {
                // 儲存當前狀態以便恢復
                TensionState previousState = tensionState;
                tensionState = TensionState.Pause;
                
                // 暫停加分區移動
                bonusAreaGlowSequence?.Pause();
                
                Debug.Log($"[QTE] 張力系統已暫停，原狀態: {previousState}");
            }
        }
        
        // 添加恢復方法，用於QTE結束時恢復張力系統
        public void Resume()
        {
            // 檢查當前是否為暫停狀態
            if (tensionState == TensionState.Pause)
            {
                // 根據按鈕狀態決定恢復到哪個狀態
                tensionState = IsButtonPressed ? TensionState.Pull : TensionState.Release;
                
                // 恢復加分區移動
                bonusAreaGlowSequence?.Play();
                
                Debug.Log($"[QTE] 張力系統已恢復，當前狀態: {tensionState}");
            }
        }
        
        // 添加获取张力系统当前状态的方法，用于调试
        public string GetTensionState()
        {
            return tensionState.ToString();
        }
    }
} 