using UnityEngine;
using Cysharp.Threading.Tasks;

namespace Fishing
{
    /// <summary>
    /// 釣魚策略接口，定義不同釣魚狀態下的行為
    /// </summary>
    public interface IFishingStrategy
    {
        /// <summary>
        /// 進入該狀態時執行的方法
        /// </summary>
        UniTask OnEnter(FishingManager context);
        
        /// <summary>
        /// 在該狀態中每幀執行的方法
        /// </summary>
        void OnUpdate(FishingManager context);
        
        /// <summary>
        /// 退出該狀態時執行的方法
        /// </summary>
        UniTask OnExit(FishingManager context);
        
        /// <summary>
        /// 獲取此策略對應的釣魚狀態
        /// </summary>
        FishingState GetStateType();
    }
} 