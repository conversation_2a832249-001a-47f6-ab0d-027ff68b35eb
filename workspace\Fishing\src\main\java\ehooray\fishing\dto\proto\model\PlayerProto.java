// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/Player.proto

package ehooray.fishing.dto.proto.model;

public final class PlayerProto {
  private PlayerProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PlayerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.Player)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string account = 3;</code>
     * @return The account.
     */
    java.lang.String getAccount();
    /**
     * <code>string account = 3;</code>
     * @return The bytes for account.
     */
    com.google.protobuf.ByteString
        getAccountBytes();

    /**
     * <code>int32 finalLevel = 4;</code>
     * @return The finalLevel.
     */
    int getFinalLevel();

    /**
     * <code>int32 diffLevel = 5;</code>
     * @return The diffLevel.
     */
    int getDiffLevel();

    /**
     * <code>int32 finalExp = 6;</code>
     * @return The finalExp.
     */
    int getFinalExp();

    /**
     * <code>int32 diffExp = 7;</code>
     * @return The diffExp.
     */
    int getDiffExp();

    /**
     * <code>int32 serverId = 8;</code>
     * @return The serverId.
     */
    int getServerId();
  }
  /**
   * Protobuf type {@code model.Player}
   */
  public static final class Player extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.Player)
      PlayerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player.newBuilder() to construct.
    private Player(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player() {
      name_ = "";
      account_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.PlayerProto.internal_static_model_Player_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.PlayerProto.internal_static_model_Player_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.PlayerProto.Player.class, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ACCOUNT_FIELD_NUMBER = 3;
    private volatile java.lang.Object account_;
    /**
     * <code>string account = 3;</code>
     * @return The account.
     */
    @java.lang.Override
    public java.lang.String getAccount() {
      java.lang.Object ref = account_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        account_ = s;
        return s;
      }
    }
    /**
     * <code>string account = 3;</code>
     * @return The bytes for account.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAccountBytes() {
      java.lang.Object ref = account_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        account_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FINALLEVEL_FIELD_NUMBER = 4;
    private int finalLevel_;
    /**
     * <code>int32 finalLevel = 4;</code>
     * @return The finalLevel.
     */
    @java.lang.Override
    public int getFinalLevel() {
      return finalLevel_;
    }

    public static final int DIFFLEVEL_FIELD_NUMBER = 5;
    private int diffLevel_;
    /**
     * <code>int32 diffLevel = 5;</code>
     * @return The diffLevel.
     */
    @java.lang.Override
    public int getDiffLevel() {
      return diffLevel_;
    }

    public static final int FINALEXP_FIELD_NUMBER = 6;
    private int finalExp_;
    /**
     * <code>int32 finalExp = 6;</code>
     * @return The finalExp.
     */
    @java.lang.Override
    public int getFinalExp() {
      return finalExp_;
    }

    public static final int DIFFEXP_FIELD_NUMBER = 7;
    private int diffExp_;
    /**
     * <code>int32 diffExp = 7;</code>
     * @return The diffExp.
     */
    @java.lang.Override
    public int getDiffExp() {
      return diffExp_;
    }

    public static final int SERVERID_FIELD_NUMBER = 8;
    private int serverId_;
    /**
     * <code>int32 serverId = 8;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerProto.Player parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.PlayerProto.Player prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.Player}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.Player)
        ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.PlayerProto.internal_static_model_Player_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.PlayerProto.internal_static_model_Player_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.PlayerProto.Player.class, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.PlayerProto.Player.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        name_ = "";

        account_ = "";

        finalLevel_ = 0;

        diffLevel_ = 0;

        finalExp_ = 0;

        diffExp_ = 0;

        serverId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.PlayerProto.internal_static_model_Player_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerProto.Player getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerProto.Player build() {
        ehooray.fishing.dto.proto.model.PlayerProto.Player result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerProto.Player buildPartial() {
        ehooray.fishing.dto.proto.model.PlayerProto.Player result = new ehooray.fishing.dto.proto.model.PlayerProto.Player(this);
        result.entityId_ = entityId_;
        result.name_ = name_;
        result.account_ = account_;
        result.finalLevel_ = finalLevel_;
        result.diffLevel_ = diffLevel_;
        result.finalExp_ = finalExp_;
        result.diffExp_ = diffExp_;
        result.serverId_ = serverId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object account_ = "";
      /**
       * <code>string account = 3;</code>
       * @return The account.
       */
      public java.lang.String getAccount() {
        java.lang.Object ref = account_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          account_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string account = 3;</code>
       * @return The bytes for account.
       */
      public com.google.protobuf.ByteString
          getAccountBytes() {
        java.lang.Object ref = account_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          account_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string account = 3;</code>
       * @param value The account to set.
       * @return This builder for chaining.
       */
      public Builder setAccount(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        account_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string account = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccount() {
        
        account_ = getDefaultInstance().getAccount();
        onChanged();
        return this;
      }
      /**
       * <code>string account = 3;</code>
       * @param value The bytes for account to set.
       * @return This builder for chaining.
       */
      public Builder setAccountBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        account_ = value;
        onChanged();
        return this;
      }

      private int finalLevel_ ;
      /**
       * <code>int32 finalLevel = 4;</code>
       * @return The finalLevel.
       */
      @java.lang.Override
      public int getFinalLevel() {
        return finalLevel_;
      }
      /**
       * <code>int32 finalLevel = 4;</code>
       * @param value The finalLevel to set.
       * @return This builder for chaining.
       */
      public Builder setFinalLevel(int value) {
        
        finalLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 finalLevel = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFinalLevel() {
        
        finalLevel_ = 0;
        onChanged();
        return this;
      }

      private int diffLevel_ ;
      /**
       * <code>int32 diffLevel = 5;</code>
       * @return The diffLevel.
       */
      @java.lang.Override
      public int getDiffLevel() {
        return diffLevel_;
      }
      /**
       * <code>int32 diffLevel = 5;</code>
       * @param value The diffLevel to set.
       * @return This builder for chaining.
       */
      public Builder setDiffLevel(int value) {
        
        diffLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 diffLevel = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiffLevel() {
        
        diffLevel_ = 0;
        onChanged();
        return this;
      }

      private int finalExp_ ;
      /**
       * <code>int32 finalExp = 6;</code>
       * @return The finalExp.
       */
      @java.lang.Override
      public int getFinalExp() {
        return finalExp_;
      }
      /**
       * <code>int32 finalExp = 6;</code>
       * @param value The finalExp to set.
       * @return This builder for chaining.
       */
      public Builder setFinalExp(int value) {
        
        finalExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 finalExp = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearFinalExp() {
        
        finalExp_ = 0;
        onChanged();
        return this;
      }

      private int diffExp_ ;
      /**
       * <code>int32 diffExp = 7;</code>
       * @return The diffExp.
       */
      @java.lang.Override
      public int getDiffExp() {
        return diffExp_;
      }
      /**
       * <code>int32 diffExp = 7;</code>
       * @param value The diffExp to set.
       * @return This builder for chaining.
       */
      public Builder setDiffExp(int value) {
        
        diffExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 diffExp = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiffExp() {
        
        diffExp_ = 0;
        onChanged();
        return this;
      }

      private int serverId_ ;
      /**
       * <code>int32 serverId = 8;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <code>int32 serverId = 8;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 serverId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.Player)
    }

    // @@protoc_insertion_point(class_scope:model.Player)
    private static final ehooray.fishing.dto.proto.model.PlayerProto.Player DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.PlayerProto.Player();
    }

    public static ehooray.fishing.dto.proto.model.PlayerProto.Player getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Player>
        PARSER = new com.google.protobuf.AbstractParser<Player>() {
      @java.lang.Override
      public Player parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Player> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerProto.Player getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Player_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Player_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022model/Player.proto\022\005model\"\225\001\n\006Player\022\020" +
      "\n\010entityId\030\001 \001(\003\022\014\n\004name\030\002 \001(\t\022\017\n\007accoun" +
      "t\030\003 \001(\t\022\022\n\nfinalLevel\030\004 \001(\005\022\021\n\tdiffLevel" +
      "\030\005 \001(\005\022\020\n\010finalExp\030\006 \001(\005\022\017\n\007diffExp\030\007 \001(" +
      "\005\022\020\n\010serverId\030\010 \001(\005BH\n\037ehooray.fishing.d" +
      "to.proto.modelB\013PlayerProtoH\002\252\002\025Fishing." +
      "Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_Player_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_Player_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Player_descriptor,
        new java.lang.String[] { "EntityId", "Name", "Account", "FinalLevel", "DiffLevel", "FinalExp", "DiffExp", "ServerId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
