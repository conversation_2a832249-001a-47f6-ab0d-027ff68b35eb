package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("GachaItemTable.xlsx")
public class GachaItemCsv extends ACsv<GachaItemCsv.Row>
{
    public enum ItemType
    {
        FishRod(1),
        PlayerItem(2)
        ;
        
        protected int id;
        private ItemType(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<ItemType> valueOf(int id)
        {
            return Arrays.stream(ItemType.values()).filter(x-> x.getId() == id).findFirst();
        }

    }

    public static class Row extends ARow
    {

        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("ItemGroup")
        protected int itemGroup;

        @ExcelCellName("ItemType")
        protected int itemType;

        @ExcelCellName("ItemID")
        protected int itemId;

        @ExcelCellName("ItemNum")
        protected int itemNum;

        @ExcelCellName("Weight")
        protected int weight;

        @Override
        public int getId()
        {
            return id;
        }

        public int getItemGroup()
        {
            return itemGroup;
        }

        public void setItemGroup(int itemGroup)
        {
            this.itemGroup = itemGroup;
        }

        public int getItemType()
        {
            return itemType;
        }

        public ItemType getItemTypeEnum()
        {
            return ItemType.valueOf(getItemType()).get();
        }

        public int getItemId()
        {
            return itemId;
        }

        public int getItemNum()
        {
            return itemNum;
        }

        public int getWeight()
        {
            return weight;
        }
    }

    // another utility function at here
    protected HashMap<Integer, List<Row>> groupMap;

    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();

        Collection<Row> rows = getRows();
        groupMap = new HashMap<>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsInGroup = groupMap.get(row.getItemGroup());
            if(rowsInGroup == null)
            {
                rowsInGroup = new ArrayList<>();
                groupMap.put(row.getItemGroup(), rowsInGroup);
            }
            rowsInGroup.add(row);
        }
    }

    public List<Row> findAllByGroup(int group)
    {
        return groupMap.get(group);
    }
}
