---
description: Details the game's data architecture and configuration systems, including various JSON data files (fish, lures, equipment, attributes), data managers, fish data structures, crafting system with 60 recipes, inventory management system, and equipment system. This file serves as the guiding document for data layer design and implementation.
globs: 
alwaysApply: false
---
# 数据和配置系统

## 数据文件
- **[gameConfig.json](mdc:FishingPhaser/src/data/gameConfig.json)** - 游戏基础配置
- **[fish.json](mdc:FishingPhaser/src/data/fish.json)** - 鱼类数据（50种鱼卡）
- **[lures.json](mdc:FishingPhaser/src/data/lures.json)** - 诱饵类型和配置
- **[equipment.json](mdc:FishingPhaser/src/data/equipment.json)** - 装备数据（钓竿、诱饵、船只、服装）
- **[attributes.json](mdc:FishingPhaser/src/data/attributes.json)** - 属性系统配置
- **[inventorySchema.json](mdc:FishingPhaser/src/data/inventorySchema.json)** - 库存系统架构

## 数据管理
- **[DataLoader.js](mdc:FishingPhaser/src/scripts/DataLoader.js)** - 数据加载和初始化
- **[InventoryManager.js](mdc:FishingPhaser/src/scripts/InventoryManager.js)** - 库存管理系统
- **[InventoryValidator.js](mdc:FishingPhaser/src/scripts/InventoryValidator.js)** - 库存验证逻辑

## 鱼类数据结构
- **基本属性**：大小（1-10）、侵略性（1-10）、难以捕捉性（1-10）
- **能力属性**：力量（1-10）、稀有度（1-10）、重量（0.1-500kg）
- **行为属性**：速度（1-10）、深度偏好（1-10）、饵料偏好（1-10）
- **特殊属性**：耐力（1-10）、活跃时间段/天气偏好

## 制作系统
- **[CraftingManager.js](mdc:FishingPhaser/src/scripts/CraftingManager.js)** - 合成系统管理
- **60个合成配方**：
  - 钓竿（15个）：竹竿 → 玻璃纤维竿 → 碳纤维竿 → 钢制竿 → 钛合金竿
  - 诱饵（15个）：基础旋转器 → 软虫 → 飞钓诱饵 → 波普诱饵 → 勺形诱饵
  - 船只（15个）：划艇 → 小艇 → 快艇 → 游艇 → 豪华邮轮
  - 服装（15个）：渔夫帽 → 太阳镜 → 钓鱼背心 → 比基尼上衣 → 凉鞋

## 制作成本
- **输入**：鱼卡（1-5星）
- **费用**：200-16,000金币
- **时间**：2-30分钟
- **升级**：合并低级装备创造高级版本

## 库存系统
- **容量**：50个槽位（可通过船只或技能扩展到100）
- **分类**：鱼卡、装备（钓竿、诱饵、船只、服装）、消耗品
- **管理**：按类型、稀有度或获取时间排序
- **批量操作**：出售重复物品等功能

## 装备系统
- **装备槽位**：7个槽位（1钓竿、1诱饵、1船只、3服装、1-3同伴）
- **属性加成**：装备属性与玩家属性叠加
- **获取方式**：通过合成制作或商店购买

- **升级机制**：合并低级装备创造高级版本