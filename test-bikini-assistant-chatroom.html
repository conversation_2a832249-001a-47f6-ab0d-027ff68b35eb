<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bikini Assistant Cha<PERSON><PERSON> Test - <PERSON><PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
            color: #ecf0f1;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .test-info {
            background: rgba(44, 62, 80, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .feature-card {
            background: rgba(52, 73, 94, 0.8);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .feature-card h3 {
            color: #e74c3c;
            margin-top: 0;
        }
        .npc-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .npc-card {
            background: rgba(22, 33, 62, 0.9);
            padding: 15px;
            border-radius: 10px;
            flex: 1;
            border: 2px solid #34495e;
        }
        .npc-card.mia { border-color: #64b5f6; }
        .npc-card.sophie { border-color: #ff9800; }
        .npc-card.luna { border-color: #9c27b0; }
        .romance-meter {
            background: #2c3e50;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .romance-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .controls {
            background: rgba(39, 55, 70, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #2980b9;
        }
        .button.primary {
            background: #e74c3c;
        }
        .button.primary:hover {
            background: #c0392b;
        }
        .instructions {
            background: rgba(46, 125, 50, 0.2);
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            color: #4caf50;
            margin-top: 0;
        }
        #gameContainer {
            width: 100%;
            height: 600px;
            background: #000;
            border-radius: 10px;
            border: 2px solid #34495e;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .tech-item {
            background: rgba(127, 140, 141, 0.2);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏖️ Bikini Assistant Romance Meter Chatroom System</h1>
            <p>Modern chat interface for Luxury Angler NPCs</p>
        </div>

        <div class="instructions">
            <h4>🚀 How to Test the Chatroom System:</h4>
            <ol>
                <li><strong>Access:</strong> Go to Boat Menu → Click "CHAT" button</li>
                <li><strong>Navigate:</strong> Select different NPCs from the left panel</li>
                <li><strong>Chat:</strong> Use quick action buttons or send custom messages</li>
                <li><strong>Romance:</strong> Watch romance meters increase with interactions</li>
                <li><strong>Features:</strong> Test gifts, flirting, and fishing invitations</li>
            </ol>
        </div>

        <div class="test-info">
            <h2>🌟 System Features Implemented</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>💬 Modern Chat Interface</h3>
                    <ul>
                        <li>Three-panel layout design</li>
                        <li>Real-time message bubbles</li>
                        <li>Message persistence</li>
                        <li>Timestamps and formatting</li>
                        <li>Auto-response system</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>💕 Romance Meter System</h3>
                    <ul>
                        <li>Visual romance progress bars</li>
                        <li>Color-coded relationship levels</li>
                        <li>Real-time meter updates</li>
                        <li>Relationship status tracking</li>
                        <li>Achievement integration</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>🎯 Quick Actions</h3>
                    <ul>
                        <li>Send Message (+ 1 romance)</li>
                        <li>Give Gift (+ 5 romance)</li>
                        <li>Flirt (+ 3 romance)</li>
                        <li>Romantic Talk (+ 5 romance)</li>
                        <li>Fishing Together (+ 3 romance)</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>🏖️ Bikini Assistants</h3>
                    <ul>
                        <li>Mia - Friendly & Helpful</li>
                        <li>Sophie - Energetic & Competitive</li>
                        <li>Luna - Mysterious & Wise</li>
                        <li>Unique personalities</li>
                        <li>Specialized knowledge</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>💾 Data Persistence</h3>
                    <ul>
                        <li>Chat history saved locally</li>
                        <li>Romance progress tracking</li>
                        <li>Relationship status memory</li>
                        <li>Cross-session continuity</li>
                        <li>Quest integration</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>🔗 System Integration</h3>
                    <ul>
                        <li>Full dialog system access</li>
                        <li>Quest system hooks</li>
                        <li>Achievement unlocks</li>
                        <li>Event-driven updates</li>
                        <li>Save/load integration</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="npc-preview">
            <div class="npc-card mia">
                <h3>💙 Bikini Assistant Mia</h3>
                <p><strong>Personality:</strong> Friendly, enthusiastic, knowledgeable</p>
                <p><strong>Specialties:</strong> Beginner tips, equipment advice, local spots</p>
                <div class="romance-meter">
                    <div class="romance-fill" style="width: 0%; background: #64b5f6;"></div>
                </div>
                <p><em>"Hey there! Welcome to our private chat! 😊"</em></p>
            </div>

            <div class="npc-card sophie">
                <h3>🧡 Bikini Assistant Sophie</h3>
                <p><strong>Personality:</strong> Energetic, competitive, passionate</p>
                <p><strong>Specialties:</strong> Advanced techniques, competitions, rare fish</p>
                <div class="romance-meter">
                    <div class="romance-fill" style="width: 0%; background: #ff9800;"></div>
                </div>
                <p><em>"OMG hi!! I'm so excited to chat with you! 🌟"</em></p>
            </div>

            <div class="npc-card luna">
                <h3>💜 Bikini Assistant Luna</h3>
                <p><strong>Personality:</strong> Mysterious, wise, spiritual</p>
                <p><strong>Specialties:</strong> Deep sea fishing, ocean folklore, meditation</p>
                <div class="romance-meter">
                    <div class="romance-fill" style="width: 0%; background: #9c27b0;"></div>
                </div>
                <p><em>"Greetings... I sense a kindred spirit... 🌙"</em></p>
            </div>
        </div>

        <div class="controls">
            <h3>🎮 Test Controls</h3>
            <button class="button primary" onclick="startGame()">🚀 Launch Game & Test Chatroom</button>
            <button class="button" onclick="resetRomanceData()">🔄 Reset Romance Data</button>
            <button class="button" onclick="simulateRomanceProgress()">💕 Simulate Romance Progress</button>
            <button class="button" onclick="showDebugInfo()">🔍 Show Debug Info</button>
        </div>

        <div id="gameContainer">
            <!-- Phaser game will be injected here -->
        </div>

        <div class="test-info">
            <h3>🛠️ Technical Implementation</h3>
            
            <div class="tech-stack">
                <div class="tech-item">
                    <strong>ChatroomScene.js</strong><br>
                    Main chat interface (500+ lines)
                </div>
                <div class="tech-item">
                    <strong>Romance System</strong><br>
                    Real-time meter tracking
                </div>
                <div class="tech-item">
                    <strong>UI Components</strong><br>
                    Three-panel responsive layout
                </div>
                <div class="tech-item">
                    <strong>Message System</strong><br>
                    Bubbles, timestamps, persistence
                </div>
                <div class="tech-item">
                    <strong>Quick Actions</strong><br>
                    Buttons for romance interactions
                </div>
                <div class="tech-item">
                    <strong>NPC Personalities</strong><br>
                    Unique response patterns
                </div>
            </div>

            <h4>🔧 Key Features:</h4>
            <ul>
                <li><strong>Responsive Design:</strong> Adapts to different screen sizes</li>
                <li><strong>Event-Driven:</strong> Real-time updates via Phaser events</li>
                <li><strong>Persistence:</strong> LocalStorage for chat history and romance data</li>
                <li><strong>Integration:</strong> Seamless connection with existing dialog system</li>
                <li><strong>Accessibility:</strong> Keyboard and mouse navigation support</li>
                <li><strong>Performance:</strong> Efficient rendering and memory management</li>
            </ul>

            <h4>📊 Romance Meter Thresholds:</h4>
            <ul>
                <li><strong>Stranger:</strong> 0-19 (Gray meter)</li>
                <li><strong>Acquaintance:</strong> 20-39 (Green meter)</li>
                <li><strong>Friend:</strong> 40-59 (Yellow meter)</li>
                <li><strong>Close Friend:</strong> 60-79 (Orange meter)</li>
                <li><strong>Romantic Interest:</strong> 80-99 (Red meter)</li>
                <li><strong>Lover:</strong> 100 (Deep red meter)</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { game } from './src/main.js';

        window.startGame = function() {
            console.log('Starting Luxury Angler with Bikini Assistant Chatroom...');
            
            // Game should auto-start from main.js
            // Navigate to chatroom via: Boat Menu → CHAT button
            
            setTimeout(() => {
                updateRomanceMeters();
            }, 2000);
        };

        window.resetRomanceData = function() {
            console.log('Resetting romance data...');
            
            // Clear romance data from localStorage
            const npcs = ['mia', 'sophie', 'luna'];
            npcs.forEach(npc => {
                localStorage.removeItem(`npc_${npc}`);
                localStorage.removeItem(`chat_history_${npc}`);
            });
            
            // Reset UI meters
            document.querySelectorAll('.romance-fill').forEach(fill => {
                fill.style.width = '0%';
            });
            
            alert('Romance data reset! Start fresh with all NPCs.');
        };

        window.simulateRomanceProgress = function() {
            console.log('Simulating romance progress...');
            
            const meters = document.querySelectorAll('.romance-fill');
            meters.forEach((meter, index) => {
                const progress = Math.random() * 100;
                meter.style.width = progress + '%';
                
                // Animate the progress
                setTimeout(() => {
                    meter.style.transition = 'width 2s ease-in-out';
                }, index * 500);
            });
        };

        window.showDebugInfo = function() {
            console.log('=== BIKINI ASSISTANT CHATROOM DEBUG ===');
            console.log('NPCs available:', ['mia', 'sophie', 'luna']);
            
            // Check localStorage data
            const npcs = ['mia', 'sophie', 'luna'];
            npcs.forEach(npc => {
                const npcData = localStorage.getItem(`npc_${npc}`);
                const chatData = localStorage.getItem(`chat_history_${npc}`);
                console.log(`${npc.toUpperCase()}:`, {
                    romanceData: npcData ? JSON.parse(npcData) : 'Not found',
                    chatHistory: chatData ? JSON.parse(chatData).length + ' messages' : 'No history'
                });
            });
            
            console.log('Scene registration: ChatroomScene should be available');
            console.log('Access path: BoatMenuScene → openChatroom() → ChatroomScene');
            console.log('==========================================');
        };

        function updateRomanceMeters() {
            // This would normally get data from the game
            // For demo purposes, we'll show static progress
            const meters = [
                { element: document.querySelectorAll('.romance-fill')[0], progress: 25, color: '#64b5f6' },
                { element: document.querySelectorAll('.romance-fill')[1], progress: 40, color: '#ff9800' },
                { element: document.querySelectorAll('.romance-fill')[2], progress: 15, color: '#9c27b0' }
            ];
            
            meters.forEach(meter => {
                if (meter.element) {
                    meter.element.style.width = meter.progress + '%';
                    meter.element.style.background = meter.color;
                }
            });
        }

        // Auto-start game when page loads
        window.addEventListener('load', () => {
            console.log('🏖️ Bikini Assistant Chatroom Test Ready!');
            console.log('📖 Instructions: Go to Boat Menu → Click CHAT to test the system');
            
            // Show initial romance meters
            setTimeout(updateRomanceMeters, 1000);
        });
    </script>
</body>
</html> 