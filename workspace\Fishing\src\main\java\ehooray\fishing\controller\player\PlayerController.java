package ehooray.fishing.controller.player;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.protomap.model.PlayerProtoMap;
import ehooray.fishing.pojo.WebSocketHandleMediator;

@Controller("PlayerController")
public class PlayerController 
{
    @Autowired
    protected PlayerProtoMap playerMap;
    
    @TransactionMethod(Method.Read)
	public void getPlayer(WebSocketHandleMediator handleMediator) throws Exception
	{
		handleMediator.addResult(playerMap.toAnyByEntity(handleMediator.getPlayer().get()));
	}
}
