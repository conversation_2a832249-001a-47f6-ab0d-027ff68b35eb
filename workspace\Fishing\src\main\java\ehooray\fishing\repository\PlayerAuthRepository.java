package ehooray.fishing.repository;

import java.util.Optional;

import org.springframework.data.repository.CrudRepository;

import ehooray.fishing.entity.PlayerAuthEntity;


public interface PlayerAuthRepository extends CrudRepository<PlayerAuthEntity, Long> 
{
	  Optional<PlayerAuthEntity> findByAccountAndServerId(String account, int serverId);
	  Optional<PlayerAuthEntity> findByToken(String token);
}
