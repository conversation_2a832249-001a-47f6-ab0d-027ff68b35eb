package ehooray.fishing.component.component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.trigger.BaseTrigger;
import ehooray.fishing.component.trigger.ConditionComponentTrigger;
//import ehooray.formosa.entity.BuildingEntity;
//import ehooray.formosa.entity.CharacterEntity;
//import ehooray.fishing.entity.ComponentEntity;
import ehooray.fishing.entity.PlayerEntity;
//import ehooray.formosa.pojo.csv.BuildingCsv;
//import ehooray.formosa.pojo.csv.CharacterCsv;
import ehooray.fishing.pojo.csv.ComponentCsv;
//import ehooray.formosa.pojo.csv.DisasterBuildingCsv;
//import ehooray.fishing.pojo.csv.SkillCsv;
//import ehooray.fishing.service.component.ComponentService;
import ehooray.fishing.service.player.PlayerService;

@Component
public class TriggeredComponentCollector
{
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected ConditionComponentTrigger conditionTrigger;
    
    @Autowired
    protected PlayerService playerService;
    
//    @Autowired
//    protected ComponentService componentService;
    
//    protected void findByCharacter(Input input, List<ComponentCsv.Row> collector) throws Exception
//    {
//        if(input.getCharacter().isEmpty()) return;
//        
//        // get from csv
//        CharacterCsv.Row characterRow = csvContainer.getCsv(CharacterCsv.class).getById(input.getCharacter().get().getCsvId());
//        for(int i = 0; i < characterRow.getSkills().length; i++)
//        {
//            int skillGroup = characterRow.getSkill(i);
//            addSkillRow(input, skillGroup, collector);
//        }
//        // get from entity
//        for(int i = 0; i < input.getCharacter().get().getSkillCount(); i++)
//        {
//            int skillGroup = input.getCharacter().get().getSkill(i);
//            addSkillRow(input, skillGroup, collector);
//        }
//    }
//    
//    protected void findByBuilding(Input input, List<ComponentCsv.Row> collector) throws Exception
//    {
//        if(input.getBuilding().isEmpty()) return;
//        
//        BuildingCsv.Row buildingRow = csvContainer.getCsv(BuildingCsv.class).getById(input.getBuilding().get().getCsvId());
//        addSkillRow(input, buildingRow.getSkillGroup(), collector);
//        
//        // check disaster have skill
//        Optional<DisasterBuildingCsv.Row> disasterBuildingRow = csvContainer.getCsv(DisasterBuildingCsv.class).getOptionalById(input.getBuilding().get().getDisasterBuildingCsvId());
//        if(disasterBuildingRow.isEmpty()) return;
//        
//        addSkillRow(input, disasterBuildingRow.get().getSkillId(), collector);
//    }
//    
//    protected void findByPlayerBuff(Input input, List<ComponentCsv.Row> collector) throws Exception
//    {
//        PlayerEntity player = playerService.findByEntityId(input.getTriggerInput().getPlayerId()).get();
//        List<ComponentEntity> playerBuffs = componentService.findByType(player, ComponentService.ComponentType.PlayerBuff.getId());
//        
//        ComponentCsv componentCsv = csvContainer.getCsv(ComponentCsv.class);
//        for(ComponentEntity component : playerBuffs)
//        {
//            ComponentCsv.Row componentRow = componentCsv.getById(component.getCsvId());
//            addSkillRow(input, componentRow.getParameterByInt(0), collector);
//        }
//    }
    
//    protected void addSkillRow(Input input, int skillGroup, List<ComponentCsv.Row> collector) throws Exception
//    {
//        List<SkillCsv.Row> skillRows = csvContainer.getCsv(SkillCsv.class).findAllByTriggerGroup(
//                input.getTriggerInput().getTriggerId(), skillGroup);
//        
//        ComponentCsv componentCsv = csvContainer.getCsv(ComponentCsv.class);
//        List<ComponentCsv.Row> tempList = new ArrayList<ComponentCsv.Row>(1);
//        tempList.add(null);
//        for(SkillCsv.Row skillRow : skillRows)
//        {
//            // check filter
//            Optional<ComponentCsv.Row> filterComponentRow = componentCsv.getOptionalById(skillRow.getFilter());
//            if(filterComponentRow.isPresent())
//            {
//                tempList.set(0, filterComponentRow.get());
//                boolean filterPass = (boolean)conditionTrigger.invoke(input.getTriggerInput(), tempList);
//                if(!filterPass) continue;
//            }
//            
//            collector.add(componentCsv.getById(skillRow.getComponentId()));
//        }
//    }
    
    public List<ComponentCsv.Row> collect(Input input) throws Exception
    {
        List<ComponentCsv.Row> collector = new ArrayList<ComponentCsv.Row>();
        // collect function put here
//        findByBuilding(input, collector);
//        findByCharacter(input, collector);
//        findByPlayerBuff(input, collector);
        
        return collector;
    }
    
    public static class Input
    {
        protected BaseTrigger.Input triggerInput;
        
//        // find component by these data
//        protected Optional<BuildingEntity> building = Optional.empty();
//        protected Optional<CharacterEntity> character = Optional.empty();
//
//        public Optional<BuildingEntity> getBuilding()
//        {
//            return building;
//        }
//        public void setBuilding(Optional<BuildingEntity> building)
//        {
//            this.building = building;
//        }
//        public Optional<CharacterEntity> getCharacter()
//        {
//            return character;
//        }
//        public void setCharacter(Optional<CharacterEntity> character)
//        {
//            this.character = character;
//        }
        public BaseTrigger.Input getTriggerInput()
        {
            return triggerInput;
        }
        public void setTriggerInput(BaseTrigger.Input triggerInput)
        {
            this.triggerInput = triggerInput;
        }
        
    }
    
}
