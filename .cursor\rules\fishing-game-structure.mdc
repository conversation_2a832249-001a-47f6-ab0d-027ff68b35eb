---
description: 
globs: 
alwaysApply: false
---
# 釣魚遊戲專案結構指南

本專案是一個基於 Phaser 3 的 2D 釣魚模擬遊戲 "Luxury Angler"。

## GDD 目錄參考
1.  Game Overview
2.  Core Gameplay Loop
3.  Attributes System
4.  Fishing System
    *   4.1 Fishing Mechanics
        *   4.1.1 Cast Minigame
        *   4.1.2 Lure Minigame (Enhanced)
        *   4.1.3 Reel-In Minigame
    *   4.2 Time System
    *   4.3 Weather System
    *   4.4 Lure Types
    *   4.5 Fish Struggle Styles
    *   4.6 QTE Reaction Controls
5.  Crafting System
6.  Level Progression
7.  Inventory and Equipment
    *   7.1 Inventory System
    *   7.2 Equipment System
8.  Social System
    *   8.1 Cruise Cabin
    *   8.2 Story and Dialog System (RenJS)
9.  Shop System
10. Achievement System
11. Economy System
12. Art and Audio
13. Technical Design

## 專案檔案組織

### 數據層 (Data Layer)
數據檔案位於 `src/data/` 目錄，包含遊戲的核心配置和內容：

- [attributes.json](mdc:src/data/attributes.json): 屬性數據，定義角色或物品的各種屬性 [GDD: 3, 3.1, 3.2]
- [equipment.json](mdc:src/data/equipment.json): 裝備數據，定義各種裝備的屬性 [GDD: 7.2]
- [fish.json](mdc:src/data/fish.json): 魚類數據，定義各種魚類的屬性和行為 [GDD: 3.2, 4.5]
- [gameConfig.json](mdc:src/data/gameConfig.json): 遊戲配置，包含全域遊戲設定 [GDD: 13]
- [inventorySchema.json](mdc:src/data/inventorySchema.json): 庫存模式，定義庫存系統的數據結構 [GDD: 7.1]
- [LocationData.js](mdc:src/data/LocationData.js): 地點數據，定義遊戲中不同地點的資訊 [GDD: 2.1, 2.2, 4.4]
- [lures.json](mdc:src/data/lures.json): 魚餌數據，定義各種魚餌的屬性 [GDD: 4.4, 7.2]
- [sample-renjs-quest-dialog.yaml](mdc:src/data/sample-renjs-quest-dialog.yaml): RenJS 任務對話範例，用於對話系統 [GDD: 8.2, 8.3]
- [TextConfig.js](mdc:src/data/TextConfig.js): 文字配置，可能包含遊戲中各種文字字串 [GDD: 12, 13]

### 場景層 (Scene Layer)
場景檔案位於 `src/scenes/` 目錄，管理遊戲的不同畫面和狀態：

- [AlbumScene.js](mdc:src/scenes/AlbumScene.js): 圖鑑場景，可能用於展示收集到的魚類或其他物品 [GDD: 8.1]
- [BoatMenuScene.js](mdc:src/scenes/BoatMenuScene.js): 船隻選單場景，可能用於選擇船隻或相關操作 [GDD: 2.1, 2.2]
- [BootScene.js](mdc:src/scenes/BootScene.js): 啟動場景，遊戲的初始進入點 [GDD: 13]
- [CabinScene.js](mdc:src/scenes/CabinScene.js): 小屋場景，可能是玩家的基地或休息場所 [GDD: 8.1]
- [DialogScene.js](mdc:src/scenes/DialogScene.js): 對話場景，處理遊戲中的角色對話 [GDD: 8.2]
- [GameScene.js](mdc:src/scenes/GameScene.js): 核心遊戲場景，主要的釣魚活動發生在此 [GDD: 2, 4]
- [HUDScene.js](mdc:src/scenes/HUDScene.js): HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊 [GDD: 2.2, 12]
- [MenuScene.js](mdc:src/scenes/MenuScene.js): 主選單場景 [GDD: 2.1]
- [PreloadScene.js](mdc:src/scenes/PreloadScene.js): 預載入場景，在遊戲開始前載入資源 [GDD: 13]
- [QuestScene.js](mdc:src/scenes/QuestScene.js): 任務場景，顯示和管理任務 [GDD: 2.2, 8.3]
- [SettingsScene.js](mdc:src/scenes/SettingsScene.js): 設定場景，處理遊戲設定選項 [GDD: 13]
- [ShopScene.js](mdc:src/scenes/ShopScene.js): 商店場景，用於購買物品 [GDD: 2, 9]

### 腳本層 (Scripts Layer)
核心邏輯腳本位於 `src/scripts/` 目錄：

- [AchievementEnhancer.js](mdc:src/scripts/AchievementEnhancer.js): 成就增強器，可能與成就系統的擴展功能相關 [GDD: 10]
- [AdvancedQuestSystem.js](mdc:src/scripts/AdvancedQuestSystem.js): 進階任務系統，提供更複雜的任務功能 [GDD: 8.3]
- [AudioManager.js](mdc:src/scripts/AudioManager.js): 音訊管理器，處理遊戲中的聲音和音樂 [GDD: 12]
- [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿迷你遊戲邏輯 [GDD: 4.1.1]
- [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成管理器，處理物品的製作 [GDD: 5]
- [DataLoader.js](mdc:src/scripts/DataLoader.js): 數據載入器，負責載入遊戲所需的數據檔案 [GDD: 13]
- [DialogManager.js](mdc:src/scripts/DialogManager.js): 對話管理器，管理遊戲中的對話流程 [GDD: 8.2]
- [EnhancedAudioManager.js](mdc:src/scripts/EnhancedAudioManager.js): 增強型音訊管理器，提供更進階的音訊控制功能 [GDD: 12]
- [EquipmentEnhancer.js](mdc:src/scripts/EquipmentEnhancer.js): 裝備強化器，處理裝備的升級和強化 [GDD: 7.2]
- [EventEmitter.js](mdc:src/scripts/EventEmitter.js): 事件發射器，用於實現事件驅動的程式設計模式 [GDD: 13]
- [FishDatabase.js](mdc:src/scripts/FishDatabase.js): 魚類資料庫，儲存和管理魚類數據 [GDD: 3.2, 4.5, 13]
- [FishSpawner.js](mdc:src/scripts/FishSpawner.js): 魚類生成器，控制魚類在遊戲世界的生成 [GDD: 4, 4.5]
- [FishingMinigames.js](mdc:src/scripts/FishingMinigames.js): 釣魚迷你遊戲的主控腳本或集合 [GDD: 4.1]
- [GameLoop.js](mdc:src/scripts/GameLoop.js): 遊戲循環，驅動遊戲的核心邏輯 [GDD: 2, 13]
- [GameState.js](mdc:src/scripts/GameState.js): 遊戲狀態管理器，追蹤和管理遊戲的整體狀態 [GDD: 2, 13]
- [HCGNotificationManager.js](mdc:src/scripts/HCGNotificationManager.js): HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統) [GDD: 8.1, 10]
- [HCGUnlockSystem.js](mdc:src/scripts/HCGUnlockSystem.js): HCG 解鎖系統 (具體 HCG 指代不明) [GDD: 8.1, 10]
- [InputManager.js](mdc:src/scripts/InputManager.js): 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入 [GDD: 4.1, 13]
- [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理器，處理玩家物品的儲存和使用 [GDD: 7.1]
- [InventoryValidator.js](mdc:src/scripts/InventoryValidator.js): 庫存驗證器，驗證庫存操作的有效性 [GDD: 7.1, 13]
- [LocationManager.js](mdc:src/scripts/LocationManager.js): 地點管理器，管理遊戲中的不同地點 [GDD: 2, 4.4]
- [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚迷你遊戲邏輯 [GDD: 4.1.2]
- [PlayerController.js](mdc:src/scripts/PlayerController.js): 玩家控制器，處理玩家輸入和行為 [GDD: 2, 3, 4]
- [PlayerProgression.js](mdc:src/scripts/PlayerProgression.js): 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容 [GDD: 3.1.1, 6]
- [QuestManager.js](mdc:src/scripts/QuestManager.js): 任務管理器，管理遊戲中的任務 [GDD: 8.3]
- [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線迷你遊戲邏輯 [GDD: 4.1.3]
- [RenJsContentValidator.js](mdc:src/scripts/RenJsContentValidator.js): RenJS 內容驗證器，驗證 RenJS 相關內容的正確性 [GDD: 8.2, 13]
- [RenJsLoader.js](mdc:src/scripts/RenJsLoader.js): RenJS 載入器，載入 RenJS 相關資源 [GDD: 8.2, 13]
- [RenJsSaveIntegration.js](mdc:src/scripts/RenJsSaveIntegration.js): RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中 [GDD: 8.2, 13]
- [SaveUtility.js](mdc:src/scripts/SaveUtility.js): 存檔工具，處理遊戲的儲存和載入 [GDD: 13]
- [SceneManager.js](mdc:src/scripts/SceneManager.js): 場景管理器，管理不同遊戲場景的切換 [GDD: 13]
- [SettingsManager.js](mdc:src/scripts/SettingsManager.js): 設定管理器，管理遊戲的各項設定 [GDD: 13]
- [TimeManager.js](mdc:src/scripts/TimeManager.js): 時間管理器，控制遊戲中的時間流逝 [GDD: 4.2]
- [TournamentManager.js](mdc:src/scripts/TournamentManager.js): 錦標賽管理器，組織和管理釣魚比賽 [GDD: 6, specific events]
- [WeatherManager.js](mdc:src/scripts/WeatherManager.js): 天氣管理器，控制遊戲中的天氣變化 [GDD: 4.3]

### UI層 (UI Layer)
用戶界面檔案位於 `src/ui/` 目錄：

- [AchievementPopupSystem.js](mdc:src/ui/AchievementPopupSystem.js): 成就彈出提示系統 [GDD: 10, 12]
- [AudioSettingsUI.js](mdc:src/ui/AudioSettingsUI.js): 音訊設定的使用者介面 [GDD: 12, 13]
- [CraftingUI.js](mdc:src/ui/CraftingUI.js): 合成系統的使用者介面 [GDD: 5, 12]
- [EquipmentEnhancementUI.js](mdc:src/ui/EquipmentEnhancementUI.js): 裝備強化系統的使用者介面 [GDD: 7.2, 12]
- [FishCollectionUI.js](mdc:src/ui/FishCollectionUI.js): 魚類圖鑑或收集冊的使用者介面 [GDD: 7.1, 8.1, 12]
- [InventoryUI.js](mdc:src/ui/InventoryUI.js): 庫存系統的使用者介面 [GDD: 7.1, 12]
- [LoadingStateManager.js](mdc:src/ui/LoadingStateManager.js): 載入狀態管理器，可能負責顯示載入進度條等 [GDD: 12, 13]
- [MapSelectionUI.js](mdc:src/ui/MapSelectionUI.js): 地圖選擇使用者介面 [GDD: 2, 4.4, 12]
- [PlayerProgressionUI.js](mdc:src/ui/PlayerProgressionUI.js): 玩家進程的使用者介面，顯示玩家等級、經驗等 [GDD: 6, 12]
- [RenJsDebugUI.js](mdc:src/ui/RenJsDebugUI.js): RenJS 調試使用者介面，用於開發和測試 [GDD: 8.2, 13]
- [ShopUI.js](mdc:src/ui/ShopUI.js): 商店的使用者介面 [GDD: 9, 12]
- [TimeWeatherUI.js](mdc:src/ui/TimeWeatherUI.js): 時間和天氣資訊的使用者介面 [GDD: 4.2, 4.3, 12]
- [UITheme.js](mdc:src/ui/UITheme.js): UI 主題，定義介面元素的統一樣式 [GDD: 12]

## 開發指導原則

### 文件命名規範
- **場景**: 以 `Scene.js` 結尾
- **管理器**: 以 `Manager.js` 結尾  
- **UI組件**: 以 `UI.js` 結尾
- **迷你遊戲**: 以 `MiniGame.js` 結尾
- **數據檔案**: 使用 `.json` 或 `.js` 格式

### GDD對應關係
每個檔案都標記了對應的 GDD 章節編號，請在修改前參考相關的遊戲設計文檔。

### 核心架構
- 使用 Phaser 3 遊戲引擎
- 採用場景管理系統
- 數據驅動的設計模式
- 模組化的UI組件設計

### 重要系統檔案

**釣魚核心系統:**
- [CastingMiniGame.js](mdc:src/scripts/CastingMiniGame.js): 拋竿機制
- [LuringMiniGame.js](mdc:src/scripts/LuringMiniGame.js): 誘魚機制  
- [ReelingMiniGame.js](mdc:src/scripts/ReelingMiniGame.js): 收線機制
- [FishingMinigames.js](mdc:src/scripts/FishingMinigames.js): 迷你遊戲協調器

**管理系統:**
- [GameState.js](mdc:src/scripts/GameState.js): 遊戲狀態管理
- [PlayerController.js](mdc:src/scripts/PlayerController.js): 玩家控制
- [InventoryManager.js](mdc:src/scripts/InventoryManager.js): 庫存管理
- [CraftingManager.js](mdc:src/scripts/CraftingManager.js): 合成系統

**環境系統:**
- [TimeManager.js](mdc:src/scripts/TimeManager.js): 時間系統
- [WeatherManager.js](mdc:src/scripts/WeatherManager.js): 天氣系統
- [LocationManager.js](mdc:src/scripts/LocationManager.js): 地點管理

### 重要提醒
修改任何核心系統檔案時，請確保：
1. 檢查相關的GDD章節
2. 考慮對其他系統的影響
3. 更新相關的UI組件
4. 測試所有相關的迷你遊戲



