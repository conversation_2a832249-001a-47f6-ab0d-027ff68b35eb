// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/auth/FirebaseDelete.proto

package ehooray.fishing.dto.proto.request.auth;

public final class FirebaseDeleteProto {
  private FirebaseDeleteProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FirebaseDeleteOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.auth.FirebaseDelete)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string idToken = 1;</code>
     * @return The idToken.
     */
    java.lang.String getIdToken();
    /**
     * <code>string idToken = 1;</code>
     * @return The bytes for idToken.
     */
    com.google.protobuf.ByteString
        getIdTokenBytes();

    /**
     * <code>string userId = 2;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>string userId = 2;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();
  }
  /**
   * Protobuf type {@code request.auth.FirebaseDelete}
   */
  public static final class FirebaseDelete extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.auth.FirebaseDelete)
      FirebaseDeleteOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FirebaseDelete.newBuilder() to construct.
    private FirebaseDelete(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FirebaseDelete() {
      idToken_ = "";
      userId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FirebaseDelete();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.internal_static_request_auth_FirebaseDelete_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.internal_static_request_auth_FirebaseDelete_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.class, ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.Builder.class);
    }

    public static final int IDTOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object idToken_;
    /**
     * <code>string idToken = 1;</code>
     * @return The idToken.
     */
    @java.lang.Override
    public java.lang.String getIdToken() {
      java.lang.Object ref = idToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idToken_ = s;
        return s;
      }
    }
    /**
     * <code>string idToken = 1;</code>
     * @return The bytes for idToken.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdTokenBytes() {
      java.lang.Object ref = idToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private volatile java.lang.Object userId_;
    /**
     * <code>string userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userId_ = s;
        return s;
      }
    }
    /**
     * <code>string userId = 2;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.auth.FirebaseDelete}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.auth.FirebaseDelete)
        ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDeleteOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.internal_static_request_auth_FirebaseDelete_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.internal_static_request_auth_FirebaseDelete_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.class, ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        idToken_ = "";

        userId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.internal_static_request_auth_FirebaseDelete_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete build() {
        ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete buildPartial() {
        ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete result = new ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete(this);
        result.idToken_ = idToken_;
        result.userId_ = userId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private java.lang.Object idToken_ = "";
      /**
       * <code>string idToken = 1;</code>
       * @return The idToken.
       */
      public java.lang.String getIdToken() {
        java.lang.Object ref = idToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          idToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string idToken = 1;</code>
       * @return The bytes for idToken.
       */
      public com.google.protobuf.ByteString
          getIdTokenBytes() {
        java.lang.Object ref = idToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string idToken = 1;</code>
       * @param value The idToken to set.
       * @return This builder for chaining.
       */
      public Builder setIdToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        idToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string idToken = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdToken() {
        
        idToken_ = getDefaultInstance().getIdToken();
        onChanged();
        return this;
      }
      /**
       * <code>string idToken = 1;</code>
       * @param value The bytes for idToken to set.
       * @return This builder for chaining.
       */
      public Builder setIdTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        idToken_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object userId_ = "";
      /**
       * <code>string userId = 2;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string userId = 2;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>string userId = 2;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        userId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.auth.FirebaseDelete)
    }

    // @@protoc_insertion_point(class_scope:request.auth.FirebaseDelete)
    private static final ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete();
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FirebaseDelete>
        PARSER = new com.google.protobuf.AbstractParser<FirebaseDelete>() {
      @java.lang.Override
      public FirebaseDelete parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FirebaseDelete> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FirebaseDelete> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto.FirebaseDelete getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_auth_FirebaseDelete_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_auth_FirebaseDelete_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!request/auth/FirebaseDelete.proto\022\014req" +
      "uest.auth\"1\n\016FirebaseDelete\022\017\n\007idToken\030\001" +
      " \001(\t\022\016\n\006userId\030\002 \001(\tBW\n&ehooray.fishing." +
      "dto.proto.request.authB\023FirebaseDeletePr" +
      "otoH\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_auth_FirebaseDelete_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_auth_FirebaseDelete_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_auth_FirebaseDelete_descriptor,
        new java.lang.String[] { "IdToken", "UserId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
