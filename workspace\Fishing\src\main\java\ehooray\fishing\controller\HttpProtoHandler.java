package ehooray.fishing.controller;

import java.time.LocalDateTime;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.protobuf.Any;

import ehooray.fishing.component.ProtoHandler;
import ehooray.fishing.dto.proto.BaseProto;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.response.ResponseProto;
import ehooray.fishing.pojo.HttpProtoHandleMediator;

@RestController
@RequestMapping("/http-proto")
public class HttpProtoHandler
{
    @Autowired
    protected ProtoHandler protoHandler;

    @PostMapping
    public BaseProto.Base handleHttpRequest(@RequestBody BaseProto.Base baseProto, HttpSession session) throws Exception
    {
        try
        {
            RequestProto.Request request = baseProto.getData().unpack(RequestProto.Request.class);


            HttpProtoHandleMediator.Builder handleMediatorBuilder = new HttpProtoHandleMediator.Builder();
            handleMediatorBuilder.setRequest(request);
            handleMediatorBuilder.setSession(session);
            handleMediatorBuilder.setRequestStartTime(LocalDateTime.now());

            return toBaseProto(protoHandler.handleRequest(request, handleMediatorBuilder));
        }
        catch (Throwable e)
        {
            return toBaseProto(protoHandler.handleUnknownException(e));
        }
    }

    protected BaseProto.Base toBaseProto(ResponseProto.Response response)
    {
        BaseProto.Base.Builder baseProto = BaseProto.Base.newBuilder();
        baseProto.setOperator(BaseProto.Base.Operator.Response);
        baseProto.setData(Any.pack(response));
        return baseProto.build();
    }
}
