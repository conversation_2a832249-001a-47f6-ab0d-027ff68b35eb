package ehooray.fishing.config;

import java.util.Arrays;
import java.util.Optional;

public enum IabPlatformEnum
{
    UnityEditor(1),
    UnityIphone(2), 
    UnityAndroid(3)
    ;
    
    protected int id;
    private IabPlatformEnum(int id)
    {
        this.id = id;
    }
    
    public int getId()
    {
        return id;
    }
    
    public static Optional<IabPlatformEnum> valueOf(int id)
    {
        return Arrays.stream(IabPlatformEnum.values()).filter(x-> x.getId() == id).findFirst();
    }
}
