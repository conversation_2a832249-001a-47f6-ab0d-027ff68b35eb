package ehooray.fishing.controller.gacha;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.FishRodProtoMap;
import ehooray.fishing.component.protomap.model.GachaProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.component.utils.WeightRandomer;
import ehooray.fishing.dto.proto.request.gacha.BuyGachaProto;
import ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.DisplayCsv;
import ehooray.fishing.pojo.csv.GachaItemCsv;
import ehooray.fishing.pojo.csv.GachaMachineCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.RodCsv;
import ehooray.fishing.service.fishrod.FishRodService;
import ehooray.fishing.service.gacha.GachaService;
import ehooray.fishing.service.gacha.GachaService.BuyValue;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;

@Controller("GachaController")
public class GachaController
{
    @Autowired
    protected CsvContainer csvContainer;
    @Autowired
    protected GachaService gachaService;
    @Autowired
    protected PlayerItemService playerItemService;
    @Autowired
    protected FishRodService fishRodService;
    @Autowired
    protected GachaProtoMap gachaProtoMap;
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    @Autowired
    protected FishRodProtoMap fishRodProtoMap;
    @Autowired
    protected ScheduleCalculator scheduleCalculator;
    @Autowired
    protected WeightRandomer weightRandomer;
    
    @TransactionMethod(Method.Write)
    public void getGachas(WebSocketHandleMediator handleMediator) throws Exception
    {
        var player = handleMediator.getPlayer().get();
        var now = handleMediator.getRequestStartTime();
        var gachaCsv = csvContainer.getCsv(GachaMachineCsv.class);
        
        // remove not on csv gacha
        var missingCsvGachas = gachaService.getAllByPlayer(player).stream().filter(
                gacha -> gachaCsv.getOptionalById(gacha.getCsvId()).isEmpty()).collect(Collectors.toList());
        gachaService.deleteAll(player, missingCsvGachas);
        // remove already out of date shops
        var expiredGachas = gachaService.getAllByPlayer(player).stream().filter(gacha ->
        scheduleCalculator.isOutStartCycle(DisplayCsv.Type.Gacha, gacha.getCsvId(), gacha.getStartTime(), now)).collect(Collectors.toList());
        gachaService.deleteAll(player, expiredGachas);
        
        // find not exist gacha rows
        var existCsvIds = gachaService.getAllByPlayer(player).stream().map(gacha -> gacha.getCsvId()).collect(Collectors.toList());
        var candidateGachaRows = gachaCsv.getRows().stream().filter(row -> !existCsvIds.contains(row.getId())).collect(Collectors.toList());
        // filter not on schedule
        candidateGachaRows.removeIf((row) -> !scheduleCalculator.isInCycle(DisplayCsv.Type.Gacha, row.getId(), now));
        
        // create new gachas
        for (GachaMachineCsv.Row gachaRow : candidateGachaRows)
            gachaService.create(player, gachaRow, now);

        handleMediator.addResult(gachaProtoMap.toAnyByEntities(player.getGachas()));
    }
    
    @TransactionMethod(Method.Write)
    public void buyGacha(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var buyGacha = handleMediator.getRequest().getDataList(0).unpack(BuyGachaProto.BuyGacha.class);
        var now = handleMediator.getRequestStartTime();
        var player = handleMediator.getPlayer().get();
        var gacha = gachaService.findByEntityId(player, buyGacha.getEntityId()).get();
        var gachaRow = csvContainer.getCsv(GachaMachineCsv.class).getById(gacha.getCsvId());
        var gachaItemCsv = csvContainer.getCsv(GachaItemCsv.class);
        
        // action
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.GachaController_buyGacha, gachaRow.getId()));
        var buyValue = BuyValue.valueOf(buyGacha.getBuyValueId()).get();
        var consumeItemResults = new ArrayList<DiffItem>();
        switch (buyValue)
        {
            case Free:
                gachaService.buyFreeGacha(gacha, now);
                break;
            case Single:
            case Ten:
                // consume item
                int[] consumeItemIds = { gachaRow.getCostItem() };
                int[] consumeItemCounts = { gachaRow.getCostValue() * buyValue.getCount() };
                consumeItemResults = playerItemService.consumeItems(player, consumeItemIds, consumeItemCounts);
                gachaService.buyGacha(gacha, buyValue.getCount());
                break;
        }
        
        // random gacha item
        var gachaItemRows = gachaItemCsv.findAllByGroup(gachaRow.getItemGroup());
        int[] weights = gachaItemRows.stream().mapToInt(row -> row.getWeight()).toArray();
        var randomItemRows = IntStream.range(0, buyValue.getCount())
                .mapToObj(i -> gachaItemRows.get(weightRandomer.random(weights))).collect(Collectors.toList());
        
        // create result
        var gachaItemResults = new ArrayList<Any>();
        for(GachaItemCsv.Row gachaItemRow : randomItemRows)
            gachaItemResults.add(createGachaItemResult(player, gachaItemRow));
        
        // response
        var resultBuilder = BuyGachaResultProto.BuyGachaResult.newBuilder();
        resultBuilder.setGacha(gachaProtoMap.toBuilderByEntity(gacha));
        resultBuilder.addAllConsumeItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResults));
        resultBuilder.addAllModels(gachaItemResults);
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    protected Any createGachaItemResult(PlayerEntity player, GachaItemCsv.Row gachaItemRow) throws Exception
    {
        switch(gachaItemRow.getItemTypeEnum())
        {
            case PlayerItem:
                var playerItemRow = csvContainer.getCsv(PlayerItemCsv.class).getById(gachaItemRow.getItemId());
                var produceItem = playerItemService.produceItem(player, playerItemRow, gachaItemRow.getItemNum());
                return playerItemProtoMap.toAnyByDiffItem(produceItem);

            case FishRod:
                var fishRodRow = csvContainer.getCsv(RodCsv.class).getById(gachaItemRow.getItemId());
                var newFishRod = fishRodService.create(player, fishRodRow);
                return fishRodProtoMap.toAnyByEntity(newFishRod);

            default:
                throw new ExceptionByCsvId("not support gacha item type = " + gachaItemRow.getItemType() + ", csvId = " + gachaItemRow.getId());
        }
    }
}
