# Task ID: 10
# Title: Social System - RenJS Integration and Basic Companion Interaction
# Status: pending
# Dependencies: 1, 7
# Priority: medium
# Description: Integrate RenJS for the story and dialog system. Implement the Cruise Chatroom UI and basic interactions (gift, chat stub) with one sample companion.
# Details:
Set up RenJS library within the Phaser 3 project. This might involve an iframe or direct integration if RenJS supports it. Create data structures for 10 companions (name, personality, romanceMeter=0). Implement Cruise Chatroom UI: list companions, select one, show interaction options (Gift, Chat). Gift: Allow player to select a Fish Card from inventory to gift. Stub romance point increase. Chat: On selecting 'Chat', trigger a basic RenJS scene for the selected companion (e.g., `RenJS.call('mia_intro_dialogue')`). Create a minimal RenJS script for one companion's introductory dialogue. Focus on the technical bridge between Phaser and RenJS. 
```javascript
// CruiseChatroomScene.js (Phaser)
// onGift(companionId, fishCardId) { /* remove fishCard from inventory, call RenJS to update romance */ renJS.call('update_romance', { companion: companionId, points: 5 }); }
// onChat(companionId) { renJS.call(companionId + '_dialogue_start'); /* potentially hide Phaser UI, show RenJS UI */ }

// RenJS script (example)
// label mia_intro_dialogue:
//   mia "Hello there, Angler!"
//   return
```

# Test Strategy:
Verify RenJS initializes correctly within or alongside Phaser. Test displaying a simple RenJS dialogue scene triggered from Phaser. Test gifting a Fish Card (inventory deduction, stubbed romance update). Ensure UI for chatroom and companion selection works. Check basic communication between Phaser and RenJS (e.g., passing companion ID).
