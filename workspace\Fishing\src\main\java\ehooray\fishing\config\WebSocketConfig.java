package ehooray.fishing.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import ehooray.fishing.component.WebSocketHandler;
import ehooray.fishing.component.WebSocketHandshakeInterceptor;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer 
{
    @Autowired
    protected WebSocketHandler handler;
    
    @Autowired
    protected WebSocketHandshakeInterceptor interceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) 
    {
    	// TODO: path should extracting by application.properties 
        registry.addHandler(handler, "websocket").setAllowedOrigins("*").addInterceptors(interceptor);
    }
}
