package ehooray.fishing.component.shop;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.ShopEntity;
import ehooray.fishing.pojo.csv.CommodityCsv;
import ehooray.fishing.pojo.event.IabAcceptEvent;
import ehooray.fishing.service.shop.ShopService;

@Component
public class UpdateShopItemOnIabAccept
{
    @Autowired
    protected ShopService shopService;

    @Autowired
    protected CsvContainer csvContainer;

    @EventListener
    public void onEvent(IabAcceptEvent event) throws Exception
    {
        List<ShopEntity> shops = shopService.getAllByPlayer(event.getPlayer());
        CommodityCsv commodityCsv = csvContainer.getCsv(CommodityCsv.class);
        for(ShopEntity shop : shops)
        {
            CommodityCsv.Row commodityRow = commodityCsv.getById(shop.getCommodityCsvId());
            if(commodityRow.getIabId() != event.getIabTransaction().getCsvId()) continue;

            shopService.buyShopItem(shop, 1);
        }
    }

}
