package ehooray.fishing.pojo.csv;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("LanguageTable.xlsx")
public class LanguageCsv extends ACsv<LanguageCsv.Row>
{
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }
    }
    
    // another utility function at here, should order by id
    
    public Row getDuplicateLoginRow()
    {
        return getById(120030);
    }
    
    public Row getMaintainRow()
    {
        return getById(120036);
    }
    
    public Row getClientPatchRow()
    {
        return getById(120037);
    }
}
