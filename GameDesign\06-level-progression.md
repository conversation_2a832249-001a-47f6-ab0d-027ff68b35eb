# 6. 等級進程系統設計文檔

## 6.1 等級系統總覽

### 6.1.1 等級結構設計

《Luxury Angler》採用50等級的進程系統，為玩家提供長期的成長目標和成就感。

#### 等級分段設計
**新手期 (1-10級)**:
- 學習基本釣魚機制
- 解鎖核心功能
- 快速等級提升體驗
- 引導式進程設計

**發展期 (11-25級)**:
- 掌握進階技巧
- 解鎖新地圖和內容
- 中等等級提升速度
- 策略選擇重要性增加

**成熟期 (26-40級)**:
- 精通各種釣魚技巧
- 解鎖高級裝備和配方
- 較慢等級提升速度
- 深度遊戲體驗

**大師期 (41-50級)**:
- 挑戰最困難內容
- 解鎖終極裝備
- 最慢等級提升速度
- 完美主義者目標

#### 等級需求曲線
```javascript
// 等級經驗需求計算
const getRequiredXP = (level) => {
    if (level <= 10) return 100 * level * level;           // 新手期：快速增長
    if (level <= 25) return 1000 + 150 * (level - 10) * (level - 10);  // 發展期：中等增長
    if (level <= 40) return 4000 + 200 * (level - 25) * (level - 25);  // 成熟期：較慢增長
    return 8000 + 300 * (level - 40) * (level - 40);      // 大師期：最慢增長
};
```

技術實現：
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 等級進程核心邏輯
- [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 等級介面顯示

### 6.1.2 經驗值獲得機制

#### 主要經驗值來源

**釣魚活動經驗值**:
- **成功釣魚**: 50-200 XP (基於魚類稀有度)
- **完美釣魚**: 額外 25% XP 加成
- **首次捕獲**: 額外 50% XP 加成
- **Boss魚類**: 500-1000 XP

**合成活動經驗值**:
- **基礎合成**: 20-50 XP
- **精英合成**: 100-200 XP  
- **大師合成**: 300-500 XP
- **首次製作**: 額外 30% XP 加成

**探索活動經驗值**:
- **發現新地點**: 100-300 XP
- **解鎖新地圖**: 500-1000 XP
- **完成地圖探索**: 200-600 XP

**社交活動經驗值**:
- **伴侶互動**: 10-30 XP
- **關係提升**: 100-500 XP
- **解鎖HCG**: 200-800 XP

#### 經驗值計算公式
```javascript
// 釣魚經驗值計算
const fishingXP = baseFishXP * 
    rarityMultiplier * 
    (isFirstCatch ? 1.5 : 1.0) * 
    (isPerfectCatch ? 1.25 : 1.0) * 
    playerXPBonus;

// 合成經驗值計算  
const craftingXP = baseCraftingXP * 
    itemTierMultiplier * 
    (isFirstCraft ? 1.3 : 1.0) * 
    craftingXPBonus;
```

### 6.1.3 等級獎勵系統

#### 每級固定獎勵
- **屬性點**: 每級獲得 3 點屬性分配點
- **金幣獎勵**: 等級 × 100 金幣
- **能量上限**: 每級 +2 點最大能量

#### 特殊等級里程碑
**等級5里程碑**:
- 解鎖第二個地圖
- 獲得Boss魚挑戰權限
- 解鎖基礎合成配方

**等級10里程碑**:
- 解鎖第三個地圖
- 解鎖精英合成配方
- 獲得第一位伴侶

**等級15里程碑**:
- 解鎖第四個地圖
- 解鎖船隻升級系統
- 獲得自動釣魚功能

**等級20里程碑**:
- 解鎖第五個地圖
- 解鎖大師合成配方
- 獲得第二位伴侶

**等級25里程碑**:
- 解鎖神秘海溝
- 解鎖傳奇裝備
- 獲得船隊管理系統

**等級30里程碑**:
- 解鎖PvP釣魚比賽
- 解鎖限定Boss魚
- 獲得第三位伴侶

**等級35里程碑**:
- 解鎖深海探險模式
- 解鎖神話級裝備
- 獲得天氣控制能力

**等級40里程碑**:
- 解鎖時空釣魚
- 解鎖終極Boss戰
- 獲得第四位伴侶

**等級45里程碑**:
- 解鎖創世級裝備
- 解鎖無限模式
- 獲得第五位伴侶

**等級50里程碑**:
- 獲得大師稱號
- 解鎖所有內容
- 獲得完美獎勵

## 6.2 技能樹系統設計

### 6.2.1 技能樹結構

技能樹分為4個主要分支，每個分支專注不同的遊戲領域。

#### 釣魚專精分支 (Fishing Mastery)
**核心技能路線**:
1. **精準拋竿** (等級1): 拋竿精準度 +10%
2. **敏銳感知** (等級3): 魚類偵測 +15%  
3. **完美時機** (等級5): QTE精準度 +20%
4. **線控大師** (等級8): 張力穩定性 +25%
5. **釣魚宗師** (等級12): 所有釣魚屬性 +10%

**分支技能**:
- **誘魚專家** (等級7): 咬餌率 +25%
- **收線高手** (等級10): 收線速度 +30%
- **稀有獵人** (等級15): 稀有魚機率 +20%

#### 合成專精分支 (Crafting Mastery)
**核心技能路線**:
1. **基礎工藝** (等級2): 合成效率 +15%
2. **材料學** (等級4): 材料節省機率 10%
3. **精湛工藝** (等級7): 合成品質 +20%
4. **大師工匠** (等級11): 合成失敗率 -50%
5. **傳奇匠人** (等級16): 完美品質機率 +25%

**分支技能**:
- **快速製作** (等級6): 合成時間 -30%
- **資源回收** (等級9): 失敗時材料回收 75%
- **批量生產** (等級13): 同時進行3個合成

#### 探索專精分支 (Exploration Mastery)
**核心技能路線**:
1. **航海知識** (等級1): 船隻速度 +15%
2. **地理學** (等級3): 發現隱藏地點機率 +20%
3. **天氣預測** (等級6): 提前3小時天氣預報
4. **深海探險** (等級10): 解鎖深海專用地點
5. **時空導航** (等級20): 時間旅行能力

**分支技能**:
- **能量管理** (等級4): 行動能量消耗 -25%
- **熱點感知** (等級8): 熱點持續時間 +50%
- **傳送門** (等級14): 瞬間傳送到已探索地點

#### 社交專精分支 (Social Mastery)
**核心技能路線**:
1. **魅力** (等級1): 浪漫效果 +20%
2. **交際能力** (等級3): 伴侶關係提升速度 +30%
3. **領導力** (等級6): 同時攜帶伴侶 +1
4. **心理學** (等級9): 伴侶特殊能力觸發率 +25%
5. **完美伴侶** (等級18): 所有伴侶同時獲得最大加成

**分支技能**:
- **溝通專家** (等級5): 對話選項增加
- **禮物達人** (等級7): 禮物效果 +50%
- **關係大師** (等級12): 維持多重高級關係

### 6.2.2 技能點分配系統

#### 技能點獲得
- **等級提升**: 每級獲得 1 技能點
- **成就完成**: 特定成就獎勵 1-3 技能點
- **里程碑獎勵**: 重要里程碑額外獎勵技能點

#### 技能重置機制
- **免費重置**: 等級15前可免費重置
- **付費重置**: 等級15後需要支付金幣
- **完全重置**: 消耗稀有材料重置所有技能

#### 技能前置需求
```javascript
// 技能解鎖條件範例
const skillRequirements = {
    "線控大師": {
        level: 8,
        prerequisite: ["精準拋竿", "完美時機"],
        fishingXP: 1000
    },
    "大師工匠": {
        level: 11,
        prerequisite: ["材料學", "精湛工藝"],
        craftingXP: 800
    }
};
```

技術實現：
- [SkillTree.js](../src/scripts/SkillTree.js): 技能樹邏輯
- [SkillTreeUI.js](../src/ui/SkillTreeUI.js): 技能樹介面

## 6.3 內容解鎖系統

### 6.3.1 地圖解鎖順序

**等級基礎解鎖**:
- **等級1**: 珊瑚灣 (新手地圖)
- **等級5**: 深海深淵 (中級地圖)  
- **等級10**: 北極海域 (進階地圖)
- **等級15**: 火山島嶼 (高級地圖)
- **等級20**: 神秘海溝 (終極地圖)

**額外解鎖條件**:
- 完成前一地圖的所有Boss魚
- 達到指定的釣魚熟練度
- 擁有對應的裝備等級

### 6.3.2 功能解鎖時程

**基礎功能 (1-10級)**:
- 等級1: 基礎釣魚、庫存管理
- 等級2: 合成系統
- 等級3: 商店系統
- 等級5: 社交系統
- 等級8: 成就系統
- 等級10: 自動釣魚

**進階功能 (11-25級)**:
- 等級12: 船隻升級
- 等級15: 競技模式
- 等級18: 公會系統
- 等級20: 拍賣行
- 等級22: 寵物系統
- 等級25: 飛行船隻

**高級功能 (26-50級)**:
- 等級30: PvP釣魚
- 等級35: 時空釣魚
- 等級40: 維度探索
- 等級45: 創造模式
- 等級50: 無限挑戰

### 6.3.3 裝備解鎖機制

**釣竿解鎖等級**:
- 等級1: 竹製釣竿
- 等級5: 玻璃纖維釣竿
- 等級10: 碳纖維釣竿
- 等級15: 鋼製釣竿
- 等級20: 鈦合金釣竿
- 等級30: 神話釣竿
- 等級40: 傳奇釣竿
- 等級50: 創世釣竿

**船隻解鎖等級**:
- 等級1: 划艇
- 等級8: 小艇
- 等級15: 快艇
- 等級25: 遊艇
- 等級35: 豪華郵輪
- 等級45: 星際戰艦

## 6.4 成就與里程碑系統

### 6.4.1 成就分類

#### 釣魚成就
**捕獲成就**:
- "初次下鉤": 捕獲第一條魚
- "百魚大師": 捕獲100條不同魚類
- "稀有獵人": 捕獲10條稀有魚
- "Boss終結者": 擊敗所有Boss魚
- "完美釣手": 連續20次完美釣魚

**技巧成就**:
- "精準射手": 拋竿命中熱點100次
- "誘魚專家": 完成1000次誘魚挑戰
- "收線大師": 零失誤收線50次

#### 收集成就
**圖鑑成就**:
- "魚類學者": 完成25%魚類圖鑑
- "海洋專家": 完成50%魚類圖鑑
- "海洋之王": 完成100%魚類圖鑑

**裝備成就**:
- "初級工匠": 製作第一件裝備
- "裝備收藏家": 收集50件不同裝備
- "傳奇裝備師": 製作傳奇級裝備

#### 探索成就
**地圖成就**:
- "探險家": 發現所有地圖
- "熱點獵人": 發現所有熱點
- "秘境探索者": 發現隱藏地點

#### 社交成就
**關係成就**:
- "初戀": 第一次與伴侶達到親密關係
- "萬人迷": 與所有伴侶建立良好關係
- "完美戀人": 與一位伴侶達到最高關係等級

### 6.4.2 里程碑獎勵系統

#### 獎勵類型
**屬性獎勵**:
- 永久屬性加成
- 特殊能力解鎖
- 獨特稱號獲得

**物質獎勵**:
- 稀有材料
- 限定裝備
- 大量金幣

**功能獎勵**:
- 新功能解鎖
- 介面自定義選項
- 特殊權限

#### 里程碑進度追蹤
```javascript
// 里程碑進度檢查
const checkMilestone = (milestone) => {
    const progress = player.getMilestoneProgress(milestone);
    if (progress >= milestone.requirement) {
        milestone.unlock();
        player.addReward(milestone.rewards);
        displayMilestoneNotification(milestone);
    }
};
```

技術實現：
- [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
- [MilestoneManager.js](../src/scripts/MilestoneManager.js): 里程碑管理

## 6.5 等級平衡與設計考量

### 6.5.1 等級曲線設計

#### 早期遊戲 (1-15級)
**設計目標**:
- 快速獲得成就感
- 學習遊戲機制
- 建立遊戲習慣

**平衡要點**:
- 經驗需求低，升級快速
- 頻繁的新內容解鎖
- 寬鬆的失敗懲罰

#### 中期遊戲 (16-35級)
**設計目標**:
- 深化策略思考
- 建立長期目標
- 培養專精領域

**平衡要點**:
- 適中的經驗需求
- 有意義的選擇
- 技能特化重要性

#### 後期遊戲 (36-50級)
**設計目標**:
- 挑戰硬核玩家
- 完美主義追求
- 社區地位象徵

**平衡要點**:
- 高經驗需求
- 極具挑戰性內容
- 精確的操作要求

### 6.5.2 進程驅動設計

#### 短期目標 (每次遊戲session)
- 完成日常釣魚目標
- 推進當前等級經驗
- 完成特定成就

#### 中期目標 (每週)
- 達到下一個里程碑
- 解鎖新內容
- 提升技能分支

#### 長期目標 (每月)
- 達到新的等級段落
- 完成重大成就
- 精通特定系統

### 6.5.3 防止等級通膨設計

#### 經驗來源限制
- 每日活動經驗上限
- 重複活動遞減獎勵
- 高級內容門檻要求

#### 內容深度確保
- 每個等級段有意義的新內容
- 避免純數值提升
- 質量導向的進程設計

## 6.6 等級系統與其他系統整合

### 6.6.1 與屬性系統整合
- 等級提升自動增加基礎屬性
- 技能點分配影響專精屬性
- 里程碑獎勵提供獨特屬性加成

### 6.6.2 與釣魚系統整合
- 等級解鎖新釣魚地點
- 技能影響釣魚機制表現
- 高級內容需要對應等級

### 6.6.3 與合成系統整合
- 等級解鎖新合成配方
- 技能提供合成加成
- 高級裝備需要等級前置

### 6.6.4 與社交系統整合
- 等級影響伴侶解鎖
- 社交技能提升關係效率
- 高等級解鎖特殊互動

### 6.6.5 與經濟系統整合
- 等級獎勵提供金幣收入
- 高等級解鎖昂貴內容
- 技能影響經濟效率

相關檔案：
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 等級進程核心
- [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 等級介面
- [SkillTree.js](../src/scripts/SkillTree.js): 技能樹系統
- [AchievementEnhancer.js](../src/scripts/AchievementEnhancer.js): 成就系統
- [MilestoneManager.js](../src/scripts/MilestoneManager.js): 里程碑管理
- [GameState.js](../src/scripts/GameState.js): 進程狀態管理 