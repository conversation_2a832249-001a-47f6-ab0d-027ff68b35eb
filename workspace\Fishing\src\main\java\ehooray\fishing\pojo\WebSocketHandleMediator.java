package ehooray.fishing.pojo;

import org.springframework.web.socket.WebSocketSession;

public class WebSocketHandleMediator extends ProtoHandleMediator
{
	protected WebSocketSession session;
	
	protected WebSocketHandleMediator() {}
	
	public WebSocketSession getSession() 
	{
		return session;
	}
	
	public static class Builder extends ProtoHandleMediator.Builder
	{
		protected WebSocketHandleMediator castInstance;
		
		public Builder()
		{
		    super(new WebSocketHandleMediator());
		    castInstance = (WebSocketHandleMediator)instance;
		}
		
		public void setSession(WebSocketSession session)
		{
		    castInstance.session = session;
		}
	}
	
}
