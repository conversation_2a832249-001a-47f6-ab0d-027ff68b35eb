// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/fishrod/UpgradeFishRodResult.proto

package ehooray.fishing.dto.proto.response.fishrod;

public final class UpgradeFishRodResultProto {
  private UpgradeFishRodResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UpgradeFishRodResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.fishrod.UpgradeFishRodResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool isSuccess = 1;</code>
     * @return The isSuccess.
     */
    boolean getIsSuccess();

    /**
     * <code>.model.FishRod fishRod = 2;</code>
     * @return Whether the fishRod field is set.
     */
    boolean hasFishRod();
    /**
     * <code>.model.FishRod fishRod = 2;</code>
     * @return The fishRod.
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod();
    /**
     * <code>.model.FishRod fishRod = 2;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumeItemsList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    int getConsumeItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index);

    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod> 
        getConsumeFishRodsList();
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRod getConsumeFishRods(int index);
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    int getConsumeFishRodsCount();
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
        getConsumeFishRodsOrBuilderList();
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getConsumeFishRodsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.fishrod.UpgradeFishRodResult}
   */
  public static final class UpgradeFishRodResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.fishrod.UpgradeFishRodResult)
      UpgradeFishRodResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpgradeFishRodResult.newBuilder() to construct.
    private UpgradeFishRodResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpgradeFishRodResult() {
      consumeItems_ = java.util.Collections.emptyList();
      consumeFishRods_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpgradeFishRodResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.internal_static_response_fishrod_UpgradeFishRodResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.internal_static_response_fishrod_UpgradeFishRodResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.Builder.class);
    }

    public static final int ISSUCCESS_FIELD_NUMBER = 1;
    private boolean isSuccess_;
    /**
     * <code>bool isSuccess = 1;</code>
     * @return The isSuccess.
     */
    @java.lang.Override
    public boolean getIsSuccess() {
      return isSuccess_;
    }

    public static final int FISHROD_FIELD_NUMBER = 2;
    private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
    /**
     * <code>.model.FishRod fishRod = 2;</code>
     * @return Whether the fishRod field is set.
     */
    @java.lang.Override
    public boolean hasFishRod() {
      return fishRod_ != null;
    }
    /**
     * <code>.model.FishRod fishRod = 2;</code>
     * @return The fishRod.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
      return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
    }
    /**
     * <code>.model.FishRod fishRod = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
      return getFishRod();
    }

    public static final int CONSUMEITEMS_FIELD_NUMBER = 3;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_;
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    @java.lang.Override
    public int getConsumeItemsCount() {
      return consumeItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
      return consumeItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index) {
      return consumeItems_.get(index);
    }

    public static final int CONSUMEFISHRODS_FIELD_NUMBER = 4;
    private java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod> consumeFishRods_;
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod> getConsumeFishRodsList() {
      return consumeFishRods_;
    }
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
        getConsumeFishRodsOrBuilderList() {
      return consumeFishRods_;
    }
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    @java.lang.Override
    public int getConsumeFishRodsCount() {
      return consumeFishRods_.size();
    }
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getConsumeFishRods(int index) {
      return consumeFishRods_.get(index);
    }
    /**
     * <code>repeated .model.FishRod consumeFishRods = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getConsumeFishRodsOrBuilder(
        int index) {
      return consumeFishRods_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.fishrod.UpgradeFishRodResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.fishrod.UpgradeFishRodResult)
        ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.internal_static_response_fishrod_UpgradeFishRodResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.internal_static_response_fishrod_UpgradeFishRodResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumeItemsFieldBuilder();
          getConsumeFishRodsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isSuccess_ = false;

        if (fishRodBuilder_ == null) {
          fishRod_ = null;
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeItemsBuilder_.clear();
        }
        if (consumeFishRodsBuilder_ == null) {
          consumeFishRods_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          consumeFishRodsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.internal_static_response_fishrod_UpgradeFishRodResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult build() {
        ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult buildPartial() {
        ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult result = new ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult(this);
        int from_bitField0_ = bitField0_;
        result.isSuccess_ = isSuccess_;
        if (fishRodBuilder_ == null) {
          result.fishRod_ = fishRod_;
        } else {
          result.fishRod_ = fishRodBuilder_.build();
        }
        if (consumeItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumeItems_ = java.util.Collections.unmodifiableList(consumeItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumeItems_ = consumeItems_;
        } else {
          result.consumeItems_ = consumeItemsBuilder_.build();
        }
        if (consumeFishRodsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            consumeFishRods_ = java.util.Collections.unmodifiableList(consumeFishRods_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.consumeFishRods_ = consumeFishRods_;
        } else {
          result.consumeFishRods_ = consumeFishRodsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private boolean isSuccess_ ;
      /**
       * <code>bool isSuccess = 1;</code>
       * @return The isSuccess.
       */
      @java.lang.Override
      public boolean getIsSuccess() {
        return isSuccess_;
      }
      /**
       * <code>bool isSuccess = 1;</code>
       * @param value The isSuccess to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuccess(boolean value) {
        
        isSuccess_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isSuccess = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuccess() {
        
        isSuccess_ = false;
        onChanged();
        return this;
      }

      private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> fishRodBuilder_;
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       * @return Whether the fishRod field is set.
       */
      public boolean hasFishRod() {
        return fishRodBuilder_ != null || fishRod_ != null;
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       * @return The fishRod.
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
        if (fishRodBuilder_ == null) {
          return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        } else {
          return fishRodBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public Builder setFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishRod_ = value;
          onChanged();
        } else {
          fishRodBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public Builder setFishRod(
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (fishRodBuilder_ == null) {
          fishRod_ = builderForValue.build();
          onChanged();
        } else {
          fishRodBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public Builder mergeFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (fishRod_ != null) {
            fishRod_ =
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.newBuilder(fishRod_).mergeFrom(value).buildPartial();
          } else {
            fishRod_ = value;
          }
          onChanged();
        } else {
          fishRodBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public Builder clearFishRod() {
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
          onChanged();
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder getFishRodBuilder() {
        
        onChanged();
        return getFishRodFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
        if (fishRodBuilder_ != null) {
          return fishRodBuilder_.getMessageOrBuilder();
        } else {
          return fishRod_ == null ?
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        }
      }
      /**
       * <code>.model.FishRod fishRod = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
          getFishRodFieldBuilder() {
        if (fishRodBuilder_ == null) {
          fishRodBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder>(
                  getFishRod(),
                  getParentForChildren(),
                  isClean());
          fishRod_ = null;
        }
        return fishRodBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumeItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumeItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumeItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumeItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
        if (consumeItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeItems_);
        } else {
          return consumeItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public int getConsumeItemsCount() {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.size();
        } else {
          return consumeItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);
        } else {
          return consumeItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder addConsumeItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder addConsumeItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder addAllConsumeItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeItems_);
          onChanged();
        } else {
          consumeItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder clearConsumeItems() {
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public Builder removeConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.remove(index);
          onChanged();
        } else {
          consumeItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
          int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);  } else {
          return consumeItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumeItemsOrBuilderList() {
        if (consumeItemsBuilder_ != null) {
          return consumeItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder() {
        return getConsumeItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumeItemsBuilderList() {
        return getConsumeItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumeItemsFieldBuilder() {
        if (consumeItemsBuilder_ == null) {
          consumeItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumeItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumeItems_ = null;
        }
        return consumeItemsBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod> consumeFishRods_ =
        java.util.Collections.emptyList();
      private void ensureConsumeFishRodsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          consumeFishRods_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.FishRodProto.FishRod>(consumeFishRods_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> consumeFishRodsBuilder_;

      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod> getConsumeFishRodsList() {
        if (consumeFishRodsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeFishRods_);
        } else {
          return consumeFishRodsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public int getConsumeFishRodsCount() {
        if (consumeFishRodsBuilder_ == null) {
          return consumeFishRods_.size();
        } else {
          return consumeFishRodsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getConsumeFishRods(int index) {
        if (consumeFishRodsBuilder_ == null) {
          return consumeFishRods_.get(index);
        } else {
          return consumeFishRodsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder setConsumeFishRods(
          int index, ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (consumeFishRodsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.set(index, value);
          onChanged();
        } else {
          consumeFishRodsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder setConsumeFishRods(
          int index, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (consumeFishRodsBuilder_ == null) {
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeFishRodsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder addConsumeFishRods(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (consumeFishRodsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.add(value);
          onChanged();
        } else {
          consumeFishRodsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder addConsumeFishRods(
          int index, ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (consumeFishRodsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.add(index, value);
          onChanged();
        } else {
          consumeFishRodsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder addConsumeFishRods(
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (consumeFishRodsBuilder_ == null) {
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.add(builderForValue.build());
          onChanged();
        } else {
          consumeFishRodsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder addConsumeFishRods(
          int index, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (consumeFishRodsBuilder_ == null) {
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeFishRodsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder addAllConsumeFishRods(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.FishRodProto.FishRod> values) {
        if (consumeFishRodsBuilder_ == null) {
          ensureConsumeFishRodsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeFishRods_);
          onChanged();
        } else {
          consumeFishRodsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder clearConsumeFishRods() {
        if (consumeFishRodsBuilder_ == null) {
          consumeFishRods_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          consumeFishRodsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public Builder removeConsumeFishRods(int index) {
        if (consumeFishRodsBuilder_ == null) {
          ensureConsumeFishRodsIsMutable();
          consumeFishRods_.remove(index);
          onChanged();
        } else {
          consumeFishRodsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder getConsumeFishRodsBuilder(
          int index) {
        return getConsumeFishRodsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getConsumeFishRodsOrBuilder(
          int index) {
        if (consumeFishRodsBuilder_ == null) {
          return consumeFishRods_.get(index);  } else {
          return consumeFishRodsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
           getConsumeFishRodsOrBuilderList() {
        if (consumeFishRodsBuilder_ != null) {
          return consumeFishRodsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeFishRods_);
        }
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder addConsumeFishRodsBuilder() {
        return getConsumeFishRodsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder addConsumeFishRodsBuilder(
          int index) {
        return getConsumeFishRodsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishRod consumeFishRods = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder> 
           getConsumeFishRodsBuilderList() {
        return getConsumeFishRodsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
          getConsumeFishRodsFieldBuilder() {
        if (consumeFishRodsBuilder_ == null) {
          consumeFishRodsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder>(
                  consumeFishRods_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          consumeFishRods_ = null;
        }
        return consumeFishRodsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.fishrod.UpgradeFishRodResult)
    }

    // @@protoc_insertion_point(class_scope:response.fishrod.UpgradeFishRodResult)
    private static final ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult();
    }

    public static ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UpgradeFishRodResult>
        PARSER = new com.google.protobuf.AbstractParser<UpgradeFishRodResult>() {
      @java.lang.Override
      public UpgradeFishRodResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UpgradeFishRodResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpgradeFishRodResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto.UpgradeFishRodResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_fishrod_UpgradeFishRodResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_fishrod_UpgradeFishRodResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+response/fishrod/UpgradeFishRodResult." +
      "proto\022\020response.fishrod\032\023model/FishRod.p" +
      "roto\032\026model/PlayerItem.proto\"\234\001\n\024Upgrade" +
      "FishRodResult\022\021\n\tisSuccess\030\001 \001(\010\022\037\n\007fish" +
      "Rod\030\002 \001(\0132\016.model.FishRod\022\'\n\014consumeItem" +
      "s\030\003 \003(\0132\021.model.PlayerItem\022\'\n\017consumeFis" +
      "hRods\030\004 \003(\0132\016.model.FishRodBa\n*ehooray.f" +
      "ishing.dto.proto.response.fishrodB\031Upgra" +
      "deFishRodResultProtoH\002\252\002\025Fishing.Network" +
      ".Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_fishrod_UpgradeFishRodResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_fishrod_UpgradeFishRodResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_fishrod_UpgradeFishRodResult_descriptor,
        new java.lang.String[] { "IsSuccess", "FishRod", "ConsumeItems", "ConsumeFishRods", });
    ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
