package ehooray.fishing.component.protomap.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.FishKitProto;
import ehooray.fishing.entity.FishKitEntity;

@Component
public class FishKitProtoMap
{
    public FishKitProto.FishKit.Builder toBuilderByEntity(FishKitEntity fishKit)
    {
        FishKitProto.FishKit.Builder builder = FishKitProto.FishKit.newBuilder();
        builder.setEntityId(fishKit.getId());
        builder.setFishRodEntityId(fishKit.getFishRod().getId());
        builder.setFishLineCsvId(fishKit.getFishLine().getCsvId());
        builder.setBaitCsvId(fishKit.getBait().getCsvId());

        return builder;
    }
    
    public Any toAnyByEntity(FishKitEntity fishKit)
    {
        return Any.pack(toBuilderByEntity(fishKit).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<FishKitEntity> fishKits)
    {
        ArrayList<Any> fishRodProtos = new ArrayList<>(fishKits.size());
        for(var fishKit : fishKits)
            fishRodProtos.add(toAnyByEntity(fishKit));

        return fishRodProtos;
    }
}
