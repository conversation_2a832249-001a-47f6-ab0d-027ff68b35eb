package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;
import ehooray.fishing.entity.AchievementEntity;

@CsvPath("QuestTargetTable.xlsx")
public class QuestTargetCsv extends ACsv<QuestTargetCsv.Row>
{
    public enum QuestType
    {
        HaveItem(101),
        GainItem(102),
        GainFishItem(104),
        PlayerLevel(201),
        UpgradeRod(301),
        GachaCount(401),
        FishSize(501),
        BuyShopItem(11),
        AwardQuest(12)
        ;

        protected int id;
        private QuestType(int id)
        {
            this.id = id;
        }
        
        public int getId()
        {
            return id;
        }
        
        public static Optional<QuestType> valueOf(int id)
        {
            return Arrays.stream(QuestType.values()).filter(x-> x.getId() == id).findFirst();
        }
        
        public static boolean CheckFinishTarget(QuestTargetCsv.Row targetRow, int targetCount)
        {
            var questTarget = QuestType.valueOf(targetRow.getQuestType()).get();
            switch (questTarget)
            {
                case HaveItem: return targetCount >= targetRow.getParameter2();
                case GainItem: return targetCount >= targetRow.getParameter2();
                case GainFishItem: return targetCount >= targetRow.getParameter2();
                case PlayerLevel: return targetCount >= targetRow.getParameter1();
                case UpgradeRod: return targetCount >= targetRow.getParameter1();
                case GachaCount: return targetCount >= targetRow.getParameter1();
                case FishSize: return targetCount >= targetRow.getParameter2();
                case BuyShopItem: return targetCount >= targetRow.getParameter1();
                case AwardQuest: return targetCount >= targetRow.getParameter2();
                default: return false;
            }
        }
        
        public static Predicate<AchievementEntity> GetAchievementFilter(QuestTargetCsv.Row targetRow)
        {
            var questTarget = QuestType.valueOf(targetRow.getQuestType()).get();
            Predicate<AchievementEntity> findTarget = x -> x.getQuestType() == targetRow.getQuestType();
            Predicate<AchievementEntity> paramFilter;
            switch (questTarget)
            {
                case HaveItem: paramFilter = x -> x.getParameter_1() == targetRow.getParameter1();
                case GainItem: paramFilter = x -> x.getParameter_1() == targetRow.getParameter1();
                case GainFishItem: paramFilter = x -> x.getParameter_1() == targetRow.getParameter1();
                case PlayerLevel: paramFilter = x -> true;
                case UpgradeRod: paramFilter = x -> true;
                case GachaCount: paramFilter = x -> true;
                case FishSize: paramFilter = x -> x.getParameter_1() == targetRow.getParameter1();
                case BuyShopItem: paramFilter = x -> true;
                case AwardQuest: paramFilter = x -> x.getParameter_1() == targetRow.getParameter1();
                default: paramFilter = x -> true;
            }
            
            return findTarget.and(paramFilter);
        }
    }
    
    public enum LogicType
    {
        And(1),
        Or(2)
        ;
        
        protected int id;
        private LogicType(int id)
        {
            this.id = id;
        }
        
        public int getId()
        {
            return id;
        }
        
        public static Optional<LogicType> valueOf(int id)
        {
            return Arrays.stream(LogicType.values()).filter(x-> x.getId() == id).findFirst();
        }
    }
    
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("Group")
        protected int group;
        @ExcelCellName("Logic")
        protected int logic;
        @ExcelCellName("QuestType")
        protected int questType;
        @ExcelCellName("Parameter_1")
        protected int parameter1;
        @ExcelCellName("Parameter_2")
        protected int parameter2;
        @ExcelCellName("Parameter_3")
        protected int parameter3;
        @ExcelCellName("Parameter_4")
        protected int parameter4;
        @ExcelCellName("Parameter_5")
        protected int parameter5;

        @Override
        public int getId()
        {
            return id;
        }
        public void setId(int id)
        {
            this.id = id;
        }
        public int getRowIndex()
        {
            return rowIndex;
        }
        public void setRowIndex(int rowIndex)
        {
            this.rowIndex = rowIndex;
        }
        public int getGroup()
        {
            return group;
        }
        public void setGroup(int group)
        {
            this.group = group;
        }
        public int getLogic()
        {
            return logic;
        }
        public void setLogic(int logic)
        {
            this.logic = logic;
        }
        public int getQuestType()
        {
            return questType;
        }
        public void setQuestType(int questType)
        {
            this.questType = questType;
        }
        public int getParameter1()
        {
            return parameter1;
        }
        public void setParameter1(int parameter1)
        {
            this.parameter1 = parameter1;
        }
        public int getParameter2()
        {
            return parameter2;
        }
        public void setParameter2(int parameter2)
        {
            this.parameter2 = parameter2;
        }
        public int getParameter3()
        {
            return parameter3;
        }
        public void setParameter3(int parameter3)
        {
            this.parameter3 = parameter3;
        }
        public int getParameter4()
        {
            return parameter4;
        }
        public void setParameter4(int parameter4)
        {
            this.parameter4 = parameter4;
        }
        public int getParameter5()
        {
            return parameter5;
        }
        public void setParameter5(int parameter5)
        {
            this.parameter5 = parameter5;
        }
        
        protected int[] paramters;
        protected int[] getParameters()
        {
            if(paramters != null) return paramters;
            
            // atomic operation, do not assign separately
            paramters = new int[] 
            { 
                parameter1, 
                parameter2, 
                parameter3, 
                parameter4, 
                parameter5
            };
            return paramters;
        }
        
        public int getParameterByInt(int index)
        {
            return getParameters()[index];
        }
    }
    
    // another utility function at here
    protected HashMap<Integer, List<Row>> targetGroupMap;
    
    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();
        
        Collection<Row> rows = getRows();
        targetGroupMap = new HashMap<Integer, List<Row>>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsInGroup = targetGroupMap.get(row.getGroup());
            if (rowsInGroup== null)
            {
                rowsInGroup = new ArrayList<Row>();
                targetGroupMap.put(row.getGroup(), rowsInGroup);
            }
            rowsInGroup.add(row);
        }
    }
    
    public List<Row> findAllByGroup(int group)
    {
        return targetGroupMap.get(group);
    }
    
    public Optional<Row> findByTargetGroup(int targetType, int group)
    {
        return findAllByGroup(group).stream().filter(r -> r.getQuestType() == targetType).findFirst();
    }
}
