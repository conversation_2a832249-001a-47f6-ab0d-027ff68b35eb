package ehooray.fishing.entity;

import java.time.LocalDateTime;
import java.util.Optional;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
//@formatter:off
@Table
(
    name = "fish_item",
    indexes = 
    {
        @Index(name = "csvId", columnList = "csvId")
    }
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class FishItemEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected int size;
    protected LocalDateTime expireTime;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    private PlayerEntity player;
    
    public long getId()
    {
        return id;
    }
    
    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }
    
    public int getCsvId()
    {
        return csvId;
    }
    
    public void setSize(int size)
    {
        this.size = size;
    }
    
    public int getSize()
    {
        return size;
    }

    public Optional<LocalDateTime> getExpireTime()
    {
        return Optional.ofNullable(expireTime);
    }

    public void setExpireTime(LocalDateTime expireTime)
    {
        this.expireTime = expireTime;
    }

    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
}
