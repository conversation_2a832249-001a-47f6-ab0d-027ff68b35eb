package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
//@formatter:off
@Table
(
  name = "fishkit"
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class FishKitEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fish_rod_id", referencedColumnName = "id")
    protected FishRodEntity fishRod;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fish_line_id", referencedColumnName = "id")
    protected PlayerItemEntity fishLine;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bait_id", referencedColumnName = "id")
    protected PlayerItemEntity bait;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    protected PlayerEntity player; 
     
    public long getId()
    {
        return id;
    }

    public void setId(long id)
    {
        this.id = id;
    }

    public FishRodEntity getFishRod()
    {
        return fishRod;
    }

    public void setFishRod(FishRodEntity fishRod)
    {
        this.fishRod = fishRod;
    }

    public PlayerItemEntity getFishLine()
    {
        return fishLine;
    }

    public void setFishLine(PlayerItemEntity fishLine)
    {
        this.fishLine = fishLine;
    }

    public PlayerItemEntity getBait()
    {
        return bait;
    }

    public void setBait(PlayerItemEntity bait)
    {
        this.bait = bait;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }

    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
}
