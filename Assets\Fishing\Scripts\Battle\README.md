# 釣魚系統策略模式重構

## 概述
本項目使用策略模式重構了釣魚系統，使其更容易擴展和修改。透過策略模式，我們將不同狀態下的行為邏輯抽離出來，放到單獨的策略類中，使得系統更加靈活、可維護。

## 核心組件

### 接口與基類
- `IFishingStrategy`: 定義釣魚策略的接口
- `FishingStrategyBase`: 提供策略基本實現的抽象類

### 原始策略實現
位於 `Strategies` 文件夾，包括：
- `PrepareStrategy`: 準備狀態
- `BeforeStartStrategy`: 開始前狀態
- `StartStrategy`: 投擲狀態
- `WaitStrategy`: 等待狀態
- `FightStrategy`: 戰鬥狀態
- `CatchStrategy`: 捕獲狀態
- `FailStrategy`: 失敗狀態
- `WinStrategy`: 勝利狀態

### 新策略實現
位於 `NewStrategies` 文件夾，包括：
- `NewFightStrategy`: 新戰鬥系統策略，實現新的張力系統機制
- `NewCatchStrategy`: 新的捕獲策略
- `NewFailStrategy`: 新的失敗處理策略

### 安裝器
- `FishingStrategyInstaller`: 原始策略安裝器
- `NewFishingStrategyInstaller`: 新策略安裝器，可配置替換部分或全部策略

### 釣魚管理器
- `FishingManager`: 基礎釣魚管理器，使用策略模式管理狀態
- `NewFishingManager`: 新玩法釣魚管理器

## 系統流程
1. 釣魚管理器初始化時，通過依賴注入獲取所有策略
2. 策略初始化並綁定到對應狀態
3. 釣魚管理器通過`TransitionToState`方法切換狀態
4. 退出當前策略，進入新策略
5. 策略類負責處理具體狀態下的行為

## 擴展方式
1. 創建新的策略類，繼承`FishingStrategyBase`
2. 實現`OnEnter`、`OnUpdate`、`OnExit`方法
3. 在安裝器中註冊新策略
4. 根據需要修改管理器類

## 優勢
1. **高內聚、低耦合**: 每個策略類只處理特定狀態的邏輯
2. **易於擴展**: 添加新的策略無需修改核心管理器
3. **清晰的職責劃分**: 每個策略類的職責明確
4. **容易測試**: 可以單獨測試每個策略類
5. **易於更換**: 可以動態替換策略實現

## 注意事項
1. 確保策略安裝器正確註冊所有需要的策略
2. 使用`TransitionToState`方法切換狀態，而非直接設置`currentState`變數
3. 在策略類中不應直接修改狀態，而是通過管理器提供的方法 