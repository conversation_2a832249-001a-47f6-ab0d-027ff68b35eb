package ehooray.fishing.component.iab.unity;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;

import ehooray.fishing.pojo.iab.unity.UnityIabReceiptResult;
import ehooray.fishing.pojo.iab.unity.UnityIabReceiptVerifyException;


/**
 * This verifier only verify receipt over iOS7.0.
 */
@Component
public class IosIabReceiptVerifier_v7 implements IIabReceiptVerifier
{
	public static final int VerifyException = 1;
	public static final int BundleIdException = 2;
	public static final int MissReceiptException = 3;

	public static final String StoreURL = "https://buy.itunes.apple.com/verifyReceipt";
	public static final String SandboxStoreURL = "https://sandbox.itunes.apple.com/verifyReceipt";
	public static final String SendDataToStoreFormat = "{ \"receipt-data\":\"%s\" }";// { "receipt-data":"data" }
	protected String appId;
	public enum ResultCode
	{

		Success(0),
		JsonFormatError(21000),// The App Store could not read the JSON object you provided.
		ParsingReceiptDataFailed(21002),// The data in the receipt-data property was malformed or missing.
		AuthenticateFailed(21003),// The receipt could not be authenticated.

		// The shared secret you provided does not match the shared secret on file for your account.
		// Only returned for iOS 6 style transaction receipts for auto-renewable subscriptions.
		SharedSecretError(21004),

		ServerFailed(21005),// The receipt server is not currently available.

		// This receipt is valid but the subscription has expired. When this status code is returned to your server, the receipt data is also decoded and returned as part of the response.
		// Only returned for iOS 6 style transaction receipts for auto-renewable subscriptions.
		SubscriptionExpired(21006),

		// This receipt is from the test environment, but it was sent to the production environment for verification. Send it to the test environment instead.
		FromTestEnvironment(21007),

		// This receipt is from the production environment, but it was sent to the test environment for verification. Send it to the production environment instead.
		FromProductionEnvironment(21008)
		;

        private int id;
        private ResultCode(int id)
        {
            this.id = id;
        }
        public int getId()
        {
            return id;
        }

	    public static Optional<ResultCode> valueOf(int id)
	    {
	        return Arrays.stream(ResultCode.values()).filter(x-> x.getId() == id).findFirst();
	    }
	}

	@Override
    public UnityIabReceiptResult verify(String receiptStr) throws Exception
	{
		return internalVerifyAndUpdatePurchase(receiptStr, StoreURL);
	}

	protected UnityIabReceiptResult internalVerifyAndUpdatePurchase(String receiptStr, String verifyStoreURL) throws Exception
	{
		Receipt receipt = new ObjectMapper().readValue(receiptStr, Receipt.class);

		String requestData = String.format(SendDataToStoreFormat, receipt.Payload);

		// send request
		HttpRequest httpRequest = HttpRequest.newBuilder()
    		  .uri(new URI(verifyStoreURL))
    		  .POST(HttpRequest.BodyPublishers.ofByteArray(requestData.getBytes()))
    		  .build();

		HttpResponse<String> response = HttpClient.newHttpClient().send(httpRequest, BodyHandlers.ofString());

		// convert to object
		ReceiptVerifyResponseResult responseResult
			= new ObjectMapper().readValue(response.body(), ReceiptVerifyResponseResult.class);
		switch(ResultCode.valueOf(responseResult.status).get())
		{
			case Success:
				ReceiptDetail receiptDetail = responseResult.receipt;

				// receipt detail may have the previous receipt, only the same transaction id can pass this time verification
				for(InAppReceiptDetail inAppReceipt : receiptDetail.in_app)
				{
					if(!inAppReceipt.transaction_id.equals(receipt.TransactionID)) continue;

					// check bundle id, for blocking hacker passing other app receipt
					if(!appId.equals(receiptDetail.bundle_id))
					    throw new UnityIabReceiptVerifyException(receipt.TransactionID, responseResult.status, 
					            "app id not the same receiptDetail.bundle_id");

					// update purchase
					LocalDateTime purchaseDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(receiptDetail.receipt_creation_date_ms), ZoneId.systemDefault());

					UnityIabReceiptResult receiptResult = new UnityIabReceiptResult();
					receiptResult.transactionId = inAppReceipt.transaction_id;
					receiptResult.productId = inAppReceipt.product_id;
					receiptResult.purchaseDate = purchaseDate;
					receiptResult.isTest = verifyStoreURL.equals(SandboxStoreURL);
					receiptResult.receiptVerifyResultStr = response.body();
					receiptResult.platformState = responseResult.status;

					return receiptResult;
				}

				throw new UnityIabReceiptVerifyException(receipt.TransactionID, responseResult.status, 
				        "can't find the same transaction id receipt = " + receipt.TransactionID);

			case FromTestEnvironment:
				return internalVerifyAndUpdatePurchase(receiptStr, SandboxStoreURL);

			default:
			    throw new UnityIabReceiptVerifyException(receipt.TransactionID, responseResult.status, 
			            "responseResult.status != Success or FromTestEnvironment");
		}
	}

	public void initial(String appId) throws Exception
    {
        this.appId = appId;
    }

	// inner class must have static
	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class ReceiptVerifyResponseResult
	{
		@JsonProperty public int status;
		@JsonProperty public ReceiptDetail receipt;
		@JsonProperty public String environment;

		public ReceiptVerifyResponseResult() {}
	}// end class ResponseResult

	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class InAppReceiptDetail
	{
		@JsonProperty public int quantity;
		@JsonProperty public String product_id;
		@JsonProperty public String transaction_id;
		@JsonProperty public String original_transaction_id;

		@JsonProperty public String purchase_date;
		@JsonProperty public long purchase_date_ms;
		@JsonProperty public String purchase_date_pst;

		@JsonProperty public String original_purchase_date;
		@JsonProperty public long original_purchase_date_ms;
		@JsonProperty public String original_purchase_date_pst;
		@JsonProperty public boolean is_trial_period;

		public InAppReceiptDetail() {}
	}// end class ReceiptDetail

	// Note: format is suitable for iOS 7.0
	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class ReceiptDetail
	{
		@JsonProperty public String receipt_type;
		@JsonProperty public String adam_id;
		@JsonProperty public String app_item_id;
		@JsonProperty public String bundle_id;
		@JsonProperty public String application_version;
		@JsonProperty public String download_id;
		@JsonProperty public String version_external_identifier;
		@JsonProperty public String receipt_creation_date;
		@JsonProperty public long receipt_creation_date_ms;
		@JsonProperty public String receipt_creation_date_pst;
		@JsonProperty public String request_date;
		@JsonProperty public long request_date_ms;
		@JsonProperty public String request_date_pst;
		@JsonProperty public String original_purchase_date;
		@JsonProperty public long original_purchase_date_ms;
		@JsonProperty public String original_purchase_date_pst;
		@JsonProperty public String original_application_version;

		@JsonProperty public List<InAppReceiptDetail> in_app;

		public ReceiptDetail() {}
	}// end class ReceiptDetail

	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class Receipt
	{
		@JsonProperty public String TransactionID;
		@JsonProperty public String Payload;

		public Receipt() {}

	}// end class Receipt
}
