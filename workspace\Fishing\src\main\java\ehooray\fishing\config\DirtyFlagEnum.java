package ehooray.fishing.config;

import java.util.Arrays;
import java.util.Optional;

// the flag for client detecting and can send necessary request again
public enum DirtyFlagEnum
{
    Quest(1),
    //DisasterBuilding(2),
    //Component(3)
    ;
    
    protected int id;
    private DirtyFlagEnum(int id)
    {
        this.id = id;
    }
    
    public int getId()
    {
        return id;
    }
    
    public static Optional<DirtyFlagEnum> valueOf(int id)
    {
        return Arrays.stream(DirtyFlagEnum.values()).filter(x-> x.getId() == id).findFirst();
    }
}
