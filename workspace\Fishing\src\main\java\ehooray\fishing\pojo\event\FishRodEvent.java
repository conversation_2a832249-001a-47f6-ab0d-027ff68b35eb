package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class FishRodEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final FishRodEntity fishRod;
    
    public FishRodEvent(Object source, PlayerEntity player, FishRodEntity fishRod)
    {
        super(source);
        this.player = player;
        this.fishRod = fishRod;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }
    
    public FishRodEntity getFishRod()
    {
        return fishRod;
    }
}
