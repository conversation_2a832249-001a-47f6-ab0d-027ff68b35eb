package ehooray.fishing.component.iab;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.component.csv.CsvContainer;
//import ehooray.formosa.component.protomap.model.BuildingProtoMap;
//import ehooray.formosa.component.protomap.model.CharacterProtoMap;
import ehooray.fishing.component.protomap.model.IabTransactionProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
//import ehooray.formosa.entity.BuildingEntity;
//import ehooray.formosa.entity.CharacterEntity;
import ehooray.fishing.entity.IabTransactionEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
//import ehooray.formosa.pojo.csv.BuildingCsv;
//import ehooray.formosa.pojo.csv.CharacterCsv;
import ehooray.fishing.pojo.csv.IabCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.iab.IabTransactionStateEnum;
//import ehooray.formosa.service.building.BuildingService;
//import ehooray.formosa.service.character.CharacterService;
import ehooray.fishing.service.iab.IabTransactionService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;

@Component
public class IabTransactionResultCalculator
{
    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected IabTransactionProtoMap iabTransactionProtoMap;

    @Autowired
    protected IabTransactionService iabTransactionService;

//    @Autowired
//    protected CharacterProtoMap characterProtoMap;
//
//    @Autowired
//    protected CharacterService characterService;

    @Autowired
    protected PlayerItemService playerItemService;

    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;

//    @Autowired
//    protected BuildingProtoMap buildingProtoMap;
//
//    @Autowired
//    protected BuildingService buildingService;

	public List<Result> calculate(PlayerEntity player, LocalDateTime now) throws Exception
	{
	    IabCsv iabCsv = csvContainer.getCsv(IabCsv.class);
        List<IabTransactionEntity> iabTransactions = iabTransactionService.findByState(player, IabTransactionStateEnum.WaitPlayerAccept.getId());

        List<Result> transactionResults = new ArrayList<>();
        for(IabTransactionEntity transaction : iabTransactions)
        {
            iabTransactionService.accept(player, transaction, now);

            IabCsv.Row iabRow = iabCsv.getById(transaction.getCsvId());
            Result result = new Result();
            result.iabTransaction = transaction;
            for(int i = 0, iMax = iabRow.getItemIds().length; i < iMax; i++)
            {
                List<Any> itemProtoResults = createItemResult(player, iabRow.getItemTypes()[i], iabRow.getItemIds()[i], iabRow.getItemNums()[i]);
                if(itemProtoResults.isEmpty()) continue;
                
                result.itemProtoResults.addAll(itemProtoResults);
            }

            transactionResults.add(result);
        }

        return transactionResults;
	}

    protected List<Any> createItemResult(PlayerEntity player, int itemType, int itemId, int itemNum) throws Exception
    {
        Optional<IabCsv.ItemType> itemTypeEnum = IabCsv.ItemType.valueOf(itemType);
        List<Any> results = new ArrayList<Any>();
        switch(itemTypeEnum.get())
        {
            case PlayerItem:
                PlayerItemCsv.Row playerItemRow = csvContainer.getCsv(PlayerItemCsv.class).getById(itemId);
                DiffItem produceItem = playerItemService.produceItem(player, playerItemRow, itemNum);
                results.add(playerItemProtoMap.toAnyByDiffItem(produceItem));
                break;

//            case Character:
//                CharacterCsv.Row characterRow = csvContainer.getCsv(CharacterCsv.class).getById(itemId);
//                for(int i = 0; i < itemNum; i++)
//                {
//                    CharacterEntity newCharacter = characterService.createCharacter(player, characterRow, 1);
//                    results.add(characterProtoMap.toAnyByEntity(newCharacter));
//                }
//                break;
//
//            case Building:
//                BuildingCsv.Row buildingRow = csvContainer.getCsv(BuildingCsv.class).getById(itemId);
//                for(int i = 0; i < itemNum; i++)
//                {
//                    BuildingEntity newBuilding = buildingService.createStoredBuilding(player, buildingRow);
//                    results.add(buildingProtoMap.toAnyByEntity(newBuilding));
//                }
//                break;

            case Ignore: break;
            
            default:
                throw new ExceptionByCsvId("not support iab item type = " + itemType);
        }
        
        return results;
    }

	public class Result
	{
		protected IabTransactionEntity iabTransaction;
		protected List<Any> itemProtoResults = new ArrayList<>();
        public IabTransactionEntity getIabTransaction()
        {
            return iabTransaction;
        }
        public List<Any> getItemProtoResults()
        {
            return itemProtoResults;
        }

	}// end class Receipt
}
