package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("PresetDataTable.xlsx")
public class PresetDataCsv extends ACsv<PresetDataCsv.Row>
{
    public enum Type
    {
        PlayerItem(1),
        FishRod(2);
        
        protected int id;
        private Type(int id)
        {
            this.id = id;
        }
        
        public int getId()
        {
            return id;
        }
        
        public static Optional<Type> valueOf(int id)
        {
            return Arrays.stream(Type.values()).filter(x-> x.getId() == id).findFirst();
        }
    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("TYPE")
        protected int type;
        
        @ExcelCellName("ITEM_ID")
        protected int itemId;
        
        @ExcelCellName("COUNT")
        protected int count;

        public int getRowIndex()
        {
            return rowIndex;
        }
        
        @Override
        public int getId()
        {
            return id;
        }

        public Optional<Type> getType()
        {
            return Type.valueOf(type);
        }

        public int getItemId()
        {
            return itemId;
        }

        public int getCount()
        {
            return count;
        }
    }

    // another utility function at here
}
