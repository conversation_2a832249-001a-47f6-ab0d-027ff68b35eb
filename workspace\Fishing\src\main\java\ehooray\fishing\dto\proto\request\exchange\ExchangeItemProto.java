// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/exchange/ExchangeItem.proto

package ehooray.fishing.dto.proto.request.exchange;

public final class ExchangeItemProto {
  private ExchangeItemProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ExchangeItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.exchange.ExchangeItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 exchangeEntity = 1;</code>
     * @return The exchangeEntity.
     */
    long getExchangeEntity();

    /**
     * <code>int32 exchangeCount = 2;</code>
     * @return The exchangeCount.
     */
    int getExchangeCount();
  }
  /**
   * Protobuf type {@code request.exchange.ExchangeItem}
   */
  public static final class ExchangeItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.exchange.ExchangeItem)
      ExchangeItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ExchangeItem.newBuilder() to construct.
    private ExchangeItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ExchangeItem() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ExchangeItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.internal_static_request_exchange_ExchangeItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.internal_static_request_exchange_ExchangeItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.class, ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.Builder.class);
    }

    public static final int EXCHANGEENTITY_FIELD_NUMBER = 1;
    private long exchangeEntity_;
    /**
     * <code>int64 exchangeEntity = 1;</code>
     * @return The exchangeEntity.
     */
    @java.lang.Override
    public long getExchangeEntity() {
      return exchangeEntity_;
    }

    public static final int EXCHANGECOUNT_FIELD_NUMBER = 2;
    private int exchangeCount_;
    /**
     * <code>int32 exchangeCount = 2;</code>
     * @return The exchangeCount.
     */
    @java.lang.Override
    public int getExchangeCount() {
      return exchangeCount_;
    }

    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.exchange.ExchangeItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.exchange.ExchangeItem)
        ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.internal_static_request_exchange_ExchangeItem_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.internal_static_request_exchange_ExchangeItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.class, ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        exchangeEntity_ = 0L;

        exchangeCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.internal_static_request_exchange_ExchangeItem_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem build() {
        ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem buildPartial() {
        ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem result = new ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem(this);
        result.exchangeEntity_ = exchangeEntity_;
        result.exchangeCount_ = exchangeCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long exchangeEntity_ ;
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @return The exchangeEntity.
       */
      @java.lang.Override
      public long getExchangeEntity() {
        return exchangeEntity_;
      }
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @param value The exchangeEntity to set.
       * @return This builder for chaining.
       */
      public Builder setExchangeEntity(long value) {
        
        exchangeEntity_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearExchangeEntity() {
        
        exchangeEntity_ = 0L;
        onChanged();
        return this;
      }

      private int exchangeCount_ ;
      /**
       * <code>int32 exchangeCount = 2;</code>
       * @return The exchangeCount.
       */
      @java.lang.Override
      public int getExchangeCount() {
        return exchangeCount_;
      }
      /**
       * <code>int32 exchangeCount = 2;</code>
       * @param value The exchangeCount to set.
       * @return This builder for chaining.
       */
      public Builder setExchangeCount(int value) {
        
        exchangeCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 exchangeCount = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExchangeCount() {
        
        exchangeCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.exchange.ExchangeItem)
    }

    // @@protoc_insertion_point(class_scope:request.exchange.ExchangeItem)
    private static final ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem();
    }

    public static ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ExchangeItem>
        PARSER = new com.google.protobuf.AbstractParser<ExchangeItem>() {
      @java.lang.Override
      public ExchangeItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ExchangeItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ExchangeItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto.ExchangeItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_exchange_ExchangeItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_exchange_ExchangeItem_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#request/exchange/ExchangeItem.proto\022\020r" +
      "equest.exchange\"=\n\014ExchangeItem\022\026\n\016excha" +
      "ngeEntity\030\001 \001(\003\022\025\n\rexchangeCount\030\002 \001(\005BY" +
      "\n*ehooray.fishing.dto.proto.request.exch" +
      "angeB\021ExchangeItemProtoH\002\252\002\025Fishing.Netw" +
      "ork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_exchange_ExchangeItem_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_exchange_ExchangeItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_exchange_ExchangeItem_descriptor,
        new java.lang.String[] { "ExchangeEntity", "ExchangeCount", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
