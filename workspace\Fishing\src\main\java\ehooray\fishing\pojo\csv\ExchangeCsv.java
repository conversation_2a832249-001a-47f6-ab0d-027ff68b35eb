package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("ExchangeTable.xlsx")
public class ExchangeCsv extends ACsv<ExchangeCsv.Row>
{
    public enum Type
    {
        PlayerItem(101),
        FishItem(202);
        
        protected int id;
        private Type(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<Type> valueOf(int id)
        {
            return Arrays.stream(Type.values()).filter(x-> x.getId() == id).findFirst();
        }
    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;   
        @ExcelCellName("Type")
        protected int type;  
        @ExcelCellName("ExchangeID")
        protected int exchangeId;    
        @ExcelCellName("ExchangeAmount")
        protected int exchangeAmount;
        @ExcelCellName("ExchangeLimit")
        protected int exchangeLimit;
        
        @ExcelCellName("RequireID_1")
        protected int requireId1;
        @ExcelCellName("RequireID_2")
        protected int requireId2;
        @ExcelCellName("RequireID_3")
        protected int requireId3;
        @ExcelCellName("RequireID_4")
        protected int requireId4;
        
        @ExcelCellName("RequireAmount_1")
        protected int requireAmount1;
        @ExcelCellName("RequireAmount_2")
        protected int requireAmount2;
        @ExcelCellName("RequireAmount_3")
        protected int requireAmount3;
        @ExcelCellName("RequireAmount_4")
        protected int requireAmount4;
        
        protected int[] requireIds;
        protected int[] requireAmounts;
        
        public int getRowIndex()
        {
            return rowIndex;
        }

        @Override
        public int getId()
        {
            return id;
        }

        public int getType()
        {
            return type;
        }

        public int getExchangeId()
        {
            return exchangeId;
        }

        public int getExchangeAmount()
        {
            return exchangeAmount;
        }

        public int getExchangeLimit()
        {
            return exchangeLimit;
        }
        
        public int getRequireId1()
        {
            return requireId1;
        }

        public int getRequireId2()
        {
            return requireId2;
        }

        public int getRequireId3()
        {
            return requireId3;
        }

        public int getRequireId4()
        {
            return requireId4;
        }

        public int getRequireAmount1()
        {
            return requireAmount1;
        }

        public int getRequireAmount2()
        {
            return requireAmount2;
        }

        public int getRequireAmount3()
        {
            return requireAmount3;
        }

        public int getRequireAmount4()
        {
            return requireAmount4;
        }

        public int[] getRequireIds()
        {
            if(requireIds != null) return requireIds;
            
            // atomic operation, do not assign separately
            requireIds = new int[] { requireId1, requireId2, requireId3, requireId4 };
            return requireIds;
        }
        
        public int[] getRequireAmounts()
        {
            if(requireAmounts != null) return requireAmounts;
            
            // atomic operation, do not assign separately
            requireAmounts = new int[] { requireAmount1, requireAmount2, requireAmount3, requireAmount4 };
            return requireAmounts;
        }
        
        public boolean ignoreExchangeLimit()
        {
            return getExchangeLimit() <= 0;
        }
    }

}
