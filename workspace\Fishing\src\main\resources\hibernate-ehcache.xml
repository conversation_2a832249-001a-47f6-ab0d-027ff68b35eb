<config xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
	xmlns='http://www.ehcache.org/v3'
	xmlns:jsr107='http://www.ehcache.org/v3/jsr107'
	xsi:schemaLocation="
        http://www.ehcache.org/v3/ http://www.ehcache.org/schema/ehcache-core-3.0.xsd
        http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.0.xsd">

	<service>
		<jsr107:defaults default-template="default">
			<jsr107:cache name="responseCache" template="responseCacheTemplate"/>
		</jsr107:defaults>
	</service>

	<cache-template name="default">
		<expiry>
			<!-- time-to-idle -->
			<tti unit="seconds">60</tti>
		</expiry>
		<heap unit="entries">100000</heap>
	</cache-template>
	
	<!-- each player cache capacity is in application.yml -->
	<cache-template name="responseCacheTemplate">
		<expiry>
			<!-- time-to-idle -->
			<tti unit="seconds">600</tti>
		</expiry>
		<heap unit="entries">100000</heap>
	</cache-template>


</config>
