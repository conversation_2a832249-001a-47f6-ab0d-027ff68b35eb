# Task ID: 7
# Title: Inventory and Equipment System
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Implement the inventory system (50-100 slots) for Fish Cards, Equipment (Rods, Lures, Boats, Clothing), and Consumables. Implement the equipment system with 7 slots and attribute stacking.
# Details:
Data structure for inventory: `player.inventory = { items: [], capacity: 50 }`. Items have properties like `id`, `type`, `name`, `quantity`, `attributes`. Fishtank: `player.fishtank = { cards: [], capacity: initialValueFromBoat }`. Equipment slots: `player.equipment = { rod: null, lure: null, boat: null, clothing1: null, clothing2: null, clothing3: null, companion1: null }`. Implement functions to add/remove items, equip/unequip gear. Equipped items' stats stack additively with player attributes (modify `getEffectiveAttribute` from Task 3). UI for displaying inventory (categorized, sortable) and equipment slots. Fishtank capacity linked to boat attribute. Selling fish cards from fishtank at port. 
```javascript
// InventoryManager.js
// function addItem(item) { if (player.inventory.items.length < player.inventory.capacity) player.inventory.items.push(item); }
// function equipItem(item, slot) { player.equipment[slot] = item; updatePlayerStats(); }
```

# Test Strategy:
Test adding/removing items from inventory. Test equipping/unequipping items in all slots. Verify attribute changes when equipment is changed. Test inventory capacity limits. Test fishtank capacity and selling fish cards.
