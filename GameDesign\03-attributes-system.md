# 3. 屬性系統設計文檔

## 3.1 玩家屬性系統 (34項屬性)

### 3.1.1 玩家升級屬性 (10項)

這些屬性透過等級提升、裝備或技能來增強，影響釣魚、合成和社交互動。

#### 精準度與技能屬性
**拋竿精準度 (Cast Accuracy)**
- **功能**: 增加熱點目標區域大小
- **提升方式**: 每級+1% (最大20%)
- **影響系統**: 拋竿迷你遊戲成功率
- **技術實現**: [CastingMiniGame.js](../src/scripts/CastingMiniGame.js)

**咬餌率 (Bite Rate)**
- **功能**: 提高魚類咬餌的可能性
- **提升方式**: 每級+0.5%
- **影響系統**: 誘魚階段成功率
- **技術實現**: [LuringMiniGame.js](../src/scripts/LuringMiniGame.js)

**QTE精準度 (QTE Precision)**
- **功能**: 延長QTE反應時間窗口
- **提升方式**: 每級+0.5%
- **影響系統**: 收線迷你遊戲反應時間
- **技術實現**: [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js)

**誘魚成功率 (Lure Success)**
- **功能**: 誘魚輸入的成功率
- **提升方式**: 每級+0.5%
- **影響系統**: 魚餌控制成功率
- **技術實現**: [LuringMiniGame.js](../src/scripts/LuringMiniGame.js)

#### 資源與能力屬性
**能量 (Energy)**
- **功能**: 行動的體力值 (基礎最大值100)
- **提升方式**: 每級+2點
- **影響系統**: 釣魚、旅行活動消耗
- **技術實現**: [PlayerController.js](../src/scripts/PlayerController.js)

**稀有魚機率 (Rare Fish Chance)**
- **功能**: 遇到稀有魚類的機率
- **提升方式**: 每級+0.2%
- **影響系統**: 魚類生成系統
- **技術實現**: [FishSpawner.js](../src/scripts/FishSpawner.js)

**線強度 (Line Strength)**
- **功能**: 減少線斷裂機會
- **提升方式**: 每級+0.3%
- **影響系統**: 收線迷你遊戲張力控制
- **技術實現**: [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js)

**拋竿距離 (Casting Range)**
- **功能**: 增加拋竿射程
- **提升方式**: 每級+0.5%
- **影響系統**: 可達釣魚區域
- **技術實現**: [CastingMiniGame.js](../src/scripts/CastingMiniGame.js)

**船隻速度 (Boat Speed)**
- **功能**: 減少旅行時間
- **提升方式**: 每級+0.3%
- **影響系統**: 地點間移動效率
- **技術實現**: [LocationManager.js](../src/scripts/LocationManager.js)

**金幣獲得 (Coin Gain)**
- **功能**: 增加獲得的金幣
- **提升方式**: 每級+0.3%
- **影響系統**: 經濟收益
- **技術實現**: [PlayerProgression.js](../src/scripts/PlayerProgression.js)

### 3.1.2 釣竿屬性 (6項)

釣竿裝備提供的屬性加成，影響釣魚的各個階段。

**拋竿精準度加成 (Cast Accuracy Boost)**
- **功能**: 釣竿提供的精準度加成
- **範例**: 5星釣竿 +25%
- **數據來源**: [equipment.json](../src/data/equipment.json)

**張力穩定性 (Tension Stability)**
- **功能**: 擴大安全張力區域
- **範例**: 5星釣竿 +28%
- **影響**: 收線迷你遊戲容錯率

**稀有魚機率加成 (Rare Fish Chance Boost)**
- **功能**: 釣竿增加的稀有魚遇到率
- **範例**: 5星釣竿 +15%
- **影響**: 高價值魚類捕獲率

**拋竿距離加成 (Casting Range Boost)**
- **功能**: 釣竿延伸的拋竿射程
- **範例**: 5星釣竿 +35%
- **影響**: 更遠釣魚點存取

**收線速度 (Reel Speed)**
- **功能**: 更快的收線速度
- **範例**: 5星釣竿 +15%
- **影響**: 收線迷你遊戲效率

**掙扎抗性 (Struggle Resistance)**
- **功能**: 減少魚類掙扎強度
- **範例**: 5星釣竿 +15%
- **影響**: 減少收線難度

技術實現：
- [equipment.json](../src/data/equipment.json): 裝備屬性數據
- [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化系統

### 3.1.3 魚餌屬性 (4項)

魚餌裝備影響誘魚階段的成功率和效果。

**咬餌率加成 (Bite Rate Boost)**
- **功能**: 魚餌提供的咬餌率增加
- **範例**: 5星魚餌 +60%
- **影響**: 魚類興趣度

**誘魚成功率加成 (Lure Success Boost)**
- **功能**: 魚餌改善誘魚輸入成功率
- **範例**: 3星魚餌 +40%
- **影響**: 誘魚迷你遊戲容錯

**魚餌耐久性 (Lure Durability)**
- **功能**: 減少魚餌失效機會
- **範例**: 5星魚餌 +10%
- **影響**: 魚餌使用壽命

**魚餌精準度 (Lure Precision)**
- **功能**: 緊縮輸入窗口要求
- **範例**: 5星魚餌 +10%
- **影響**: 誘魚技能需求

技術實現：
- [lures.json](../src/data/lures.json): 魚餌屬性數據
- [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 魚餌效果應用

### 3.1.4 比基尼助手屬性 (6項)

伴侶系統提供的各種加成效果。

**浪漫效果 (Romance Effectiveness)**
- **功能**: 增加浪漫點數獲得
- **提升方式**: 透過技能改善
- **影響**: 社交互動效果

**船艙親和力 (Cabin Affinity)**
- **功能**: 增強船艙互動效果
- **提升方式**: 透過船隻改善
- **影響**: 伴侶互動品質

**娛樂心情加成 (Entertainment Mood Boost)**
- **功能**: 提升伴侶心情
- **提升方式**: 透過船隻改善
- **影響**: 長期關係發展

**魚類偵測 (Fish Detection)**
- **功能**: 發現高價值魚類
- **提升方式**: 透過伴侶改善
- **影響**: 稀有魚類發現率

**能量加成 (Energy Boost)**
- **功能**: 伴侶提供的能量增加
- **提升方式**: 透過服裝改善
- **影響**: 活動持續能力

**QTE精準度加成 (QTE Precision Boost)**
- **功能**: 伴侶延長QTE反應時間
- **提升方式**: 透過技能改善
- **影響**: 收線迷你遊戲表現

技術實現：
- [CabinScene.js](../src/scenes/CabinScene.js): 伴侶互動
- [DialogManager.js](../src/scripts/DialogManager.js): 社交系統

### 3.1.5 船隻屬性 (8項)

船隻裝備提供的整體遊戲體驗改善。

**合成效率 (Crafting Efficiency)**
- **功能**: 減少合成時間/成本
- **提升方式**: 透過船隻改善
- **影響**: 合成系統效率

**自動釣魚產量 (Auto-Fishing Yield)**
- **功能**: 增加自動釣魚獎勵
- **提升方式**: 透過船隻改善
- **影響**: 放置遊戲收益

**魚類偵測增強 (Fish Detection Enhancement)**
- **功能**: 船隻提供的魚類偵測
- **範例**: 5星船隻 +10%
- **影響**: 魚類發現機率

**熱點穩定性 (Hotspot Stability)**
- **功能**: 增加熱點持續時間
- **提升方式**: 透過船隻改善
- **影響**: 釣魚點可用時間

**伴侶槽容量 (Companion Slot Capacity)**
- **功能**: 增加伴侶槽位
- **提升方式**: 透過船隻改善
- **影響**: 同時攜帶伴侶數量

**自動釣魚效率 (Auto-Fishing Efficiency)**
- **功能**: 減少自動釣魚時間
- **提升方式**: 透過船隻改善
- **影響**: 放置遊戲速度

**船隻耐久性 (Boat Durability)**
- **功能**: 減少惡劣條件懲罰
- **提升方式**: 透過船隻改善
- **影響**: 天氣影響抗性

**魚缸儲存 (Fishtank Storage)**
- **功能**: 擴展魚卡儲存空間
- **提升方式**: 透過船隻改善
- **影響**: 魚卡容量

技術實現：
- [BoatMenuScene.js](../src/scenes/BoatMenuScene.js): 船隻介面
- [CraftingManager.js](../src/scripts/CraftingManager.js): 合成效率

## 3.2 魚類屬性系統 (11項)

### 3.2.1 物理屬性

**大小 (Size)**
- **範圍**: 1-10
- **範例**: 海妖 (Kraken): 10
- **影響**: 釣魚難度和視覺呈現

**重量 (Weight)**
- **範圍**: 0.1-500 公斤
- **範例**: 巨型烏賊: 500公斤
- **影響**: 收線力量需求

**速度 (Speed)**
- **範圍**: 1-10
- **範例**: 旗魚 (Marlin): 10
- **影響**: 誘魚和收線階段的反應時間

### 3.2.2 行為屬性

**攻擊性 (Aggressiveness)**
- **範圍**: 1-10
- **範例**: 鯊魚: 10
- **影響**: 咬餌行為和掙扎模式

**狡猾度 (Elusiveness)**
- **範圍**: 1-10
- **範例**: 鮟鱇魚: 10
- **影響**: 誘魚難度和逃脫機率

**力量 (Strength)**
- **範圍**: 1-10
- **範例**: 鮪魚: 10
- **影響**: 收線張力和掙扎強度

**耐力 (Endurance)**
- **範圍**: 1-10
- **範例**: 海妖: 10
- **影響**: 收線持續時間

### 3.2.3 遊戲機制屬性

**稀有度 (Rarity)**
- **範圍**: 1-10
- **範例**: 藍旗魚: 10
- **影響**: 出現機率和獎勵價值

**深度偏好 (Depth Preference)**
- **範圍**: 1-10
- **範例**: 蝰魚: 10 (深海)
- **影響**: 釣魚地點和時間限制

**魚餌偏好 (Bait Preference)**
- **範圍**: 1-10
- **範例**: 鱒魚: 10 (偏好飛釣餌)
- **影響**: 特定魚餌的效果加成

**活躍時間段/天氣偏好 (Active Time/Weather Preference)**
- **範例**: 月光鱸魚: 夜晚、雨天
- **影響**: 特定時間和天氣的出現機率

技術實現：
- [fish.json](../src/data/fish.json): 魚類屬性數據
- [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據管理
- [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成邏輯

## 3.3 屬性計算與平衡系統

### 3.3.1 屬性疊加機制

**加法疊加**
```javascript
// 範例：最終拋竿精準度
finalCastAccuracy = 
    playerLevel.castAccuracy +     // 玩家等級加成
    equipment.rod.castAccuracy +   // 釣竿加成
    companion.skillBonus +         // 伴侶技能加成
    temporaryBuffs                 // 臨時增益
```

**乘法疊加**
```javascript
// 範例：最終咬餌率
finalBiteRate = 
    baseBiteRate * 
    (1 + playerLevel.biteRateBonus/100) *
    (1 + lure.biteRateBonus/100) *
    (1 + weather.biteRateModifier/100)
```

### 3.3.2 屬性平衡考量

**早期遊戲平衡**
- 低等級屬性提供明顯改善
- 基礎裝備有顯著差異
- 容錯率較高

**中期遊戲平衡**
- 屬性增長趨緩
- 策略選擇重要性增加
- 專精化開始顯現

**後期遊戲平衡**
- 邊際效益遞減
- 最適化配置重要
- 技能表現決定性

技術實現：
- [attributes.json](../src/data/attributes.json): 屬性平衡數據
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 屬性計算
- [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備屬性處理

## 3.4 屬性UI與視覺反饋

### 3.4.1 屬性顯示介面

**玩家屬性面板**
- 當前屬性值顯示
- 升級預覽
- 裝備影響顯示
- 相關檔案: [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js)

**裝備比較介面**
- 屬性差異對比
- 裝備前後預覽
- 推薦配置建議
- 相關檔案: [EquipmentEnhancementUI.js](../src/ui/EquipmentEnhancementUI.js)

**魚類圖鑑介面**
- 魚類屬性展示
- 捕獲難度指示
- 最佳釣魚條件
- 相關檔案: [FishCollectionUI.js](../src/ui/FishCollectionUI.js)

### 3.4.2 即時反饋系統

**屬性變化通知**
- 升級獲得屬性提示
- 裝備效果即時顯示
- 臨時增益/減益提示

**遊戲中屬性影響**
- 迷你遊戲難度調整視覺化
- 成功率指示器
- 屬性加成效果顯示

技術實現：
- [UITheme.js](../src/ui/UITheme.js): UI主題和樣式
- [LoadingStateManager.js](../src/ui/LoadingStateManager.js): 載入狀態顯示

## 3.5 屬性系統與其他系統整合

### 3.5.1 與釣魚系統整合
- 拋竿精準度影響熱點成功率
- 誘魚屬性影響魚類興趣
- 收線屬性影響張力控制

### 3.5.2 與合成系統整合
- 裝備屬性決定合成結果
- 屬性需求限制合成配方
- 合成效率影響製作時間

### 3.5.3 與社交系統整合
- 浪漫屬性影響伴侶關係
- 伴侶屬性提供遊戲加成
- 社交互動影響屬性獲得

### 3.5.4 與進程系統整合
- 等級提升自動增加屬性
- 成就解鎖屬性加成
- 里程碑獎勵特殊屬性

相關檔案：
- [PlayerController.js](../src/scripts/PlayerController.js): 玩家控制與屬性應用
- [GameState.js](../src/scripts/GameState.js): 屬性狀態管理
- [EventEmitter.js](../src/scripts/EventEmitter.js): 屬性變化事件 