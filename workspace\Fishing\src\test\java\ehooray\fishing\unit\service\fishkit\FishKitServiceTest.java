package ehooray.fishing.unit.service.fishkit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import java.util.ArrayList;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishKitEntity;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.repository.FishKitRepository;
import ehooray.fishing.repository.PlayerRepository;
import ehooray.fishing.service.fishkit.FishKitService;
import ehooray.fishing.service.player.PlayerService;

public class FishKitServiceTest
{
    CsvContainer csvContainer;
    FishKitService fishKitService;
    PlayerService playerService;
    
    @BeforeEach
    public void createService()
    {
        csvContainer = mock(CsvContainer.class);
        var fishKitRepository = mock(FishKitRepository.class);
        fishKitService = new FishKitService(csvContainer, fishKitRepository);
        var playerRespoitory = mock(PlayerRepository.class);
        playerService = new PlayerService(csvContainer, playerRespoitory);
    }
    
    @ParameterizedTest
    @CsvSource({
        "1,2,3",
        "4,5,6"
    })
    public void testUpdateFishKit(long fishRodId, long fishLineId, long baitId)
    {
        // arrange
        var player = preparePlayerAndActiveKit(fishRodId, fishLineId, baitId);
        
        // act
        fishKitService.updateFishKit(player.getActiveFishKit(), player.getActiveFishKit().getFishRod(), 
                player.getActiveFishKit().getFishLine(), player.getActiveFishKit().getBait());
        var fishKit = player.getActiveFishKit();

        // assert
        assertThat(fishKit.getFishRod().getId()).isEqualTo(fishRodId);
        assertThat(fishKit.getFishLine().getId()).isEqualTo(fishLineId);
        assertThat(fishKit.getBait().getId()).isEqualTo(baitId);
    }
    
    @Test
    public void testActiveFishKit()
    {
        // arrange
        var player = preparePlayerAndActiveKit(1, 2, 3);
        var fishRod = new FishRodEntity();
        fishRod.setId(4l);
        var fishLine = mock(PlayerItemEntity.class);
        when(fishLine.getId()).thenReturn(5l);
        var bait = mock(PlayerItemEntity.class);
        when(bait.getId()).thenReturn(6l);
        var newFishKit = new FishKitEntity();
        newFishKit.setPlayer(player);
        newFishKit.setFishRod(fishRod);
        newFishKit.setFishLine(fishLine);
        newFishKit.setBait(bait);
        player.getFishKits().add(newFishKit);
        
        
        // act
        playerService.activeFishKit(player, newFishKit);
        
        // assert
        assertThat(player.getActiveFishKit().getFishRod().getId()).isEqualTo(4);
        assertThat(player.getActiveFishKit().getFishLine().getId()).isEqualTo(5);
        assertThat(player.getActiveFishKit().getBait().getId()).isEqualTo(6);
    }
    
    private PlayerEntity preparePlayerAndActiveKit(long fishRodId, long fishLineId, long baitId)
    {
        var fishKits = new ArrayList<FishKitEntity>();
        var fishRod = new FishRodEntity();
        fishRod.setId(fishRodId);
        var fishLine = mock(PlayerItemEntity.class);
        when(fishLine.getId()).thenReturn(fishLineId);
        var bait = mock(PlayerItemEntity.class);
        when(bait.getId()).thenReturn(baitId);
        var fishKitNew = new FishKitEntity();
        fishKitNew.setFishRod(fishRod);
        fishKitNew.setFishLine(fishLine);
        fishKitNew.setBait(bait);
        fishKits.add(fishKitNew);
        var player = new PlayerEntity();
        fishKitNew.setPlayer(player);
        player.setFishKits(fishKits);
        player.setActiveFishKit(fishKitNew);
        
        return player;
    }
}
