// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Base.proto

package ehooray.fishing.dto.proto;

public final class BaseProto {
  private BaseProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BaseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Base)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Base.Operator operator = 1;</code>
     * @return The enum numeric value on the wire for operator.
     */
    int getOperatorValue();
    /**
     * <code>.Base.Operator operator = 1;</code>
     * @return The operator.
     */
    ehooray.fishing.dto.proto.BaseProto.Base.Operator getOperator();

    /**
     * <code>.google.protobuf.Any data = 2;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>.google.protobuf.Any data = 2;</code>
     * @return The data.
     */
    com.google.protobuf.Any getData();
    /**
     * <code>.google.protobuf.Any data = 2;</code>
     */
    com.google.protobuf.AnyOrBuilder getDataOrBuilder();
  }
  /**
   * Protobuf type {@code Base}
   */
  public static final class Base extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Base)
      BaseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Base.newBuilder() to construct.
    private Base(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Base() {
      operator_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Base();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.BaseProto.internal_static_Base_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.BaseProto.internal_static_Base_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.BaseProto.Base.class, ehooray.fishing.dto.proto.BaseProto.Base.Builder.class);
    }

    /**
     * Protobuf enum {@code Base.Operator}
     */
    public enum Operator
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <code>Request = 0;</code>
       */
      Request(0),
      /**
       * <code>Response = 1;</code>
       */
      Response(1),
      UNRECOGNIZED(-1),
      ;

      /**
       * <code>Request = 0;</code>
       */
      public static final int Request_VALUE = 0;
      /**
       * <code>Response = 1;</code>
       */
      public static final int Response_VALUE = 1;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static Operator valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static Operator forNumber(int value) {
        switch (value) {
          case 0: return Request;
          case 1: return Response;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<Operator>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          Operator> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<Operator>() {
              public Operator findValueByNumber(int number) {
                return Operator.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.BaseProto.Base.getDescriptor().getEnumTypes().get(0);
      }

      private static final Operator[] VALUES = values();

      public static Operator valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private Operator(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:Base.Operator)
    }

    public static final int OPERATOR_FIELD_NUMBER = 1;
    private int operator_;
    /**
     * <code>.Base.Operator operator = 1;</code>
     * @return The enum numeric value on the wire for operator.
     */
    @java.lang.Override public int getOperatorValue() {
      return operator_;
    }
    /**
     * <code>.Base.Operator operator = 1;</code>
     * @return The operator.
     */
    @java.lang.Override public ehooray.fishing.dto.proto.BaseProto.Base.Operator getOperator() {
      @SuppressWarnings("deprecation")
      ehooray.fishing.dto.proto.BaseProto.Base.Operator result = ehooray.fishing.dto.proto.BaseProto.Base.Operator.valueOf(operator_);
      return result == null ? ehooray.fishing.dto.proto.BaseProto.Base.Operator.UNRECOGNIZED : result;
    }

    public static final int DATA_FIELD_NUMBER = 2;
    private com.google.protobuf.Any data_;
    /**
     * <code>.google.protobuf.Any data = 2;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return data_ != null;
    }
    /**
     * <code>.google.protobuf.Any data = 2;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.google.protobuf.Any getData() {
      return data_ == null ? com.google.protobuf.Any.getDefaultInstance() : data_;
    }
    /**
     * <code>.google.protobuf.Any data = 2;</code>
     */
    @java.lang.Override
    public com.google.protobuf.AnyOrBuilder getDataOrBuilder() {
      return getData();
    }

    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.BaseProto.Base parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.BaseProto.Base prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Base}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Base)
        ehooray.fishing.dto.proto.BaseProto.BaseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.BaseProto.internal_static_Base_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.BaseProto.internal_static_Base_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.BaseProto.Base.class, ehooray.fishing.dto.proto.BaseProto.Base.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.BaseProto.Base.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        operator_ = 0;

        if (dataBuilder_ == null) {
          data_ = null;
        } else {
          data_ = null;
          dataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.BaseProto.internal_static_Base_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.BaseProto.Base getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.BaseProto.Base.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.BaseProto.Base build() {
        ehooray.fishing.dto.proto.BaseProto.Base result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.BaseProto.Base buildPartial() {
        ehooray.fishing.dto.proto.BaseProto.Base result = new ehooray.fishing.dto.proto.BaseProto.Base(this);
        result.operator_ = operator_;
        if (dataBuilder_ == null) {
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int operator_ = 0;
      /**
       * <code>.Base.Operator operator = 1;</code>
       * @return The enum numeric value on the wire for operator.
       */
      @java.lang.Override public int getOperatorValue() {
        return operator_;
      }
      /**
       * <code>.Base.Operator operator = 1;</code>
       * @param value The enum numeric value on the wire for operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperatorValue(int value) {
        
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Base.Operator operator = 1;</code>
       * @return The operator.
       */
      @java.lang.Override
      public ehooray.fishing.dto.proto.BaseProto.Base.Operator getOperator() {
        @SuppressWarnings("deprecation")
        ehooray.fishing.dto.proto.BaseProto.Base.Operator result = ehooray.fishing.dto.proto.BaseProto.Base.Operator.valueOf(operator_);
        return result == null ? ehooray.fishing.dto.proto.BaseProto.Base.Operator.UNRECOGNIZED : result;
      }
      /**
       * <code>.Base.Operator operator = 1;</code>
       * @param value The operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperator(ehooray.fishing.dto.proto.BaseProto.Base.Operator value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        operator_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Base.Operator operator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperator() {
        
        operator_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Any data_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> dataBuilder_;
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       * @return Whether the data field is set.
       */
      public boolean hasData() {
        return dataBuilder_ != null || data_ != null;
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       * @return The data.
       */
      public com.google.protobuf.Any getData() {
        if (dataBuilder_ == null) {
          return data_ == null ? com.google.protobuf.Any.getDefaultInstance() : data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public Builder setData(com.google.protobuf.Any value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
          onChanged();
        } else {
          dataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public Builder setData(
          com.google.protobuf.Any.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
          onChanged();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public Builder mergeData(com.google.protobuf.Any value) {
        if (dataBuilder_ == null) {
          if (data_ != null) {
            data_ =
              com.google.protobuf.Any.newBuilder(data_).mergeFrom(value).buildPartial();
          } else {
            data_ = value;
          }
          onChanged();
        } else {
          dataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = null;
          onChanged();
        } else {
          data_ = null;
          dataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public com.google.protobuf.Any.Builder getDataBuilder() {
        
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      public com.google.protobuf.AnyOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_ == null ?
              com.google.protobuf.Any.getDefaultInstance() : data_;
        }
      }
      /**
       * <code>.google.protobuf.Any data = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  getData(),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Base)
    }

    // @@protoc_insertion_point(class_scope:Base)
    private static final ehooray.fishing.dto.proto.BaseProto.Base DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.BaseProto.Base();
    }

    public static ehooray.fishing.dto.proto.BaseProto.Base getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Base>
        PARSER = new com.google.protobuf.AbstractParser<Base>() {
      @java.lang.Override
      public Base parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Base> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Base> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.BaseProto.Base getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Base_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Base_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nBase.proto\032\031google/protobuf/any.proto\"" +
      "s\n\004Base\022 \n\010operator\030\001 \001(\0162\016.Base.Operato" +
      "r\022\"\n\004data\030\002 \001(\0132\024.google.protobuf.Any\"%\n" +
      "\010Operator\022\013\n\007Request\020\000\022\014\n\010Response\020\001B@\n\031" +
      "ehooray.fishing.dto.protoB\tBaseProtoH\002\252\002" +
      "\025Ehooray.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
        });
    internal_static_Base_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Base_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Base_descriptor,
        new java.lang.String[] { "Operator", "Data", });
    com.google.protobuf.AnyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
