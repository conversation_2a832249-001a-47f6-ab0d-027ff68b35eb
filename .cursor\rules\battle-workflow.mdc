---
description: Core gameplay development workflow for Fishing project, detailing battle system architecture with FishingManager state machine, fishing components (rod, line, fish AI), strategy pattern implementation, UI-battle interaction through Signal system, network communication flow, and comprehensive development process for fishing game mechanics.
globs: 
alwaysApply: false
---
# 遊戲核心玩法開發工作流程

本規則描述了Fishing項目中遊戲核心玩法的開發工作流程和架構模式。

## 核心玩法架構

### 戰鬥系統核心
- 遊戲核心玩法邏輯位於 [Assets/Fishing/Scripts/Battle](mdc:Assets/Fishing/Scripts/Battle) 目錄
- 主要控制器: [FishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/FishingManager.cs) - 管理釣魚遊戲狀態機
- 新版張力系統: [NewFishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/NewFishingManager.cs) - 處理新釣魚玩法
- 魚類行為系統: [Fish.cs](mdc:Assets/Fishing/Scripts/Battle/Fish.cs) - 控制魚的AI和挣扎行為

### 狀態機設計
釣魚系統包含以下狀態流程:
1. **Prepare**: 準備狀態
2. **BeforeStart**: 開始前準備
3. **Start**: 抛竿開始
4. **Wait**: 等待魚上鉤
5. **Fight**: 與魚搏鬥
6. **Catch**: 成功捕獲
7. **Fail**: 釣魚失敗
8. **Win**: 釣魚成功

## 遊戲組件系統

### 釣魚設備組件
- **釣魚竿系統**: [FishRod.cs](mdc:Assets/Fishing/Scripts/Battle/FishRod.cs) - 控制釣魚竿物理和動畫
- **釣魚線系統**: [FishLine.cs](mdc:Assets/Fishing/Scripts/Battle/FishLine.cs) - 管理釣魚線的物理表現
- **魚餌系統**: [BaitLine.cs](mdc:Assets/Fishing/Scripts/Battle/BaitLine.cs) - 處理魚餌的行為
- **力度指示器**: [FishRodForceItem.cs](mdc:Assets/Fishing/Scripts/Battle/FishRodForceItem.cs) - 顯示釣魚力度

### 輸入控制系統
- **手勢檢測**: [ThrowGestureDetector.cs](mdc:Assets/Fishing/Scripts/Battle/ThrowGestureDetector.cs) - 檢測抛竿手勢
- **兩點抛竿**: [ThrowBaitTwoPoint.cs](mdc:Assets/Fishing/Scripts/Battle/ThrowBaitTwoPoint.cs) - 處理雙點觸控抛竿

## 戰略模式系統

### 釣魚策略架構
- 策略基類: [FishingStrategyBase.cs](mdc:Assets/Fishing/Scripts/Battle/FishingStrategyBase.cs)
- 策略介面: [IFishingStrategy.cs](mdc:Assets/Fishing/Scripts/Battle/IFishingStrategy.cs)
- 舊版策略: [Assets/Fishing/Scripts/Battle/Strategies](mdc:Assets/Fishing/Scripts/Battle/Strategies) 目錄
- 新版策略: [Assets/Fishing/Scripts/Battle/NewStrategies](mdc:Assets/Fishing/Scripts/Battle/NewStrategies) 目錄

### 策略依賴注入
- 策略安裝器: [FishingStrategyInstaller.cs](mdc:Assets/Fishing/Scripts/Battle/FishingStrategyInstaller.cs)
- 新策略安裝器: [NewFishingStrategyInstaller.cs](mdc:Assets/Fishing/Scripts/Battle/NewFishingStrategyInstaller.cs)

## 數據管理

### 戰鬥數據結構
- **魚類數據**: [FishData.cs](mdc:Assets/Fishing/Scripts/Battle/FishData.cs) - 定義魚的屬性
- **裝備數據**: [BattleEquipeData.cs](mdc:Assets/Fishing/Scripts/Battle/BattleEquipeData.cs) - 戰鬥裝備配置
- **單例容器**: [FishingSingleton.cs](mdc:Assets/Fishing/Scripts/Battle/FishingSingleton.cs) - 戰鬥場景單例管理

### 核心組件
- **釣魚竿節點**: [FishRodPoint.cs](mdc:Assets/Fishing/Scripts/Battle/FishRodPoint.cs) - 釣魚竿控制點
- **釣魚線虛擬**: [FishLineDummy.cs](mdc:Assets/Fishing/Scripts/Battle/FishLineDummy.cs) - 釣魚線輔助組件

## 與UI系統交互

### UI-戰鬥交互模式
1. **狀態通知**: 戰鬥系統通過Signal通知UI狀態變化
2. **數據同步**: UI監聽戰鬥數據變化並實時更新顯示
3. **用戶輸入**: UI接收用戶操作並傳遞給戰鬥系統
4. **視覺反饋**: 戰鬥系統觸發UI動畫和特效

### 張力系統UI
- 張力管理器位於UI系統中，與戰鬥系統緊密配合
- 提供實時張力值顯示和警告功能
- 支援挣扎係數和張力閾值配置

## 網路通信模式

### 戰鬥網路流程
1. **戰鬥開始**: 向伺服器發送開始釣魚請求
2. **狀態同步**: 關鍵狀態變化時與伺服器同步
3. **結果提交**: 戰鬥結束時提交結果到伺服器
4. **獎勵獲取**: 接收伺服器返回的獎勵數據

### 數據持久化
- 戰鬥結果存儲到Repository
- 玩家進度和統計數據更新
- 成就和任務進度檢查

## 開發流程

### 核心玩法開發步驟
1. **狀態機設計**: 定義遊戲流程和狀態轉換
2. **組件實現**: 開發各個遊戲組件的邏輯
3. **策略模式**: 實現不同的遊戲策略和玩法變化
4. **UI集成**: 與UI系統建立通信和數據綁定
5. **網路集成**: 實現與伺服器的數據交換
6. **測試優化**: 進行玩法測試和性能優化

### 架構原則
- 使用Zenject進行依賴注入，避免硬編碼依賴
- 狀態機模式管理複雜的遊戲流程
- 策略模式支援不同玩法變化
- Signal系統實現組件間解耦通信
- 數據驅動設計，通過配置表控制遊戲行為

