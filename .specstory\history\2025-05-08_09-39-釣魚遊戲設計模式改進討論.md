<!-- Generated by SpecStory -->

# 釣魚遊戲設計模式改進討論 (2025-05-08 17:39:47)

_**User**_

這個釣魚遊戲要換皮製作新玩法，但是目前狀態動作寫得有點死，適合用design pattern來改動嗎

---

_**Assistant**_



---

# 状态模式重构建议

当前釣魚游戏代码使用简单的枚举状态和switch语句处理不同状态下的行为，这确实不够灵活，难以扩展新玩法。建议使用**状态模式**进行重构。

## 问题分析
1. 所有状态逻辑集中在FishingManager类中
2. 状态转换逻辑分散在多个SetStateTo*方法中
3. 各个Do*方法耦合度高，不易修改或扩展

## 状态模式实现方案

```c#
// 定义状态接口
public interface IFishingState
{
    void Enter(FishingManager context); // 进入状态执行的操作
    void Update(FishingManager context); // 状态中每帧执行的操作
    void Exit(FishingManager context);   // 退出状态执行的操作
}

// 示例：准备状态实现
public class PrepareState : IFishingState
{
    public void Enter(FishingManager context)
    {
        // 原SetStateToPrepare方法中的初始化逻辑
        FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick += context.prepareManager.OnStartButtonClick;
        context.cordReelManager.OpenStartText();
        context.fishingGamePanelManager.OpenAsync<PrepareManager>().Forget();
        // 其他初始化逻辑...
    }

    public void Update(FishingManager context)
    {
        // 原DoPrepare方法中的逻辑
    }

    public void Exit(FishingManager context)
    {
        // 状态退出逻辑
        FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick -= context.prepareManager.OnStartButtonClick;
    }
}

// 状态工厂类，负责创建和管理状态实例
public class FishingStateFactory
{
    private Dictionary<FishingState, IFishingState> _states = new Dictionary<FishingState, IFishingState>();

    public FishingStateFactory()
    {
        // 注册所有状态
        _states[FishingState.Prepare] = new PrepareState();
        _states[FishingState.BeforeStart] = new BeforeStartState();
        // 其他状态...
    }

    public IFishingState GetState(FishingState stateType)
    {
        return _states[stateType];
    }
}
```

## FishingManager修改方案

```c#
public class FishingManager : MonoBehaviour
{
    // 其他现有字段...
    
    private FishingStateFactory _stateFactory;
    private IFishingState _currentStateImpl;
    
    // Zenject依赖注入后初始化
    private void Start()
    {
        _stateFactory = new FishingStateFactory();
        // 其他初始化...
        
        TransitionToState(FishingState.Prepare);
    }
    
    private void Update()
    {
        _currentStateImpl?.Update(this);
    }
    
    // 统一的状态转换方法
    public void TransitionToState(FishingState newState)
    {
        // 退出当前状态
        _currentStateImpl?.Exit(this);
        
        // 记录新状态
        currentState = newState;
        
        // 获取并进入新状态
        _currentStateImpl = _stateFactory.GetState(newState);
        _currentStateImpl.Enter(this);
        
        // 触发状态变化事件
        OnStateChanged?.Invoke(currentState);
    }
    
    // 移除所有SetStateTo*方法，改用TransitionToState
    // 移除所有Do*方法，逻辑移至对应状态类
}
```

## 优势
1. 每个状态的逻辑封装在单独的类中，易于理解和修改
2. 添加新状态只需创建新的状态类，无需修改FishingManager
3. 状态间的转换更清晰
4. 可以为不同皮肤/玩法创建不同的状态实现

这种模式特别适合游戏这种有明确状态转换的系统，能使代码更模块化，便于进行不同玩法的定制和扩展。

fetch_rules 釣魚玩法邏輯場景相關部分?

---

_**User**_

請幫我根據以上使用design pattern重構狀態執行動作，新腳本比如interface會放在 @Battle ，不用使用工廠，請使用********************************* 注入進去實作，我想使用策略模式
不要使用狀態模式

---

_**Assistant**_



---

# 釣魚狀態系統策略模式重構

我將幫你設計一個基於策略模式的釣魚狀態系統，通過Zenject注入而非工廠模式實現。

## 1. 策略接口定義

首先，創建一個表示釣魚策略的接口：

---

```diff
- 
+ using UnityEngine;
+ using Cysharp.Threading.Tasks;
+ 
+ namespace Fishing
+ {
+     /// <summary>
+     /// 釣魚策略接口，定義不同釣魚狀態下的行為
+     /// </summary>
+     public interface IFishingStrategy
+     {
+         /// <summary>
+         /// 進入該狀態時執行的方法
+         /// </summary>
+         UniTask OnEnter(FishingManager context);
+         
+         /// <summary>
+         /// 在該狀態中每幀執行的方法
+         /// </summary>
+         void OnUpdate(FishingManager context);
+         
+         /// <summary>
+         /// 退出該狀態時執行的方法
+         /// </summary>
+         UniTask OnExit(FishingManager context);
+         
+         /// <summary>
+         /// 獲取此策略對應的釣魚狀態
+         /// </summary>
+         FishingState GetStateType();
+     }
+ } 
```

---

## 2. 實現策略基類

為了減少代碼重複，先創建一個基礎策略類：

---

```diff
- 
+ using System.Collections;
+ using System.Collections.Generic;
+ using UnityEngine;
+ using Cysharp.Threading.Tasks;
+ 
+ namespace Fishing
+ {
+     /// <summary>
+     /// 釣魚策略基類，提供通用實現
+     /// </summary>
+     public abstract class FishingStrategyBase : IFishingStrategy
+     {
+         protected FishingState stateType;
+         
+         protected FishingStrategyBase(FishingState stateType)
+         {
+             this.stateType = stateType;
+         }
+         
+         /// <summary>
+         /// 獲取此策略對應的釣魚狀態
+         /// </summary>
+         public FishingState GetStateType() => stateType;
+         
+         /// <summary>
+         /// 進入該狀態時執行的方法，默認返回已完成的任務
+         /// </summary>
+         public virtual UniTask OnEnter(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+         
+         /// <summary>
+         /// 在該狀態中每幀執行的方法，默認為空實現
+         /// </summary>
+         public virtual void OnUpdate(FishingManager context) { }
+         
+         /// <summary>
+         /// 退出該狀態時執行的方法，默認返回已完成的任務
+         /// </summary>
+         public virtual UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 3. 具體策略實現

現在讓我們實現幾個關鍵的策略類：

### 3.1 準備狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Ehooray;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚準備狀態策略
+     /// </summary>
+     public class PrepareStrategy : FishingStrategyBase
+     {
+         public PrepareStrategy() : base(FishingState.Prepare) { }
+         
+         public override UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToPrepare方法中的初始化邏輯
+             FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick += context.prepareManager.OnStartButtonClick;
+ 
+             context.cordReelManager.OpenStartText();
+             context.fishingGamePanelManager.OpenAsync<PrepareManager>().Forget();
+             context.cordReelManager.wheelRotate.StopRotate();
+             context.fishingGamePanelManager.OpenAsync<TopInfoManager>().Forget();
+             context.prepareManager.leftArrowButton.onClick.Invoke();
+             
+             return UniTask.CompletedTask;
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoPrepare方法中的邏輯，目前為空
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             // 清理事件監聽
+             FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick -= context.prepareManager.OnStartButtonClick;
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

### 3.2 戰鬥狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Ehooray;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚戰鬥狀態策略
+     /// </summary>
+     public class FightStrategy : FishingStrategyBase
+     {
+         public FightStrategy() : base(FishingState.Fight) { }
+         
+         public override async UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToFight方法中的初始化邏輯
+             context.forceItem.PlayWaterEffectForFight();
+             context.fishLine.StopLineAnimation();
+             
+             await context.fishingGamePanelManager.OpenAsync<LineTensionManager>();
+             context.lineTensionManager.Play();
+             
+             var trigger = FishingEventTrigger.GetTrigger(context.cordReelManager.pullButton.gameObject);
+             trigger.onPointerDown += context.lineTensionManager.OnPullButtonPressed;
+             trigger.onPointerUp += context.lineTensionManager.OnPullButtonUp;
+             
+             context.cordReelManager.SetCordReelButtonColorNormal();
+             context.cordReelManager.StartInteractive();
+             
+             context.fishInfoManager.ResetText();
+             context.fishInfoManager.SetLineLengthText(context.fish.lineLength + context.EquipeData.lineData.CsvData.Parameter_1);
+             await context.fishingGamePanelManager.OpenAsync<FishInfoManager>();
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoFight方法中的邏輯
+             context.fish.Move();
+             context.fish.Leave();
+             context.fishLine.StraightLine();
+             context.forceItem.SetForce(40f);
+             context.fishRod.KeepFacingFish();
+             context.FightRotateCamera();
+             
+             if (context.lineTensionManager.IsValidFight())
+             {
+                 context.fish.Fight();
+             }
+             
+             context.fish.SetDistanceZ();
+             
+             // 狀態轉換邏輯
+             if (context.fish.distance <= context.fish.catchDistance)
+             {
+                 context.TransitionToState(FishingState.Catch).Forget();
+                 return;
+             }
+             
+             if (context.fish.distance >= context.fish.lineLength + context.EquipeData.lineData.CsvData.Parameter_1)
+             {
+                 context.TransitionToState(FishingState.Fail).Forget();
+                 return;
+             }
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             // 清理事件監聽
+             var trigger = FishingEventTrigger.GetTrigger(context.cordReelManager.pullButton.gameObject);
+             trigger.onPointerDown -= context.lineTensionManager.OnPullButtonPressed;
+             trigger.onPointerUp -= context.lineTensionManager.OnPullButtonUp;
+             
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 4. 更新FishingManager以使用策略模式

現在讓我們修改FishingManager，使其支持策略模式：

---

```diff
  
          public BattleEquipeData EquipeData => equipeData;
+ 
+         // 策略相關字段
+         protected Dictionary<FishingState, IFishingStrategy> strategies = new Dictionary<FishingState, IFishingStrategy>();
+         protected IFishingStrategy currentStrategy;
  
          #region Zenject Inject
```

```diff
          protected BattleLevelManager battleLevelManager;
          protected TextIDConfig textIDConfig;
+         protected List<IFishingStrategy> allStrategies;
          #endregion
  
```

```diff
                      FishingRepositories fishingRepositories,
                      BattleLevelManager battleLevelManager,
-                     TextIDConfig textIDConfig)
+                     TextIDConfig textIDConfig,
+                     List<IFishingStrategy> strategies)
          {
              this.fishingContainer = fishingContainer;
```

```diff
              this.battleLevelManager = battleLevelManager;
              this.textIDConfig = textIDConfig;
+             this.allStrategies = strategies;
+             
+             // 初始化策略字典
+             foreach (var strategy in strategies)
+             {
+                 this.strategies[strategy.GetStateType()] = strategy;
+             }
          }
  
```

```diff
              baitPath.animate.enabled = false;
  
-             SetState(FishingState.Prepare);
+             // 使用新的狀態轉換方法
+             TransitionToState(FishingState.Prepare).Forget();
          }
  
          private void Update()
          {
-             switch (currentState)
-             {
-                 case FishingState.Prepare:
-                     DoPrepare();
-                     break;
- 
-                 case FishingState.BeforeStart:
-                     DoBeforeStart();
-                     break;
- 
-                 case FishingState.Start:
-                     DoStart();
-                     break;
- 
-                 case FishingState.Wait:
-                     DoWait();
-                     break;
- 
-                 case FishingState.Fight:
-                     DoFight();
-                     break;
- 
-                 case FishingState.Catch:
-                     DoCatch();
-                     break;
- 
-                 case FishingState.Fail:
-                     DoFail();
-                     break;
- 
-                 case FishingState.Win:
-                     DoWin();
-                     break;
-             }
+             // 使用當前策略進行更新
+             currentStrategy?.OnUpdate(this);
+         }
+ 
+         /// <summary>
+         /// 轉換到新狀態
+         /// </summary>
+         public async UniTaskVoid TransitionToState(FishingState newState)
+         {
+             // 如果沒有變化或策略不存在，則直接返回
+             if (currentState == newState || !strategies.ContainsKey(newState))
+                 return;
+             
+             // 退出當前策略
+             if (currentStrategy != null)
+             {
+                 await currentStrategy.OnExit(this);
+             }
+             
+             // 更新狀態
+             currentState = newState;
+             currentStrategy = strategies[newState];
+             
+             // 觸發狀態變化事件
+             OnStateChanged?.Invoke(currentState);
+             
+             // 進入新策略
+             await currentStrategy.OnEnter(this);
          }
  
```

```diff
          }
  
+         // 保留舊的SetState方法以兼容現有代碼
          public void SetState(FishingState state)
          {
```

```diff
              OnStateChanged?.Invoke(currentState);
          }
- 
-         public void SetStateToPrepare()
-         {
-             FishingEventTrigger.GetTrigger(prepareManager.startButton.gameObject).onPointerClick += prepareManager.OnStartButtonClick;
- 
-             cordReelManager.OpenStartText();
-             fishingGamePanelManager.OpenAsync<PrepareManager>().Forget();
-             cordReelManager.wheelRotate.StopRotate();
-             fishingGamePanelManager.OpenAsync<TopInfoManager>().Forget();
-             prepareManager.leftArrowButton.onClick.Invoke();
-             SetState(FishingState.Prepare);
-         }
- 
-         public void SetStateToBeforeStart()
-         {
-             equipeData.SetEquipeData();
-             if (!CheckPlayerItem())
-                 return;
- 
-             fish.ChangeFishModel().Forget();
- 
-             cordReelManager.CloseStartText();
-             fishingGamePanelManager.Close<PrepareManager>();
-             fishingGamePanelManager.Close<TopInfoManager>();
- 
-             FishingEventTrigger.GetTrigger(prepareManager.startButton.gameObject).onPointerClick -= prepareManager.OnStartButtonClick;
-             FishingEventTrigger.GetTrigger(throwBaitManager.pullButton.gameObject).onPointerDown += throwBaitManager.OnPullButtonPressed;
- 
-             fish.continueTimes = fishData.RetryCount;
-             fish.stunTimes = fish.continueTimes;
-             fish.SetFishData();
- 
-             // 重置甩動檢測器，確保可以檢測拋竿動作
-             if (GetComponent<ThrowGestureDetector>() != null)
-             {
-                 GetComponent<ThrowGestureDetector>().ResetDetector();
-             }
- 
-             fishingGamePanelManager.OpenAsync<ThrowBaitManager>().Forget();
-             throwBaitManager.Play();
- 
-             fishingGamePanelManager.OpenAsync<CordReelManager>().Forget();
-             if (lineTensionManager != null)
-             lineTensionManager.SetRodControlValue(equipeData.rodData.control);
-             SetState(FishingState.BeforeStart);
-         }
- 
-         public void SetStateToStart()
-         {
-             FishingEventTrigger.GetTrigger(throwBaitManager.pullButton.gameObject).onPointerDown -= throwBaitManager.OnPullButtonPressed;
- 
-             fishRod.PullOutRod();
-             fishingGamePanelManager.Close<ThrowBaitManager>();
-             throwBaitManager.CloseRoot();
-             cordReelManager.wheelRotate.StartRotate();
-             RandomStartPosition();
-             fishLine.FaceToBaitWhenThrowBait();
-             SetState(FishingState.Start);
-         }
- 
-         public void SetStateToWait()
-         {
-             cordReelManager.wheelRotate.SetWaitFishSpeed();
-             baitPath.StopBaitAnimation();
-             fishRod.HideBaitObject();
-             SetState(FishingState.Wait);
-         }
- 
-         public virtual void SetStateToFight()
-         {
-             forceItem.PlayWaterEffectForFight();
-             fishLine.StopLineAnimation();
-             fishingGamePanelManager.OpenAsync<LineTensionManager>().Forget();
-             lineTensionManager.Play();
-             FishingEventTrigger.GetTrigger(cordReelManager.pullButton.gameObject).onPointerDown += lineTensionManager.OnPullButtonPressed;
-             FishingEventTrigger.GetTrigger(cordReelManager.pullButton.gameObject).onPointerUp += lineTensionManager.OnPullButtonUp;
-             cordReelManager.SetCordReelButtonColorNormal();
-             cordReelManager.StartInteractive();
-             fishInfoManager.ResetText();
-             fishInfoManager.SetLineLengthText(fish.lineLength + equipeData.lineData.CsvData.Parameter_1);
-             fishingGamePanelManager.OpenAsync<FishInfoManager>().Forget();
-             SetState(FishingState.Fight);
-         }
- 
-         public virtual void SetStateToCatch()
-         {
-             fishLine.SetTopPointToRod();
-             cordReelManager.ResetCordReel();
-             fishingGamePanelManager.Close<CordReelManager>();
-             fishingGamePanelManager.Close<PausePanelManager>();
-             lineTensionManager.CloseRoot();
-             fishingGamePanelManager.Close<LineTensionManager>();
-             fishingGamePanelManager.Close<FishInfoManager>();
-             ShakeCamera.ShakeOnce(mainCamera).Forget();
-             forceItem.CloseWaterEffect();
-             forceItem.SetForce(0);
-             fishRod.rodModel.gameObject.SetActive(false);
-             fishLine.gameObject.SetActive(false);
-             fish.transform.gameObject.SetActive(true);
-             fish.PullUpAnimation();
-             fish.PlayWaterEffect();
-             mainCamera.GetComponent<Animator>().enabled = true;
-             mainCamera.GetComponent<Animator>().Play("Show", 0, 0);
-             SetBlankScreen(true);
- 
-             SetState(FishingState.Catch);
-         }
- 
-         public virtual void SetStateToFail()
-         {
-             fish.ChangeContinueShowFish();
-             fish.useSkill = false;
-             continueManager.SetFishInfo();
-             cordReelManager.ResetCordReel();
-             fishingGamePanelManager.Close<CordReelManager>();
-             fishingGamePanelManager.Close<PausePanelManager>();
-             lineTensionManager.CloseRoot();
-             fishingGamePanelManager.Close<LineTensionManager>();
-             fishingGamePanelManager.Close<FishInfoManager>();
-             UniTask.Void(async() => 
-             {
-                 var panel = await fishingGamePanelManager.OpenAsync<ContinueManager>();
-                 panel.Refresh();
-             });
-             SetState(FishingState.Fail);
-         }
- 
-         public async UniTask SetStateToWin()
-         {
-             var WinFishResult = await fishingContainer.networkService.battleService.WinFish();
-             await fishingContainer.networkService.questService.GetAchievements();
- 
-             fish.continueTimes = 0;
-             fish.SetFishData();
-             fish.SetFishToShowPoint();
-             await fishingGamePanelManager.OpenAsync<GameEndManager>();
- 
-             gameEndManager.SetFishInfoText(WinFishResult);
-             fish.line.gameObject.SetActive(true);
-             mainCamera.transform.forward = (fish.showTarget.transform.position + new Vector3(0.15f, 0, 0)) - mainCamera.transform.position;
-             SetBlankScreen(false);
-             fishData.UpdataFishData(WinFishResult.NextFishCsvId);
-             lineTensionManager.fishingSkillBarManager.ResetSkillBar();
-             SetState(FishingState.Win);
-         }
- 
-         public async UniTaskVoid FinishCatchAnimate()
-         {
-             mainCamera.GetComponent<Animator>().enabled = false;
-             fish.Show(mainCamera);
-             await SetStateToWin();
-         }
- 
-         public void ResetGame()
-         {
-             fish.ResetValue();
-             ResetValue();
-             
-             // 重置甩動檢測器
-             if (GetComponent<ThrowGestureDetector>() != null)
-             {
-                 GetComponent<ThrowGestureDetector>().ResetDetector();
-             }
-             
-             SetStateToPrepare();
-         }
- 
-         public async UniTask ContinueGame()
-         {
-             var continueResult = await battleService.RetryFish();
-             fish.continueTimes = continueResult.RetryCount;
-             fishData.RetryCount = continueResult.RetryCount;
-             fish.Continue();
-             ResetValue();
- 
-             // 重置甩動檢測器
-             if (GetComponent<ThrowGestureDetector>() != null)
-             {
-                 GetComponent<ThrowGestureDetector>().ResetDetector();
-             }
- 
-             fishingGamePanelManager.OpenAsync<ThrowBaitManager>().Forget();
-             throwBaitManager.Play();
-             fishingGamePanelManager.OpenAsync<CordReelManager>().Forget();
-             FishingEventTrigger.GetTrigger(throwBaitManager.pullButton.gameObject).onPointerDown += throwBaitManager.OnPullButtonPressed;
-             SetState(FishingState.BeforeStart);
-         }
- 
-         public void ResetCamera()
-         {
-             mainCamera.transform.position = oriCameraPosition;
-             mainCamera.transform.eulerAngles = oriCameraRotation;
-         }
  
          public void RandomStartPosition()
```

```diff
                  blankScreen.Play("ToNone", 0, 0);
              }
-             //blankScreen.alpha = Mathf.Lerp(blankScreen.alpha, set, Time.deltaTime * 5);
-         }
- 
-         
-         /// <summary>
-         /// 載入所有遊戲面板並進行初始化設置
-         /// 1. 載入魚類資訊面板並設置其大小和渲染模式
-         /// 2. 載入捲線器面板並開啟開始文字
-         /// 3. 載入拋餌面板並設置按鈕
-         /// 4. 載入張力面板
-         /// 5. 載入遊戲結束面板
-         /// 6. 載入繼續面板
-         /// 7. 載入準備面板並設置開始按鈕事件
-         /// 8. 載入頂部資訊面板
-         /// 9. 載入特寫文字面板
-         /// 10. 載入暫停面板
-         /// 11. 設置技能相關事件和提示
-         /// 12. 關閉部分面板並重置狀態
-         /// </summary>
+         }
+ 
          public virtual void LoadPanel()
          {
```

```diff
  
              prepareManager.startButton = cordReelManager.pullButton;
-             FishingEventTrigger.GetTrigger(prepareManager.startButton.gameObject).onPointerClick += prepareManager.OnStartButtonClick;
  
              if (fishingGamePanelManager.TryGetGamePanel<TopInfoManager>(out TopInfoManager topInfoPanel))
```

```diff
          }
          
-         // 內部流程（protected virtual 供子類覆寫，否則 private）
-         protected virtual void DoFight()
-         {
-             fish.Move();
-             fish.Leave();
-             fishLine.StraightLine();
-             forceItem.SetForce(40f);
-             fishRod.KeepFacingFish();
-             FightRotateCamera();
-             if (lineTensionManager.IsValidFight())
-             {
-                 fish.Fight();
+         // 保留為了向後兼容的方法
+         public void ResetCamera()
+         {
+             mainCamera.transform.position = oriCameraPosition;
+             mainCamera.transform.eulerAngles = oriCameraRotation;
          }
-             fish.SetDistanceZ();
-             if (fish.distance <= fish.catchDistance)
+         
+         public async UniTaskVoid FinishCatchAnimate()
          {
-                 SetStateToCatch();
-                 return;
-             }
-             if (fish.distance >= fish.lineLength + equipeData.lineData.CsvData.Parameter_1)
-             {
-                 SetStateToFail();
-                 return;
-             }
-         }
- 
-         private void DoPrepare()
-         {
- 
-         }
-         private void DoBeforeStart()
-         {
- 
-         }
-         private void DoStart()
+             mainCamera.GetComponent<Animator>().enabled = false;
+             fish.Show(mainCamera);
+             await TransitionToState(FishingState.Win);
+         }
+ 
+         public void ResetGame()
          {
-             if (CheckRodAnimatorState("leave"))
-             {
-                 if (fishRod.fishRodAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime > 0.55f)
+             fish.ResetValue();
+             ResetValue();
+             
+             // 重置甩動檢測器
+             if (GetComponent<ThrowGestureDetector>() != null)
              {
-                     baitPath.DoMove();
-                     fishLine.PlayLineAnimation();
-                     baitLine.gameObject.SetActive(false);
-                     baitPath.PlayBaitAnimation();
-                     fishLine.SetTopPointToMesh();
-                     ThrowBaitCamera();
+                 GetComponent<ThrowGestureDetector>().ResetDetector();
+             }
+             
+             TransitionToState(FishingState.Prepare).Forget();
          }
-             }
-             if (CheckRodAnimatorState("idle"))
-             {
-                 SetStateToWait();
-             }
-         }
-         private void DoWait()
-         {
-             fish.Wait();
-             forceItem.PlayWaterEffectForWait();
-             forceItem.SetForce(5);
-             if (fish.waitTime <= 0)
-             {
-                 SetStateToFight();
-             }
-         }
-         private void DoCatch()
-         {
-             //showTime += Time.deltaTime;
-             //if (showTime >= 2.25f)
-             //{
-             //    UniTask.Void(async () =>
-             //    {
-             //        mainCamera.GetComponent<Animator>().enabled = false;
-             //        fish.Show(mainCamera);
-             //        showTime = 0;
-             //        await SetStateToWin();
-             //        return;
-             //    });
-             //}
-         }
-         private void DoFail()
-         {
- 
-         }
-         private void DoWin()
-         {
- 
+         
+         public async UniTask ContinueGame()
+         {
+             var continueResult = await battleService.RetryFish();
+             fish.continueTimes = continueResult.RetryCount;
+             fishData.RetryCount = continueResult.RetryCount;
+             fish.Continue();
+             ResetValue();
+ 
+             // 重置甩動檢測器
+             if (GetComponent<ThrowGestureDetector>() != null)
+             {
+                 GetComponent<ThrowGestureDetector>().ResetDetector();
+             }
+ 
+             await TransitionToState(FishingState.BeforeStart);
          }
  
```

---

## 5. 實現更多策略類

現在我們來實現更多的策略類：

### 5.1 開始前狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Ehooray;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚開始前狀態策略
+     /// </summary>
+     public class BeforeStartStrategy : FishingStrategyBase
+     {
+         public BeforeStartStrategy() : base(FishingState.BeforeStart) { }
+         
+         public override async UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToBeforeStart方法中的初始化邏輯
+             context.equipeData.SetEquipeData();
+             if (!context.CheckPlayerItem())
+                 return;
+ 
+             context.fish.ChangeFishModel().Forget();
+ 
+             context.cordReelManager.CloseStartText();
+             context.fishingGamePanelManager.Close<PrepareManager>();
+             context.fishingGamePanelManager.Close<TopInfoManager>();
+ 
+             FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick -= context.prepareManager.OnStartButtonClick;
+             FishingEventTrigger.GetTrigger(context.throwBaitManager.pullButton.gameObject).onPointerDown += context.throwBaitManager.OnPullButtonPressed;
+ 
+             context.fish.continueTimes = context.fishData.RetryCount;
+             context.fish.stunTimes = context.fish.continueTimes;
+             context.fish.SetFishData();
+ 
+             // 重置甩動檢測器，確保可以檢測拋竿動作
+             if (context.GetComponent<ThrowGestureDetector>() != null)
+             {
+                 context.GetComponent<ThrowGestureDetector>().ResetDetector();
+             }
+ 
+             await context.fishingGamePanelManager.OpenAsync<ThrowBaitManager>();
+             context.throwBaitManager.Play();
+ 
+             await context.fishingGamePanelManager.OpenAsync<CordReelManager>();
+             if (context.lineTensionManager != null)
+                 context.lineTensionManager.SetRodControlValue(context.EquipeData.rodData.control);
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoBeforeStart方法中的邏輯，目前為空
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             // 在退出此狀態時清理資源或事件
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

### 5.2 投擲狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Ehooray;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚投擲狀態策略
+     /// </summary>
+     public class StartStrategy : FishingStrategyBase
+     {
+         public StartStrategy() : base(FishingState.Start) { }
+         
+         public override UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToStart方法中的初始化邏輯
+             FishingEventTrigger.GetTrigger(context.throwBaitManager.pullButton.gameObject).onPointerDown -= context.throwBaitManager.OnPullButtonPressed;
+ 
+             context.fishRod.PullOutRod();
+             context.fishingGamePanelManager.Close<ThrowBaitManager>();
+             context.throwBaitManager.CloseRoot();
+             context.cordReelManager.wheelRotate.StartRotate();
+             context.RandomStartPosition();
+             context.fishLine.FaceToBaitWhenThrowBait();
+             
+             return UniTask.CompletedTask;
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoStart方法中的邏輯
+             if (context.CheckRodAnimatorState("leave"))
+             {
+                 if (context.fishRod.fishRodAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime > 0.55f)
+                 {
+                     context.baitPath.DoMove();
+                     context.fishLine.PlayLineAnimation();
+                     context.baitLine.gameObject.SetActive(false);
+                     context.baitPath.PlayBaitAnimation();
+                     context.fishLine.SetTopPointToMesh();
+                     context.ThrowBaitCamera();
+                 }
+             }
+             if (context.CheckRodAnimatorState("idle"))
+             {
+                 context.TransitionToState(FishingState.Wait).Forget();
+             }
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 6. 更新Zenject配置

創建一個注入器，用於注冊所有釣魚策略：

---

```diff
- 
+ using System.Collections.Generic;
+ using Fishing.Strategies;
+ using UnityEngine;
+ using Zenject;
+ 
+ namespace Fishing
+ {
+     /// <summary>
+     /// 釣魚策略安裝器
+     /// 用於向Zenject容器註冊所有釣魚策略
+     /// </summary>
+     public class FishingStrategyInstaller : MonoInstaller<FishingStrategyInstaller>
+     {
+         public override void InstallBindings()
+         {
+             // 註冊所有策略實例
+             Container.Bind<IFishingStrategy>().To<PrepareStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<BeforeStartStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<StartStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<FightStrategy>().AsSingle();
+             
+             // 將來可以繼續添加其他策略
+             // Container.Bind<IFishingStrategy>().To<WaitStrategy>().AsSingle();
+             // Container.Bind<IFishingStrategy>().To<CatchStrategy>().AsSingle();
+             // Container.Bind<IFishingStrategy>().To<FailStrategy>().AsSingle();
+             // Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
+             
+             // 注入所有IFishingStrategy實例列表
+             Container.Bind<List<IFishingStrategy>>().FromResolveAll<IFishingStrategy>().AsSingle();
+         }
+     }
+ } 
```

---

```diff
              // 遊戲啟動器綁定
              Container.BindInterfacesAndSelfTo<FishingGameBootstrap>().AsSingle();
+             
+             // 安裝釣魚策略系統
+             FishingStrategyInstaller.Install(Container);
          }
  
```

---

## 7. 等待狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚等待狀態策略
+     /// </summary>
+     public class WaitStrategy : FishingStrategyBase
+     {
+         public WaitStrategy() : base(FishingState.Wait) { }
+         
+         public override UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToWait方法中的初始化邏輯
+             context.cordReelManager.wheelRotate.SetWaitFishSpeed();
+             context.baitPath.StopBaitAnimation();
+             context.fishRod.HideBaitObject();
+             
+             return UniTask.CompletedTask;
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoWait方法中的邏輯
+             context.fish.Wait();
+             context.forceItem.PlayWaterEffectForWait();
+             context.forceItem.SetForce(5);
+             if (context.fish.waitTime <= 0)
+             {
+                 context.TransitionToState(FishingState.Fight).Forget();
+             }
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 8. 捕獲狀態策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Ehooray;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚捕獲狀態策略
+     /// </summary>
+     public class CatchStrategy : FishingStrategyBase
+     {
+         public CatchStrategy() : base(FishingState.Catch) { }
+         
+         public override UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToCatch方法中的初始化邏輯
+             context.fishLine.SetTopPointToRod();
+             context.cordReelManager.ResetCordReel();
+             context.fishingGamePanelManager.Close<CordReelManager>();
+             context.fishingGamePanelManager.Close<PausePanelManager>();
+             context.lineTensionManager.CloseRoot();
+             context.fishingGamePanelManager.Close<LineTensionManager>();
+             context.fishingGamePanelManager.Close<FishInfoManager>();
+             
+             ShakeCamera.ShakeOnce(context.mainCamera).Forget();
+             context.forceItem.CloseWaterEffect();
+             context.forceItem.SetForce(0);
+             context.fishRod.rodModel.gameObject.SetActive(false);
+             context.fishLine.gameObject.SetActive(false);
+             context.fish.transform.gameObject.SetActive(true);
+             context.fish.PullUpAnimation();
+             context.fish.PlayWaterEffect();
+             context.mainCamera.GetComponent<Animator>().enabled = true;
+             context.mainCamera.GetComponent<Animator>().Play("Show", 0, 0);
+             context.SetBlankScreen(true);
+             
+             return UniTask.CompletedTask;
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoCatch方法中的邏輯，通常由動畫事件驅動，此處可以不做處理
+             // 實際動畫完成後，會通過context.FinishCatchAnimate()轉到Win狀態
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 9. 更新策略安裝器

---

```diff
              Container.Bind<IFishingStrategy>().To<BeforeStartStrategy>().AsSingle();
              Container.Bind<IFishingStrategy>().To<StartStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<WaitStrategy>().AsSingle();
              Container.Bind<IFishingStrategy>().To<FightStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<CatchStrategy>().AsSingle();
              
              // 將來可以繼續添加其他策略
-             // Container.Bind<IFishingStrategy>().To<WaitStrategy>().AsSingle();
-             // Container.Bind<IFishingStrategy>().To<CatchStrategy>().AsSingle();
              // Container.Bind<IFishingStrategy>().To<FailStrategy>().AsSingle();
              // Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
```

---

## 10. 失敗和勝利策略

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚失敗狀態策略
+     /// </summary>
+     public class FailStrategy : FishingStrategyBase
+     {
+         public FailStrategy() : base(FishingState.Fail) { }
+         
+         public override async UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToFail方法中的初始化邏輯
+             context.fish.ChangeContinueShowFish();
+             context.fish.useSkill = false;
+             context.continueManager.SetFishInfo();
+             context.cordReelManager.ResetCordReel();
+             context.fishingGamePanelManager.Close<CordReelManager>();
+             context.fishingGamePanelManager.Close<PausePanelManager>();
+             context.lineTensionManager.CloseRoot();
+             context.fishingGamePanelManager.Close<LineTensionManager>();
+             context.fishingGamePanelManager.Close<FishInfoManager>();
+             
+             var panel = await context.fishingGamePanelManager.OpenAsync<ContinueManager>();
+             panel.Refresh();
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoFail方法中的邏輯，目前為空
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

```diff
- 
+ using Cysharp.Threading.Tasks;
+ using Fishing.UI;
+ using UnityEngine;
+ 
+ namespace Fishing.Strategies
+ {
+     /// <summary>
+     /// 釣魚勝利狀態策略
+     /// </summary>
+     public class WinStrategy : FishingStrategyBase
+     {
+         public WinStrategy() : base(FishingState.Win) { }
+         
+         public override async UniTask OnEnter(FishingManager context)
+         {
+             // 原SetStateToWin方法中的初始化邏輯
+             var WinFishResult = await context.fishingContainer.networkService.battleService.WinFish();
+             await context.fishingContainer.networkService.questService.GetAchievements();
+ 
+             context.fish.continueTimes = 0;
+             context.fish.SetFishData();
+             context.fish.SetFishToShowPoint();
+             var gameEndManager = await context.fishingGamePanelManager.OpenAsync<GameEndManager>();
+ 
+             context.gameEndManager.SetFishInfoText(WinFishResult);
+             context.fish.line.gameObject.SetActive(true);
+             context.mainCamera.transform.forward = (context.fish.showTarget.transform.position + new Vector3(0.15f, 0, 0)) - context.mainCamera.transform.position;
+             context.SetBlankScreen(false);
+             context.fishData.UpdataFishData(WinFishResult.NextFishCsvId);
+             context.lineTensionManager.fishingSkillBarManager.ResetSkillBar();
+         }
+         
+         public override void OnUpdate(FishingManager context)
+         {
+             // 原DoWin方法中的邏輯，目前為空
+         }
+         
+         public override UniTask OnExit(FishingManager context)
+         {
+             return UniTask.CompletedTask;
+         }
+     }
+ } 
```

---

## 11. 完成策略安裝器

---

```diff
              Container.Bind<IFishingStrategy>().To<FightStrategy>().AsSingle();
              Container.Bind<IFishingStrategy>().To<CatchStrategy>().AsSingle();
-             
-             // 將來可以繼續添加其他策略
-             // Container.Bind<IFishingStrategy>().To<FailStrategy>().AsSingle();
-             // Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<FailStrategy>().AsSingle();
+             Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
              
              // 注入所有IFishingStrategy實例列表
```

---

## 重構總結

我們已完成使用策略模式對釣魚系統的重構，主要變化如下：

1. **核心接口與基類**：
   - `IFishingStrategy` 定義了策略接口
   - `FishingStrategyBase` 提供基本實現

2. **狀態策略實現**：
   - 為每個釣魚狀態創建了單獨的策略類
   - 每個策略類負責進入、更新和退出該狀態的邏輯

3. **依賴注入**：
   - 使用Zenject進行策略注冊
   - 在FishingManager中注入所有策略

4. **狀態轉換**：
   - 實現了統一的`TransitionToState`方法
   - 移除了舊的switch語句和SetStateTo*方法

5. **向後兼容**：
   - 保留了部分舊方法以確保向後兼容

此重構的優勢：
- 便於擴展不同的釣魚玩法
- 每個狀態的邏輯清晰獨立
- 更容易測試和維護
- 支持多種不同的釣魚玩法並存

現在你可以輕鬆地為新的釣魚玩法創建專用的策略類，例如`NewFightStrategy`，而無需修改核心管理器類。

---

_**User**_

請修正 撰寫新策略在@NewStrategies 

---

_**Assistant**_



---



---

