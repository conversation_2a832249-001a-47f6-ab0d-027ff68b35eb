using System.Collections.Generic;
using Fishing.NewStrategies;
using Fishing.Strategies;
using UnityEngine;
using Zenject;

namespace Fishing
{
    /// <summary>
    /// 新釣魚策略安裝器
    /// 用於向Zenject容器註冊新版本的釣魚策略
    /// </summary>
    public class NewFishingStrategyInstaller : MonoInstaller<NewFishingStrategyInstaller>
    {
        public override void InstallBindings()
        {
            // 註冊基本策略
            Container.Bind<IFishingStrategy>().To<PrepareStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<BeforeStartStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<StartStrategy>().AsSingle();
            
            // 註冊新策略
            Container.Bind<IFishingStrategy>().To<NewWaitStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<NewFightStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<NewCatchStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<NewFailStrategy>().AsSingle();

            // 註冊勝利策略
            Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
        }
    }
} 