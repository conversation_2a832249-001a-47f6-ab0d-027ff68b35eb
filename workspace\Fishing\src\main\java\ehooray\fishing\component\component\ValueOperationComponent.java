package ehooray.fishing.component.component;

import org.springframework.stereotype.Component;

import ehooray.fishing.component.trigger.BaseTrigger;
import ehooray.fishing.pojo.csv.ComponentCsv;


@Component
public class ValueOperationComponent extends BasePassiveComponent
{

    @Override
    public Object invoke(BaseTrigger.Input input, ComponentCsv.Row componentRow) throws Exception
    {
        int target = (int)input.getTargets()[0];
        float result = 0;
        result += target * componentRow.getParameterByInt(0) * 0.001f;
        result += componentRow.getParameterByInt(1);

        Object[] results = { (int)result };
        return results;
    }
}
