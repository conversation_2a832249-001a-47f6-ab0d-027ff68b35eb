using Cysharp.Threading.Tasks;
using Ehooray;
using Ehooray.UI;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Zenject;
using Fishing.UI;
using Fishing.Network;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/Battle/PausePanelCanvas")]
    public class PausePanelManager : GamePanel
    {
        [SerializeField]
        GameObject pausePanel;
        [SerializeField]
        GameObject settingPanel;
        [SerializeField]
        GameObject pauseButton;
        [SerializeField]
        Slider BgmSlider;
        [SerializeField]
        Slider SfxSlider;
        [SerializeField]
        Toggle ShakeToggle;
        [SerializeField]
        Toggle NotShakeToggle;

        #region Zenject Inject
        FishingManager fishingManager;
        SceneLoadingManager sceneLoadingManager;
        FishingGamePanelManager fishingGamePanelManager;
        FishingContainer fishingContainer;
        BattleService battleService;
        FishData fishData;
        SoundManager soundManager;
        FishingRepositories fishingRepositories;
        GameCache gameCache;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
                                SceneLoadingManager sceneLoadingManager,
                                FishingGamePanelManager fishingGamePanelManager,
                                FishingContainer fishingContainer,
                                BattleService battleService,
                                FishData fishData,
                                FishingRepositories fishingRepositories,
                                GameCache gameCache,
                                SoundManager soundManager)
        {
            this.fishingManager = fishingManager;
            this.sceneLoadingManager = sceneLoadingManager;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.fishingContainer = fishingContainer;
            this.battleService = battleService;
            this.fishData = fishData;
            this.fishingRepositories = fishingRepositories;
            this.gameCache = gameCache;
            this.soundManager = soundManager;
            Init();
        }
        private void Awake()
        {
            BgmSlider.onValueChanged.AddListener(BgmVolume);
            SfxSlider.onValueChanged.AddListener(SfxVolume);
        }

        public void ClosePausePanel()
        {
            Time.timeScale = 1;
            gamePanelManager.Close<PausePanelManager>();
            gamePanelManager.OpenAsync<FishInfoManager>().Forget();
        }
        public void ReturnToSelectPlace()
        {
            fishingGamePanelManager.ConfirmMessage("是否放棄並回到釣點選擇頁面 ?", () =>
            {
                var runner = ReturnToSelectPlaceRunner();
                runner.RunAsync(showMask: true).ContinueWith(() =>
                {

                });
            });

        }
        private AsyncProgressRunner ReturnToSelectPlaceRunner()
        {
            var runner = fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                await UpdateReturnToSelectPlaceAsync();
            });
            return runner;
        }

        private async UniTask UpdateReturnToSelectPlaceAsync()
        {
            await battleService.FailFish();

            await sceneLoadingManager.LoadSceneAsync("Lobby", (container) =>
            {
                container.BindInstance(true).WithId("isOpenMapPanel").WhenInjectedInto<LobbyBootstrap>();
                Time.timeScale = 1;
            });
        }
        public void GiveUpFishing()
        {
            fishingGamePanelManager.ConfirmMessage("是否放棄 ?", () =>
             {
                 var runner = GiveUpRunner();
                 runner.RunAsync(showMask: true).ContinueWith(() =>
                 {

                 });
             });
        }
        private AsyncProgressRunner GiveUpRunner()
        {
            var runner = fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                await UpdateGiveUpAsync();
            });
            return runner;
        }

        private async UniTask UpdateGiveUpAsync()
        {
            var giveUpResult = await battleService.FailFish();
            fishData.UpdataFishData(giveUpResult.NextFishCsvId);
            fishData.RetryCount = 0;
            fishingManager.fish.SetFishData();
            CloseSettingPanel();
            ClosePausePanel();
            fishingManager.ResetGame();
        }
        public void OpenSettingPanel()
        {
            settingPanel.SetActive(true);
            pausePanel.SetActive(false);
        }

        public void CloseSettingPanel()
        {
            settingPanel.SetActive(false);
            pausePanel.SetActive(true);
        }
        public void BgmVolume(float value)
        {
            soundManager.Bgm.volume = value;
            gameCache.playerData.loginCache.bgmVolume = value;
            gameCache.SavePlayerData();
        }

        public void SfxVolume(float value)
        {
            soundManager.Sfx.volume = value;
            gameCache.playerData.loginCache.sfxVolume = value;
            gameCache.SavePlayerData();
        }
        public void SetVibration(bool isShake)
        {
            ShakeCamera.isShakingOn = isShake;
            gameCache.playerData.loginCache.isShake = isShake;
            gameCache.SavePlayerData();
            if (ShakeCamera.isShakingOn)
                ShakeCamera.ShakeOnce(Camera.main).Forget();
        }
        private void Init()
        {
            BgmSlider.value = gameCache.playerData.loginCache.bgmVolume;
            SfxSlider.value = gameCache.playerData.loginCache.sfxVolume;
            if (gameCache.playerData.loginCache.isShake)
                ShakeToggle.isOn = true;
            else
                NotShakeToggle.isOn = true;
            ShakeToggle.onValueChanged.AddListener(SetVibration);
        }
    }
}