package ehooray.fishing.component.protomap.model;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.dto.proto.model.ExchangeProto;
import ehooray.fishing.entity.ExchangeEntity;
import ehooray.fishing.pojo.csv.DisplayCsv;

@Component
public class ExchangeProtoMap
{
    @Autowired
    protected ScheduleCalculator scheduleCalculator;
    
    public ExchangeProto.Exchange.Builder toBuilderByEntity(ExchangeEntity shop, LocalDateTime now)
    {
        var builder = ExchangeProto.Exchange.newBuilder();
        builder.setEntityId(shop.getId());
        builder.setCsvId(shop.getCsvId());
        builder.setBuyCount(shop.getBuyCount());
        if(shop.getStartTime().isPresent())
            builder.setStartTime(shop.getStartTime().get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        Optional<LocalDateTime> nextStartTime = scheduleCalculator.getNextCycleStartDate(DisplayCsv.Type.Exchange, shop.getCsvId(), now);
        if(nextStartTime.isPresent())
            builder.setNextStartTime(nextStartTime.get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        return builder;
    }
    
    public Any toAnyByEntity(ExchangeEntity shop, LocalDateTime now)
    {
        return Any.pack(toBuilderByEntity(shop, now).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<ExchangeEntity> exchanges, LocalDateTime now)
    {
        ArrayList<Any> exchangeProtos = new ArrayList<>(exchanges.size());
        for(ExchangeEntity exchange : exchanges)
            exchangeProtos.add(toAnyByEntity(exchange, now));

        return exchangeProtos;
    }
}
