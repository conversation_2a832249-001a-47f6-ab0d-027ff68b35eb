// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/exchange/ExchangeFishResult.proto

package ehooray.fishing.dto.proto.response.exchange;

public final class ExchangeFishResultProto {
  private ExchangeFishResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ExchangeFishResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.exchange.ExchangeFishResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Exchange exchange = 1;</code>
     * @return Whether the exchange field is set.
     */
    boolean hasExchange();
    /**
     * <code>.model.Exchange exchange = 1;</code>
     * @return The exchange.
     */
    ehooray.fishing.dto.proto.model.ExchangeProto.Exchange getExchange();
    /**
     * <code>.model.Exchange exchange = 1;</code>
     */
    ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder getExchangeOrBuilder();

    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> 
        getConsumeItemsList();
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItem getConsumeItems(int index);
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    int getConsumeItemsCount();
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
        getConsumeItemsOrBuilderList();
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getConsumeItemsOrBuilder(
        int index);

    /**
     * <code>.model.FishItem produceItem = 3;</code>
     * @return Whether the produceItem field is set.
     */
    boolean hasProduceItem();
    /**
     * <code>.model.FishItem produceItem = 3;</code>
     * @return The produceItem.
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItem getProduceItem();
    /**
     * <code>.model.FishItem produceItem = 3;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getProduceItemOrBuilder();
  }
  /**
   * Protobuf type {@code response.exchange.ExchangeFishResult}
   */
  public static final class ExchangeFishResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.exchange.ExchangeFishResult)
      ExchangeFishResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ExchangeFishResult.newBuilder() to construct.
    private ExchangeFishResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ExchangeFishResult() {
      consumeItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ExchangeFishResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.internal_static_response_exchange_ExchangeFishResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.internal_static_response_exchange_ExchangeFishResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.class, ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.Builder.class);
    }

    public static final int EXCHANGE_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.ExchangeProto.Exchange exchange_;
    /**
     * <code>.model.Exchange exchange = 1;</code>
     * @return Whether the exchange field is set.
     */
    @java.lang.Override
    public boolean hasExchange() {
      return exchange_ != null;
    }
    /**
     * <code>.model.Exchange exchange = 1;</code>
     * @return The exchange.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ExchangeProto.Exchange getExchange() {
      return exchange_ == null ? ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.getDefaultInstance() : exchange_;
    }
    /**
     * <code>.model.Exchange exchange = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder getExchangeOrBuilder() {
      return getExchange();
    }

    public static final int CONSUMEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> consumeItems_;
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> getConsumeItemsList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
        getConsumeItemsOrBuilderList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public int getConsumeItemsCount() {
      return consumeItems_.size();
    }
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getConsumeItems(int index) {
      return consumeItems_.get(index);
    }
    /**
     * <code>repeated .model.FishItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getConsumeItemsOrBuilder(
        int index) {
      return consumeItems_.get(index);
    }

    public static final int PRODUCEITEM_FIELD_NUMBER = 3;
    private ehooray.fishing.dto.proto.model.FishItemProto.FishItem produceItem_;
    /**
     * <code>.model.FishItem produceItem = 3;</code>
     * @return Whether the produceItem field is set.
     */
    @java.lang.Override
    public boolean hasProduceItem() {
      return produceItem_ != null;
    }
    /**
     * <code>.model.FishItem produceItem = 3;</code>
     * @return The produceItem.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getProduceItem() {
      return produceItem_ == null ? ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : produceItem_;
    }
    /**
     * <code>.model.FishItem produceItem = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getProduceItemOrBuilder() {
      return getProduceItem();
    }

    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.exchange.ExchangeFishResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.exchange.ExchangeFishResult)
        ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.internal_static_response_exchange_ExchangeFishResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.internal_static_response_exchange_ExchangeFishResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.class, ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumeItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (exchangeBuilder_ == null) {
          exchange_ = null;
        } else {
          exchange_ = null;
          exchangeBuilder_ = null;
        }
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeItemsBuilder_.clear();
        }
        if (produceItemBuilder_ == null) {
          produceItem_ = null;
        } else {
          produceItem_ = null;
          produceItemBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.internal_static_response_exchange_ExchangeFishResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult build() {
        ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult buildPartial() {
        ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult result = new ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult(this);
        int from_bitField0_ = bitField0_;
        if (exchangeBuilder_ == null) {
          result.exchange_ = exchange_;
        } else {
          result.exchange_ = exchangeBuilder_.build();
        }
        if (consumeItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumeItems_ = java.util.Collections.unmodifiableList(consumeItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumeItems_ = consumeItems_;
        } else {
          result.consumeItems_ = consumeItemsBuilder_.build();
        }
        if (produceItemBuilder_ == null) {
          result.produceItem_ = produceItem_;
        } else {
          result.produceItem_ = produceItemBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.ExchangeProto.Exchange exchange_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ExchangeProto.Exchange, ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.Builder, ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder> exchangeBuilder_;
      /**
       * <code>.model.Exchange exchange = 1;</code>
       * @return Whether the exchange field is set.
       */
      public boolean hasExchange() {
        return exchangeBuilder_ != null || exchange_ != null;
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       * @return The exchange.
       */
      public ehooray.fishing.dto.proto.model.ExchangeProto.Exchange getExchange() {
        if (exchangeBuilder_ == null) {
          return exchange_ == null ? ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.getDefaultInstance() : exchange_;
        } else {
          return exchangeBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public Builder setExchange(ehooray.fishing.dto.proto.model.ExchangeProto.Exchange value) {
        if (exchangeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          exchange_ = value;
          onChanged();
        } else {
          exchangeBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public Builder setExchange(
          ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.Builder builderForValue) {
        if (exchangeBuilder_ == null) {
          exchange_ = builderForValue.build();
          onChanged();
        } else {
          exchangeBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public Builder mergeExchange(ehooray.fishing.dto.proto.model.ExchangeProto.Exchange value) {
        if (exchangeBuilder_ == null) {
          if (exchange_ != null) {
            exchange_ =
              ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.newBuilder(exchange_).mergeFrom(value).buildPartial();
          } else {
            exchange_ = value;
          }
          onChanged();
        } else {
          exchangeBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public Builder clearExchange() {
        if (exchangeBuilder_ == null) {
          exchange_ = null;
          onChanged();
        } else {
          exchange_ = null;
          exchangeBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.Builder getExchangeBuilder() {
        
        onChanged();
        return getExchangeFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder getExchangeOrBuilder() {
        if (exchangeBuilder_ != null) {
          return exchangeBuilder_.getMessageOrBuilder();
        } else {
          return exchange_ == null ?
              ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.getDefaultInstance() : exchange_;
        }
      }
      /**
       * <code>.model.Exchange exchange = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ExchangeProto.Exchange, ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.Builder, ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder> 
          getExchangeFieldBuilder() {
        if (exchangeBuilder_ == null) {
          exchangeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.ExchangeProto.Exchange, ehooray.fishing.dto.proto.model.ExchangeProto.Exchange.Builder, ehooray.fishing.dto.proto.model.ExchangeProto.ExchangeOrBuilder>(
                  getExchange(),
                  getParentForChildren(),
                  isClean());
          exchange_ = null;
        }
        return exchangeBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> consumeItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumeItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumeItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.FishItemProto.FishItem>(consumeItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> consumeItemsBuilder_;

      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem> getConsumeItemsList() {
        if (consumeItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeItems_);
        } else {
          return consumeItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public int getConsumeItemsCount() {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.size();
        } else {
          return consumeItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);
        } else {
          return consumeItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder addAllConsumeItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItem> values) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeItems_);
          onChanged();
        } else {
          consumeItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder clearConsumeItems() {
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public Builder removeConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.remove(index);
          onChanged();
        } else {
          consumeItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder getConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getConsumeItemsOrBuilder(
          int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);  } else {
          return consumeItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
           getConsumeItemsOrBuilderList() {
        if (consumeItemsBuilder_ != null) {
          return consumeItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeItems_);
        }
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder addConsumeItemsBuilder() {
        return getConsumeItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder addConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.FishItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder> 
           getConsumeItemsBuilderList() {
        return getConsumeItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
          getConsumeItemsFieldBuilder() {
        if (consumeItemsBuilder_ == null) {
          consumeItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder>(
                  consumeItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumeItems_ = null;
        }
        return consumeItemsBuilder_;
      }

      private ehooray.fishing.dto.proto.model.FishItemProto.FishItem produceItem_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> produceItemBuilder_;
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       * @return Whether the produceItem field is set.
       */
      public boolean hasProduceItem() {
        return produceItemBuilder_ != null || produceItem_ != null;
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       * @return The produceItem.
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getProduceItem() {
        if (produceItemBuilder_ == null) {
          return produceItem_ == null ? ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : produceItem_;
        } else {
          return produceItemBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public Builder setProduceItem(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (produceItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          produceItem_ = value;
          onChanged();
        } else {
          produceItemBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public Builder setProduceItem(
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (produceItemBuilder_ == null) {
          produceItem_ = builderForValue.build();
          onChanged();
        } else {
          produceItemBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public Builder mergeProduceItem(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (produceItemBuilder_ == null) {
          if (produceItem_ != null) {
            produceItem_ =
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem.newBuilder(produceItem_).mergeFrom(value).buildPartial();
          } else {
            produceItem_ = value;
          }
          onChanged();
        } else {
          produceItemBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public Builder clearProduceItem() {
        if (produceItemBuilder_ == null) {
          produceItem_ = null;
          onChanged();
        } else {
          produceItem_ = null;
          produceItemBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder getProduceItemBuilder() {
        
        onChanged();
        return getProduceItemFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getProduceItemOrBuilder() {
        if (produceItemBuilder_ != null) {
          return produceItemBuilder_.getMessageOrBuilder();
        } else {
          return produceItem_ == null ?
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : produceItem_;
        }
      }
      /**
       * <code>.model.FishItem produceItem = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
          getProduceItemFieldBuilder() {
        if (produceItemBuilder_ == null) {
          produceItemBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder>(
                  getProduceItem(),
                  getParentForChildren(),
                  isClean());
          produceItem_ = null;
        }
        return produceItemBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.exchange.ExchangeFishResult)
    }

    // @@protoc_insertion_point(class_scope:response.exchange.ExchangeFishResult)
    private static final ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult();
    }

    public static ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ExchangeFishResult>
        PARSER = new com.google.protobuf.AbstractParser<ExchangeFishResult>() {
      @java.lang.Override
      public ExchangeFishResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ExchangeFishResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ExchangeFishResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto.ExchangeFishResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_exchange_ExchangeFishResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_exchange_ExchangeFishResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*response/exchange/ExchangeFishResult.p" +
      "roto\022\021response.exchange\032\024model/FishItem." +
      "proto\032\024model/Exchange.proto\"\204\001\n\022Exchange" +
      "FishResult\022!\n\010exchange\030\001 \001(\0132\017.model.Exc" +
      "hange\022%\n\014consumeItems\030\002 \003(\0132\017.model.Fish" +
      "Item\022$\n\013produceItem\030\003 \001(\0132\017.model.FishIt" +
      "emB`\n+ehooray.fishing.dto.proto.response" +
      ".exchangeB\027ExchangeFishResultProtoH\002\252\002\025F" +
      "ishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.ExchangeProto.getDescriptor(),
        });
    internal_static_response_exchange_ExchangeFishResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_exchange_ExchangeFishResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_exchange_ExchangeFishResult_descriptor,
        new java.lang.String[] { "Exchange", "ConsumeItems", "ProduceItem", });
    ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor();
    ehooray.fishing.dto.proto.model.ExchangeProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
