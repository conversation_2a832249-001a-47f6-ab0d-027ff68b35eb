<!-- Generated by SpecStory -->

# 指針區域圖檔變更設定 (2025-05-13 16:26:48)

_**User**_

指針指到判定區域時，targetAreaIndicator要改圖檔，沒有指到時，則顯示原本的圖

---

_**Assistant**_



---

好的，我将为您修改 `LureActionSystem.cs` 文件，以实现当指针指向QTE判定区域时，`targetAreaIndicator` 更换为高亮图片，未指向时显示原始图片的功能。

具体步骤如下：
1.  在 `LureActionSystem` 类中添加两个新的 `public Sprite` 成员变量：`targetAreaNormalSprite` （原始图片）和 `targetAreaHighlightSprite` （高亮图片）。
2.  在 `OnEnable` 方法中增加对这两个新 Sprite 是否已在 Inspector 中分配的检查。
3.  修改 `OnJoystickMoved` 方法，使其在摇杆移动时，根据指针是否在判定区域内，动态切换 `targetAreaIndicator` 的 `sprite`。如果摇杆回到死区，则恢复为 `targetAreaNormalSprite`。
4.  修改 `StartNextAttempt` 方法，在每次新的QTE尝试开始时，将 `targetAreaIndicator` 的 `sprite` 初始化为 `targetAreaNormalSprite`。

我将对 `Assets/Fishing/Scripts/Fish/LureActionSystem.cs` 文件进行修改。

---

```diff
      public Image qteBackground; // QTE背景
      public Image targetAreaIndicator; // QTE背景上指示判定区域的UI
+     public Sprite targetAreaNormalSprite; // 指针未进入判定区域时显示的图片
+     public Sprite targetAreaHighlightSprite; // 指针进入判定区域时显示的图片
      public RectTransform scalingRingRectTransform; // 外圈UI的RectTransform
      public Image resultImage; // 显示 "Perfect", "Good", "Miss" 的图片
```

```diff
              return;
          }
+         if (targetAreaNormalSprite == null || targetAreaHighlightSprite == null)
+         {
+             Debug.LogError("Target area sprites (normal or highlight) not assigned!");
+             enabled = false;
+             return;
+         }
  
          joystick.JoystickExecuted = OnJoystickMoved;
```

```diff
      private void OnJoystickMoved(FingersJoystickScript script, Vector2 amount)
      {
-         if (pointerRectTransform != null && amount.magnitude > 0.1f) // 设置一个阈值，避免摇杆轻微晃动就触发旋转
+         if (pointerRectTransform == null) return;
+ 
+         Image indicatorImage = targetAreaIndicator.GetComponent<Image>();
+         if (indicatorImage == null)
+         {
+             Debug.LogError("Target Area Indicator 没有 Image 组件!");
+             return;
+         }
+ 
+         if (qteActive && targetAreaIndicator.gameObject.activeSelf)
+         {
+             if (amount.magnitude > 0.1f) // 设置一个阈值，避免摇杆轻微晃动就触发旋转
              {
                  float angle = Mathf.Atan2(amount.y, amount.x) * Mathf.Rad2Deg;
                  // 如果指针图片的基准是朝右，则减90度；如果是朝上，则可能不需要调整。根据实际图片朝向调整。
                  pointerRectTransform.localEulerAngles = new Vector3(0, 0, angle);
+ 
+                 // 检查指针是否在判定区域内
+                 float pointerAngleCurrent = pointerRectTransform.localEulerAngles.z;
+                 float minAngle = currentTargetAngle - (targetAreaAngleWidth / 2f);
+                 float maxAngle = currentTargetAngle + (targetAreaAngleWidth / 2f);
+ 
+                 bool isInTargetArea = IsAngleBetween(pointerAngleCurrent, minAngle, maxAngle);
+                 indicatorImage.sprite = isInTargetArea ? targetAreaHighlightSprite : targetAreaNormalSprite;
+             }
+             else // 摇杆在死区或没有输入
+             {
+                 // 如果当前是高亮状态，则恢复为普通状态
+                 if (indicatorImage.sprite == targetAreaHighlightSprite)
+                 {
+                     indicatorImage.sprite = targetAreaNormalSprite;
+                 }
+             }
          }
      }
```

```diff
          if (indicatorImage != null)
          {
+             indicatorImage.sprite = targetAreaNormalSprite; // 初始化为普通图片
              indicatorImage.fillAmount = targetAreaAngleWidth / 360f;
  
```

---

我已经按照您的要求修改了 `Assets/Fishing/Scripts/Fish/LureActionSystem.cs` 文件。

**主要更改包括：**

1.  在 `LureActionSystem` 类中添加了 `targetAreaNormalSprite` 和 `targetAreaHighlightSprite` 两个 `Sprite` 类型的公共变量。请记得在 Unity 编辑器中为这两个变量分配相应的图片资源。
2.  在 `OnEnable()` 方法中添加了对这两个新 Sprite 是否已分配的空检查。
3.  修改了 `OnJoystickMoved()` 方法：
    *   现在该方法会获取 `targetAreaIndicator` 的 `Image` 组件。
    *   当QTE激活且判定区域可见时：
        *   如果摇杆有明显输入，则会根据指针当前角度是否在判定区域内（通过 `IsAngleBetween()` 判断），来设置 `indicatorImage.sprite` 为 `targetAreaHighlightSprite` 或 `targetAreaNormalSprite`。
        *   如果摇杆输入幅度很小（在死区内），并且当前 `indicatorImage.sprite` 是高亮状态，则会将其切换回 `targetAreaNormalSprite`。
4.  修改了 `StartNextAttempt()` 方法：
    *   在显示判定区域 `targetAreaIndicator` 之前，会将其 `sprite` 设置为 `targetAreaNormalSprite`，确保每次尝试开始时都显示正常的指示器图片。

请检查代码更改，并在Unity编辑器中为新的 `targetAreaNormalSprite` 和 `targetAreaHighlightSprite` 字段分配图片。

---

