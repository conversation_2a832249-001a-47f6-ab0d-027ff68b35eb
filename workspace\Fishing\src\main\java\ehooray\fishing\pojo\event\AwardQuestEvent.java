package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.QuestEntity;

@SuppressWarnings("serial")
public class AwardQuestEvent extends ApplicationEvent
{
	private final PlayerEntity player;
	private final QuestEntity quest;
	
	public AwardQuestEvent(Object source, PlayerEntity player, QuestEntity quest)
	{
		super(source);
        this.player = player;
        this.quest = quest;
	}
	
	public PlayerEntity getPlayer()
    {
        return player;
    }
	
	public QuestEntity getQuest()
    {
        return quest;
    }
}
