plugins {
	id 'org.springframework.boot' version '2.7.2'
	id 'io.spring.dependency-management' version '1.0.12.RELEASE'
	id 'java'
	id 'war'
}

group = 'ehooray.fishing'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '11'

repositories {
	mavenCentral()
}

configurations.all {
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
}

dependencies {
	// spring framework
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	implementation 'org.springframework.boot:spring-boot-starter-jta-atomikos'
	// server
	providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'
	// db
	runtimeOnly 'mysql:mysql-connector-java'
	
	// proto
	implementation 'com.google.protobuf:protobuf-java:3.17.3'
	implementation 'com.google.protobuf:protobuf-java-util:3.17.3'
	
	// firebase
	implementation 'com.google.firebase:firebase-admin:8.1.0'
	
	// log
	implementation 'org.springframework.boot:spring-boot-starter-log4j2'
	implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml'
	implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
	
	// hibernate l2 cache
	implementation 'org.hibernate:hibernate-jcache'
	implementation 'org.hibernate:hibernate-ehcache'
	implementation 'org.ehcache:ehcache'
	
	// csv
	implementation 'com.github.ozlerhakan:poiji:3.1.1'
	
	// schedule
	implementation 'com.cronutils:cron-utils:9.1.3'
	
	// other utility
	implementation 'org.reflections:reflections:0.9.11'
	implementation 'org.javatuples:javatuples:1.2'
	
	// lua
	// https://mvnrepository.com/artifact/org.luaj/luaj-jse
	implementation 'org.luaj:luaj-jse:3.0.1'
	
	// unit testing
	testImplementation 'org.assertj:assertj-core:3.20.2'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
}

tasks.named('test') {
	useJUnitPlatform()
}
