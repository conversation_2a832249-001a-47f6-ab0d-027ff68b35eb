using Cysharp.Threading.Tasks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using Ehooray.UI;
using Fishing.UI;
using Naninovel;
using Zenject;

namespace Fishing
{
    public class FishingGameBootstrap : IInitializable
    {
        private const int NewPlayerLv = 1;
        private const int NewPlayerExp = 0;
        Camera mainCamera;
        FishingManager fishingManager;
        GamePanelManager gamePanelManager;
        SceneLoadingManager sceneLoadingManager;
        FishingRepositories fishingRepositories;
        TableContainer tableContainer;
        SoundManager soundManager;
        GameCache gameCache;
        GameTeaching gameTeaching;

        public FishingGameBootstrap(Camera mainCamera,
            FishingManager fishingManager,
            GamePanelManager gamePanelManager,
            SceneLoadingManager sceneLoadingManager,
            FishingRepositories fishingRepositories,
            TableContainer tableContainer,
            SoundManager soundManager,
            GameCache gameCache,
            GameTeaching gameTeaching)
        {
            this.mainCamera = mainCamera;
            this.fishingManager = fishingManager;
            this.gamePanelManager = gamePanelManager;
            this.sceneLoadingManager = sceneLoadingManager;
            this.fishingRepositories = fishingRepositories;
            this.tableContainer = tableContainer;
            this.soundManager = soundManager;
            this.gameCache = gameCache;
            this.gameTeaching = gameTeaching;
        }

        public void Initialize()
        {
            Cysharp.Threading.Tasks.UniTask.Void(async () =>
            {
                InitNaninovel();
                await gamePanelManager.OpenAsync<FishInfoManager>();
                gamePanelManager.Close<FishInfoManager>();

                await gamePanelManager.OpenAsync<CordReelManager>();

                await gamePanelManager.OpenAsync<ThrowBaitManager>();
                gamePanelManager.Close<ThrowBaitManager>();

                await gamePanelManager.OpenAsync<LineTensionManager>();
                gamePanelManager.Close<LineTensionManager>();

                await gamePanelManager.OpenAsync<GameEndManager>();
                gamePanelManager.Close<GameEndManager>();

                await gamePanelManager.OpenAsync<ContinueManager>();
                gamePanelManager.Close<ContinueManager>();

                await gamePanelManager.OpenAsync<PrepareManager>();

                await gamePanelManager.OpenAsync<TopInfoManager>();

                await gamePanelManager.OpenAsync<CloseupTextManager>();
                gamePanelManager.Close<CloseupTextManager>();

                await gamePanelManager.OpenAsync<PausePanelManager>();
                gamePanelManager.Close<PausePanelManager>();

                await gamePanelManager.OpenAsync<LureActionSystem>();
                gamePanelManager.Close<LureActionSystem>();

                await gamePanelManager.OpenAsync<LureAnimationController>();
                gamePanelManager.Close<LureAnimationController>();

                fishingManager.LoadPanel();

                Debug.Log("Battle loading");
                sceneLoadingManager.HideSceneLoadingUI();
                LoadPlayerSetting();
                if (IsNewPlayer())
                    gameTeaching.PlayGameTeachingAsync().Forget();
            });
            DynamicGI.UpdateEnvironment();
        }

        private void InitNaninovel()
        {
            var naniCameraManager = Naninovel.Engine.GetService<Naninovel.ICameraManager>();
            var cameraData = mainCamera.GetUniversalAdditionalCameraData();
            cameraData.cameraStack.Add(naniCameraManager.Camera);
        }

        private bool IsNewPlayer() 
        {
            var player = fishingRepositories.playerRepository.player;
            Debug.Log($"player level : {player.level}");
            Debug.Log($"player exp : {player.exp}");
            if(player.level == NewPlayerLv && player.exp == NewPlayerExp)
                return true;
            else
                return false;
        }

        private void LoadPlayerSetting()
        {
            soundManager.Bgm.volume = gameCache.playerData.loginCache.bgmVolume;
            soundManager.Bgm.Play();
            soundManager.Sfx.volume = gameCache.playerData.loginCache.sfxVolume;
            ShakeCamera.isShakingOn = gameCache.playerData.loginCache.isShake;
            Application.targetFrameRate = gameCache.playerData.loginCache.fps;
        }
    }
}