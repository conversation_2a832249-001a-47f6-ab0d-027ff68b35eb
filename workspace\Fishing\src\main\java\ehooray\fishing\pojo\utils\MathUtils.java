package ehooray.fishing.pojo.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public final class MathUtils
{
    public static int clamp(int value, int min, int max)
    {
        return Math.min(Math.max(value, min), max);
    }

    public static float clamp(float value, float min, float max)
    {
        return Math.min(Math.max(value, min), max);
    }
    
    // The min parameter (the origin) is inclusive, whereas the upper bound max is exclusive.
    public static int getRandomNumberUsingNextInt(int min, int max)
    {
        Random random = new Random();
        return random.nextInt(max - min) + min;
    }
    
    // the specified origin min is inclusive, and max is exclusive.
    public static int getRandomNumberUsingInts(int min, int max)
    {
        Random random = new Random();
        return random.ints(min, max)
          .findFirst()
          .getAsInt();
    }
    
    public static int randomRange(int min, int max)
    {
        return getRandomNumberUsingNextInt(min, max);
    }
    
    /* n numbers with sum k
     * In [1]: numbers_with_sum(3, 100)                                               
     * Out[1]: [75, 9, 16]
     */
    public static List<Integer> numbersWithSum(int n, int k)
    {
        var result = new ArrayList<Integer>();
        if (n == 1)
        {
            result.add(k);
            return result;
        }
        
        int num = randomRange(0, k+1);
        result.add(num);
        result.addAll(numbersWithSum(n-1,k-num));
        return result;
    }
}
