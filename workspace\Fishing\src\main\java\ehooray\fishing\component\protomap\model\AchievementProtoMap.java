package ehooray.fishing.component.protomap.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.AchievementProto;
import ehooray.fishing.entity.AchievementEntity;

@Component
public class AchievementProtoMap
{
    public AchievementProto.Achievement.Builder toBuilderByEntity(AchievementEntity achievement)
    {
        var builder = AchievementProto.Achievement.newBuilder();
        
        builder.setEntityId(achievement.getId());
        builder.setQuestType(achievement.getQuestType());
        builder.addParameters(achievement.getParameter_1());
        builder.addParameters(achievement.getParameter_2());
        builder.addParameters(achievement.getParameter_3());
        builder.addParameters(achievement.getParameter_4());
        builder.addParameters(achievement.getParameter_5());
        builder.setTargetCount(achievement.getTargetCount());
        
        return builder;
    }
    
    public Any toAnyByEntity(AchievementEntity achievement)
    {
        return Any.pack(toBuilderByEntity(achievement).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<AchievementEntity> achievements)
    {
        ArrayList<Any> achievementsProtos = new ArrayList<Any>(achievements.size());
        for(var achievement : achievements)
            achievementsProtos.add(toAnyByEntity(achievement));
        
        return achievementsProtos;
    }
}
