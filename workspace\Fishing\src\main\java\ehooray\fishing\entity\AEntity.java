package ehooray.fishing.entity;

import java.lang.reflect.Field;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;

import ehooray.fishing.component.lua.IToLua;
import ehooray.fishing.pojo.ExceptionByCsvId;


public class AEntity implements IToLua
{

    @Override
    public LuaTable toLua() throws Exception
    {
        LuaTable table = LuaValue.tableOf();
        Field[] fields = this.getClass().getDeclaredFields();
        for(Field field : fields)
        {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = field.get(this);
            if(fieldValue == null) continue;

            table.set(fieldName, objectToLua(fieldValue));
        }

        return table;
    }

    protected LuaValue objectToLua(Object value) throws ExceptionByCsvId
    {
        if(value.getClass() == Integer.class) return LuaValue.valueOf((int)value);
        if(value.getClass() == Long.class) return LuaValue.valueOf((long)value);
        if(value.getClass() == Boolean.class) return LuaValue.valueOf((boolean)value);

        return LuaValue.NIL;
    }

}
