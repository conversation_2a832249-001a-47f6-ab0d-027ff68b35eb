package ehooray.fishing.component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.BinaryWebSocketHandler;

import com.google.protobuf.Any;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.config.SessionName;
import ehooray.fishing.dto.proto.BaseProto;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.response.ResponseProto;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.LanguageCsv;
import ehooray.fishing.service.auth.PlayerAuthService;
import ehooray.fishing.service.player.PlayerService;

@Component
public class WebSocketHandler extends BinaryWebSocketHandler
{
	@Autowired
	protected BeanFactory beanFactory;

	@Autowired
	protected ProtoHandler protoHandler;

	@Autowired
	protected PlayerService playerService;

	@Autowired
    protected PlayerAuthService playerAuthService;

	@Autowired
	protected CsvContainer csvContainer;

	@Autowired
	protected WebSocketHandler self;

	// Note: although hashmap is not thread safe, but here we don't care player connect race condition
	protected Map<Long, WebSocketSession> sessionContainer = new HashMap<>();

	@Override
	@Transactional(rollbackFor = Throwable.class, timeoutString = "${game.transactional.timeout}")
	public void afterConnectionEstablished(WebSocketSession session)
	{
	    long playerId = (long)session.getAttributes().get(SessionName.PlayerId);
        PlayerEntity player = playerService.findByEntityId(playerId).get();
        playerAuthService.setSessionId(player.getPlayerAuth(), session.getId());
        sessionContainer.put(playerId, session);
	}

	@Override
	protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message)
	{
	    self.handleBinaryMessage_public(session, message);
	}

	protected <T extends com.google.protobuf.Message> BinaryMessage toBinaryMessage(BaseProto.Base.Operator operator, T message)
	{
	    BaseProto.Base.Builder baseProto = BaseProto.Base.newBuilder();
        baseProto.setOperator(operator);
        baseProto.setData(Any.pack(message));
        return new BinaryMessage(baseProto.build().toByteArray());
	}

	// Note: must public function, or aop logger won't work
	public void handleBinaryMessage_public(WebSocketSession session, BinaryMessage message)
	{
	    try
        {
            self.validateDuplicateLogin(session);
            BaseProto.Base baseProto = BaseProto.Base.parseFrom(message.getPayload());
            RequestProto.Request request = baseProto.getData().unpack(RequestProto.Request.class);

            WebSocketHandleMediator.Builder handleMediatorBuilder = new WebSocketHandleMediator.Builder();
            handleMediatorBuilder.setRequest(request);
            handleMediatorBuilder.setSession(session);
            handleMediatorBuilder.setPlayerId((long)session.getAttributes().get(SessionName.PlayerId));
            handleMediatorBuilder.setRequestStartTime(LocalDateTime.now());

            ResponseProto.Response response = protoHandler.handleRequest(request, handleMediatorBuilder);
            session.sendMessage(toBinaryMessage(BaseProto.Base.Operator.Response, response));
        }
        catch(DuplicateLoginException e)
        {
            handleExceptionResponse(session, protoHandler.handleRequestException(e, Optional.empty()), true);
        }
        catch(Throwable e)
        {
            handleExceptionResponse(session, protoHandler.handleUnknownException(e), false);
        }
	}

	// Note: we suppose that game server is distributed server architecture.
	//       if player login to different device sequentially, we won't get session instance in other server.
	//       but we make sure token will be different after each login, so the previous token will be invalid.
	// Note: must public function, or @Transactional won't work
	@Transactional(readOnly = true, timeoutString = "${game.transactional.timeout}")
	public void validateDuplicateLogin(WebSocketSession session) throws Exception
	{
        long playerId = (long)session.getAttributes().get(SessionName.PlayerId);
        String authToken = (String)session.getAttributes().get(SessionName.AuthToken);
        PlayerEntity player = playerService.findByEntityId(playerId).get();
        PlayerAuthEntity playerAuth = player.getPlayerAuth();
        if(!authToken.equals(playerAuth.getToken()) || !session.getId().equals(playerAuth.getSessionId()))
            throw new DuplicateLoginException(csvContainer.getCsv(LanguageCsv.class).getDuplicateLoginRow().getId());
	}

	protected void handleExceptionResponse(WebSocketSession session, ResponseProto.Response response, boolean closeSession)
	{
	    try
	    {
	        session.sendMessage(toBinaryMessage(BaseProto.Base.Operator.Response, response));
	        if(closeSession)
	            closeSession(session, this.getClass().getCanonicalName());
	    }
	    catch(Exception e)
	    {
	        // here should not enter, but still need try catch. just put log.
	        LogManager.getLogger(this.getClass()).error("websocket send unknown exception response proto still have exception", e);
	    }
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus status)
	{
	    long playerId = (long)session.getAttributes().get(SessionName.PlayerId);
        sessionContainer.remove(playerId);
	}

	// Note: must public function, or aop logger won't work
	public void broadcastRequest(List<WebSocketSession> sessions, RequestProto.Request request, String loggerName)
	{
	    for(WebSocketSession session : sessions)
	    {
	        try
	        {
	            session.sendMessage(toBinaryMessage(BaseProto.Base.Operator.Request, request));
	        }
	        catch(Throwable e)
	        {
	            long playerId = (long)session.getAttributes().get(SessionName.PlayerId);
	            LogManager.getLogger(loggerName).error("broadcastRequest exception, playerId = " + playerId + ", session id = " + session.getId(), e);
	        }
	    }
	}

	public void closeAllSessions(RequestProto.Request request, String loggerName)
	{
	    List<WebSocketSession> sessions = new ArrayList<>(sessionContainer.values());
	    self.broadcastRequest(sessions, request, loggerName);

	    for(WebSocketSession session : sessions)
	        closeSession(session, loggerName);
	    sessionContainer.clear();
	    sessions.clear();
	}

	protected void closeSession(WebSocketSession session, String loggerName)
	{
	    try
        {
            session.close();
        }
        catch (IOException e)
        {
            long playerId = (long)session.getAttributes().get(SessionName.PlayerId);
            LogManager.getLogger(loggerName).error("close session exception, playerId = " + playerId + ", session id = " + session.getId(), e);
        }
	}


    @SuppressWarnings("serial")
    protected class DuplicateLoginException extends ExceptionByCsvId
    {
        public DuplicateLoginException(int id)
        {
            super(id);
        }

        public DuplicateLoginException(String message)
        {
            super(message);
        }
    }
}
