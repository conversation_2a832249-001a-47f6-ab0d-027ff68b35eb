package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("ShopTable.xlsx")
public class ShopCsv extends ACsv<ShopCsv.Row>
{
    public enum RefreshTypeEnum
    {
        Random(1),
        Serial(2),
        ;

        protected int id;
        private RefreshTypeEnum(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<RefreshTypeEnum> valueOf(int id)
        {
            return Arrays.stream(RefreshTypeEnum.values()).filter(x-> x.getId() == id).findFirst();
        }

    }


    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Unlock")
        protected int unlock;

        @ExcelCellName("CommodityGroup")
        protected int commodityGroup;

        @ExcelCellName("RefreshType")
        protected int refreshType;


        @Override
        public int getId()
        {
            return id;
        }

        public int getUnlock()
        {
            return unlock;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }

        public int getCommodityGroup()
        {
            return commodityGroup;
        }

        public int getRefreshType()
        {
            return refreshType;
        }

        public Optional<RefreshTypeEnum> getRefreshTypeEnum()
        {
            return RefreshTypeEnum.valueOf(getRefreshType());
        }
    }

}
