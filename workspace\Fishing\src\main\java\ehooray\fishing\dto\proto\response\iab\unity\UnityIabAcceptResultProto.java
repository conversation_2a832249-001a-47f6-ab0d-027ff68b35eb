// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/iab/unity/UnityIabAcceptResult.proto

package ehooray.fishing.dto.proto.response.iab.unity;

public final class UnityIabAcceptResultProto {
  private UnityIabAcceptResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnityIabAcceptResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.iab.unity.UnityIabAcceptResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return Whether the transaction field is set.
     */
    boolean hasTransaction();
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return The transaction.
     */
    ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction();
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     */
    ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder();

    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    java.util.List<com.google.protobuf.Any> 
        getModelsList();
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    com.google.protobuf.Any getModels(int index);
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    int getModelsCount();
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getModelsOrBuilderList();
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
        int index);
  }
  /**
   * <pre>
   * may have multiple result in response
   * </pre>
   *
   * Protobuf type {@code response.iab.unity.UnityIabAcceptResult}
   */
  public static final class UnityIabAcceptResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.iab.unity.UnityIabAcceptResult)
      UnityIabAcceptResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UnityIabAcceptResult.newBuilder() to construct.
    private UnityIabAcceptResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UnityIabAcceptResult() {
      models_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UnityIabAcceptResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.internal_static_response_iab_unity_UnityIabAcceptResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.internal_static_response_iab_unity_UnityIabAcceptResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.class, ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.Builder.class);
    }

    public static final int TRANSACTION_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction transaction_;
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return Whether the transaction field is set.
     */
    @java.lang.Override
    public boolean hasTransaction() {
      return transaction_ != null;
    }
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return The transaction.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction() {
      return transaction_ == null ? ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
    }
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder() {
      return getTransaction();
    }

    public static final int MODELS_FIELD_NUMBER = 2;
    private java.util.List<com.google.protobuf.Any> models_;
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.Any> getModelsList() {
      return models_;
    }
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getModelsOrBuilderList() {
      return models_;
    }
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    @java.lang.Override
    public int getModelsCount() {
      return models_.size();
    }
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    @java.lang.Override
    public com.google.protobuf.Any getModels(int index) {
      return models_.get(index);
    }
    /**
     * <code>repeated .google.protobuf.Any models = 2;</code>
     */
    @java.lang.Override
    public com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
        int index) {
      return models_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * may have multiple result in response
     * </pre>
     *
     * Protobuf type {@code response.iab.unity.UnityIabAcceptResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.iab.unity.UnityIabAcceptResult)
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.internal_static_response_iab_unity_UnityIabAcceptResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.internal_static_response_iab_unity_UnityIabAcceptResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.class, ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getModelsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (transactionBuilder_ == null) {
          transaction_ = null;
        } else {
          transaction_ = null;
          transactionBuilder_ = null;
        }
        if (modelsBuilder_ == null) {
          models_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          modelsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.internal_static_response_iab_unity_UnityIabAcceptResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult build() {
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult buildPartial() {
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult result = new ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult(this);
        int from_bitField0_ = bitField0_;
        if (transactionBuilder_ == null) {
          result.transaction_ = transaction_;
        } else {
          result.transaction_ = transactionBuilder_.build();
        }
        if (modelsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            models_ = java.util.Collections.unmodifiableList(models_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.models_ = models_;
        } else {
          result.models_ = modelsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction transaction_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder> transactionBuilder_;
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       * @return Whether the transaction field is set.
       */
      public boolean hasTransaction() {
        return transactionBuilder_ != null || transaction_ != null;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       * @return The transaction.
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction() {
        if (transactionBuilder_ == null) {
          return transaction_ == null ? ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
        } else {
          return transactionBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder setTransaction(ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction value) {
        if (transactionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          transaction_ = value;
          onChanged();
        } else {
          transactionBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder setTransaction(
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder builderForValue) {
        if (transactionBuilder_ == null) {
          transaction_ = builderForValue.build();
          onChanged();
        } else {
          transactionBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder mergeTransaction(ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction value) {
        if (transactionBuilder_ == null) {
          if (transaction_ != null) {
            transaction_ =
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.newBuilder(transaction_).mergeFrom(value).buildPartial();
          } else {
            transaction_ = value;
          }
          onChanged();
        } else {
          transactionBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder clearTransaction() {
        if (transactionBuilder_ == null) {
          transaction_ = null;
          onChanged();
        } else {
          transaction_ = null;
          transactionBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder getTransactionBuilder() {
        
        onChanged();
        return getTransactionFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder() {
        if (transactionBuilder_ != null) {
          return transactionBuilder_.getMessageOrBuilder();
        } else {
          return transaction_ == null ?
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
        }
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder> 
          getTransactionFieldBuilder() {
        if (transactionBuilder_ == null) {
          transactionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder>(
                  getTransaction(),
                  getParentForChildren(),
                  isClean());
          transaction_ = null;
        }
        return transactionBuilder_;
      }

      private java.util.List<com.google.protobuf.Any> models_ =
        java.util.Collections.emptyList();
      private void ensureModelsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          models_ = new java.util.ArrayList<com.google.protobuf.Any>(models_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> modelsBuilder_;

      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public java.util.List<com.google.protobuf.Any> getModelsList() {
        if (modelsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(models_);
        } else {
          return modelsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public int getModelsCount() {
        if (modelsBuilder_ == null) {
          return models_.size();
        } else {
          return modelsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public com.google.protobuf.Any getModels(int index) {
        if (modelsBuilder_ == null) {
          return models_.get(index);
        } else {
          return modelsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder setModels(
          int index, com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.set(index, value);
          onChanged();
        } else {
          modelsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder setModels(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.set(index, builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder addModels(com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.add(value);
          onChanged();
        } else {
          modelsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder addModels(
          int index, com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.add(index, value);
          onChanged();
        } else {
          modelsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder addModels(
          com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.add(builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder addModels(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.add(index, builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder addAllModels(
          java.lang.Iterable<? extends com.google.protobuf.Any> values) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, models_);
          onChanged();
        } else {
          modelsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder clearModels() {
        if (modelsBuilder_ == null) {
          models_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          modelsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public Builder removeModels(int index) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.remove(index);
          onChanged();
        } else {
          modelsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public com.google.protobuf.Any.Builder getModelsBuilder(
          int index) {
        return getModelsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
          int index) {
        if (modelsBuilder_ == null) {
          return models_.get(index);  } else {
          return modelsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
           getModelsOrBuilderList() {
        if (modelsBuilder_ != null) {
          return modelsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(models_);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public com.google.protobuf.Any.Builder addModelsBuilder() {
        return getModelsFieldBuilder().addBuilder(
            com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public com.google.protobuf.Any.Builder addModelsBuilder(
          int index) {
        return getModelsFieldBuilder().addBuilder(
            index, com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any models = 2;</code>
       */
      public java.util.List<com.google.protobuf.Any.Builder> 
           getModelsBuilderList() {
        return getModelsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getModelsFieldBuilder() {
        if (modelsBuilder_ == null) {
          modelsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  models_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          models_ = null;
        }
        return modelsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.iab.unity.UnityIabAcceptResult)
    }

    // @@protoc_insertion_point(class_scope:response.iab.unity.UnityIabAcceptResult)
    private static final ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult();
    }

    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnityIabAcceptResult>
        PARSER = new com.google.protobuf.AbstractParser<UnityIabAcceptResult>() {
      @java.lang.Override
      public UnityIabAcceptResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnityIabAcceptResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnityIabAcceptResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.iab.unity.UnityIabAcceptResultProto.UnityIabAcceptResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_iab_unity_UnityIabAcceptResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_iab_unity_UnityIabAcceptResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-response/iab/unity/UnityIabAcceptResul" +
      "t.proto\022\022response.iab.unity\032\031google/prot" +
      "obuf/any.proto\032\032model/IabTransaction.pro" +
      "to\"h\n\024UnityIabAcceptResult\022*\n\013transactio" +
      "n\030\001 \001(\0132\025.model.IabTransaction\022$\n\006models" +
      "\030\002 \003(\0132\024.google.protobuf.AnyBc\n,ehooray." +
      "fishing.dto.proto.response.iab.unityB\031Un" +
      "ityIabAcceptResultProtoH\002\252\002\025Fishing.Netw" +
      "ork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.IabTransactionProto.getDescriptor(),
        });
    internal_static_response_iab_unity_UnityIabAcceptResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_iab_unity_UnityIabAcceptResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_iab_unity_UnityIabAcceptResult_descriptor,
        new java.lang.String[] { "Transaction", "Models", });
    com.google.protobuf.AnyProto.getDescriptor();
    ehooray.fishing.dto.proto.model.IabTransactionProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
