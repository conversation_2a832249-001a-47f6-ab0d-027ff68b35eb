// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/battle/RetryFishResult.proto

package ehooray.fishing.dto.proto.response.battle;

public final class RetryFishResultProto {
  private RetryFishResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RetryFishResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.battle.RetryFishResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 retryCount = 1;</code>
     * @return The retryCount.
     */
    int getRetryCount();

    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     * @return Whether the consumePlayerItem field is set.
     */
    boolean hasConsumePlayerItem();
    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     * @return The consumePlayerItem.
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItem();
    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemOrBuilder();
  }
  /**
   * Protobuf type {@code response.battle.RetryFishResult}
   */
  public static final class RetryFishResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.battle.RetryFishResult)
      RetryFishResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RetryFishResult.newBuilder() to construct.
    private RetryFishResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RetryFishResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RetryFishResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.internal_static_response_battle_RetryFishResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.internal_static_response_battle_RetryFishResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.class, ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.Builder.class);
    }

    public static final int RETRYCOUNT_FIELD_NUMBER = 1;
    private int retryCount_;
    /**
     * <code>int32 retryCount = 1;</code>
     * @return The retryCount.
     */
    @java.lang.Override
    public int getRetryCount() {
      return retryCount_;
    }

    public static final int CONSUMEPLAYERITEM_FIELD_NUMBER = 2;
    private ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem consumePlayerItem_;
    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     * @return Whether the consumePlayerItem field is set.
     */
    @java.lang.Override
    public boolean hasConsumePlayerItem() {
      return consumePlayerItem_ != null;
    }
    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     * @return The consumePlayerItem.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItem() {
      return consumePlayerItem_ == null ? ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : consumePlayerItem_;
    }
    /**
     * <code>.model.PlayerItem consumePlayerItem = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemOrBuilder() {
      return getConsumePlayerItem();
    }

    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.battle.RetryFishResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.battle.RetryFishResult)
        ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.internal_static_response_battle_RetryFishResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.internal_static_response_battle_RetryFishResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.class, ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        retryCount_ = 0;

        if (consumePlayerItemBuilder_ == null) {
          consumePlayerItem_ = null;
        } else {
          consumePlayerItem_ = null;
          consumePlayerItemBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.internal_static_response_battle_RetryFishResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult build() {
        ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult buildPartial() {
        ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult result = new ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult(this);
        result.retryCount_ = retryCount_;
        if (consumePlayerItemBuilder_ == null) {
          result.consumePlayerItem_ = consumePlayerItem_;
        } else {
          result.consumePlayerItem_ = consumePlayerItemBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int retryCount_ ;
      /**
       * <code>int32 retryCount = 1;</code>
       * @return The retryCount.
       */
      @java.lang.Override
      public int getRetryCount() {
        return retryCount_;
      }
      /**
       * <code>int32 retryCount = 1;</code>
       * @param value The retryCount to set.
       * @return This builder for chaining.
       */
      public Builder setRetryCount(int value) {
        
        retryCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 retryCount = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRetryCount() {
        
        retryCount_ = 0;
        onChanged();
        return this;
      }

      private ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem consumePlayerItem_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumePlayerItemBuilder_;
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       * @return Whether the consumePlayerItem field is set.
       */
      public boolean hasConsumePlayerItem() {
        return consumePlayerItemBuilder_ != null || consumePlayerItem_ != null;
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       * @return The consumePlayerItem.
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItem() {
        if (consumePlayerItemBuilder_ == null) {
          return consumePlayerItem_ == null ? ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : consumePlayerItem_;
        } else {
          return consumePlayerItemBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public Builder setConsumePlayerItem(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          consumePlayerItem_ = value;
          onChanged();
        } else {
          consumePlayerItemBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public Builder setConsumePlayerItem(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemBuilder_ == null) {
          consumePlayerItem_ = builderForValue.build();
          onChanged();
        } else {
          consumePlayerItemBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public Builder mergeConsumePlayerItem(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemBuilder_ == null) {
          if (consumePlayerItem_ != null) {
            consumePlayerItem_ =
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.newBuilder(consumePlayerItem_).mergeFrom(value).buildPartial();
          } else {
            consumePlayerItem_ = value;
          }
          onChanged();
        } else {
          consumePlayerItemBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public Builder clearConsumePlayerItem() {
        if (consumePlayerItemBuilder_ == null) {
          consumePlayerItem_ = null;
          onChanged();
        } else {
          consumePlayerItem_ = null;
          consumePlayerItemBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumePlayerItemBuilder() {
        
        onChanged();
        return getConsumePlayerItemFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemOrBuilder() {
        if (consumePlayerItemBuilder_ != null) {
          return consumePlayerItemBuilder_.getMessageOrBuilder();
        } else {
          return consumePlayerItem_ == null ?
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : consumePlayerItem_;
        }
      }
      /**
       * <code>.model.PlayerItem consumePlayerItem = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumePlayerItemFieldBuilder() {
        if (consumePlayerItemBuilder_ == null) {
          consumePlayerItemBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  getConsumePlayerItem(),
                  getParentForChildren(),
                  isClean());
          consumePlayerItem_ = null;
        }
        return consumePlayerItemBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.battle.RetryFishResult)
    }

    // @@protoc_insertion_point(class_scope:response.battle.RetryFishResult)
    private static final ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult();
    }

    public static ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RetryFishResult>
        PARSER = new com.google.protobuf.AbstractParser<RetryFishResult>() {
      @java.lang.Override
      public RetryFishResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RetryFishResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RetryFishResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.battle.RetryFishResultProto.RetryFishResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_battle_RetryFishResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_battle_RetryFishResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%response/battle/RetryFishResult.proto\022" +
      "\017response.battle\032\026model/PlayerItem.proto" +
      "\"S\n\017RetryFishResult\022\022\n\nretryCount\030\001 \001(\005\022" +
      ",\n\021consumePlayerItem\030\002 \001(\0132\021.model.Playe" +
      "rItemB[\n)ehooray.fishing.dto.proto.respo" +
      "nse.battleB\024RetryFishResultProtoH\002\252\002\025Fis" +
      "hing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_battle_RetryFishResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_battle_RetryFishResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_battle_RetryFishResult_descriptor,
        new java.lang.String[] { "RetryCount", "ConsumePlayerItem", });
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
