using StylizedWater2;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Fishing.UI;
using Zenject;
using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.Network;

namespace Fishing
{
    /// <summary>
    /// 釣魚遊戲的核心管理器
    /// 負責管理整個釣魚遊戲的流程和狀態
    /// 包含釣竿、魚線、魚類、相機等遊戲物件的控制
    /// 以及各種遊戲狀態的管理（拋餌、收線、結束等）
    /// </summary>
    public class FishingManager : MonoBehaviour
    {
        // 定義狀態變更事件委託和事件
        public delegate void FishingStateChangedHandler(FishingState newState);
        public event FishingStateChangedHandler OnStateChanged;

        public FishRod fishRod;
        public FishLine fishLine;
        public Fish fish;
        public FishRodForceItem forceItem;
        public ThrowBaitTwoPoint baitPath;
        public BaitLine baitLine;
        public Animator blankScreen;

        public Camera mainCamera;
        private Vector3 oriCameraPosition;
        private Vector3 oriCameraRotation;
        [Range(0f, 10f)]
        public float cameraRotateRange;
        [Range(0f, 1f)]
        public float cameraRotatePercent;

        public FishingState currentState;
        public int fishRandomPosition;

        public float showTime = 0;

        public ThrowBaitManager throwBaitManager;
        public LineTensionManager lineTensionManager;
        public GameEndManager gameEndManager;
        public ContinueManager continueManager;
        public PrepareManager prepareManager;
        public TopInfoManager topInfoManager;
        public CloseupTextManager closeupTextManager;
        public FishInfoManager fishInfoManager;
        public CordReelManager cordReelManager;
        public PausePanelManager pausePanelManager;
        public LureActionSystem lureActionSystem;
        public LureAnimationController lureAnimationController;

        public float throwBaitTime;
        public AnimationCurve curve;

        public BattleEquipeData EquipData => equipData;

        // 策略相關字段
        protected Dictionary<FishingState, IFishingStrategy> strategies = new Dictionary<FishingState, IFishingStrategy>();
        protected IFishingStrategy currentStrategy;

        #region Zenject Inject
        public FishingContainer fishingContainer;
        public IResourceProvider resourceProvider;
        public FishingGamePanelManager fishingGamePanelManager;
        public BattleService battleService;
        public FishData fishData;
        public BattleEquipeData equipData;
        public FishingRepositories fishingRepositories;
        public BattleLevelManager battleLevelManager;
        public TextIDConfig textIDConfig;
        protected List<IFishingStrategy> allStrategies;
        #endregion

        [Inject]
        public void Construct(FishingContainer fishingContainer,
                    IResourceProvider resourceProvider,
                    FishingGamePanelManager fishingGamePanelManager,
                    BattleService battleService,
                    FishData fishData,
                    BattleEquipeData equipeData,
                    FishingRepositories fishingRepositories,
                    BattleLevelManager battleLevelManager,
                    TextIDConfig textIDConfig,
                    List<IFishingStrategy> strategies)
        {
            this.fishingContainer = fishingContainer;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.resourceProvider = resourceProvider;
            this.battleService = battleService;
            this.fishData = fishData;
            this.equipData = equipeData;
            this.fishingRepositories = fishingRepositories;
            this.battleLevelManager = battleLevelManager;
            this.textIDConfig = textIDConfig;
            this.allStrategies = strategies;
            
            // 初始化策略字典
            foreach (var strategy in strategies)
            {
                this.strategies[strategy.GetStateType()] = strategy;
            }
        }

        // Unity 生命週期
        private void Start()
        {
            oriCameraPosition = mainCamera.transform.position;
            oriCameraRotation = mainCamera.transform.eulerAngles;

            fishLine.animate.enabled = false;
            baitPath.animate.enabled = false;

            // 使用新的狀態轉換方法
            TransitionToState(FishingState.Prepare).Forget();
        }

        private void Update()
        {
            // 使用當前策略進行更新
            currentStrategy?.OnUpdate(this);
        }

        /// <summary>
        /// 轉換到新狀態
        /// </summary>
        public async UniTask TransitionToState(FishingState newState)
        {
            // 如果沒有變化或策略不存在，則直接返回
            if (currentState == newState || !strategies.ContainsKey(newState))
                return;
            
            // 退出當前策略
            if (currentStrategy != null)
            {
                await currentStrategy.OnExit(this);
            }
            
            // 更新狀態
            currentState = newState;
            currentStrategy = strategies[newState];
            
            // 觸發狀態變化事件
            OnStateChanged?.Invoke(currentState);
            
            // 進入新策略
            await currentStrategy.OnEnter(this);
        }

        // 狀態切換與流程控制
        public void OpenManager()
        {
            var runner = OpenManagerRunner();
            runner.RunAsync(showMask: true).Forget();
        }

        public AsyncProgressRunner OpenManagerRunner()
        {
            var runner = fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                var infoPanel = await fishingGamePanelManager.OpenAsync<FishInfoManager>();
            });

            return runner;
        }

        // 保留舊的SetState方法以兼容現有代碼
        public void SetState(FishingState state)
        {
            currentState = state;
            // 觸發狀態變化事件
            OnStateChanged?.Invoke(currentState);
        }

        public void RandomStartPosition()
        {
            fishRandomPosition = Random.Range(-1, 2);
            forceItem.transform.position = new Vector3(fishRandomPosition * fish.stepDistanceScale, forceItem.transform.position.y, forceItem.transform.position.z);
            fish.moveStep = fishRandomPosition;
        }

        public bool CheckRodAnimatorState(string state)
        {
            return fishRod.fishRodAnimator.GetCurrentAnimatorStateInfo(0).IsName(state);
        }

        public void FightRotateCamera()
        {
            var force = new Vector3(forceItem.transform.position.x, mainCamera.transform.position.y, forceItem.transform.position.z);
            var forward = new Vector3(0, mainCamera.transform.position.y, 0) - mainCamera.transform.position;
            var target = force - mainCamera.transform.position;
            var limit = Vector3.Angle(forward, target) > cameraRotateRange ? cameraRotateRange : Vector3.Angle(forward, target);
            var leftRight = Mathf.Sign(Vector3.Dot(Vector3.Cross(forward, target), mainCamera.transform.up));
            var angle = limit * leftRight;
            mainCamera.transform.eulerAngles = new Vector3(oriCameraRotation.x, angle * cameraRotatePercent, 0);
        }

        public void ThrowBaitCamera()
        {
            throwBaitTime += Time.deltaTime;
            var angle = curve.Evaluate(throwBaitTime) * -30;
            mainCamera.transform.eulerAngles = new Vector3(oriCameraRotation.x + angle, mainCamera.transform.rotation.y, mainCamera.transform.rotation.z);
        }

        public void SetBlankScreen(bool set)
        {
            if (set)
            {
                blankScreen.Play("ToWhite",0,0);
            }
            else
            {
                blankScreen.Play("ToNone", 0, 0);
            }
        }

        public virtual void LoadPanel()
        {
            if (fishingGamePanelManager.TryGetGamePanel(out FishInfoManager fishInfoPanel))
            {
                fishInfoManager = fishInfoPanel;
                fishInfoManager.moveObj.transform.localScale = new Vector3(2, 2, 2);
                fishInfoManager.GetComponent<Canvas>().renderMode = RenderMode.ScreenSpaceOverlay;
            }

            if (fishingGamePanelManager.TryGetGamePanel(out CordReelManager cordReelPanel))
            {
                cordReelManager = cordReelPanel;
                cordReelManager.OpenStartText();
            }

            if (fishingGamePanelManager.TryGetGamePanel(out ThrowBaitManager throwBaitPanel))
            {
                throwBaitManager = throwBaitPanel;
                throwBaitManager.pullButton = cordReelManager.pullButton;
            }

            if (fishingGamePanelManager.TryGetGamePanel(out LineTensionManager lineTensionPanel))
            {
                lineTensionManager = lineTensionPanel;
                lineTensionManager.fishingSkillBarManager.onUseSkillEvent += fish.CheckSkill;
                lineTensionManager.fishingSkillBarManager.onUseSkillEvent += cordReelManager.SkillShake;
                lineTensionManager.fishingSkillBarManager.hintLightHalo = cordReelManager.hintLightHalo;
                lineTensionManager.fishingSkillBarManager.hintArrow = cordReelManager.hintArrow;
                lineTensionManager.CloseRoot();
            }

            if (fishingGamePanelManager.TryGetGamePanel(out GameEndManager gameEndPanel))
                gameEndManager = gameEndPanel;

            if (fishingGamePanelManager.TryGetGamePanel(out ContinueManager continuePanel))
                continueManager = continuePanel;

            if (fishingGamePanelManager.TryGetGamePanel(out PrepareManager preparePanel))
            {
                prepareManager = preparePanel;
                prepareManager.startButton = cordReelManager.pullButton;
                FishingEventTrigger.GetTrigger(prepareManager.startButton.gameObject).onPointerClick += prepareManager.OnStartButtonClick;
            }

            if (fishingGamePanelManager.TryGetGamePanel(out TopInfoManager topInfoPanel))
                topInfoManager = topInfoPanel;

            if (fishingGamePanelManager.TryGetGamePanel(out CloseupTextManager closeupTextPanel))
                closeupTextManager = closeupTextPanel;

            if (fishingGamePanelManager.TryGetGamePanel(out PausePanelManager pausePanelPanel))
                pausePanelManager = pausePanelPanel;

            if (fishingGamePanelManager.TryGetGamePanel(out LureActionSystem lureActionPanel))
                lureActionSystem = lureActionPanel;

            if (fishingGamePanelManager.TryGetGamePanel(out LureAnimationController lureAnimationPanel))
                lureAnimationController = lureAnimationPanel;

            throwBaitManager.CloseRoot();
            cordReelManager.wheelRotate.StopRotate();
            equipData.SetEquipData();
            prepareManager.RefreshIcon();
        }

        public bool CheckPlayerItem()
        {
            if (equipData.rodData.times <= 0)
            {
                var notEnoughFishRodTimes = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.NotEnoughFishRodTimes).Content;
                fishingGamePanelManager.Remind(notEnoughFishRodTimes);
                return false;
            }
            if (equipData.lineData.finalCount <= 0 )
            {
                var notEnoughFishLine = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.NotEnoughFishLine).Content;
                fishingGamePanelManager.Remind(notEnoughFishLine);
                return false;
            }
            if (equipData.baitData.finalCount <= 0)
            {
                var notEnoughFishBait = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.NotEnoughFishBait).Content;
                fishingGamePanelManager.Remind(notEnoughFishBait);
                return false;
            }
            if (fishingRepositories.playerItemRepository.GetItemsByTag(ItemTableDataExtension.ItemTag.Ap)[0].finalCount < battleLevelManager.mapData.Apcost)
            {
                var notEnoughApCount = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.NotEnoughApCount).Content;
                fishingGamePanelManager.Remind(notEnoughApCount);
                return false;
            }

            return true;
        }
        
        // 保留為了向後兼容的方法
        public void ResetCamera()
        {
            mainCamera.transform.position = oriCameraPosition;
            mainCamera.transform.eulerAngles = oriCameraRotation;
        }
        
        public async UniTask FinishCatchAnimate()
        {
            mainCamera.GetComponent<Animator>().enabled = false;
            fish.Show(mainCamera);
            await TransitionToState(FishingState.Win);
        }

        public void ResetGame()
        {
            fish.ResetValue();
            ResetValue();
            
            // 重置甩動檢測器
            if (GetComponent<ThrowGestureDetector>() != null)
            {
                GetComponent<ThrowGestureDetector>().ResetDetector();
            }
            
            TransitionToState(FishingState.Prepare).Forget();
        }
        
        public async UniTask ContinueGame()
        {
            var continueResult = await battleService.RetryFish();
            fish.continueTimes = continueResult.RetryCount;
            fishData.RetryCount = continueResult.RetryCount;
            fish.Continue();
            ResetValue();

            // 重置甩動檢測器
            if (GetComponent<ThrowGestureDetector>() != null)
            {
                GetComponent<ThrowGestureDetector>().ResetDetector();
            }

            await TransitionToState(FishingState.BeforeStart);
        }

        // 內部重置（protected virtual 供子類覆寫）
        protected virtual void ResetValue()
        {
            fishRod.ResetValue();
            forceItem.ResetValue();
            fishLine.ResetValue();
            baitPath.ResetValue();
            baitLine.gameObject.SetActive(true);
            ResetCamera();

            fishingGamePanelManager.Close<CloseupTextManager>();
            lineTensionManager.CloseRoot();
            fishingGamePanelManager.Close<LineTensionManager>();
            fishingGamePanelManager.Close<FishInfoManager>();
            fishingGamePanelManager.Close<GameEndManager>();

            fishingGamePanelManager.OpenAsync<CordReelManager>().Forget();
            cordReelManager.StartInteractive();
            cordReelManager.StopInteractive();
            cordReelManager.StartInteractive();

            lineTensionManager.ResetStatus();
            fishingGamePanelManager.Close<ContinueManager>();
            cordReelManager.wheelRotate.StopRotate();
            throwBaitTime = 0f;

            FishingEventTrigger.GetTrigger(cordReelManager.pullButton.gameObject).onPointerDown -= lineTensionManager.OnPullButtonPressed;
            FishingEventTrigger.GetTrigger(cordReelManager.pullButton.gameObject).onPointerUp -= lineTensionManager.OnPullButtonUp;
        }
    }
    public enum FishingState
    {
        Prepare = 0,
        BeforeStart,
        Start,
        Wait,
        Fight,
        Catch,
        Fail,
        Win
    }
}

