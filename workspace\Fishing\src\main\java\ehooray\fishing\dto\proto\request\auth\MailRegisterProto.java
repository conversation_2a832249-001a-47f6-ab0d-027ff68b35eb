// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/auth/MailRegister.proto

package ehooray.fishing.dto.proto.request.auth;

public final class MailRegisterProto {
  private MailRegisterProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MailRegisterOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.auth.MailRegister)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     * @return Whether the mailAuth field is set.
     */
    boolean hasMailAuth();
    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     * @return The mailAuth.
     */
    ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getMailAuth();
    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     */
    ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder getMailAuthOrBuilder();

    /**
     * <code>string playerName = 2;</code>
     * @return The playerName.
     */
    java.lang.String getPlayerName();
    /**
     * <code>string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();
  }
  /**
   * Protobuf type {@code request.auth.MailRegister}
   */
  public static final class MailRegister extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.auth.MailRegister)
      MailRegisterOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MailRegister.newBuilder() to construct.
    private MailRegister(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MailRegister() {
      playerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MailRegister();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.internal_static_request_auth_MailRegister_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.internal_static_request_auth_MailRegister_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.class, ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.Builder.class);
    }

    public static final int MAILAUTH_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth mailAuth_;
    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     * @return Whether the mailAuth field is set.
     */
    @java.lang.Override
    public boolean hasMailAuth() {
      return mailAuth_ != null;
    }
    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     * @return The mailAuth.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getMailAuth() {
      return mailAuth_ == null ? ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.getDefaultInstance() : mailAuth_;
    }
    /**
     * <code>.request.auth.MailAuth mailAuth = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder getMailAuthOrBuilder() {
      return getMailAuth();
    }

    public static final int PLAYERNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object playerName_;
    /**
     * <code>string playerName = 2;</code>
     * @return The playerName.
     */
    @java.lang.Override
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        playerName_ = s;
        return s;
      }
    }
    /**
     * <code>string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.auth.MailRegister}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.auth.MailRegister)
        ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegisterOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.internal_static_request_auth_MailRegister_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.internal_static_request_auth_MailRegister_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.class, ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (mailAuthBuilder_ == null) {
          mailAuth_ = null;
        } else {
          mailAuth_ = null;
          mailAuthBuilder_ = null;
        }
        playerName_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.internal_static_request_auth_MailRegister_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister build() {
        ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister buildPartial() {
        ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister result = new ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister(this);
        if (mailAuthBuilder_ == null) {
          result.mailAuth_ = mailAuth_;
        } else {
          result.mailAuth_ = mailAuthBuilder_.build();
        }
        result.playerName_ = playerName_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth mailAuth_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder> mailAuthBuilder_;
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       * @return Whether the mailAuth field is set.
       */
      public boolean hasMailAuth() {
        return mailAuthBuilder_ != null || mailAuth_ != null;
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       * @return The mailAuth.
       */
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth getMailAuth() {
        if (mailAuthBuilder_ == null) {
          return mailAuth_ == null ? ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.getDefaultInstance() : mailAuth_;
        } else {
          return mailAuthBuilder_.getMessage();
        }
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public Builder setMailAuth(ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth value) {
        if (mailAuthBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mailAuth_ = value;
          onChanged();
        } else {
          mailAuthBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public Builder setMailAuth(
          ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder builderForValue) {
        if (mailAuthBuilder_ == null) {
          mailAuth_ = builderForValue.build();
          onChanged();
        } else {
          mailAuthBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public Builder mergeMailAuth(ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth value) {
        if (mailAuthBuilder_ == null) {
          if (mailAuth_ != null) {
            mailAuth_ =
              ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.newBuilder(mailAuth_).mergeFrom(value).buildPartial();
          } else {
            mailAuth_ = value;
          }
          onChanged();
        } else {
          mailAuthBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public Builder clearMailAuth() {
        if (mailAuthBuilder_ == null) {
          mailAuth_ = null;
          onChanged();
        } else {
          mailAuth_ = null;
          mailAuthBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder getMailAuthBuilder() {
        
        onChanged();
        return getMailAuthFieldBuilder().getBuilder();
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      public ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder getMailAuthOrBuilder() {
        if (mailAuthBuilder_ != null) {
          return mailAuthBuilder_.getMessageOrBuilder();
        } else {
          return mailAuth_ == null ?
              ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.getDefaultInstance() : mailAuth_;
        }
      }
      /**
       * <code>.request.auth.MailAuth mailAuth = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder> 
          getMailAuthFieldBuilder() {
        if (mailAuthBuilder_ == null) {
          mailAuthBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuth.Builder, ehooray.fishing.dto.proto.request.auth.MailAuthProto.MailAuthOrBuilder>(
                  getMailAuth(),
                  getParentForChildren(),
                  isClean());
          mailAuth_ = null;
        }
        return mailAuthBuilder_;
      }

      private java.lang.Object playerName_ = "";
      /**
       * <code>string playerName = 2;</code>
       * @return The playerName.
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          playerName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string playerName = 2;</code>
       * @return The bytes for playerName.
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string playerName = 2;</code>
       * @param value The playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string playerName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerName() {
        
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <code>string playerName = 2;</code>
       * @param value The bytes for playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        playerName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.auth.MailRegister)
    }

    // @@protoc_insertion_point(class_scope:request.auth.MailRegister)
    private static final ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister();
    }

    public static ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailRegister>
        PARSER = new com.google.protobuf.AbstractParser<MailRegister>() {
      @java.lang.Override
      public MailRegister parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailRegister> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailRegister> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.MailRegisterProto.MailRegister getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_auth_MailRegister_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_auth_MailRegister_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037request/auth/MailRegister.proto\022\014reque" +
      "st.auth\032\033request/auth/MailAuth.proto\"L\n\014" +
      "MailRegister\022(\n\010mailAuth\030\001 \001(\0132\026.request" +
      ".auth.MailAuth\022\022\n\nplayerName\030\002 \001(\tBU\n&eh" +
      "ooray.fishing.dto.proto.request.authB\021Ma" +
      "ilRegisterProtoH\002\252\002\025Fishing.Network.Prot" +
      "ob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.request.auth.MailAuthProto.getDescriptor(),
        });
    internal_static_request_auth_MailRegister_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_auth_MailRegister_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_auth_MailRegister_descriptor,
        new java.lang.String[] { "MailAuth", "PlayerName", });
    ehooray.fishing.dto.proto.request.auth.MailAuthProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
