package ehooray.fishing.pojo.csv;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("VariableTable.xlsx")
public class VariableCsv extends ACsv<VariableCsv.Row>
{
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("Parameter")
        protected String parameter;

        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }
        
        public String getParameter()
        {
            return parameter;
        }
        
        public int getParameterByInt()
        {
            return Integer.parseInt(parameter);
        }
        
        public float getParameterByFloat()
        {
            return Float.parseFloat(parameter);
        }
    }
    
    // action point(AP)
    public long getCountApMilliseconds()
    {
        return getById(1102001).getParameterByInt() * 1000L;
    }
    
    // fishing
    public int getLightAddPct()
    {
        return getById(1101001).getParameterByInt();
    }
    
    public int getDarkAddPct()
    {
        return getById(1101002).getParameterByInt();
    }
    
    public int getContCost()
    {
        return getById(1101018).getParameterByInt();
    }
    
    public int getContCostMF()
    {
        return getById(1101019).getParameterByInt();
    }
    
    // fishing rod lucky
    public float getLuckyWeightMf(FishCsv.Rare rare)
    {
        switch (rare)
        {
            case C: return 0f;
            case B: return getById(1101020).getParameterByFloat();
            case A: return getById(1101021).getParameterByFloat();
            case S: return getById(1101022).getParameterByFloat();
            default: return 0f;
        }
    }
    
    public int getSkillAdd()
    {
        return getById(1101023).getParameterByInt();
    }
}
    
