package ehooray.fishing.pojo.csv;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.google.common.base.Strings;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;
import ehooray.fishing.pojo.utils.LocalDateTimeUtils;

@CsvPath("ScheduleTable.xlsx")
public class ScheduleCsv extends ACsv<ScheduleCsv.Row>
{
    public static final DateTimeFormatter DateTimeFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
    public static final CronParser parser = new CronParser(cronDefinition);

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Start_Date")
        protected String startDateStr;

        @ExcelCellName("End_Date")
        protected String endDateStr;

        @ExcelCellName("Cycle_Expression")
        protected String cycleExpression;

        @ExcelCellName("Cycle_Duration")
        protected int cycleDuration;

        @Override
        public int getId()
        {
            return id;
        }

        public boolean isOpened(LocalDateTime localDateTime)
        {
            return !(localDateTime.isBefore(getStartDateTime()) || localDateTime.isAfter(getEndDateTime()));
        }

        public Optional<LocalDateTime> getNextCycleStartDate(LocalDateTime localDateTime)
        {
            if(localDateTime.isBefore(getStartDateTime())) return Optional.of(getStartDateTime());
            if(localDateTime.isAfter(getEndDateTime())) return Optional.empty();
            if(isSingleCycle()) return Optional.empty();
            
            ZonedDateTime zonedDateTime = LocalDateTimeUtils.toZonedDateTime(localDateTime);
            LocalDateTime nextCycleStartDate = getCycleExecution().nextExecution(zonedDateTime).get().toLocalDateTime();
            return Optional.of(nextCycleStartDate);
        }
        
        public Optional<LocalDateTime> getCycleStartDate(LocalDateTime localDateTime)
        {
            ZonedDateTime zonedDateTime = LocalDateTimeUtils.toZonedDateTime(localDateTime);

            // not in any cycle
            if(!isOpened(localDateTime)) return Optional.empty();
            if(isSingleCycle()) return Optional.of(getStartDateTime());

            Optional<ZonedDateTime> cycleStart = getCycleExecution().lastExecution(zonedDateTime);
            // no cycle expression, mean startDate - endDate is one cycle
            if(!cycleStart.isPresent()) return Optional.of(getStartDateTime());

            ZonedDateTime cycleEnd = cycleStart.get().plusSeconds(getCycleDuration());
            // not in cycle, just return
            if(cycleEnd.isBefore(zonedDateTime)) return Optional.empty();

            // make sure cycle start is after startDateTime
            LocalDateTime result = LocalDateTimeUtils.Max(cycleStart.get().toLocalDateTime(), getStartDateTime());
            return Optional.of(result);
        }

        public Optional<LocalDateTime> getCycleEndDate(LocalDateTime localDateTime)
        {
            ZonedDateTime zonedDateTime = LocalDateTimeUtils.toZonedDateTime(localDateTime);

            // not in any cycle
            if(!isOpened(localDateTime)) return Optional.empty();
            if(isSingleCycleButDuration())
            {
                LocalDateTime cycleEnd = localDateTime.plusSeconds(getCycleDuration());
                LocalDateTime result = LocalDateTimeUtils.Min(cycleEnd, getEndDateTime());
                return Optional.of(result);
            }
            
            if(isSingleCycle()) return Optional.of(getEndDateTime());

            Optional<ZonedDateTime> cycleStart = getCycleExecution().lastExecution(zonedDateTime);
            // no cycle expression, mean startDate - endDate is one cycle
            if(!cycleStart.isPresent()) return Optional.of(getEndDateTime());

            ZonedDateTime cycleEnd = cycleStart.get().plusSeconds(getCycleDuration());
            // not in cycle, just return
            if(cycleEnd.isBefore(zonedDateTime)) return Optional.empty();

            // make sure cycle end is before endDateTime
            LocalDateTime result = LocalDateTimeUtils.Min(cycleEnd.toLocalDateTime(), getEndDateTime());
            return Optional.of(result);
        }

        public boolean isOutStartCycle(LocalDateTime startDate, LocalDateTime now)
        {
            // startDate may not in any cycle, just change to new cycle
            Optional<LocalDateTime> cycleStart = getCycleStartDate(startDate);
            if(!cycleStart.isPresent()) return true;

            Optional<LocalDateTime> cycleEnd = getCycleEndDate(startDate);
            if(!cycleEnd.isPresent()) return true;

            // find the smallest end date
            return now.isBefore(cycleStart.get()) || now.isAfter(cycleEnd.get());
        }

        protected ExecutionTime cycleExecution;
        public ExecutionTime getCycleExecution()
        {
            if(cycleExecution != null) return cycleExecution;

            cycleExecution = ExecutionTime.forCron(parser.parse(getCycleExpression()));
            return cycleExecution;
        }
        protected LocalDateTime startDateTime;
        public LocalDateTime getStartDateTime()
        {
            if(startDateTime != null) return startDateTime;

            startDateTime = LocalDateTime.parse(getStartDateStr(), DateTimeFormat);
            return startDateTime;
        }

        protected LocalDateTime endDateTime;
        public LocalDateTime getEndDateTime()
        {
            if(endDateTime != null) return endDateTime;

            endDateTime = LocalDateTime.parse(getEndDateStr(), DateTimeFormat);
            return endDateTime;
        }

        public String getStartDateStr()
        {
            return startDateStr;
        }

        public String getEndDateStr()
        {
            return endDateStr;
        }

        public String getCycleExpression()
        {
            return cycleExpression;
        }
        
        public boolean isSingleCycle()
        {
            return Strings.isNullOrEmpty(getCycleExpression());
        }
        
        public boolean isSingleCycleButDuration()
        {
            return isSingleCycle() && getCycleDuration() > 0;
        }

        public int getCycleDuration()
        {
            return cycleDuration;
        }
    }



}
