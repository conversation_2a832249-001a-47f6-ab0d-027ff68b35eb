# Task ID: 2
# Title: Time and Weather System Implementation
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Develop the in-game time system with 8 distinct periods and the weather system (Sunny/Rainy). Ensure these systems can influence gameplay events and are displayed on the UI.
# Details:
Implement a `TimeSystem` class: Manages 8 time periods (<PERSON>, <PERSON><PERSON>, etc.), each 3 in-game hours. Fishing advances time by 30 mins, travel by 1 hour. Implement a `WeatherSystem` class: Manages Sunny/Rainy states. Weather changes every 2 in-game hours. Sunny boosts Fish Detection, Rainy increases Bite Rate by 10%. Store current time and weather globally or pass to relevant scenes. Update Boat Menu UI to reflect current time/weather. 
```javascript
// Pseudo-code TimeSystem
class TimeSystem {
  constructor() { this.periods = ['Dawn', ...]; this.currentPeriodIndex = 0; this.gameHour = 0; }
  advance(minutes) { this.gameHour = (this.gameHour + minutes/60) % 24; /* update period */ }
  getCurrentPeriod() { return this.periods[Math.floor(this.gameHour / 3)]; }
}
// Pseudo-code WeatherSystem
class WeatherSystem {
  constructor() { this.states = ['<PERSON>', 'Rainy']; this.currentState = 'Sunny'; this.timeSinceLastChange = 0; }
  update(elapsedGameHours) { this.timeSinceLastChange += elapsedGameHours; if (this.timeSinceLastChange >= 2) { this.currentState = /* random state */; this.timeSinceLastChange = 0; } }
}
```

# Test Strategy:
Unit test time advancement logic across period changes. Unit test weather change logic. Observe UI updates for time and weather in the Boat Menu. Verify game actions (fishing, travel) correctly advance time. Manually trigger weather changes to test effects if dependent systems are available.
