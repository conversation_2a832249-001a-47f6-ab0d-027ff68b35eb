package ehooray.fishing.pojo.csv;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("RodTable.xlsx")
public class RodCsv extends ACsv<RodCsv.Row>
{
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        @ExcelCellName("Name")
        protected int name;      
        @ExcelCellName("Info")
        protected int info;      
        @ExcelCellName("Rare")
        protected int rare;
        @ExcelCellName("PointMin")
        protected int pointMin;
        @ExcelCellName("PointMax")
        protected int pointMax;
        @ExcelCellName("Str")
        protected int strength;
        @ExcelCellName("Ctrl")
        protected int control;
        @ExcelCellName("Luk")
        protected int luck;
        @ExcelCellName("Dqr")
        protected int dqr;
        @ExcelCellName("Times")
        protected int times;
        @ExcelCellName("PerFee")
        protected int perFee;
        @ExcelCellName("sell")
        protected int sell; // sell coin amount, coin id is defined by variable csv
        @ExcelCellName("LvUpPoint")
        protected int lvUpPoint;

        public int getRowIndex()
        {
            return rowIndex;
        }
        
        @Override
        public int getId()
        {
            return id;
        }

        public int getName()
        {
            return name;
        }

        public int getInfo()
        {
            return info;
        }

        public int getRare()
        {
            return rare;
        }

        public int getPointMin()
        {
            return pointMin;
        }

        public int getPointMax()
        {
            return pointMax;
        }

        public int getStrength()
        {
            return strength;
        }

        public int getControl()
        {
            return control;
        }

        public int getLuck()
        {
            return luck;
        }

        public int getDqr()
        {
            return dqr;
        }

        public int getTimes()
        {
            return times;
        }

        public int getPerFee()
        {
            return perFee;
        }

        public int getSell()
        {
            return sell;
        }

        public int getLvUpPoint()
        {
            return lvUpPoint;
        }
    }
    
    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();
    }
}
