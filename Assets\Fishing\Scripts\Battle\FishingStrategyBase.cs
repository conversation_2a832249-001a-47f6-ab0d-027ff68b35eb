using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace Fishing
{
    /// <summary>
    /// 釣魚策略基類，提供通用實現
    /// </summary>
    public abstract class FishingStrategyBase : IFishingStrategy
    {
        protected FishingState stateType;
        
        protected FishingStrategyBase(FishingState stateType)
        {
            this.stateType = stateType;
        }
        
        /// <summary>
        /// 獲取此策略對應的釣魚狀態
        /// </summary>
        public FishingState GetStateType() => stateType;
        
        /// <summary>
        /// 進入該狀態時執行的方法，默認返回已完成的任務
        /// </summary>
        public virtual UniTask OnEnter(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 在該狀態中每幀執行的方法，默認為空實現
        /// </summary>
        public virtual void OnUpdate(FishingManager context) { }
        
        /// <summary>
        /// 退出該狀態時執行的方法，默認返回已完成的任務
        /// </summary>
        public virtual UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 