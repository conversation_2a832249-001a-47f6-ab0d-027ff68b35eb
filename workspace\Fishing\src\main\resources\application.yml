server:
    port: 7443
#    http2:
#        enabled: false
#    ssl:
#        key-store: classpath:https/star_ehooray_ch.pfx
#        key-store-password: ehooray
#        keyStoreType: PKCS12
        
logging:
    config: classpath:log4j2.yml

spring:
    main:
        allow-circular-references: true
    datasource:
        game:
            unique-resource-name: game
            xa-data-source-class-name: com.mysql.cj.jdbc.MysqlXADataSource
            xa-properties.url: **************************************************************************
            xa-properties.user: root
            xa-properties.password: 
            #driver-class-name: com.mysql.cj.jdbc.Driver
            #db thread pool
            max-pool-size: 2000
            min-pool-size: 10
            test-query: SELECT 1
            #pool-name: "game-db-pool"
            entity-class-package: "ehooray.fishing.entity"
            repository-class-package: "ehooray.fishing.repository"
            hibernate-dialect: org.hibernate.dialect.MySQL57Dialect
        game-log:
            unique-resource-name: game-log
            xa-data-source-class-name: com.mysql.cj.jdbc.MysqlXADataSource
            xa-properties.url: ******************************************************************************
            xa-properties.user: root
            xa-properties.password: 
            #driver-class-name: com.mysql.cj.jdbc.Driver
            #db thread pool
            max-pool-size: 2000
            min-pool-size: 10
            test-query: SELECT 1
            #pool-name: "game-log-db-pool"
            entity-class-package: "ehooray.fishing.log.entity"
            repository-class-package: "ehooray.fishing.log.repository"
            hibernate-dialect: org.hibernate.dialect.MySQL57Dialect
    
    jpa:
        open-in-view: false
        hibernate:
            ddl-auto: update
        properties:
            # https://docs.jboss.org/hibernate/orm/5.0/userguide/html_single/Hibernate_User_Guide.html#caching
            javax:
                persistence:
                    sharedCache:
                        #required - enable selective caching mode - only entities with @Cacheable annotation will use L2 cache.
                        mode: ENABLE_SELECTIVE
            hibernate:
                javax:
                    cache:
                        #provider: org.ehcache.jsr107.EhcacheCachingProvider
                        uri: classpath:hibernate-ehcache.xml
                        # here we provide cache template by ehcache.xml, so we use "create" directly. 
                        # or should use "fail" for make sure cache is specified in ehcache.xml
                        missing_cache_strategy: create
                #optional - enable SQL statements formatting.
                format-sql: true 
                #generate-statistics: true
                cache:
                    #required - turn on L2 cache.
                    use_second_level_cache: true
                    #optional - query cache.
                    use_query_cache: false 
                    region:
                    #required - classpath to cache region factory.
                        factory_class: jcache
game:
    transactional:
        timeout: 30
        # rollback: "java.lang.Exception" # Can't work by spel expression, why? 
    csv:
        file-path: "csv/"
        class-packages: "ehooray.fishing.pojo.csv"
    proto:
        class-packages: "ehooray.fishing.dto.proto.request"
    lua:
        file-path: "lua/"
        file-ext: ".lua.txt"
        cache-class-package: "ehooray.fishing.component.lua.cache"
    firebase:
        key-path: classpath:fishing-firebase.json
    iab-unity:
        client-app-id: "com.ehooray.fishing"
        android-public-key: "" 
        editor-md5-key: "UNITY_EDITOR"
    responseCache:
        enable: true
        capacity: 30
    check-server:
        check-ip-url: "http://checkip.amazonaws.com/"
    check-maintain-schedule: 10000
    check-client-patch-schedule: 10000
    

