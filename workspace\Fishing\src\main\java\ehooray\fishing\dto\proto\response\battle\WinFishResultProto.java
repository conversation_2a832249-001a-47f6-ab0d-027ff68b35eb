// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/battle/WinFishResult.proto

package ehooray.fishing.dto.proto.response.battle;

public final class WinFishResultProto {
  private WinFishResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface WinFishResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.battle.WinFishResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Player player = 1;</code>
     * @return Whether the player field is set.
     */
    boolean hasPlayer();
    /**
     * <code>.model.Player player = 1;</code>
     * @return The player.
     */
    ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer();
    /**
     * <code>.model.Player player = 1;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder();

    /**
     * <code>.model.FishItem fishItem = 2;</code>
     * @return Whether the fishItem field is set.
     */
    boolean hasFishItem();
    /**
     * <code>.model.FishItem fishItem = 2;</code>
     * @return The fishItem.
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItem();
    /**
     * <code>.model.FishItem fishItem = 2;</code>
     */
    ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemOrBuilder();

    /**
     * <code>.model.FishRod fishRod = 3;</code>
     * @return Whether the fishRod field is set.
     */
    boolean hasFishRod();
    /**
     * <code>.model.FishRod fishRod = 3;</code>
     * @return The fishRod.
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod();
    /**
     * <code>.model.FishRod fishRod = 3;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumePlayerItemsList();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    int getConsumePlayerItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumePlayerItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
        int index);

    /**
     * <code>int32 nextFishCsvId = 5;</code>
     * @return The nextFishCsvId.
     */
    int getNextFishCsvId();
  }
  /**
   * Protobuf type {@code response.battle.WinFishResult}
   */
  public static final class WinFishResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.battle.WinFishResult)
      WinFishResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use WinFishResult.newBuilder() to construct.
    private WinFishResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private WinFishResult() {
      consumePlayerItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new WinFishResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.internal_static_response_battle_WinFishResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.internal_static_response_battle_WinFishResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.class, ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.Builder.class);
    }

    public static final int PLAYER_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.PlayerProto.Player player_;
    /**
     * <code>.model.Player player = 1;</code>
     * @return Whether the player field is set.
     */
    @java.lang.Override
    public boolean hasPlayer() {
      return player_ != null;
    }
    /**
     * <code>.model.Player player = 1;</code>
     * @return The player.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer() {
      return player_ == null ? ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
    }
    /**
     * <code>.model.Player player = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder() {
      return getPlayer();
    }

    public static final int FISHITEM_FIELD_NUMBER = 2;
    private ehooray.fishing.dto.proto.model.FishItemProto.FishItem fishItem_;
    /**
     * <code>.model.FishItem fishItem = 2;</code>
     * @return Whether the fishItem field is set.
     */
    @java.lang.Override
    public boolean hasFishItem() {
      return fishItem_ != null;
    }
    /**
     * <code>.model.FishItem fishItem = 2;</code>
     * @return The fishItem.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItem() {
      return fishItem_ == null ? ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : fishItem_;
    }
    /**
     * <code>.model.FishItem fishItem = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemOrBuilder() {
      return getFishItem();
    }

    public static final int FISHROD_FIELD_NUMBER = 3;
    private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
    /**
     * <code>.model.FishRod fishRod = 3;</code>
     * @return Whether the fishRod field is set.
     */
    @java.lang.Override
    public boolean hasFishRod() {
      return fishRod_ != null;
    }
    /**
     * <code>.model.FishRod fishRod = 3;</code>
     * @return The fishRod.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
      return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
    }
    /**
     * <code>.model.FishRod fishRod = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
      return getFishRod();
    }

    public static final int CONSUMEPLAYERITEMS_FIELD_NUMBER = 4;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumePlayerItems_;
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumePlayerItemsList() {
      return consumePlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumePlayerItemsOrBuilderList() {
      return consumePlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    @java.lang.Override
    public int getConsumePlayerItemsCount() {
      return consumePlayerItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index) {
      return consumePlayerItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
        int index) {
      return consumePlayerItems_.get(index);
    }

    public static final int NEXTFISHCSVID_FIELD_NUMBER = 5;
    private int nextFishCsvId_;
    /**
     * <code>int32 nextFishCsvId = 5;</code>
     * @return The nextFishCsvId.
     */
    @java.lang.Override
    public int getNextFishCsvId() {
      return nextFishCsvId_;
    }

    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.battle.WinFishResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.battle.WinFishResult)
        ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.internal_static_response_battle_WinFishResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.internal_static_response_battle_WinFishResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.class, ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumePlayerItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (playerBuilder_ == null) {
          player_ = null;
        } else {
          player_ = null;
          playerBuilder_ = null;
        }
        if (fishItemBuilder_ == null) {
          fishItem_ = null;
        } else {
          fishItem_ = null;
          fishItemBuilder_ = null;
        }
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumePlayerItemsBuilder_.clear();
        }
        nextFishCsvId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.internal_static_response_battle_WinFishResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult build() {
        ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult buildPartial() {
        ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult result = new ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult(this);
        int from_bitField0_ = bitField0_;
        if (playerBuilder_ == null) {
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        if (fishItemBuilder_ == null) {
          result.fishItem_ = fishItem_;
        } else {
          result.fishItem_ = fishItemBuilder_.build();
        }
        if (fishRodBuilder_ == null) {
          result.fishRod_ = fishRod_;
        } else {
          result.fishRod_ = fishRodBuilder_.build();
        }
        if (consumePlayerItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumePlayerItems_ = java.util.Collections.unmodifiableList(consumePlayerItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumePlayerItems_ = consumePlayerItems_;
        } else {
          result.consumePlayerItems_ = consumePlayerItemsBuilder_.build();
        }
        result.nextFishCsvId_ = nextFishCsvId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.PlayerProto.Player player_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder> playerBuilder_;
      /**
       * <code>.model.Player player = 1;</code>
       * @return Whether the player field is set.
       */
      public boolean hasPlayer() {
        return playerBuilder_ != null || player_ != null;
      }
      /**
       * <code>.model.Player player = 1;</code>
       * @return The player.
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer() {
        if (playerBuilder_ == null) {
          return player_ == null ? ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
        } else {
          return playerBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder setPlayer(ehooray.fishing.dto.proto.model.PlayerProto.Player value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          player_ = value;
          onChanged();
        } else {
          playerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder setPlayer(
          ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder builderForValue) {
        if (playerBuilder_ == null) {
          player_ = builderForValue.build();
          onChanged();
        } else {
          playerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder mergePlayer(ehooray.fishing.dto.proto.model.PlayerProto.Player value) {
        if (playerBuilder_ == null) {
          if (player_ != null) {
            player_ =
              ehooray.fishing.dto.proto.model.PlayerProto.Player.newBuilder(player_).mergeFrom(value).buildPartial();
          } else {
            player_ = value;
          }
          onChanged();
        } else {
          playerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = null;
          onChanged();
        } else {
          player_ = null;
          playerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder getPlayerBuilder() {
        
        onChanged();
        return getPlayerFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilder();
        } else {
          return player_ == null ?
              ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
        }
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder>(
                  getPlayer(),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }

      private ehooray.fishing.dto.proto.model.FishItemProto.FishItem fishItem_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> fishItemBuilder_;
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       * @return Whether the fishItem field is set.
       */
      public boolean hasFishItem() {
        return fishItemBuilder_ != null || fishItem_ != null;
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       * @return The fishItem.
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem getFishItem() {
        if (fishItemBuilder_ == null) {
          return fishItem_ == null ? ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : fishItem_;
        } else {
          return fishItemBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public Builder setFishItem(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (fishItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishItem_ = value;
          onChanged();
        } else {
          fishItemBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public Builder setFishItem(
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder builderForValue) {
        if (fishItemBuilder_ == null) {
          fishItem_ = builderForValue.build();
          onChanged();
        } else {
          fishItemBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public Builder mergeFishItem(ehooray.fishing.dto.proto.model.FishItemProto.FishItem value) {
        if (fishItemBuilder_ == null) {
          if (fishItem_ != null) {
            fishItem_ =
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem.newBuilder(fishItem_).mergeFrom(value).buildPartial();
          } else {
            fishItem_ = value;
          }
          onChanged();
        } else {
          fishItemBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public Builder clearFishItem() {
        if (fishItemBuilder_ == null) {
          fishItem_ = null;
          onChanged();
        } else {
          fishItem_ = null;
          fishItemBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder getFishItemBuilder() {
        
        onChanged();
        return getFishItemFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder getFishItemOrBuilder() {
        if (fishItemBuilder_ != null) {
          return fishItemBuilder_.getMessageOrBuilder();
        } else {
          return fishItem_ == null ?
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem.getDefaultInstance() : fishItem_;
        }
      }
      /**
       * <code>.model.FishItem fishItem = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder> 
          getFishItemFieldBuilder() {
        if (fishItemBuilder_ == null) {
          fishItemBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishItemProto.FishItem, ehooray.fishing.dto.proto.model.FishItemProto.FishItem.Builder, ehooray.fishing.dto.proto.model.FishItemProto.FishItemOrBuilder>(
                  getFishItem(),
                  getParentForChildren(),
                  isClean());
          fishItem_ = null;
        }
        return fishItemBuilder_;
      }

      private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> fishRodBuilder_;
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       * @return Whether the fishRod field is set.
       */
      public boolean hasFishRod() {
        return fishRodBuilder_ != null || fishRod_ != null;
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       * @return The fishRod.
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
        if (fishRodBuilder_ == null) {
          return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        } else {
          return fishRodBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public Builder setFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishRod_ = value;
          onChanged();
        } else {
          fishRodBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public Builder setFishRod(
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (fishRodBuilder_ == null) {
          fishRod_ = builderForValue.build();
          onChanged();
        } else {
          fishRodBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public Builder mergeFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (fishRod_ != null) {
            fishRod_ =
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.newBuilder(fishRod_).mergeFrom(value).buildPartial();
          } else {
            fishRod_ = value;
          }
          onChanged();
        } else {
          fishRodBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public Builder clearFishRod() {
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
          onChanged();
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder getFishRodBuilder() {
        
        onChanged();
        return getFishRodFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
        if (fishRodBuilder_ != null) {
          return fishRodBuilder_.getMessageOrBuilder();
        } else {
          return fishRod_ == null ?
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        }
      }
      /**
       * <code>.model.FishRod fishRod = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
          getFishRodFieldBuilder() {
        if (fishRodBuilder_ == null) {
          fishRodBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder>(
                  getFishRod(),
                  getParentForChildren(),
                  isClean());
          fishRod_ = null;
        }
        return fishRodBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumePlayerItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumePlayerItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumePlayerItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumePlayerItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumePlayerItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumePlayerItemsList() {
        if (consumePlayerItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumePlayerItems_);
        } else {
          return consumePlayerItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public int getConsumePlayerItemsCount() {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.size();
        } else {
          return consumePlayerItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index) {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.get(index);
        } else {
          return consumePlayerItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder setConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.set(index, value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder setConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder addConsumePlayerItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder addConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(index, value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder addConsumePlayerItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder addConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder addAllConsumePlayerItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumePlayerItems_);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder clearConsumePlayerItems() {
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public Builder removeConsumePlayerItems(int index) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.remove(index);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumePlayerItemsBuilder(
          int index) {
        return getConsumePlayerItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
          int index) {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.get(index);  } else {
          return consumePlayerItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumePlayerItemsOrBuilderList() {
        if (consumePlayerItemsBuilder_ != null) {
          return consumePlayerItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumePlayerItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumePlayerItemsBuilder() {
        return getConsumePlayerItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumePlayerItemsBuilder(
          int index) {
        return getConsumePlayerItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumePlayerItemsBuilderList() {
        return getConsumePlayerItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumePlayerItemsFieldBuilder() {
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumePlayerItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumePlayerItems_ = null;
        }
        return consumePlayerItemsBuilder_;
      }

      private int nextFishCsvId_ ;
      /**
       * <code>int32 nextFishCsvId = 5;</code>
       * @return The nextFishCsvId.
       */
      @java.lang.Override
      public int getNextFishCsvId() {
        return nextFishCsvId_;
      }
      /**
       * <code>int32 nextFishCsvId = 5;</code>
       * @param value The nextFishCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setNextFishCsvId(int value) {
        
        nextFishCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 nextFishCsvId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextFishCsvId() {
        
        nextFishCsvId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.battle.WinFishResult)
    }

    // @@protoc_insertion_point(class_scope:response.battle.WinFishResult)
    private static final ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult();
    }

    public static ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<WinFishResult>
        PARSER = new com.google.protobuf.AbstractParser<WinFishResult>() {
      @java.lang.Override
      public WinFishResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<WinFishResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<WinFishResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.battle.WinFishResultProto.WinFishResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_battle_WinFishResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_battle_WinFishResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#response/battle/WinFishResult.proto\022\017r" +
      "esponse.battle\032\022model/Player.proto\032\024mode" +
      "l/FishItem.proto\032\026model/PlayerItem.proto" +
      "\032\023model/FishRod.proto\"\270\001\n\rWinFishResult\022" +
      "\035\n\006player\030\001 \001(\0132\r.model.Player\022!\n\010fishIt" +
      "em\030\002 \001(\0132\017.model.FishItem\022\037\n\007fishRod\030\003 \001" +
      "(\0132\016.model.FishRod\022-\n\022consumePlayerItems" +
      "\030\004 \003(\0132\021.model.PlayerItem\022\025\n\rnextFishCsv" +
      "Id\030\005 \001(\005BY\n)ehooray.fishing.dto.proto.re" +
      "sponse.battleB\022WinFishResultProtoH\002\252\002\025Fi" +
      "shing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.PlayerProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor(),
        });
    internal_static_response_battle_WinFishResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_battle_WinFishResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_battle_WinFishResult_descriptor,
        new java.lang.String[] { "Player", "FishItem", "FishRod", "ConsumePlayerItems", "NextFishCsvId", });
    ehooray.fishing.dto.proto.model.PlayerProto.getDescriptor();
    ehooray.fishing.dto.proto.model.FishItemProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
    ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
