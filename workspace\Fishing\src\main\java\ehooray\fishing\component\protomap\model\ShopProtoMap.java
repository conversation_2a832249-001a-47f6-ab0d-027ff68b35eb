package ehooray.fishing.component.protomap.model;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.dto.proto.model.ShopProto;
import ehooray.fishing.entity.ShopEntity;
import ehooray.fishing.pojo.csv.DisplayCsv;

@Component
public class ShopProtoMap
{
    @Autowired
    protected ScheduleCalculator scheduleCalculator;
    
    public ShopProto.Shop.Builder toBuilderByEntity(ShopEntity shop, LocalDateTime now)
    {
        ShopProto.Shop.Builder builder = ShopProto.Shop.newBuilder();
        builder.setEntityId(shop.getId());
        builder.setCsvId(shop.getCsvId());
        builder.setCommodityCsvId(shop.getCommodityCsvId());
        builder.setBuyCount(shop.getBuyCount());
        if(shop.getStartTime().isPresent())
            builder.setStartTime(shop.getStartTime().get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

//        builder.setQuestState(shop.getQuestState());
//        if(shop.getQuestFinishTime().isPresent())
//            builder.setQuestFinishTime(shop.getQuestFinishTime().get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
//        builder.setCharacterEntityId(shop.getDispatchCharacterId());

        Optional<LocalDateTime> nextStartTime = scheduleCalculator.getNextCycleStartDate(DisplayCsv.Type.Shop, shop.getCsvId(), now);
        if(nextStartTime.isPresent())
            builder.setNextStartTime(nextStartTime.get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        return builder;
    }

    public Any toAnyByEntity(ShopEntity shop, LocalDateTime now)
    {
        return Any.pack(toBuilderByEntity(shop, now).build());
    }

    public ArrayList<Any> toAnyByEntities(List<ShopEntity> shops, LocalDateTime now)
    {
        ArrayList<Any> shopProtos = new ArrayList<>(shops.size());
        for(ShopEntity shop : shops)
            shopProtos.add(toAnyByEntity(shop, now));

        return shopProtos;
    }
}
