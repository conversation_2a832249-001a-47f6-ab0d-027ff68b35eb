---
description: Complete architecture guide for the fishing game server project, including MVC layered structure, functional module distribution, configuration file explanations, and core entry points to help developers quickly understand the overall project structure and code organization.
globs: 
alwaysApply: false
---
# 钓鱼游戏服务器项目结构指南

## 项目概述
这是一个基于 Spring Boot 的钓鱼游戏服务器项目，采用典型的 MVC 架构模式。

## 核心入口点
- 应用程序主入口：[ApplicationMain.java](mdc:src/main/java/ehooray/fishing/ApplicationMain.java)
- Servlet 初始化器：[ServletInitializer.java](mdc:src/main/java/ehooray/fishing/ServletInitializer.java)

## 主要模块架构

### 控制器层 (Controller)
位置：`src/main/java/ehooray/fishing/controller/`
负责处理 HTTP 请求和协议通信，包含以下游戏功能模块：
- **认证系统** (`auth/`) - 用户登录验证
- **玩家管理** (`player/`) - 玩家数据和状态管理
- **战斗系统** (`battle/`) - 钓鱼战斗逻辑
- **物品系统** (`item/`) - 游戏物品管理
- **鱼竿系统** (`fishrod/`) - 钓鱼工具管理
- **鱼套装系统** (`fishkit/`) - 钓鱼装备套装
- **商店系统** (`shop/`) - 游戏内商店
- **抽奖系统** (`gacha/`) - 游戏抽奖功能
- **任务系统** (`quest/`) - 游戏任务和成就
- **兑换系统** (`exchange/`) - 物品兑换功能
- **时间系统** (`time/`) - 游戏时间管理
- **AP系统** (`ap/`) - 行动点数管理

### 服务层 (Service)
位置：`src/main/java/ehooray/fishing/service/`
包含业务逻辑实现，与控制器层模块对应：
- 认证服务 (`auth/`)
- 玩家服务 (`player/`)
- 战斗服务 (`battle/`)
- 物品服务 (`item/`)
- 鱼竿服务 (`fishrod/`)
- 鱼套装服务 (`fishkit/`)
- 商店服务 (`shop/`)
- 抽奖服务 (`gacha/`)
- 任务服务 (`quest/`)
- 兑换服务 (`exchange/`)
- 服务器管理 (`server/`)
- 应用内购买 (`iab/`)

### 数据访问层 (Repository)
位置：`src/main/java/ehooray/fishing/repository/`
负责数据持久化和数据库操作

### 实体层 (Entity)
位置：`src/main/java/ehooray/fishing/entity/`
定义数据库实体和数据模型

### 数据传输对象 (DTO & POJO)
- **DTO**: `src/main/java/ehooray/fishing/dto/` - 数据传输对象
- **POJO**: `src/main/java/ehooray/fishing/pojo/` - 简单数据对象

### 配置和工具
- **配置类**: `src/main/java/ehooray/fishing/config/` - Spring 配置
- **组件**: `src/main/java/ehooray/fishing/component/` - 自定义组件
- **注解**: `src/main/java/ehooray/fishing/annotation/` - 自定义注解
- **日志**: `src/main/java/ehooray/fishing/log/` - 日志处理

## 配置文件
- **应用配置**: [application.yml](mdc:src/main/resources/application.yml) - 主要应用配置
- **日志配置**: [log4j2.yml](mdc:src/main/resources/log4j2.yml) - 日志框架配置
- **缓存配置**: [hibernate-ehcache.xml](mdc:src/main/resources/hibernate-ehcache.xml) - Hibernate 缓存配置
- **事务配置**: [jta.properties](mdc:src/main/resources/jta.properties) - JTA 事务配置
- **Firebase配置**: [fishing-firebase.json](mdc:src/main/resources/fishing-firebase.json) - Firebase 集成配置

## 游戏功能模块说明
1. **核心钓鱼系统**: 包括鱼竿、鱼套装、战斗系统
2. **经济系统**: 商店、物品、兑换功能
3. **进度系统**: 任务、成就、玩家等级
4. **抽奖系统**: Gacha 抽奖机制
5. **用户管理**: 认证、玩家数据管理
6. **资源管理**: AP 系统、时间管理

## 开发注意事项
- 项目采用标准的 Spring Boot 结构
- 使用 JPA/Hibernate 进行数据持久化
- 集成 Firebase 服务
- 支持应用内购买 (IAB)
- 具备完整的日志和缓存机制

