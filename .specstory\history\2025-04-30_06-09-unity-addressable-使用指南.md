<!-- Generated by SpecStory -->

# Unity Addressable 使用指南 (2025-04-30 14:09:41)

_**User**_

如何使用Unity Addressable

---

_**Assistant**_



---

好的，Unity Addressable Assets System（简称 Addressables）是 Unity 提供的一个强大的资源管理系统。它允许你通过“地址”来引用和加载资源，而不是像传统方式那样直接将资源打包进 Build 中。这带来了很多好处，比如：

1.  **简化资源管理**： decoupling 资源与场景/代码的直接引用。
2.  **优化内存**：按需加载和卸载资源，减少内存峰值。
3.  **缩短构建时间**：修改资源后只需重新构建 Addressables 包，无需重新构建整个应用。
4.  **支持热更新**：可以从远程服务器下载更新后的资源包，无需重新安装应用。

以下是使用 Addressables 的基本步骤和概念：

**1. 安装 Addressables 包**

*   打开 Unity 编辑器，前往 `Window` > `Package Manager`。
*   在包列表中选择 `Unity Registry`。
*   搜索 `Addressables` 并安装它。

**2. 将资源标记为 Addressable**

*   在 `Project` 窗口中选中你想要管理的资源（例如 Prefab、贴图、材质、音频文件、甚至场景文件）。
*   在 `Inspector` 窗口中，勾选 `Addressable` 复选框。
*   Unity 会自动为该资源生成一个唯一的地址（通常是资源的路径），你也可以修改这个地址为一个更易读的自定义字符串（Key）。
*   被标记的资源会被自动添加到一个默认的 Addressable Group 中。你可以在 `Window` > `Asset Management` > `Addressables` > `Groups` 窗口中管理这些组。

**3. 在代码中加载 Addressable 资源**

Addressables 主要使用异步加载方式。以下是一些常用的加载方法：

*   **加载单个资源 (Asset)**：比如加载一个材质或 ScriptableObject。
    ```csharp
    using UnityEngine.AddressableAssets;
    using UnityEngine.ResourceManagement.AsyncOperations;
    using UnityEngine;

    public class LoadPrefabAsset : MonoBehaviour
    {
        // 使用资源的地址（可以是路径或自定义Key）
        public string assetAddress = "Assets/MyPrefabs/MyCube.prefab"; 
        private AsyncOperationHandle<GameObject> loadHandle;

        async void Start()
        {
            // 异步加载资源
            loadHandle = Addressables.LoadAssetAsync<GameObject>(assetAddress);
            
            // 等待加载完成
            await loadHandle.Task; 

            if (loadHandle.Status == AsyncOperationStatus.Succeeded)
            {
                GameObject prefab = loadHandle.Result;
                Debug.Log($"资源 {assetAddress} 加载成功!");
                // 在这里可以使用加载到的 prefab，例如实例化它
                // Instantiate(prefab); 
            }
            else
            {
                Debug.LogError($"资源 {assetAddress} 加载失败: {loadHandle.OperationException}");
            }
        }

        void OnDestroy()
        {
            // 释放加载的资源句柄，非常重要！
            if (loadHandle.IsValid())
            {
                Addressables.Release(loadHandle);
            }
        }
    }
    ```

*   **实例化 Prefab**：直接加载并实例化一个 Prefab。
    ```csharp
    using UnityEngine.AddressableAssets;
    using UnityEngine.ResourceManagement.AsyncOperations;
    using UnityEngine;

    public class InstantiateAddressablePrefab : MonoBehaviour
    {
        public string prefabAddress = "MyCustomCubeKey"; // 使用自定义Key或路径
        private AsyncOperationHandle<GameObject> instantiateHandle;

        async void Start()
        {
            // 异步实例化 Prefab
            instantiateHandle = Addressables.InstantiateAsync(prefabAddress, transform); // 可以指定父节点

            // 等待实例化完成
            await instantiateHandle.Task;

            if (instantiateHandle.Status == AsyncOperationStatus.Succeeded)
            {
                GameObject instance = instantiateHandle.Result;
                Debug.Log($"Prefab {prefabAddress} 实例化成功!");
            }
            else
            {
                Debug.LogError($"Prefab {prefabAddress} 实例化失败: {instantiateHandle.OperationException}");
            }
        }

        void OnDestroy()
        {
            // 释放实例化的对象及其资源句柄
            if (instantiateHandle.IsValid())
            {
                 // 注意：这里是 ReleaseInstance
                Addressables.ReleaseInstance(instantiateHandle);
            }
        }
    }
    ```

*   **加载场景 (Scene)**：
    ```csharp
    using UnityEngine.AddressableAssets;
    using UnityEngine.SceneManagement;
    using UnityEngine.ResourceManagement.AsyncOperations;
    using UnityEngine.ResourceManagement.ResourceProviders; // 需要引入这个命名空间
    using UnityEngine;

    public class LoadAddressableScene : MonoBehaviour
    {
        // 场景文件的地址
        public string sceneAddress = "Assets/Scenes/MyAddressableScene.unity"; 

        async void Start()
        {
            // 异步加载场景 (LoadSceneMode.Additive 或 LoadSceneMode.Single)
            AsyncOperationHandle<SceneInstance> loadHandle = Addressables.LoadSceneAsync(sceneAddress, LoadSceneMode.Additive);

            // 等待场景加载完成
            await loadHandle.Task;

            if (loadHandle.Status == AsyncOperationStatus.Succeeded)
            {
                Debug.Log($"场景 {sceneAddress} 加载成功!");
                // SceneInstance sceneInstance = loadHandle.Result;
                // 可以选择激活场景: sceneInstance.ActivateAsync();
            }
            else
            {
                Debug.LogError($"场景 {sceneAddress} 加载失败: {loadHandle.OperationException}");
            }

            // 注意：场景卸载通常使用 Addressables.UnloadSceneAsync(loadHandle) 或标准的 SceneManager.UnloadSceneAsync
        }
    }
    ```

**4. 释放资源**

加载的 Addressable 资源不会被 Unity 自动卸载（即使切换场景），你需要手动释放它们以避免内存泄漏。

*   对于通过 `LoadAssetAsync` 或类似方法加载的资源，使用 `Addressables.Release(handle)` 来释放，其中 `handle` 是加载时返回的 `AsyncOperationHandle`。
*   对于通过 `InstantiateAsync` 实例化的对象，使用 `Addressables.ReleaseInstance(handle)` 或 `Addressables.ReleaseInstance(gameObject)` 来销毁实例并释放其资源。

**5. 构建 Addressables 内容**

当你标记了资源并编写了加载代码后，需要构建 Addressables 内容包 (AssetBundles)。

*   打开 `Window` > `Asset Management` > `Addressables` > `Groups` 窗口。
*   在窗口顶部菜单中选择 `Build` > `New Build` > `Default Build Script`。
*   这会在你的项目目录下生成 Addressables 内容包（默认在 `Library/com.unity.addressables`，构建播放器时会复制到 StreamingAssets 或远程目录）。

**总结**

Addressables 是一个功能丰富且灵活的系统。上面介绍的是最基本的使用方法。深入使用还包括：

*   配置不同的 Group 和 Profile 来管理本地和远程资源。
*   使用 Label 来批量加载一类资源。
*   管理资源的依赖关系。
*   实现资源的热更新流程。

建议查阅 Unity 官方文档以获取更详细的信息和高级用法：[https://docs.unity3d.com/Packages/com.unity.addressables@latest/index.html](https://docs.unity3d.com/Packages/com.unity.addressables@latest/index.html)

---

