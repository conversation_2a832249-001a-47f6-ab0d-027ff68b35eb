package ehooray.fishing.pojo.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public final class ReflectionUtils
{
    public static List<Field> getDeclaredFields(Class<?> stopSearchClass, Class<?> currentClass)
    {
        if(currentClass == null) return List.of();
        if(stopSearchClass == null) return List.of();
        // make sure current class is final search
        if(stopSearchClass == currentClass)
            stopSearchClass = null;
        
        List<Field> results = new ArrayList<Field>(getDeclaredFields(stopSearchClass, currentClass.getSuperclass()));
        Field[] currentClassFields = currentClass.getDeclaredFields();
        for(int i = 0; i < currentClassFields.length; i++)
            results.add(currentClassFields[i]);
        return results;
    }
}
