package ehooray.fishing.service.battle;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.utils.WeightRandomer;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.SpotEntity;
import ehooray.fishing.pojo.battle.HitArea;
import ehooray.fishing.pojo.csv.FishCsv;
import ehooray.fishing.pojo.csv.FishGroupCsv;
import ehooray.fishing.pojo.csv.SpotCsv;
import ehooray.fishing.pojo.csv.VariableCsv;
import ehooray.fishing.repository.PlayerRepository;
import ehooray.fishing.repository.SpotRepository;

@Service
public class SpotService
{
    protected final CsvContainer csvContainer;
    protected final SpotRepository spotRepository;
    protected final PlayerRepository playerRepository;
    protected final WeightRandomer weightRandomer;
    
    @Autowired
    public SpotService(CsvContainer csvContainer, SpotRepository spotRepository, PlayerRepository playerRepository,
            WeightRandomer weightRandomer)
    {
        this.csvContainer = csvContainer;
        this.spotRepository = spotRepository;
        this.playerRepository = playerRepository;
        this.weightRandomer = weightRandomer;
    }
    
    public List<SpotEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getSpots();
    }
    
    public Optional<SpotEntity> findByCsvId(PlayerEntity player, int csvId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getCsvId() == csvId).findFirst();
    }
    
    /*
     * createSpot and random first fish, random next fish after awardFish
     */
    public SpotEntity createSpot(PlayerEntity player, SpotCsv.Row spotRow)
    {
        var newSpot = new SpotEntity();
        newSpot.setCsvId(spotRow.getId());
        newSpot.setFishCsvId(randomFish(player, spotRow, player.getActiveFishKit().getFishRod().getLucky()));
        newSpot.setHitArea(0);
        newSpot.setRetryFish(0);
        newSpot.setPlayer(player);
        getAllByPlayer(player).add(newSpot);
        spotRepository.save(newSpot);
        return newSpot;
    }
    
    public int randomFish(PlayerEntity player, SpotCsv.Row spotRow, int lucky)
    {
        var fishGroup = csvContainer.getCsv(FishGroupCsv.class).findAllByGroup(spotRow.getFishGroup());
        int[] weights = new int[fishGroup.size()];
        for (int i = 0; i < weights.length; i++)
        {
            var fishRow = csvContainer.getCsv(FishCsv.class).getById(fishGroup.get(i).getFish());
            float luckyWeight = csvContainer.getCsv(VariableCsv.class).getLuckyWeightMf(FishCsv.Rare.valueOf(fishRow.getRare()).get());
            int baseWeight = fishGroup.get(i).getWeight();
            long bonusWeight = Math.round(baseWeight * (1 + Math.pow(lucky, luckyWeight) * 0.01f));
            weights[i] = Math.toIntExact(bonusWeight);
        }
            
        int weightIndex = weightRandomer.random(weights);
        var fishCsvId = fishGroup.get(weightIndex).getFish();
        return fishCsvId;
    }
    
    /**
     * startFish to assign hit area
     */
    public void startFish(SpotEntity spot, HitArea hitArea)
    {
        spot.setHitArea(hitArea.getId());
        spotRepository.save(spot);
    }
    
    /**
     * finish fishing, then random next fishCsvId
     */
    public void endFish(PlayerEntity player, SpotCsv.Row spotRow)
    {
        var spot = player.getBattleSpot();
        spot.setFishCsvId(randomFish(player, spotRow, player.getActiveFishKit().getFishRod().getLucky()));
        spot.setHitArea(0);
        spot.setRetryFish(0);
        
        spotRepository.save(spot);
    }
    
    public void retryFish(SpotEntity spot, int retry)
    {
        spot.setRetryFish(retry);
        spotRepository.save(spot);
    }
}
