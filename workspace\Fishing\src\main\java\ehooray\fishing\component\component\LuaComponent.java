package ehooray.fishing.component.component;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.Varargs;
import org.luaj.vm2.lib.jse.JsePlatform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.lua.LuaCacheContainer;
import ehooray.fishing.component.trigger.BaseTrigger;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.ComponentCsv;


@Component
public class LuaComponent extends BasePassiveComponent
{
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected LuaCacheContainer luaCacheContainer;
    
    @Override
    public Object invoke(BaseTrigger.Input input, ComponentCsv.Row componentRow) throws Exception
    {
        LuaTable luaTable = LuaTable.tableOf();
        luaTable.set("TriggerInput", input.toLua());
        luaTable.set("ComponentCsvId", LuaValue.valueOf(componentRow.getId()));
        luaTable.set("Service", luaCacheContainer.toLua());
        
        String script = csvContainer.getLua(componentRow.getLua());
        Globals globals = JsePlatform.standardGlobals();
        globals.load(script).call();
        
        LuaValue function = globals.get(componentRow.getLua());
        Varargs result = function.invoke(luaTable);
        
        return convertToJavaObjects(result);
    }
    
    protected Object[] convertToJavaObjects(Varargs value) throws ExceptionByCsvId
    {
        Object[] results = new Object[value.narg()];
        for(int i = 0; i < results.length; i++)
            results[i] = convertToJavaObject(value.arg(i + 1));
        
        return results;
    }
    
    protected Object convertToJavaObject(LuaValue value) throws ExceptionByCsvId
    {
        if(value.isboolean()) return value.toboolean();
        if(value.isint()) return value.toint();
        if(value.isnumber()) return value.todouble();
        
        throw new ExceptionByCsvId("not support lua value type = " + value.typename());
    }
}
