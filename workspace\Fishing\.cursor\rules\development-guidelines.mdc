---
description: Project development standards and best practices guide, covering coding conventions, Protobuf + WebSocket communication architecture, database design, security considerations, performance optimization, testing strategies, and other development standards to ensure code quality and team collaboration efficiency.
globs: 
alwaysApply: false
---
# 钓鱼游戏服务器开发规范

## 编码规范

### 包命名规范
- 控制器：`ehooray.fishing.controller.{模块名}`
- 服务：`ehooray.fishing.service.{模块名}`
- 实体：`ehooray.fishing.entity`
- DTO：`ehooray.fishing.dto.{模块名}`
- 配置：`ehooray.fishing.config`

### 类命名规范
- 控制器类：`{功能名}Controller` (如 PlayerController)
- 服务类：`{功能名}Service` (如 PlayerService)
- 实体类：`{实体名}Entity` (如 PlayerEntity)
- DTO类：`{功能名}{方向}Dto` (如 PlayerRequestDto, PlayerResponseDto)
- Proto Handler类：`{功能名}ProtoHandler` (如 AuthProtoHandler)

## 数据传输架构

### Google Protobuf 规范
- **数据序列化**: 使用 Google Protobuf 作为主要的数据载体
- **DTO 生成**: DTO 类是从 Google Protobuf 编译工具自动生成的 Java 类
- **不使用 JSON**: 项目不采用 JSON 格式进行数据传输
- **二进制传输**: 传输 Protobuf 序列化后的二进制数据

### Proto 文件管理
- Proto 定义文件应统一管理
- 遵循 Protobuf 命名规范 (snake_case)
- 版本兼容性考虑 (向前兼容和向后兼容)
- 字段编号管理 (不可重复使用已删除字段的编号)

### WebSocket 通信协议
- **传输协议**: 使用 WebSocket 而非传统 HTTP 进行数据传输
- **实时通信**: 支持服务器主动推送消息给客户端
- **连接管理**: 维护客户端连接状态和会话管理
- **协议处理**: 参考 [HttpProtoHandler.java](mdc:src/main/java/ehooray/fishing/controller/HttpProtoHandler.java)

### API 设计规范
- **协议格式**: 基于 Protobuf 的二进制协议设计
- **消息类型**: 定义请求/响应消息类型
- **错误处理**: 统一的错误码和错误消息格式
- **版本控制**: Protobuf 版本兼容性策略

### 序列化和反序列化
- 使用 Protobuf 的 `parseFrom()` 方法进行反序列化
- 使用 `toByteArray()` 方法进行序列化
- 处理序列化异常和数据校验
- 性能优化：重用 Builder 对象

## 数据库设计

### 实体关系
- 玩家 (Player) 是核心实体
- 物品 (Item) 系统支持多种类型
- 鱼竿 (FishRod) 和鱼套装 (FishKit) 是装备系统
- 任务 (Quest) 系统支持多种完成条件

### 事务管理
- 使用 `@Transactional` 注解管理事务
- 读操作使用 `readOnly = true`
- 复杂业务操作确保事务一致性

## 安全考虑

### 认证授权
- 参考认证控制器：[AuthProtoHandler.java](mdc:src/main/java/ehooray/fishing/controller/AuthProtoHandler.java)
- Firebase 认证集成
- Token 验证机制
- WebSocket 连接认证

### 数据验证
- Protobuf 消息格式验证
- 业务规则验证
- WebSocket 消息安全检查
- 防止恶意客户端攻击

## 性能优化

### 缓存策略
- 使用 Hibernate 二级缓存：[hibernate-ehcache.xml](mdc:src/main/resources/hibernate-ehcache.xml)
- 频繁查询的数据进行缓存
- 缓存失效策略

### 数据库优化
- 合理使用索引
- 避免 N+1 查询问题
- 使用分页查询

### WebSocket 性能优化
- 连接池管理
- 消息批处理
- 压缩大型消息
- 连接复用策略

### Protobuf 优化
- 重用 Builder 对象减少 GC 压力
- 避免不必要的字段序列化
- 合理设计消息结构

## 日志记录

### 日志配置
- 主要日志配置：[log4j2.yml](mdc:src/main/resources/log4j2.yml)
- 分级日志记录
- 结构化日志格式

### 日志内容
- 关键业务操作日志
- 错误异常日志
- 性能监控日志
- WebSocket 连接和断开日志
- Protobuf 序列化错误日志

## 测试策略

### 单元测试
- 测试目录：`src/test/java/`
- 服务层业务逻辑测试
- Mock 外部依赖
- Protobuf 消息测试

### 集成测试
- WebSocket 连接测试
- Protobuf 序列化/反序列化测试
- 端到端协议测试

### 协议测试
- Protobuf 消息格式验证
- WebSocket 消息传输测试
- 协议兼容性测试

## 配置管理

### 环境配置
- 开发环境配置
- 生产环境配置
- 配置文件分离

### 外部服务配置
- Firebase 配置：[fishing-firebase.json](mdc:src/main/resources/fishing-firebase.json)
- 数据库连接配置
- WebSocket 服务器配置
- 第三方服务集成

## 部署和运维

### 应用启动
- 主入口：[ApplicationMain.java](mdc:src/main/java/ehooray/fishing/ApplicationMain.java)
- 支持应用重启功能
- 优雅关闭机制
- WebSocket 服务器启动

### 监控指标
- 应用健康检查
- 性能指标监控
- 业务指标统计
- WebSocket 连接数监控
- Protobuf 消息处理性能监控



