package ehooray.fishing.component.component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.trigger.BaseTrigger;
import ehooray.fishing.component.trigger.ConditionComponentTrigger;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.ComponentCsv;

@Component
public class ConditionComponentFilter
{

    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected ConditionComponentTrigger conditionTrigger;

    public <T> void removeFalsedRows(PlayerEntity player, List<T> rows, Function<T, Integer> conditionComponentIdGetter) throws Exception
    {
        ComponentCsv componentCsv = csvContainer.getCsv(ComponentCsv.class);

        List<ComponentCsv.Row> tempList = Arrays.asList((ComponentCsv.Row)null);
        BaseTrigger.Input triggerInput = new BaseTrigger.Input();
        triggerInput.setPlayerId(player.getId());
        for(int i = rows.size() - 1; i >= 0; i--)
        {
            T row = rows.get(i);
            Optional<ComponentCsv.Row> conditionComponentRow = componentCsv.getOptionalById(conditionComponentIdGetter.apply(row));
            if(conditionComponentRow.isEmpty()) continue;

            tempList.set(0, conditionComponentRow.get());
            boolean isOpened = (boolean)conditionTrigger.invoke(triggerInput, tempList);
            if(isOpened) continue;

            rows.remove(i);
        }
    }
    
    public <T> void validateCondition(PlayerEntity player, List<T> rows, Function<T, Integer> conditionComponentIdGetter, String exceptionMessage) throws Exception
    {
        ComponentCsv componentCsv = csvContainer.getCsv(ComponentCsv.class);
        
        List<ComponentCsv.Row> tempList = Arrays.asList((ComponentCsv.Row)null);
        BaseTrigger.Input triggerInput = new BaseTrigger.Input();
        triggerInput.setPlayerId(player.getId());
        for(int i = rows.size() - 1; i >= 0; i--)
        {
            T row = rows.get(i);
            Optional<ComponentCsv.Row> conditionComponentRow = componentCsv.getOptionalById(conditionComponentIdGetter.apply(row));
            if(conditionComponentRow.isEmpty()) continue;
            
            tempList.set(0, conditionComponentRow.get());
            boolean isOpened = (boolean)conditionTrigger.invoke(triggerInput, tempList);
            if(!isOpened)
                throw new ExceptionByCsvId(exceptionMessage);
        }
        
    }

}
