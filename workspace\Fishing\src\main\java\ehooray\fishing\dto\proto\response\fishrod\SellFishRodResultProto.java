// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/fishrod/SellFishRodResult.proto

package ehooray.fishing.dto.proto.response.fishrod;

public final class SellFishRodResultProto {
  private SellFishRodResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SellFishRodResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.fishrod.SellFishRodResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return Whether the fishRod field is set.
     */
    boolean hasFishRod();
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return The fishRod.
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod();
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder();

    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getProduceItemsList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index);
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    int getProduceItemsCount();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.fishrod.SellFishRodResult}
   */
  public static final class SellFishRodResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.fishrod.SellFishRodResult)
      SellFishRodResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SellFishRodResult.newBuilder() to construct.
    private SellFishRodResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SellFishRodResult() {
      produceItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SellFishRodResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.internal_static_response_fishrod_SellFishRodResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.internal_static_response_fishrod_SellFishRodResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.Builder.class);
    }

    public static final int FISHROD_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return Whether the fishRod field is set.
     */
    @java.lang.Override
    public boolean hasFishRod() {
      return fishRod_ != null;
    }
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return The fishRod.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
      return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
    }
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
      return getFishRod();
    }

    public static final int PRODUCEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_;
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public int getProduceItemsCount() {
      return produceItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
      return produceItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index) {
      return produceItems_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.fishrod.SellFishRodResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.fishrod.SellFishRodResult)
        ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.internal_static_response_fishrod_SellFishRodResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.internal_static_response_fishrod_SellFishRodResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getProduceItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.internal_static_response_fishrod_SellFishRodResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult build() {
        ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult buildPartial() {
        ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult result = new ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult(this);
        int from_bitField0_ = bitField0_;
        if (fishRodBuilder_ == null) {
          result.fishRod_ = fishRod_;
        } else {
          result.fishRod_ = fishRodBuilder_.build();
        }
        if (produceItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            produceItems_ = java.util.Collections.unmodifiableList(produceItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.produceItems_ = produceItems_;
        } else {
          result.produceItems_ = produceItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> fishRodBuilder_;
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       * @return Whether the fishRod field is set.
       */
      public boolean hasFishRod() {
        return fishRodBuilder_ != null || fishRod_ != null;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       * @return The fishRod.
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
        if (fishRodBuilder_ == null) {
          return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        } else {
          return fishRodBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder setFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishRod_ = value;
          onChanged();
        } else {
          fishRodBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder setFishRod(
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (fishRodBuilder_ == null) {
          fishRod_ = builderForValue.build();
          onChanged();
        } else {
          fishRodBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder mergeFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (fishRod_ != null) {
            fishRod_ =
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.newBuilder(fishRod_).mergeFrom(value).buildPartial();
          } else {
            fishRod_ = value;
          }
          onChanged();
        } else {
          fishRodBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder clearFishRod() {
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
          onChanged();
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder getFishRodBuilder() {
        
        onChanged();
        return getFishRodFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
        if (fishRodBuilder_ != null) {
          return fishRodBuilder_.getMessageOrBuilder();
        } else {
          return fishRod_ == null ?
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        }
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
          getFishRodFieldBuilder() {
        if (fishRodBuilder_ == null) {
          fishRodBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder>(
                  getFishRod(),
                  getParentForChildren(),
                  isClean());
          fishRod_ = null;
        }
        return fishRodBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_ =
        java.util.Collections.emptyList();
      private void ensureProduceItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          produceItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(produceItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> produceItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
        if (produceItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(produceItems_);
        } else {
          return produceItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public int getProduceItemsCount() {
        if (produceItemsBuilder_ == null) {
          return produceItems_.size();
        } else {
          return produceItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);
        } else {
          return produceItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.set(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder addAllProduceItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, produceItems_);
          onChanged();
        } else {
          produceItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder clearProduceItems() {
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public Builder removeProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.remove(index);
          onChanged();
        } else {
          produceItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
          int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);  } else {
          return produceItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getProduceItemsOrBuilderList() {
        if (produceItemsBuilder_ != null) {
          return produceItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(produceItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder() {
        return getProduceItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getProduceItemsBuilderList() {
        return getProduceItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getProduceItemsFieldBuilder() {
        if (produceItemsBuilder_ == null) {
          produceItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  produceItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          produceItems_ = null;
        }
        return produceItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.fishrod.SellFishRodResult)
    }

    // @@protoc_insertion_point(class_scope:response.fishrod.SellFishRodResult)
    private static final ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult();
    }

    public static ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SellFishRodResult>
        PARSER = new com.google.protobuf.AbstractParser<SellFishRodResult>() {
      @java.lang.Override
      public SellFishRodResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SellFishRodResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SellFishRodResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto.SellFishRodResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_fishrod_SellFishRodResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_fishrod_SellFishRodResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(response/fishrod/SellFishRodResult.pro" +
      "to\022\020response.fishrod\032\023model/FishRod.prot" +
      "o\032\026model/PlayerItem.proto\"]\n\021SellFishRod" +
      "Result\022\037\n\007fishRod\030\001 \001(\0132\016.model.FishRod\022" +
      "\'\n\014produceItems\030\002 \003(\0132\021.model.PlayerItem" +
      "B^\n*ehooray.fishing.dto.proto.response.f" +
      "ishrodB\026SellFishRodResultProtoH\002\252\002\025Fishi" +
      "ng.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_fishrod_SellFishRodResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_fishrod_SellFishRodResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_fishrod_SellFishRodResult_descriptor,
        new java.lang.String[] { "FishRod", "ProduceItems", });
    ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
