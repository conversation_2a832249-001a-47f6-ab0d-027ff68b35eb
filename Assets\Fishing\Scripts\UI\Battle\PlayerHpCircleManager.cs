using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using Zenject;

namespace Fishing
{
    public class PlayerHpCircleManager : MonoBehaviour
    {
        [SerializeField]
        Image hpCircle;
        [SerializeField]
        Text percentText;

        [SerializeField]
        int maxAmount = 10000;
        [SerializeField]
        float currentAmount = 0;
        [SerializeField]
        float reduceHpPerTime = 50;
        [SerializeField]
        int delayTime = 1000;

        CancellationTokenSource cancellationTokenSource;

        #region Zenject Inejct
        FishingManager fishingManager;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager
        )
        {
            this.fishingManager = fishingManager;
        }

        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }

        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }

        public void CloseRoot()
        {
            cancellationTokenSource?.Cancel();
        }

        public async UniTask Play()
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Dispose();
            }
            cancellationTokenSource = new CancellationTokenSource();

            currentAmount = maxAmount;

            try
            {
                while (!cancellationTokenSource.IsCancellationRequested)
                {
                    await UniTask.Delay(delayTime, delayTiming: PlayerLoopTiming.FixedUpdate, cancellationToken: cancellationTokenSource.Token);
                    currentAmount -= reduceHpPerTime;
                    currentAmount = Mathf.Clamp(currentAmount, 0, maxAmount);
                    RefreshHpCircle();
                    percentText.text = (hpCircle.fillAmount * 100).ToString("0");
                    if(currentAmount <= 0)
                    {
                        Debug.Log("YOU DIED");
                        fishingManager.TransitionToState(FishingState.Fail).Forget();
                    }
                }
                
            }
            catch (Exception e)
            {
                //Debug.LogError(e.Message);
            }
        }

        void RefreshHpCircle()
        {
            hpCircle.fillAmount = (float)currentAmount / (float)maxAmount;
        }

        public void SetReducePerTime(float value)
        {
            reduceHpPerTime = value;
        }

        public void SetDelayTime(int millionSeconds)
        {
            delayTime = millionSeconds;
        }

        public void SetFishData(FishTableData data)
        {
            reduceHpPerTime = data.Reducehppertime;
        }
    }
}