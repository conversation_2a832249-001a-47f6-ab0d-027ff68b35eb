package ehooray.fishing.pojo.lua;

import org.apache.logging.log4j.LogManager;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.lib.TwoArgFunction;

public abstract class CatchExceptionTwoArgFunction extends TwoArgFunction
{
    public abstract LuaValue doCall(LuaValue arg1, LuaValue arg2) throws Exception;
    @Override
    public LuaValue call(LuaValue arg1, LuaValue arg2)
    {
        try
        {
            return doCall(arg1, arg2);
        }
        catch(Exception e)
        {
            LogManager.getLogger(this.getClass()).error(e);

            return LuaValue.NIL;
        }
    }



}
