package ehooray.fishing.service.item;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.battle.HitArea;
import ehooray.fishing.pojo.csv.FishCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.VariableCsv;
import ehooray.fishing.pojo.event.FishItemEvent;
import ehooray.fishing.pojo.event.SellFishEvent;
import ehooray.fishing.pojo.utils.MathUtils;
import ehooray.fishing.repository.FishItemRepository;

@Service
public class FishItemService
{
    final static int BaitBonusParamIndex = 0;
    
    @Autowired
    protected ApplicationEventPublisher publisher;
    
    protected final CsvContainer csvContainer;
    protected final FishItemRepository fishItemRepository;
    
    @Autowired
    public FishItemService(CsvContainer csvContainer, FishItemRepository fishItemRepository)
    {
        this.csvContainer = csvContainer;
        this.fishItemRepository = fishItemRepository;
    }
    
    public List<FishItemEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getFishItems();
    }

    public Optional<FishItemEntity> findByCsvId(PlayerEntity player, int csvId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getCsvId() == csvId).findFirst();
    }
    
    public Optional<FishItemEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }
    
    public List<FishItemEntity> findByEntityIds(PlayerEntity player, Iterable<Long> ids) throws Exception
    {
        var result = new ArrayList<FishItemEntity>();
        for (long id : ids)
        {
            var findRod = findByEntityId(player, id);
            if (findRod.isEmpty())
                throw new ExceptionByCsvId(String.format("id:%d is not in player fishItem:%d", id, player.getId()));
            result.add(findRod.get());
        }
        
        return result;
    }
    
    public FishItemEntity produceFish(PlayerEntity player, FishCsv.Row fishCsvRow, HitArea hitArea, LocalDateTime now)
    {
        var baitRow = csvContainer.getCsv(PlayerItemCsv.class).getById(player.getActiveFishKit().getBait().getCsvId());
        
        var fish = new FishItemEntity();
        fish.setCsvId(fishCsvRow.getId());
        fish.setSize(randomFishSize(fishCsvRow, hitArea, baitRow));
        fish.setPlayer(player);
        fish.setExpireTime(now.plusSeconds(fishCsvRow.getCountDown()));
        getAllByPlayer(player).add(fish);
        
        fishItemRepository.save(fish);
        publisher.publishEvent(new FishItemEvent(this, player, fish));
        return fish;
    }
    
    public FishItemEntity produceFishNFT(PlayerEntity player, FishCsv.Row fishCsvRow)
    {
        var fish = new FishItemEntity();
        fish.setCsvId(fishCsvRow.getId());
        fish.setPlayer(player);
        getAllByPlayer(player).add(fish);
        
        fishItemRepository.save(fish);
        publisher.publishEvent(new FishItemEvent(this, player, fish));
        return fish;
    }
    
    public void sellFish(PlayerEntity player, FishItemEntity fishItem, LocalDateTime now) throws Exception
    {
        checkFishExpireTime(fishItem, now);
      
        getAllByPlayer(player).remove(fishItem);
        fishItemRepository.delete(fishItem);
        publisher.publishEvent(new SellFishEvent(this, player, fishItem));
    }
    
    public void exchangeFish(PlayerEntity player, List<FishItemEntity> fishItems, LocalDateTime nowTime) throws Exception
    {
        for (var fish : fishItems)
        {
            var fishRow = csvContainer.getCsv(FishCsv.class).getById(fish.getCsvId());
            if (fishRow.isNFT())
                throw new ExceptionByCsvId("fish:" + fish.getId() + " isNFT");
            boolean outTime = fish.getExpireTime().isPresent() && nowTime.isAfter(fish.getExpireTime().get());
            if (outTime)
                throw new ExceptionByCsvId("fish:" + fish.getId() + " expired");
        }    
        
        deleteAll(player, fishItems);
    }
    
    public void deleteAll(PlayerEntity player, List<FishItemEntity> fishItems)
    {
        getAllByPlayer(player).removeAll(fishItems);
        fishItemRepository.deleteAll(fishItems);
    }
    
    /**
     * size = minSize + sizeRange * (randSize + hitBonus + baitBonus)
     */
    protected int randomFishSize(FishCsv.Row fishCsvRow, HitArea hitArea, PlayerItemCsv.Row baitRow)
    {
        int sizeRange = fishCsvRow.getMaxSize() - fishCsvRow.getMinSize();
        
        int randSize = MathUtils.randomRange(0, fishCsvRow.getRandSizePct() + 1);
        int hitBonus = 0;
        switch (hitArea)
        {
            case Gray: hitBonus = 0; break;
            case Light: hitBonus = csvContainer.getCsv(VariableCsv.class).getLightAddPct(); break;
            case Dark: hitBonus = csvContainer.getCsv(VariableCsv.class).getDarkAddPct(); break;
            default: hitBonus = 0; break;
        }
        int baitBonus = 0;
        if (baitRow.getTag() == PlayerItemCsv.Tag.Bait.getId())
        {
            baitBonus = baitRow.getParameterByInt(BaitBonusParamIndex);
        }
        
        float sizeDelata = sizeRange * MathUtils.clamp((randSize + hitBonus + baitBonus) * 0.001f, 0f, 1f);
        return fishCsvRow.getMinSize() + Math.round(sizeDelata);
    }
    
    private void checkFishExpireTime(FishItemEntity fishItem, LocalDateTime now) throws ExceptionByCsvId
    {
        if (now.isAfter(fishItem.getExpireTime().get()))
            throw new ExceptionByCsvId("fish:" + fishItem.getId() + " expired");
    }
}
