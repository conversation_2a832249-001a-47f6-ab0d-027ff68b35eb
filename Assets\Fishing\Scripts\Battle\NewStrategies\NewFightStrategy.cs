using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.NewStrategies
{
    /// <summary>
    /// 新釣魚戰鬥狀態策略
    /// 基於新的張力系統實現，使用新的張力條UI和加分區機制
    /// </summary>
    public class NewFightStrategy : FishingStrategyBase
    {
        private NewLineTensionManager newLineTensionManager;
        private QTECutinManager qteCutinManager;
        
        // QTE相关
        private bool isInQteMode = false; // 是否处于QTE模式
        private bool wasPressedBeforeQte = false; // QTE前是否按着按钮
        private bool isWaitingForQteInput = false; // 是否等待QTE输入
        private bool qteResult = false; // QTE结果
        
        // 引用FishingManager
        private FishingManager fishingManager;
        
        public NewFightStrategy() : base(FishingState.Fight) { }

        private bool onEnterReady = false;
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 保存FishingManager引用
            this.fishingManager = context;
            
            // 水效果和線動畫處理
            context.forceItem.PlayWaterEffectForFight();
            context.fishLine.StopLineAnimation();
            
            // 加載並獲取新張力管理器
            newLineTensionManager = await context.fishingGamePanelManager.OpenAsync<NewLineTensionManager>();
            
            // 確保捲線器UI可見
            if (!context.cordReelManager.gameObject.activeInHierarchy)
                await context.fishingGamePanelManager.OpenAsync<CordReelManager>();
            
            // 解綁可能存在的舊事件，避免重複監聽
            var trigger = FishingEventTrigger.GetTrigger(context.cordReelManager.pullButton.gameObject);
            trigger.onPointerDown -= newLineTensionManager.OnPullButtonPressed;
            trigger.onPointerUp -= newLineTensionManager.OnPullButtonUp;
            
            // 重新綁定事件到新的張力管理器
            trigger.onPointerDown += newLineTensionManager.OnPullButtonPressed;
            trigger.onPointerUp += newLineTensionManager.OnPullButtonUp;
            
            // 設置按鈕狀態
            context.cordReelManager.SetCordReelButtonColorNormal();
            context.cordReelManager.StartInteractive();
            
            // 更新魚信息顯示
            context.fishInfoManager.ResetText();
            context.fishInfoManager.SetLineLengthText(context.fish.lineLength + context.EquipData.lineData.CsvData.Parameter_1);
            // await context.fishingGamePanelManager.OpenAsync<FishInfoManager>();
            
            // 開始張力管理器
            newLineTensionManager.Play();
            onEnterReady = true;
            
            // 初始化QTE倒計時
            context.fish.ResetQteValues();
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 如果還沒進入，則不進行更新
            if (!onEnterReady)
                return;
                
            // 處理QTE輸入檢測（編輯器模式下的臨時方案）
            #if UNITY_EDITOR
            if (isInQteMode && isWaitingForQteInput)
            {
                Debug.Log("[QTE] 正在等待QTE輸入");
                // 按1代表QTE成功
                if (Input.GetKeyDown(KeyCode.Alpha1) || Input.GetKeyDown(KeyCode.Keypad1))
                {
                    Debug.Log("[QTE] 成功! 魚進入乏力狀態，收線速度增加50%，持續5秒");
                    qteResult = true;
                    isWaitingForQteInput = false;
                }
                // 按2代表QTE失敗
                else if (Input.GetKeyDown(KeyCode.Alpha2) || Input.GetKeyDown(KeyCode.Keypad2))
                {
                    Debug.Log("[QTE] 失敗! 繼續正常釣魚");
                    qteResult = false;
                    isWaitingForQteInput = false;
                }
            }
            #endif
                
            // 處理QTE邏輯
            if (isInQteMode)
            {
                return;
            }
            
            // 檢查是否需要觸發QTE
            context.fish.UpdateQteStatus(Time.deltaTime);
            
            // 如果魚觸發了QTE，進入QTE模式
            if (context.fish.isQteTriggered && !isInQteMode)
            {
                // 記錄QTE開始前的按鈕狀態
                wasPressedBeforeQte = newLineTensionManager.IsButtonPressed;
                
                // 進入QTE模式
                StartQteMode(context).Forget();
                return;
            }

            // 魚的基本行為
            context.fish.Move();
            context.fish.Leave();
            context.fishLine.StraightLine();
            context.forceItem.SetForce(40f);
            context.fishRod.KeepFacingFish();
            context.FightRotateCamera();
            
            // 新版張力檢測邏輯
            if (newLineTensionManager.IsValidFight())
            {
                context.fish.Fight();
            }
            
            // 更新魚距離
            context.fish.SetDistanceZ();
            
            // 檢查捕獲條件
            if (context.fish.distance <= context.fish.catchDistance)
            {
                context.TransitionToState(FishingState.Catch).Forget();
                return;
            }
            
            // 檢查失敗條件
            if (context.fish.distance >= context.fish.lineLength + context.EquipData.lineData.CsvData.Parameter_1)
            {
                context.TransitionToState(FishingState.Fail).Forget();
                return;
            }
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            // 清理QTE模式相关状态
            isInQteMode = false;
            wasPressedBeforeQte = false;
            isWaitingForQteInput = false;
            
            // 清理事件綁定
            return UniTask.CompletedTask;
        }

        private async UniTask InitLureActionSystemAsync(FishingManager context)
        {
            await context.fishingGamePanelManager.OpenAsync<LureActionSystem>();
            await UniTask.DelayFrame(1); // 等待一帧确保Canvas已经被正确设置
            List<int> times = new List<int> { 3 };
            context.lureActionSystem.InitAndStartLureMinigame(times.Count, times, QTECallback);
        }

        void QTECallback(LureActionSystem.QTEResult result, int round)
        {
            Debug.Log($"[QTE] 回调结果: {result}, 回合: {round}");
            
            // 根据QTE结果设置qteResult变量
            switch (result)
            {
                case LureActionSystem.QTEResult.Perfect:
                    Debug.Log("[QTE] 完美! 魚進入乏力狀態，收線速度增加75%，持續7秒");
                    qteResult = true;
                    // 設置完美成功等級為2
                    ShowQTECutinAnimation(2).Forget();
                    break;
                    
                case LureActionSystem.QTEResult.Good:
                    Debug.Log("[QTE] 良好! 魚進入乏力狀態，收線速度增加50%，持續5秒");
                    qteResult = true;
                    // 設置良好成功等級為1
                    ShowQTECutinAnimation(1).Forget();
                    break;
                    
                case LureActionSystem.QTEResult.Miss:
                default:
                    Debug.Log("[QTE] 失敗! 繼續正常釣魚");
                    qteResult = false;
                    break;
            }
            
            // 通知QTE输入已完成
            isWaitingForQteInput = false;
        }
        
        // 顯示QTE成功立繪的方法
        private async UniTask ShowQTECutinAnimation(int successLevel)
        {
            if (fishingManager == null)
            {
                Debug.LogError("[QTE] fishingManager is null, cannot show cutin animation");
                return;
            }
            
            // 如果qteCutinManager為空，先加載它
            if (qteCutinManager == null)
            {
                qteCutinManager = await fishingManager.fishingGamePanelManager.OpenAsync<UI.QTECutinManager>();
            }
            
            if (qteCutinManager == null)
            {
                Debug.LogError("[QTE] Failed to load QTECutinManager");
                return;
            }
            
            // 顯示立繪動畫，默認使用"DefaultFemale"角色
            // 在實際項目中，可以根據不同的魚種或場景選擇不同的角色立繪
            await qteCutinManager.ShowCutinAnimation("C101_Demo", successLevel);
        }
        
        // QTE相关方法
        private async UniTask StartQteMode(FishingManager context)
        {
            isInQteMode = true;
            
            // 暂停正常游戏逻辑
            newLineTensionManager.Pause();
            
            // 记录进入QTE模式时的状态
            float initialDistance = context.fish.distance;
            StruggleState initialState = context.fish.state;
            
            Debug.Log("[QTE] 觸發QTE! 在編輯器模式下: 按1表示成功, 按2表示失敗");
            Debug.Log($"[QTE-Debug] 進入QTE模式時的魚位置: {initialDistance:F1}，狀態: {initialState}");
            
            // 打开QTE界面并初始化
            await InitLureActionSystemAsync(context);
            
            // QTE输入检测
            isWaitingForQteInput = true;
            qteResult = false;
            
            // 等待玩家輸入結果
            while (isWaitingForQteInput)
            {
                // 为了验证是否真的暂停了，每秒检查一次鱼的位置是否改变
                float currentDistance = context.fish.distance;
                if (Mathf.Abs(currentDistance - initialDistance) > 0.01f)
                {
                    Debug.LogWarning($"[QTE-Warning] QTE過程中魚位置發生變化! 從 {initialDistance:F1} 變為 {currentDistance:F1}");
                }
                
                await UniTask.Yield();
            }
            
            // 处理QTE结果
            context.fish.OnQteCompleted(qteResult);
            
            // 如果QTE成功，播放角色立绘和音效
            if (qteResult)
            {
                // 立繪動畫已經在QTECallback中處理
                // 這裡不需要再次顯示
                Debug.Log("[QTE] 已播放女角立繪與音效");
                await UniTask.Delay(500); // 稍微延遲一下，確保動畫有時間顯示
            }
            
            // 记录QTE结束时的状态
            float endDistance = context.fish.distance;
            Debug.Log($"[QTE-Debug] QTE結束時的魚位置: {endDistance:F1}，位置變化: {endDistance - initialDistance:F1}");
            
            // 恢复正常游戏逻辑
            newLineTensionManager.Resume();
            
            // 恢复按钮状态
            if (wasPressedBeforeQte)
            {
                // 模拟按钮一直被按下
                newLineTensionManager.OnPullButtonPressed(null);
            }
            
            isInQteMode = false;
            Debug.Log("[QTE-Debug] QTE模式結束，恢復正常釣魚");
        }
    }
} 