package ehooray.fishing.controller.auth;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.auth.BasicAuthTokenConverter;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto;
import ehooray.fishing.dto.proto.request.auth.FirebaseDeleteProto;
import ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto;
import ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto;
import ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto;
import ehooray.fishing.dto.proto.response.auth.FirebaseDeleteResultProto;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.HttpProtoHandleMediator;
import ehooray.fishing.service.auth.PlayerAuthService;
import ehooray.fishing.service.player.PlayerService;

@Controller("FirebaseAuthController")
public class FirebaseAuthController
{
    @Autowired
    protected PlayerAuthService playerAuthService;
    @Autowired
    protected PlayerService playerService;
    @Autowired
    protected BasicAuthTokenConverter tokenConverter;
    @Autowired
    protected CsvContainer csvContainer;


    @TransactionMethod(Method.Write)
    public void auth(HttpProtoHandleMediator handleMediator) throws Exception
    {
        FirebaseAuthProto.FirebaseAuth auth = handleMediator.getRequest().getDataList(0).unpack(FirebaseAuthProto.FirebaseAuth.class);

        verifyFirebaseId(auth.getUserId(), auth.getIdToken());

        FirebaseAuthResultProto.FirebaseAuthResult.Builder authResultBuilder = FirebaseAuthResultProto.FirebaseAuthResult.newBuilder();
        Optional<PlayerAuthEntity> playerAuth = playerAuthService.findByAccountAndServerId(auth.getUserId(), auth.getServerId());
        if(playerAuth.isEmpty())
        {
            authResultBuilder.setIsResgiter(false);
        }
        else
        {
            String newToken = playerAuthService.updateToNewToken(playerAuth.get());
            authResultBuilder.setIsResgiter(true);
            authResultBuilder.setToken(tokenConverter.encode(playerAuth.get().getServerId(), playerAuth.get().getAccount(), newToken));
        }
        handleMediator.addResult(Any.pack(authResultBuilder.build()));
    }

    protected void verifyFirebaseId(String userId, String idToken) throws Exception
    {
        FirebaseToken decodedToken = FirebaseAuth.getInstance().verifyIdToken(idToken);
        String uid = decodedToken.getUid();

        if(!userId.equals(uid))
            throw new ExceptionByCsvId("invalid id token");
    }

    @TransactionMethod(Method.Write)
    public void register(HttpProtoHandleMediator handleMediator) throws Exception
    {
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.PresetData));
        
        FirebaseRegisterProto.FirebaseRegister registerAuth = handleMediator.getRequest().getDataList(0).unpack(FirebaseRegisterProto.FirebaseRegister.class);
        FirebaseAuthProto.FirebaseAuth auth = registerAuth.getFirebaseAuth();
        LocalDateTime now = handleMediator.getRequestStartTime();

        verifyFirebaseId(auth.getUserId(), auth.getIdToken());

        Optional<PlayerAuthEntity> findPlayerAuth = playerAuthService.findByAccountAndServerId(auth.getUserId(), auth.getServerId());
        if(findPlayerAuth.isPresent())
            throw new ExceptionByCsvId("duplcated account");

        PlayerAuthEntity playerAuth = playerAuthService.createPlayerAuth(auth.getServerId(), auth.getUserId(), "", now);
        PlayerEntity newPlayer = playerService.createPlayer(playerAuth, registerAuth.getPlayerName());
        // make sure player list of playerAuth is sync for player cache
        playerAuthService.addPlayer(playerAuth, newPlayer);
        String newToken = playerAuthService.updateToNewToken(playerAuth);

        FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.Builder registerResultBuilder = FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.newBuilder();
        registerResultBuilder.setToken(tokenConverter.encode(playerAuth.getServerId(), playerAuth.getAccount(), newToken));
        handleMediator.addResult(Any.pack(registerResultBuilder.build()));
    }
    
    @TransactionMethod(Method.Read)
    public void delete(HttpProtoHandleMediator handleMediator) throws Exception
    {
    	FirebaseDeleteProto.FirebaseDelete deleteProto = handleMediator.getRequest().getDataList(0).unpack(FirebaseDeleteProto.FirebaseDelete.class);
    	
    	verifyFirebaseId(deleteProto.getUserId(), deleteProto.getIdToken());
	
    	try
    	{
    		FirebaseDeleteResultProto.FirebaseDeleteResult.Builder deleteResultBuilder = FirebaseDeleteResultProto.FirebaseDeleteResult.newBuilder();
    		
    		FirebaseAuth.getInstance().deleteUser(deleteProto.getUserId());
    		
    		deleteResultBuilder.setIsSuccess(true);
    		deleteResultBuilder.setUserId(deleteProto.getUserId());
    		handleMediator.addResult(Any.pack(deleteResultBuilder.build()));
    	}
    	catch (FirebaseAuthException authException)
    	{
    		throw new ExceptionByCsvId(authException.getMessage());
    	}
    }
}
