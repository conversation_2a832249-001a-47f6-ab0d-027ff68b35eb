using System.Collections.Generic;
using Fishing.Strategies;
using UnityEngine;
using Zenject;

namespace Fishing
{
    /// <summary>
    /// 釣魚策略安裝器
    /// 用於向Zenject容器註冊所有釣魚策略
    /// </summary>
    public class FishingStrategyInstaller : MonoInstaller<FishingStrategyInstaller>
    {
        public override void InstallBindings()
        {
            // 註冊所有策略實例
            Container.Bind<IFishingStrategy>().To<PrepareStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<BeforeStartStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<StartStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<WaitStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<FightStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<CatchStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<FailStrategy>().AsSingle();
            Container.Bind<IFishingStrategy>().To<WinStrategy>().AsSingle();
            
            // 注入所有IFishingStrategy實例列表
            // Container.Bind<List<IFishingStrategy>>().FromResolveAll<IFishingStrategy>().AsSingle();
        }
    }
} 