package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import ehooray.fishing.pojo.utils.MathUtils;

@Entity
//@formatter:off
@Table
(
    name = "fish_rod",
    indexes =
    {
        @Index(name = "csvId", columnList = "csvId"),
    }
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class FishRodEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected int strength;
    protected int control;
    protected int lucky;
    protected int dqr; // Durability Quality and Reliability
    protected int times;
    protected boolean isLock;
    protected int level;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    protected PlayerEntity player;

    public long getId()
    {
        return id;
    }

    public void setId(long id)
    {
        this.id = id;
    }

    public int getCsvId()
    {
        return csvId;
    }

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }
    
    public int getStrength()
    {
        return strength;
    }

    public void setStrength(int strength)
    {
        this.strength = strength;
    }

    public int getControl()
    {
        return control;
    }

    public void setControl(int control)
    {
        this.control = control;
    }

    public int getLucky()
    {
        return lucky;
    }

    public void setLucky(int lucky)
    {
        this.lucky = lucky;
    }

    public int getDqr()
    {
        return dqr;
    }

    public void setDqr(int dqr)
    {
        this.dqr = dqr;
    }

    public int getTimes()
    {
        return times;
    }

    public void setTimes(int times)
    {
        this.times = times;
    }
    

    public boolean isLock()
    {
        return isLock;
    }

    public void setLock(boolean isLock)
    {
        this.isLock = isLock;
    }
    
    public int getLevel()
    {
        return level;
    }
    
    public void setLevel(int level)
    {
        this.level = level;
    }

    public PlayerEntity getPlayer()
    {
    	return player;
    }

    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
    
    public float getRepairDiscount()
    {
        // rate = (Durability Quality and Reliability)*0.2 %
        // discount = 1 - rate
        return MathUtils.clamp(1f - getDqr() * 0.2f * 0.01f, 0f, 1f);
    }
}
