package ehooray.fishing.controller.item;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.WebSocketHandleMediator;

@Controller("PlayerItemController")
public class PlayerItemController 
{
    @Autowired
    protected PlayerItemProtoMap playerItemMap;
    
    @TransactionMethod(Method.Read)
	public void getPlayerItems(WebSocketHandleMediator handleMediator) throws Exception
	{
        PlayerEntity player = handleMediator.getPlayer().get();
		handleMediator.addResult(playerItemMap.toAnyByEntities(player.getPlayerItems()));
	}
}
