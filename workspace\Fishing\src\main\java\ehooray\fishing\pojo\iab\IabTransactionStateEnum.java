package ehooray.fishing.pojo.iab;

import java.util.Arrays;
import java.util.Optional;

public enum IabTransactionStateEnum
{
    Create(1),
    WaitPlayerAccept(2), 
    PlayerAccepted(3),
    VerifyFail(4),
    PlayerCancel(5),
    ClientServiceFail(6)
    ;
    
    protected int id;
    private IabTransactionStateEnum(int id)
    {
        this.id = id;
    }
    
    public int getId()
    {
        return id;
    }
    
    public static Optional<IabTransactionStateEnum> valueOf(int id)
    {
        return Arrays.stream(IabTransactionStateEnum.values()).filter(x-> x.getId() == id).findFirst();
    }
}
