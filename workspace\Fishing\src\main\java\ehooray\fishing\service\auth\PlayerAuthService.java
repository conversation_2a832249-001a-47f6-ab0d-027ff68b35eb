package ehooray.fishing.service.auth;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.repository.PlayerAuthRepository;
import ehooray.fishing.repository.PlayerRepository;

@Service
public class PlayerAuthService
{
    @Autowired
    protected PlayerAuthRepository playerAuthRepository;
    @Autowired
    protected PlayerRepository playerRepository;
    
    public Optional<PlayerAuthEntity> findByAccountAndServerId(String account, int serverId)
    {
        return playerAuthRepository.findByAccountAndServerId(account, serverId);
    }
    
    public Optional<PlayerAuthEntity> findByToken(String token)
    {
        return playerAuthRepository.findByToken(token);
    }
	
	public String updateToNewToken(PlayerAuthEntity playerAuth)
	{
	    String newToken = UUID.randomUUID().toString();
	    playerAuth.setToken(newToken);
	    playerAuthRepository.save(playerAuth);
		return newToken;
	}
	
	public PlayerAuthEntity createPlayerAuth(int serverId, String account, String password, LocalDateTime now)
	{
	    PlayerAuthEntity playerAuth = new PlayerAuthEntity();
	    playerAuth.setAccount(account);
	    playerAuth.setPassword(encodePassword(password));
	    playerAuth.setCreateDate(now);
	    playerAuth.setPlayers(new ArrayList<PlayerEntity>());
	    playerAuth.setServerId(serverId);
	    playerAuthRepository.save(playerAuth);
	    
	    return playerAuth;
	}
	
	public void addPlayer(PlayerAuthEntity playerAuth, PlayerEntity player)
	{
	    playerAuth.getPlayers().add(player);
	    // Note: player_auth_id is in player, so don't need save playerAuth
	    // playerAuthRepository.save(playerAuth);
	}
	
	public void setSessionId(PlayerAuthEntity playerAuth, String sessionId)
	{
	    playerAuth.setSessionId(sessionId);
	    playerAuthRepository.save(playerAuth);
	}
	
	public boolean verifyPassword(PlayerAuthEntity playerAuth, String password)
	{
	    return new BCryptPasswordEncoder().matches(password, playerAuth.getPassword());
	}
	
	protected String encodePassword(String password)
	{
	    return new BCryptPasswordEncoder().encode(password);
	}
}
