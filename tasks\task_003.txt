# Task ID: 3
# Title: Player and Fish Attribute Systems
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Implement data structures and logic for managing all 34 player attributes (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ni Assistant, Boat) and 11 fish attributes. Store attribute data in JSON files.
# Details:
Create `PlayerAttributes.js` to manage player stats (e.g., level, energy, castAccuracy). Base attributes improve with player level. Equipment attributes will stack. Create `FishData.js` (or JSON files) to define 50 fish types with their attributes (Size, Aggressiveness, Rarity, etc.). Implement functions to calculate effective player attributes by combining base stats, level bonuses, and equipment bonuses. 
```json
// Example fish_data.json
[
  { "id": "minnow", "name": "Minnow", "size": 1, "aggressiveness": 2, ..., "activeTimePeriod": ["Dawn", "Morning"], "weatherPreference": "Sunny" }
]
// Example player_data.json (for save/load)
{ "level": 1, "xp": 0, "energy": 100, "maxEnergy": 100, ... }
```

# Test Strategy:
Unit test attribute calculation logic (base + bonuses). Validate loading of fish data from JSON. Check player leveling effects on attributes. Ensure data persistence for player attributes (stub for save/load).
