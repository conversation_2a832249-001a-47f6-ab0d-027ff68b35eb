// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/battle/SelectSpotResult.proto

package ehooray.fishing.dto.proto.response.battle;

public final class SelectSpotResultProto {
  private SelectSpotResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SelectSpotResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.battle.SelectSpotResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 fishCsvId = 1;</code>
     * @return The fishCsvId.
     */
    int getFishCsvId();

    /**
     * <code>int32 retryCount = 2;</code>
     * @return The retryCount.
     */
    int getRetryCount();
  }
  /**
   * Protobuf type {@code response.battle.SelectSpotResult}
   */
  public static final class SelectSpotResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.battle.SelectSpotResult)
      SelectSpotResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SelectSpotResult.newBuilder() to construct.
    private SelectSpotResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SelectSpotResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SelectSpotResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.internal_static_response_battle_SelectSpotResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.internal_static_response_battle_SelectSpotResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.class, ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.Builder.class);
    }

    public static final int FISHCSVID_FIELD_NUMBER = 1;
    private int fishCsvId_;
    /**
     * <code>int32 fishCsvId = 1;</code>
     * @return The fishCsvId.
     */
    @java.lang.Override
    public int getFishCsvId() {
      return fishCsvId_;
    }

    public static final int RETRYCOUNT_FIELD_NUMBER = 2;
    private int retryCount_;
    /**
     * <code>int32 retryCount = 2;</code>
     * @return The retryCount.
     */
    @java.lang.Override
    public int getRetryCount() {
      return retryCount_;
    }

    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.battle.SelectSpotResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.battle.SelectSpotResult)
        ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.internal_static_response_battle_SelectSpotResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.internal_static_response_battle_SelectSpotResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.class, ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fishCsvId_ = 0;

        retryCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.internal_static_response_battle_SelectSpotResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult build() {
        ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult buildPartial() {
        ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult result = new ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult(this);
        result.fishCsvId_ = fishCsvId_;
        result.retryCount_ = retryCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int fishCsvId_ ;
      /**
       * <code>int32 fishCsvId = 1;</code>
       * @return The fishCsvId.
       */
      @java.lang.Override
      public int getFishCsvId() {
        return fishCsvId_;
      }
      /**
       * <code>int32 fishCsvId = 1;</code>
       * @param value The fishCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setFishCsvId(int value) {
        
        fishCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 fishCsvId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishCsvId() {
        
        fishCsvId_ = 0;
        onChanged();
        return this;
      }

      private int retryCount_ ;
      /**
       * <code>int32 retryCount = 2;</code>
       * @return The retryCount.
       */
      @java.lang.Override
      public int getRetryCount() {
        return retryCount_;
      }
      /**
       * <code>int32 retryCount = 2;</code>
       * @param value The retryCount to set.
       * @return This builder for chaining.
       */
      public Builder setRetryCount(int value) {
        
        retryCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 retryCount = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRetryCount() {
        
        retryCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.battle.SelectSpotResult)
    }

    // @@protoc_insertion_point(class_scope:response.battle.SelectSpotResult)
    private static final ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult();
    }

    public static ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SelectSpotResult>
        PARSER = new com.google.protobuf.AbstractParser<SelectSpotResult>() {
      @java.lang.Override
      public SelectSpotResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SelectSpotResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SelectSpotResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto.SelectSpotResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_battle_SelectSpotResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_battle_SelectSpotResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&response/battle/SelectSpotResult.proto" +
      "\022\017response.battle\"9\n\020SelectSpotResult\022\021\n" +
      "\tfishCsvId\030\001 \001(\005\022\022\n\nretryCount\030\002 \001(\005B\\\n)" +
      "ehooray.fishing.dto.proto.response.battl" +
      "eB\025SelectSpotResultProtoH\002\252\002\025Fishing.Net" +
      "work.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_response_battle_SelectSpotResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_battle_SelectSpotResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_battle_SelectSpotResult_descriptor,
        new java.lang.String[] { "FishCsvId", "RetryCount", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
