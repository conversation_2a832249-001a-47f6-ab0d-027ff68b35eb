<!-- Generated by SpecStory -->

# Unity Application Signing Error (2025-04-30 14:17:20)

_**User**_

UnityException: Can not sign the application
Unable to sign the application; please provide passwords!
UnityEngine.GUIUtility:ProcessEvent(Int32, IntPtr, Boolean&)



---

_**Assistant**_



---

好的，这个 `UnityException: Can not sign the application` 错误通常发生在为特定平台（尤其是 Android 或 iOS）构建应用程序时，签名所需的密码没有提供或不正确。

为了帮助你解决这个问题，请告诉我你正在尝试为哪个平台构建项目？ (例如：Android, iOS, Windows 等)

知道目标平台后，我可以告诉你具体需要在 Unity 编辑器的哪个位置检查和输入签名所需的密码。

---

_**User**_

Android

---

_**Assistant**_



---

好的，对于 Android 平台，签名密码是在 `Project Settings` 中配置的。请按照以下步骤检查并输入密码：

1.  在 Unity 编辑器中，打开 `Edit` > `Project Settings`。
2.  在左侧导航栏中，选择 `Player`。
3.  在右侧的 `Inspector` 窗口中，找到 `Publishing Settings` 部分并展开它。
4.  向下滚动，找到 `Keystore Manager` 或 `Keystore` 相关的设置。
5.  **检查以下字段：**
    *   **Use Existing Keystore / Custom Keystore:** 确保你选择了一个有效的 `.keystore` 文件。
    *   **Keystore Password:** 在这里输入你的 Keystore 密码。
    *   **Key Alias:** 选择或输入正确的 Key Alias。
    *   **Key Alias Password:** 在这里输入你的 Key Alias 密码。

请确保你输入的 Keystore 密码和 Key Alias 密码是正确的。如果你不确定密码或没有 Keystore 文件，你可能需要创建一个新的 Keystore。

输入正确的密码后，再次尝试构建你的 Android 应用。

---

_**User**_

CommandInvokationFailure: Gradle build failed. 
C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK\bin\java.exe -classpath "C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\Tools\gradle\lib\gradle-launcher-8.4.jar" org.gradle.launcher.GradleMain "-Dorg.gradle.jvmargs=-Xmx4096m" "assembleRelease" 

Environment Variables:
PROCESSOR_ARCHITECTURE = AMD64
AMPLITUDE_API_KEY_YELLOWBUCKET_DEV = ********************************
ANDROID_NDK_ROOT = C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
DriverData = C:\Windows\System32\Drivers\DriverData
USERDOMAIN = DESKTOP-IRPKV05
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
COMPUTERNAME = DESKTOP-IRPKV05
DEBUG_ENV_VAR = UnityHub
LOGONSERVER = \\DESKTOP-IRPKV05
AMPLITUDE_TOKEN_GREENBUCKET_PROD = ZpUcbAdZXJYzNSoHsAWtL_s_JppwWid9
PUBLIC = C:\Users\<USER>\ProgramData
SESSIONNAME = Console
AMPLITUDE_TOKEN_YELLOWBUCKET_PROD = UYDQtPOw83dIfVy3SrW5oMyo7ru4PRkV
ALLUSERSPROFILE = C:\ProgramData
SystemRoot = C:\WINDOWS
windir = C:\WINDOWS
AMPLITUDE_API_KEY_REDBUCKET_DEV = ********************************
ProgramFiles(x86) = C:\Program Files (x86)
ProgramW6432 = C:\Program Files
ChocolateyLastPathUpdate = 133765686954375863
OS = Windows_NT
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
HOMEPATH = \Users\jerry.lin
FPS_BROWSER_USER_PROFILE_STRING = Default
AMPLITUDE_TOKEN_REDBUCKET_DEV = U4PSN7S7PuYVRODivvdcFwbu8R6IHGqa
ChocolateyInstall = C:\ProgramData\chocolatey
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_12956_GVKQIRSHHANDRJQH
PROCESSOR_LEVEL = 6
CommonProgramFiles = C:\Program Files\Common Files
ZES_ENABLE_SYSMAN = 1
JAVA_HOME = C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK
USERDOMAIN_ROAMINGPROFILE = DESKTOP-IRPKV05
TEMP = C:\Users\<USER>\AppData\Local\Temp
ProgramFiles = C:\Program Files
CommonProgramW6432 = C:\Program Files\Common Files
AMPLITUDE_TOKEN_YELLOWBUCKET_DEV = wKi8foVigDditGFvmZewRFWsEbX9Y0YO
AMPLITUDE_API_KEY_GREENBUCKET_DEV = ********************************
AMPLITUDE_API_KEY_REDBUCKET_PROD = ********************************
HOMEDRIVE = C:
AMPLITUDE_API_KEY_STAGING = ********************************
USERNAME = jerry.lin
OneDrive = C:\Users\<USER>\OneDrive
AMPLITUDE_API_KEY_YELLOWBUCKET_PROD = ********************************
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
AMPLITUDE_TOKEN_REDBUCKET_PROD = jTIgKs5KTZOjiqEEVdZFbgo_VRhABduL
NUMBER_OF_PROCESSORS = 16
AMPLITUDE_TOKEN_GREENBUCKET_DEV = 84H2gMm_FJzhr3mBaX1lM1JVukmCM7d9
AMPLITUDE_DEPLOYMENT_KEY_RED_BUCKET_DEV = client-wbjHYrmeSR87GmWOE7LDpf7sUySOIKHm
AMPLITUDE_API_KEY_GREENBUCKET_PROD = ********************************
ComSpec = C:\WINDOWS\system32\cmd.exe
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
PROCESSOR_REVISION = 8d01
AMPLITUDE_DEPLOYMENT_KEY_RED_BUCKET_PROD = client-zH8Y7OK1i331EKuG77C6UxN8ygcM6LzS
TMP = C:\Users\<USER>\AppData\Local\Temp
EFC_9328 = 1
SystemDrive = C:
AMPLITUDE_API_KEY = ********************************
FPS_BROWSER_APP_PROFILE_STRING = Internet Explorer
SENTRY_ACCESS_TOKEN = ****************************************************************
APPDATA = C:\Users\<USER>\AppData\Roaming
Path = C:\Users\<USER>\.local\bin;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;D:\NodeJs\;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
USERPROFILE = C:\Users\<USER>\Work\fishing-client\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.google.firebase.app.unity" from the source AndroidManifest.xml: D:\Work\fishing-client\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\AndroidManifest.xml.

> Task :launcher:javaPreCompileRelease
> Task :unityLibrary:compileReleaseLibraryResources

> Task :launcher:checkReleaseAarMetadata
WARNING: [Processor] Library 'D:\Work\fishing-client\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\androidx.core.core-1.2.0.aar' contains references to both AndroidX and old support library. This seems like the library is partially migrated. Jetifier will try to rewrite the library anyway.
 Example of androidX reference: 'androidx/versionedparcelable/VersionedParcel'
 Example of support library reference: 'android/support/v4/app/RemoteActionCompatParcelizer'
WARNING: [Processor] Library 'D:\Work\fishing-client\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\androidx.browser.browser-1.4.0.aar' contains references to both AndroidX and old support library. This seems like the library is partially migrated. Jetifier will try to rewrite the library anyway.
 Example of androidX reference: 'androidx/browser/browseractions/BrowserActionItem'
 Example of support library reference: 'android/support/customtabs/trusted/ITrustedWebActivityService$Default'

> Task :unityLibrary:processReleaseJavaRes
> Task :unityLibrary:extractProguardFiles
> Task :unityLibrary:parseReleaseLocalResources
> Task :unityLibrary:javaPreCompileRelease
> Task :unityLibrary:prepareLintJarForPublish
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseLibraryResources
> Task :unityLibrary:FirebaseApp.androidlib:javaPreCompileRelease
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:extractProguardFiles
> Task :unityLibrary:FirebaseApp.androidlib:parseReleaseLocalResources
> Task :unityLibrary:FirebaseApp.androidlib:prepareLintJarForPublish
> Task :unityLibrary:generateReleaseRFile
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJniLibFolders
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseNativeLibs NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:stripReleaseDebugSymbols NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksForAarRelease
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectAndLocalJars
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseShaders
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseAssets
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseRFile
> Task :unityLibrary:FirebaseApp.androidlib:prepareReleaseArtProfile
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseJavaWithJavac NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibCompileToJarRelease
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibRuntimeToJarRelease

> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseLintModel
Generating lint files for backported methods only (compile API B)

> Task :unityLibrary:FirebaseApp.androidlib:createFullJarRelease
> Task :launcher:generateReleaseResValues
> Task :launcher:checkReleaseAarMetadata FAILED
> Task :launcher:mapReleaseSourceSetPaths
> Task :unityLibrary:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :unityLibrary:FirebaseApp.androidlib:extractReleaseAnnotations
41 actionable tasks: 41 executed
]
exit code: 1
UnityEditor.Android.Command.WaitForProgramToRun (UnityEditor.Utils.Program p, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.Command.Run (System.Diagnostics.ProcessStartInfo psi, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.Command.Run (System.String command, System.String args, System.String workingdir, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg, System.Text.Encoding inputEncoding) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.AndroidJavaTools.RunJava (System.String args, System.String workingdir, System.Action`1[T] progress, System.String error) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.GradleWrapper.Run (UnityEditor.Android.AndroidJavaTools javaTools, Unity.Android.Gradle.AndroidGradle androidGradle, System.String workingdir, System.String task, System.String[] extraArguments, System.Action`1[T] progress) (at <c10ede9e037d489abf331be178382e86>:0)
Rethrow as GradleInvokationException: Gradle build failed
UnityEditor.Android.GradleWrapper.Run (UnityEditor.Android.AndroidJavaTools javaTools, Unity.Android.Gradle.AndroidGradle androidGradle, System.String workingdir, System.String task, System.String[] extraArguments, System.Action`1[T] progress) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.Tasks.TasksCommon.RunGradleTask (UnityEditor.Android.PostProcessor.PostProcessorContext context, System.String workingDirectory, System.String taskName, System.String[] extraArguments, UnityEditor.Android.PostProcessor.IPostProcessorTask owner, UnityEditor.Android.PostProcessor.ProgressHandler progress) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.Tasks.TasksCommon.RunLauncherGradleTask (UnityEditor.Android.PostProcessor.PostProcessorContext context, System.String taskName, System.String[] extraArguments, UnityEditor.Android.PostProcessor.IPostProcessorTask owner, UnityEditor.Android.PostProcessor.ProgressHandler progress) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.Tasks.BuildGradleProject.Execute (UnityEditor.Android.PostProcessor.PostProcessorContext context) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.PostProcessRunner.RunAllTasks (UnityEditor.Android.PostProcessor.PostProcessorContext context) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEngine.GUIUtility:ProcessEvent(Int32, IntPtr, Boolean&)



---

_**Assistant**_



---

好的，这个 Gradle 构建失败的错误信息很明确。

**问题原因：**

错误日志中提到：
```
Dependency ':androidx.browser.browser-1.4.0:' requires libraries and applications that depend on it to compile against version 31 or later of the Android APIs.
:launcher is currently compiled against android-30.
Recommended action: Update this project to use a newer compileSdk of at least 31, for example 34.
```
这表示你的项目中使用的一个依赖库 (`androidx.browser.browser-1.4.0`) 要求项目使用 Android API 级别 31 或更高版本进行编译，但你当前的项目设置 (`:launcher` 模块) 是使用 API 级别 30 进行编译的。

**解决方法：**

你需要提高项目的目标 Android API 级别。请按照以下步骤操作：

1.  在 Unity 编辑器中，打开 `Edit` > `Project Settings`。
2.  在左侧导航栏中，选择 `Player`。
3.  在右侧的 `Inspector` 窗口中，找到 `Other Settings` 部分并展开它。
4.  找到 `Identification` 子部分。
5.  修改 **`Target API Level`** 设置。将其从当前的 `Automatic (highest installed)` 或 `API level 30` 更改为至少 **`API level 31`**。
    *   根据错误信息的建议，你也可以直接选择 **`API level 34`** 或其他你已安装的更高稳定版本。
    *   **注意:** 你可能需要通过 `Android SDK Manager` (通常在 Android Studio 中，或者可以通过 Unity Hub 安装) 来下载并安装你选择的 API 级别的 SDK。如果 Unity 提示 SDK 缺失，请先安装对应的 SDK。

6.  修改完成后，再次尝试构建你的 Android 应用。

这个改动会告诉 Gradle 使用指定版本的 Android API 来编译你的应用，从而满足 `androidx.browser` 库的要求。

---

_**User**_

CommandInvokationFailure: Gradle build failed. 
C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK\bin\java.exe -classpath "C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\Tools\gradle\lib\gradle-launcher-8.4.jar" org.gradle.launcher.GradleMain "-Dorg.gradle.jvmargs=-Xmx4096m" "assembleRelease" 

Environment Variables:
PROCESSOR_ARCHITECTURE = AMD64
AMPLITUDE_API_KEY_YELLOWBUCKET_DEV = ********************************
ANDROID_NDK_ROOT = C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
DriverData = C:\Windows\System32\Drivers\DriverData
USERDOMAIN = DESKTOP-IRPKV05
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
COMPUTERNAME = DESKTOP-IRPKV05
DEBUG_ENV_VAR = UnityHub
LOGONSERVER = \\DESKTOP-IRPKV05
AMPLITUDE_TOKEN_GREENBUCKET_PROD = ZpUcbAdZXJYzNSoHsAWtL_s_JppwWid9
PUBLIC = C:\Users\<USER>\ProgramData
SESSIONNAME = Console
AMPLITUDE_TOKEN_YELLOWBUCKET_PROD = UYDQtPOw83dIfVy3SrW5oMyo7ru4PRkV
ALLUSERSPROFILE = C:\ProgramData
SystemRoot = C:\WINDOWS
windir = C:\WINDOWS
AMPLITUDE_API_KEY_REDBUCKET_DEV = ********************************
ProgramFiles(x86) = C:\Program Files (x86)
ProgramW6432 = C:\Program Files
ChocolateyLastPathUpdate = 133765686954375863
OS = Windows_NT
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
HOMEPATH = \Users\jerry.lin
FPS_BROWSER_USER_PROFILE_STRING = Default
AMPLITUDE_TOKEN_REDBUCKET_DEV = U4PSN7S7PuYVRODivvdcFwbu8R6IHGqa
ChocolateyInstall = C:\ProgramData\chocolatey
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_12956_GVKQIRSHHANDRJQH
PROCESSOR_LEVEL = 6
CommonProgramFiles = C:\Program Files\Common Files
ZES_ENABLE_SYSMAN = 1
JAVA_HOME = C:\Program Files\Unity\Hub\Editor\6000.0.29f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK
USERDOMAIN_ROAMINGPROFILE = DESKTOP-IRPKV05
TEMP = C:\Users\<USER>\AppData\Local\Temp
ProgramFiles = C:\Program Files
CommonProgramW6432 = C:\Program Files\Common Files
AMPLITUDE_TOKEN_YELLOWBUCKET_DEV = wKi8foVigDditGFvmZewRFWsEbX9Y0YO
AMPLITUDE_API_KEY_GREENBUCKET_DEV = ********************************
AMPLITUDE_API_KEY_REDBUCKET_PROD = ********************************
HOMEDRIVE = C:
AMPLITUDE_API_KEY_STAGING = ********************************
USERNAME = jerry.lin
OneDrive = C:\Users\<USER>\OneDrive
AMPLITUDE_API_KEY_YELLOWBUCKET_PROD = ********************************
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
AMPLITUDE_TOKEN_REDBUCKET_PROD = jTIgKs5KTZOjiqEEVdZFbgo_VRhABduL
NUMBER_OF_PROCESSORS = 16
AMPLITUDE_TOKEN_GREENBUCKET_DEV = 84H2gMm_FJzhr3mBaX1lM1JVukmCM7d9
AMPLITUDE_DEPLOYMENT_KEY_RED_BUCKET_DEV = client-wbjHYrmeSR87GmWOE7LDpf7sUySOIKHm
AMPLITUDE_API_KEY_GREENBUCKET_PROD = ********************************
ComSpec = C:\WINDOWS\system32\cmd.exe
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
PROCESSOR_REVISION = 8d01
AMPLITUDE_DEPLOYMENT_KEY_RED_BUCKET_PROD = client-zH8Y7OK1i331EKuG77C6UxN8ygcM6LzS
TMP = C:\Users\<USER>\AppData\Local\Temp
EFC_9328 = 1
SystemDrive = C:
AMPLITUDE_API_KEY = ********************************
FPS_BROWSER_APP_PROFILE_STRING = Internet Explorer
SENTRY_ACCESS_TOKEN = ****************************************************************
APPDATA = C:\Users\<USER>\AppData\Roaming
Path = C:\Users\<USER>\.local\bin;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;D:\NodeJs\;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
USERPROFILE = C:\Users\<USER>\Work\fishing-client\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\java\com\unity3d\player\UnityPlayerActivity.java uses or overrides a deprecated API.
Note: Recompile with -Xlint:deprecation for details.

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':launcher:packageRelease'.
> A failure occurred while executing com.android.build.gradle.tasks.PackageAndroidArtifact$IncrementalSplitterRunnable
   > com.android.ide.common.signing.KeytoolException: Failed to read key fishing from store "D:\Work\fishing-client\PublisherData\Android\ehooray.keystore": Cannot recover key

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 50s
]
stdout[

> Configure project :unityLibrary
Variant 'debug', will keep symbols in binaries for:
  'libunity.so'
  'libil2cpp.so'
  'libmain.so'
Variant 'release', symbols will be stripped from binaries.

> Configure project :launcher
Variant 'debug', will keep symbols in binaries for:
  'libunity.so'
  'libil2cpp.so'
  'libmain.so'
Variant 'release', symbols will be stripped from binaries.

> Configure project :unityLibrary:FirebaseApp.androidlib
WARNING: minSdkVersion (23) is greater than targetSdkVersion (9) for variant "debug". Please change the values such that minSdkVersion is less than or equal to targetSdkVersion.
WARNING: minSdkVersion (23) is greater than targetSdkVersion (9) for variant "release". Please change the values such that minSdkVersion is less than or equal to targetSdkVersion.
WARNING: We recommend using a newer Android Gradle plugin to use compileSdk = 35

This Android Gradle plugin (8.3.0) was tested up to compileSdk = 34.

You are strongly encouraged to update your project to use a newer
Android Gradle plugin that has been tested with compileSdk = 35.

If you are already using the latest version of the Android Gradle plugin,
you may need to wait until a newer version with support for compileSdk = 35 is available.

To suppress this warning, add/update
    android.suppressUnsupportedCompileSdk=35
to this project's gradle.properties.

> Task :unityLibrary:preBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preBuild UP-TO-DATE
> Task :unityLibrary:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:writeReleaseAarMetadata UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:writeReleaseAarMetadata UP-TO-DATE
> Task :unityLibrary:generateReleaseResValues UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResValues UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResources UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseResources UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseManifest UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseLibraryResources UP-TO-DATE
> Task :unityLibrary:generateReleaseResources
> Task :unityLibrary:packageReleaseResources
> Task :unityLibrary:extractDeepLinksRelease UP-TO-DATE
> Task :unityLibrary:compileReleaseLibraryResources
> Task :unityLibrary:processReleaseManifest
> Task :launcher:preBuild UP-TO-DATE
> Task :launcher:preReleaseBuild UP-TO-DATE
> Task :launcher:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:extractProguardFiles UP-TO-DATE
> Task :unityLibrary:processReleaseJavaRes UP-TO-DATE
> Task :unityLibrary:extractProguardFiles UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:prepareLintJarForPublish UP-TO-DATE
> Task :unityLibrary:prepareLintJarForPublish UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJniLibFolders UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseNativeLibs NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:stripReleaseDebugSymbols NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectAndLocalJars UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksForAarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseShaders UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:prepareReleaseArtProfile UP-TO-DATE
> Task :launcher:generateReleaseResValues UP-TO-DATE
> Task :launcher:checkReleaseAarMetadata
> Task :launcher:mapReleaseSourceSetPaths
> Task :launcher:generateReleaseResources
> Task :unityLibrary:FirebaseApp.androidlib:writeReleaseLintModelMetadata
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectOnly
> Task :unityLibrary:FirebaseApp.androidlib:parseReleaseLocalResources
> Task :unityLibrary:parseReleaseLocalResources
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJavaResource
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseRFile
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseJavaWithJavac NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibCompileToJarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibRuntimeToJarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:createFullJarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseLintModel
> Task :launcher:packageReleaseResources
> Task :launcher:createReleaseCompatibleScreenManifests
> Task :launcher:extractDeepLinksRelease
> Task :unityLibrary:FirebaseApp.androidlib:extractReleaseAnnotations
> Task :launcher:parseReleaseLocalResources
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseGeneratedProguardFiles
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseConsumerProguardFiles
> Task :unityLibrary:FirebaseApp.androidlib:syncReleaseLibJars
> Task :unityLibrary:FirebaseApp.androidlib:bundleReleaseLocalLintAar
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseLintVitalModel
> Task :launcher:processReleaseMainManifest
> Task :launcher:processReleaseManifest
> Task :launcher:extractProguardFiles
> Task :launcher:mergeReleaseNativeDebugMetadata NO-SOURCE
> Task :unityLibrary:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :unityLibrary:generateReleaseRFile
> Task :launcher:checkReleaseDuplicateClasses
> Task :unityLibrary:buildCMakeRelWithDebInfo[arm64-v8a]
> Task :launcher:mergeReleaseResources
> Task :launcher:mergeReleaseShaders
> Task :launcher:compileReleaseShaders NO-SOURCE
> Task :launcher:generateReleaseAssets UP-TO-DATE
> Task :launcher:extractReleaseVersionControlInfo
> Task :launcher:processReleaseJavaRes NO-SOURCE
> Task :launcher:collectReleaseDependencies
> Task :launcher:sdkReleaseDependencyData
> Task :launcher:mergeReleaseJniLibFolders
> Task :launcher:validateSigningRelease
> Task :launcher:writeReleaseAppMetadata
> Task :launcher:writeReleaseSigningConfigVersions
> Task :launcher:processReleaseManifestForPackage
> Task :launcher:processReleaseResources
> Task :launcher:optimizeReleaseResources
> Task :launcher:desugarReleaseFileDependencies
> Task :unityLibrary:FirebaseApp.androidlib:lintVitalAnalyzeRelease

> Task :unityLibrary:compileReleaseJavaWithJavac

> Task :unityLibrary:bundleLibCompileToJarRelease
> Task :unityLibrary:bundleLibRuntimeToJarRelease
> Task :launcher:mergeReleaseJavaResource
> Task :launcher:compileReleaseJavaWithJavac NO-SOURCE
> Task :launcher:mergeExtDexRelease
> Task :unityLibrary:generateReleaseLintModel
> Task :unityLibrary:createFullJarRelease
> Task :launcher:dexBuilderRelease
> Task :launcher:generateReleaseLintVitalReportModel
> Task :launcher:mergeReleaseGlobalSynthetics
> Task :launcher:mergeDexRelease
> Task :unityLibrary:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :unityLibrary:buildCMakeRelWithDebInfo[armeabi-v7a]
> Task :unityLibrary:mergeReleaseJniLibFolders
> Task :unityLibrary:extractDeepLinksForAarRelease
> Task :unityLibrary:extractReleaseAnnotations
> Task :unityLibrary:mergeReleaseGeneratedProguardFiles
> Task :unityLibrary:mergeReleaseConsumerProguardFiles
> Task :unityLibrary:mergeReleaseShaders
> Task :unityLibrary:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:mergeReleaseNativeLibs
> Task :unityLibrary:packageReleaseAssets
> Task :unityLibrary:prepareReleaseArtProfile
> Task :unityLibrary:writeReleaseLintModelMetadata
> Task :unityLibrary:generateReleaseLintVitalModel
> Task :unityLibrary:stripReleaseDebugSymbols
> Task :unityLibrary:copyReleaseJniLibsProjectOnly
> Task :unityLibrary:mergeReleaseJavaResource
> Task :unityLibrary:copyReleaseJniLibsProjectAndLocalJars
> Task :unityLibrary:lintVitalAnalyzeRelease
> Task :unityLibrary:syncReleaseLibJars
> Task :launcher:mergeReleaseAssets
> Task :unityLibrary:bundleReleaseLocalLintAar
> Task :launcher:mergeReleaseArtProfile
> Task :launcher:compileReleaseArtProfile
> Task :launcher:mergeReleaseNativeLibs
> Task :launcher:stripReleaseDebugSymbols
> Task :launcher:compressReleaseAssets
> Task :launcher:packageRelease FAILED
> Task :launcher:lintVitalAnalyzeRelease
112 actionable tasks: 84 executed, 28 up-to-date
]
exit code: 1
UnityEditor.Android.Command.WaitForProgramToRun (UnityEditor.Utils.Program p, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.Command.Run (System.Diagnostics.ProcessStartInfo psi, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.Command.Run (System.String command, System.String args, System.String workingdir, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg, System.Text.Encoding inputEncoding) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.AndroidJavaTools.RunJava (System.String args, System.String workingdir, System.Action`1[T] progress, System.String error) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.GradleWrapper.Run (UnityEditor.Android.AndroidJavaTools javaTools, Unity.Android.Gradle.AndroidGradle androidGradle, System.String workingdir, System.String task, System.String[] extraArguments, System.Action`1[T] progress) (at <c10ede9e037d489abf331be178382e86>:0)
Rethrow as GradleInvokationException: Gradle build failed
UnityEditor.Android.GradleWrapper.Run (UnityEditor.Android.AndroidJavaTools javaTools, Unity.Android.Gradle.AndroidGradle androidGradle, System.String workingdir, System.String task, System.String[] extraArguments, System.Action`1[T] progress) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.Tasks.TasksCommon.RunGradleTask (UnityEditor.Android.PostProcessor.PostProcessorContext context, System.String workingDirectory, System.String taskName, System.String[] extraArguments, UnityEditor.Android.PostProcessor.IPostProcessorTask owner, UnityEditor.Android.PostProcessor.ProgressHandler progress) (at <c10ede9e037d489abf331be178382e86>:0)
UnityEditor.Android.PostProcessor.Tasks.TasksCommon.RunLauncherGradleTask (UnityEditor.Android.PostProcessor.PostProcessorContext context, System.String taskName, System.String[] extraArguments, UnityEditor.Android.PostProcessor.IPostProcessorTask owner, UnityEditor.Android.PostProcessor.ProgressH<message truncated>


---

_**Assistant**_



---

