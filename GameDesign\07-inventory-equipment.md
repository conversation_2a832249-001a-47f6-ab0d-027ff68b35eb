# 7. 庫存與裝備系統設計文檔

## 7.1 庫存系統總覽

### 7.1.1 庫存架構設計

《Luxury Angler》的庫存系統採用多分類管理架構，將不同類型的物品分別儲存和管理。

#### 庫存分類結構
**魚類收藏區 (Fish Collection)**:
- **魚缸系統**: 當前釣魚 session 的暫存區域
- **魚類圖鑑**: 已收集魚類的永久記錄
- **魚卡庫存**: 用於合成的魚卡材料儲存

**裝備欄位 (Equipment Slots)**:
- **釣竿欄位**: 當前裝備的釣竿
- **魚餌欄位**: 當前使用的魚餌
- **船隻欄位**: 當前的船隻
- **服裝欄位**: 當前穿著的服裝套裝

**道具庫存 (Item Inventory)**:
- **合成材料**: 用於製作的各種材料
- **消耗品**: 臨時增益道具
- **禮物道具**: 用於社交互動的禮品

**特殊收藏 (Special Collections)**:
- **成就獎品**: 成就解鎖的特殊物品
- **限定道具**: 活動或里程碑獲得的獨特物品
- **紀念品**: 探索和冒險的收集品

#### 容量管理系統
**動態容量設計**:
```javascript
// 庫存容量計算
const inventoryCapacity = {
    fishTank: baseCapacity + boatStorage + skillBonus,
    equipment: fixedSlots + unlockedSlots,
    items: baseItemSlots + bagUpgrades,
    special: unlimitedStorage
};
```

**容量擴展方式**:
- **船隻升級**: 增加魚缸和物品儲存空間
- **技能投資**: 庫存管理技能提供額外容量
- **背包購買**: 商店購買的儲存擴展
- **成就獎勵**: 特定成就解鎖額外空間

技術實現：
- [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
- [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面系統

### 7.1.2 魚缸系統詳細設計

#### 魚缸機制核心
**基本功能**:
- 暫存當次釣魚 session 捕獲的魚類
- 提供魚類狀態保持功能
- 支援魚類品質顯示和分類

**容量限制機制**:
- **基礎容量**: 10條魚 (1級船隻)
- **擴展容量**: 最高50條魚 (頂級船隻)
- **滿載處理**: 魚缸滿載時必須返回港口出售

#### 魚缸狀態管理
**魚類狀態指標**:
- **新鮮度**: 影響出售價格 (100% → 80% 隨時間遞減)
- **品質等級**: 完美、優良、普通、劣質
- **稀有度標記**: 1-10星稀有度視覺顯示
- **首次捕獲**: 特殊標記和獎勵加成

**新鮮度系統**:
```javascript
// 新鮮度衰減計算
const freshnessDecay = (fish, timeElapsed) => {
    const decayRate = 0.02; // 每小時衰減2%
    const preservationBonus = boat.preservationSystem || 0;
    const finalFreshness = Math.max(
        0.8, // 最低保持80%新鮮度
        fish.freshness - (timeElapsed * decayRate * (1 - preservationBonus))
    );
    return finalFreshness;
};
```

#### 魚缸管理介面
**視覺設計元素**:
- 2D魚缸視圖，魚類在水中游動
- 容量指示器 (目前數量/最大容量)
- 魚類縮圖和詳細資訊
- 新鮮度和品質標記

**互動功能**:
- 點選魚類查看詳細資訊
- 拖拽整理魚類順序
- 批量選擇和處理
- 快速出售和保留選項

### 7.1.3 物品分類與管理

#### 合成材料管理
**魚卡分類系統**:
- **1星魚卡**: 基礎材料，大量儲存
- **2星魚卡**: 常用材料，中等數量
- **3星魚卡**: 優質材料，有限儲存
- **4星魚卡**: 稀有材料，精心保管
- **5星魚卡**: 傳說材料，極珍貴

**材料堆疊規則**:
- 同種魚卡自動堆疊 (最多99張)
- 不同星級分別計算
- 特殊魚卡 (Boss魚) 單獨顯示

#### 消耗品系統
**增益道具類型**:
- **經驗增益藥水**: +50% 經驗獲得 (1小時)
- **幸運護符**: +25% 稀有魚機率 (30分鐘)
- **能量飲料**: 恢復50點能量
- **完美魚餌**: 下次釣魚100%成功率
- **時間加速器**: 合成時間減半

**道具使用機制**:
- 即時使用或預約使用
- 效果堆疊限制 (同類型不可疊加)
- 持續時間顯示和提醒
- 自動使用功能 (高級功能)

技術實現：
- [ItemManager.js](../src/scripts/ItemManager.js): 物品管理
- [ConsumableEffects.js](../src/scripts/ConsumableEffects.js): 消耗品效果

## 7.2 裝備系統設計

### 7.2.1 裝備欄位架構

#### 主要裝備欄位
**釣竿欄位**:
- **功能**: 核心釣魚工具
- **屬性影響**: 拋竿精準度、張力穩定性、稀有魚機率
- **升級路徑**: 基礎 → 精英 → 大師級
- **特殊效果**: 某些釣竿有獨特被動技能

**魚餌欄位**:
- **功能**: 誘魚階段核心道具
- **類型區分**: 5種魚餌類型，各有特色
- **屬性影響**: 咬餌率、誘魚成功率、魚餌耐久性
- **消耗機制**: 使用後有機率損壞或消失

**船隻欄位**:
- **功能**: 整體遊戲體驗提升
- **屬性影響**: 船隻速度、合成效率、儲存容量
- **特殊功能**: 自動釣魚、伴侶槽、特殊能力
- **升級機制**: 透過合成系統獲得更好船隻

**服裝欄位**:
- **頭部裝備**: 帽子、頭飾 (能量、偵測加成)
- **身體裝備**: 上衣、背心 (能量、儲存加成)
- **腿部裝備**: 褲子、裙子 (移動、社交加成)
- **足部裝備**: 鞋子、涼鞋 (速度、舒適度)
- **飾品欄位**: 太陽眼鏡、手錶 (特殊效果)

#### 裝備切換系統
**快速切換功能**:
- 保存多套裝備組合
- 一鍵切換到預設配置
- 情境推薦系統 (釣魚、探索、社交)

**裝備預設組合**:
```javascript
// 裝備預設範例
const equipmentPresets = {
    "釣魚專精": {
        rod: "大師鈦合金釣竿",
        lure: "大師旋轉餌",
        boat: "精英遊艇",
        clothing: "釣魚專業套裝"
    },
    "探索模式": {
        rod: "輕量碳纖維釣竿", 
        boat: "精英快艇",
        clothing: "探險者套裝"
    },
    "社交活動": {
        boat: "豪華郵輪",
        clothing: "時尚比基尼套裝"
    }
};
```

### 7.2.2 裝備屬性系統

#### 屬性計算機制
**屬性疊加邏輯**:
```javascript
// 最終屬性計算
const calculateFinalStats = (player, equipment) => {
    let stats = {...player.baseStats};
    
    // 裝備屬性疊加
    equipment.forEach(item => {
        if (item.statBonus) {
            Object.keys(item.statBonus).forEach(stat => {
                stats[stat] += item.statBonus[stat];
            });
        }
    });
    
    // 套裝效果
    const setBonus = calculateSetBonus(equipment);
    return applySetBonus(stats, setBonus);
};
```

#### 套裝效果系統
**套裝設計理念**:
- 2件套：小型屬性加成
- 3件套：中型屬性加成 + 特殊效果
- 4件套：大型屬性加成 + 強力特殊效果
- 5件套：最強屬性加成 + 獨特能力

**範例套裝效果**:
- **釣魚大師套裝** (4件): 所有釣魚屬性 +15%, 完美釣魚機率 +20%
- **探險家套裝** (3件): 移動速度 +25%, 發現隱藏地點機率 +30%
- **社交名媛套裝** (5件): 所有社交屬性 +20%, 同時與所有伴侶互動

### 7.2.3 裝備強化系統

#### 強化機制設計
**強化等級**:
- 每件裝備可強化至 +10
- 強化成功率隨等級遞減
- 失敗可能降級或損壞裝備

**強化材料需求**:
- **基礎強化石** (+1 到 +3): 基礎魚卡製作
- **中級強化石** (+4 到 +6): 稀有魚卡製作  
- **高級強化石** (+7 到 +9): Boss魚卡製作
- **完美強化石** (+10): 傳說魚卡製作

#### 強化成功率表
```javascript
// 強化成功率計算
const enhancementRates = {
    1: 0.95,  // +1: 95%
    2: 0.90,  // +2: 90%
    3: 0.85,  // +3: 85%
    4: 0.75,  // +4: 75%
    5: 0.65,  // +5: 65%
    6: 0.55,  // +6: 55%
    7: 0.40,  // +7: 40%
    8: 0.25,  // +8: 25%
    9: 0.15,  // +9: 15%
    10: 0.05  // +10: 5%
};
```

#### 強化保護機制
**安全強化**:
- +1 到 +3：100% 安全，失敗不損壞
- +4 到 +6：失敗降級1，但不損壞
- +7 到 +10：失敗可能損壞裝備

**保護道具**:
- **保護符**: 防止裝備損壞
- **幸運石**: 提高成功率 +10%
- **完美催化劑**: 100% 成功，限量獲得

技術實現：
- [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化
- [EquipmentEnhancementUI.js](../src/ui/EquipmentEnhancementUI.js): 強化介面

## 7.3 庫存管理功能

### 7.3.1 物品整理系統

#### 自動整理功能
**智能分類**:
- 同類物品自動群組
- 稀有度由高到低排列
- 新獲得物品優先顯示
- 收藏物品固定位置

**整理規則設定**:
```javascript
// 物品排序規則
const sortingRules = {
    byRarity: (a, b) => b.rarity - a.rarity,
    byType: (a, b) => a.type.localeCompare(b.type),
    byValue: (a, b) => b.value - a.value,
    byQuantity: (a, b) => b.quantity - a.quantity,
    custom: (a, b) => player.customSortFunction(a, b)
};
```

#### 搜尋與篩選
**搜尋功能**:
- 物品名稱模糊搜尋
- 屬性關鍵字搜尋
- 稀有度範圍篩選
- 物品類型篩選

**進階篩選**:
- 可用於合成的材料
- 符合當前等級的裝備
- 有套裝效果的物品
- 可強化的裝備

### 7.3.2 物品比較系統

#### 裝備比較介面
**比較功能**:
- 並排顯示裝備屬性
- 數值差異用顏色標示
- 綜合戰力評估
- 套裝效果影響分析

**比較指標**:
- 基礎屬性對比
- 特殊效果差異
- 適用場景推薦
- 升級潛力評估

#### 價值評估系統
**物品價值計算**:
```javascript
// 物品價值評估
const calculateItemValue = (item) => {
    let value = item.baseValue;
    
    // 稀有度加成
    value *= (1 + item.rarity * 0.2);
    
    // 強化等級加成
    value *= (1 + item.enhanceLevel * 0.1);
    
    // 市場需求調整
    value *= marketDemand[item.type] || 1.0;
    
    return Math.floor(value);
};
```

### 7.3.3 批量操作功能

#### 批量處理操作
**多選操作**:
- 批量出售低價值物品
- 批量使用同類消耗品
- 批量分解不需要的裝備
- 批量移動到特定分類

**智能建議**:
- 建議出售的過時裝備
- 推薦保留的稀有材料
- 提醒即將過期的消耗品
- 標記可合成的材料組合

#### 庫存優化建議
**空間管理提醒**:
- 庫存接近滿載警告
- 建議釋放空間的方法
- 推薦購買的擴容方案
- 分析庫存使用效率

技術實現：
- [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
- [ItemSorter.js](../src/scripts/ItemSorter.js): 物品排序
- [ItemComparator.js](../src/scripts/ItemComparator.js): 物品比較

## 7.4 特殊庫存功能

### 7.4.1 魚類圖鑑系統

#### 圖鑑收集機制
**收集進度追蹤**:
- 50種魚類完整記錄
- 捕獲次數統計
- 最大尺寸記錄
- 捕獲地點和時間
- 使用魚餌類型記錄

**圖鑑獎勵系統**:
- 收集里程碑獎勵
- 完成度成就解鎖
- 特殊稱號獲得
- 隱藏內容解鎖

#### 圖鑑展示功能
**詳細資訊展示**:
- 3D魚類模型展示
- 詳細屬性和行為描述
- 棲息地和習性介紹
- 捕獲技巧和建議
- 歷史捕獲記錄

**成就展示**:
- 首次捕獲時間戳
- 個人最佳記錄
- 稀有度排名
- 收集完成度徽章

### 7.4.2 紀念品收藏系統

#### 紀念品類型
**探索紀念品**:
- 地圖探索紀念幣
- 特殊地點發現證書
- 第一次到達記錄
- 隱藏秘境紀念品

**成就紀念品**:
- 重要里程碑獎盃
- 特殊技藝證書
- 競賽參與證明
- 稀有魚類捕獲證書

**社交紀念品**:
- 伴侶關係紀念品
- 特殊事件參與證明
- 社交里程碑紀念
- 限時活動獎品

#### 紀念品展示系統
**個人博物館**:
- 3D展示空間
- 可自定義擺放位置
- 紀念品故事介紹
- 訪客觀賞功能

### 7.4.3 限定收藏系統

#### 限定物品管理
**活動限定物品**:
- 季節活動專屬道具
- 節日慶典紀念品
- 限時挑戰獎勵
- 開發者特別贈品

**收藏價值系統**:
```javascript
// 限定物品價值評估
const limitedItemValue = (item) => {
    const baseValue = item.originalValue;
    const timeMultiplier = calculateTimeScarcity(item.obtainDate);
    const rarityMultiplier = item.isEventExclusive ? 3.0 : 1.5;
    
    return baseValue * timeMultiplier * rarityMultiplier;
};
```

技術實現：
- [CollectionManager.js](../src/scripts/CollectionManager.js): 收藏管理
- [MuseumDisplay.js](../src/scripts/MuseumDisplay.js): 博物館展示
- [LimitedItemTracker.js](../src/scripts/LimitedItemTracker.js): 限定物品追蹤

## 7.5 庫存與其他系統整合

### 7.5.1 與合成系統整合

#### 材料需求預覽
**合成材料檢查**:
- 實時檢查所需材料庫存
- 缺少材料高亮顯示
- 推薦獲得途徑提示
- 預估合成成本計算

**材料預留功能**:
- 為特定配方預留材料
- 防止意外使用或出售
- 批量預留多個配方材料
- 預留狀態視覺提示

### 7.5.2 與經濟系統整合

#### 價值評估與出售
**智能定價系統**:
- 基於市場供需的動態定價
- 考慮物品稀有度和需求
- 時間因素影響價格波動
- 批量出售折扣機制

**出售建議系統**:
- 推薦出售時機
- 價格趨勢預測
- 庫存過剩警告
- 利潤最大化建議

### 7.5.3 與進程系統整合

#### 等級解鎖內容
**庫存擴展**:
- 等級提升自動增加基礎容量
- 技能投資解鎖特殊功能
- 成就獎勵額外儲存空間
- VIP功能高級庫存管理

**功能進程解鎖**:
- 等級1: 基礎庫存功能
- 等級5: 自動整理功能
- 等級10: 批量操作功能
- 等級15: 智能建議系統
- 等級20: 高級搜尋篩選
- 等級25: 個人博物館
- 等級30: 市場價值分析
- 等級35: 自動庫存優化
- 等級40: 預測性庫存管理
- 等級50: 完全自訂化庫存

### 7.5.4 與社交系統整合

#### 物品分享與贈送
**社交功能**:
- 向伴侶贈送禮物
- 展示稀有收藏品
- 交換特殊材料
- 共享庫存資源 (高級功能)

**競爭元素**:
- 收藏品排行榜
- 稀有物品炫耀系統
- 收集成就比較
- 限定物品珍藏度

相關檔案：
- [InventoryManager.js](../src/scripts/InventoryManager.js): 庫存管理核心
- [InventoryUI.js](../src/ui/InventoryUI.js): 庫存介面
- [EquipmentManager.js](../src/scripts/EquipmentManager.js): 裝備管理
- [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備強化
- [ItemManager.js](../src/scripts/ItemManager.js): 物品管理
- [CollectionManager.js](../src/scripts/CollectionManager.js): 收藏管理
- [GameState.js](../src/scripts/GameState.js): 庫存狀態管理 