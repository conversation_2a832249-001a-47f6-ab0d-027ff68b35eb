// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/exchange/ExchangeFish.proto

package ehooray.fishing.dto.proto.request.exchange;

public final class ExchangeFishProto {
  private ExchangeFishProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ExchangeFishOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.exchange.ExchangeFish)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 exchangeEntity = 1;</code>
     * @return The exchangeEntity.
     */
    long getExchangeEntity();

    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @return A list containing the fishEntites.
     */
    java.util.List<java.lang.Long> getFishEntitesList();
    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @return The count of fishEntites.
     */
    int getFishEntitesCount();
    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @param index The index of the element to return.
     * @return The fishEntites at the given index.
     */
    long getFishEntites(int index);
  }
  /**
   * Protobuf type {@code request.exchange.ExchangeFish}
   */
  public static final class ExchangeFish extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.exchange.ExchangeFish)
      ExchangeFishOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ExchangeFish.newBuilder() to construct.
    private ExchangeFish(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ExchangeFish() {
      fishEntites_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ExchangeFish();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.internal_static_request_exchange_ExchangeFish_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.internal_static_request_exchange_ExchangeFish_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.class, ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.Builder.class);
    }

    public static final int EXCHANGEENTITY_FIELD_NUMBER = 1;
    private long exchangeEntity_;
    /**
     * <code>int64 exchangeEntity = 1;</code>
     * @return The exchangeEntity.
     */
    @java.lang.Override
    public long getExchangeEntity() {
      return exchangeEntity_;
    }

    public static final int FISHENTITES_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList fishEntites_;
    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @return A list containing the fishEntites.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getFishEntitesList() {
      return fishEntites_;
    }
    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @return The count of fishEntites.
     */
    public int getFishEntitesCount() {
      return fishEntites_.size();
    }
    /**
     * <code>repeated int64 fishEntites = 2;</code>
     * @param index The index of the element to return.
     * @return The fishEntites at the given index.
     */
    public long getFishEntites(int index) {
      return fishEntites_.getLong(index);
    }
    private int fishEntitesMemoizedSerializedSize = -1;

    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.exchange.ExchangeFish}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.exchange.ExchangeFish)
        ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFishOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.internal_static_request_exchange_ExchangeFish_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.internal_static_request_exchange_ExchangeFish_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.class, ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        exchangeEntity_ = 0L;

        fishEntites_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.internal_static_request_exchange_ExchangeFish_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish build() {
        ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish buildPartial() {
        ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish result = new ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish(this);
        int from_bitField0_ = bitField0_;
        result.exchangeEntity_ = exchangeEntity_;
        if (((bitField0_ & 0x00000001) != 0)) {
          fishEntites_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.fishEntites_ = fishEntites_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private long exchangeEntity_ ;
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @return The exchangeEntity.
       */
      @java.lang.Override
      public long getExchangeEntity() {
        return exchangeEntity_;
      }
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @param value The exchangeEntity to set.
       * @return This builder for chaining.
       */
      public Builder setExchangeEntity(long value) {
        
        exchangeEntity_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 exchangeEntity = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearExchangeEntity() {
        
        exchangeEntity_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList fishEntites_ = emptyLongList();
      private void ensureFishEntitesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fishEntites_ = mutableCopy(fishEntites_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @return A list containing the fishEntites.
       */
      public java.util.List<java.lang.Long>
          getFishEntitesList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(fishEntites_) : fishEntites_;
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @return The count of fishEntites.
       */
      public int getFishEntitesCount() {
        return fishEntites_.size();
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @param index The index of the element to return.
       * @return The fishEntites at the given index.
       */
      public long getFishEntites(int index) {
        return fishEntites_.getLong(index);
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @param index The index to set the value at.
       * @param value The fishEntites to set.
       * @return This builder for chaining.
       */
      public Builder setFishEntites(
          int index, long value) {
        ensureFishEntitesIsMutable();
        fishEntites_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @param value The fishEntites to add.
       * @return This builder for chaining.
       */
      public Builder addFishEntites(long value) {
        ensureFishEntitesIsMutable();
        fishEntites_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @param values The fishEntites to add.
       * @return This builder for chaining.
       */
      public Builder addAllFishEntites(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureFishEntitesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fishEntites_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 fishEntites = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishEntites() {
        fishEntites_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.exchange.ExchangeFish)
    }

    // @@protoc_insertion_point(class_scope:request.exchange.ExchangeFish)
    private static final ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish();
    }

    public static ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ExchangeFish>
        PARSER = new com.google.protobuf.AbstractParser<ExchangeFish>() {
      @java.lang.Override
      public ExchangeFish parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ExchangeFish> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ExchangeFish> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto.ExchangeFish getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_exchange_ExchangeFish_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_exchange_ExchangeFish_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#request/exchange/ExchangeFish.proto\022\020r" +
      "equest.exchange\";\n\014ExchangeFish\022\026\n\016excha" +
      "ngeEntity\030\001 \001(\003\022\023\n\013fishEntites\030\002 \003(\003BY\n*" +
      "ehooray.fishing.dto.proto.request.exchan" +
      "geB\021ExchangeFishProtoH\002\252\002\025Fishing.Networ" +
      "k.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_exchange_ExchangeFish_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_exchange_ExchangeFish_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_exchange_ExchangeFish_descriptor,
        new java.lang.String[] { "ExchangeEntity", "FishEntites", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
