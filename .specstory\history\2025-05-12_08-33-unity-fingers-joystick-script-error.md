<!-- Generated by SpecStory -->

# Unity Fingers Joystick Script Error (2025-05-12 16:33:08)

_**User**_

Fingers joystick script requires that if JoystickImage is set, the Canvas is in ScreenSpaceOverlay mode.
UnityEngine.Debug:LogError (object)
DigitalRubyShared.FingersJoystickScript:OnEnable () (at Assets/Fingers/Prefab/Script/Controls/FingersJoystickScript.cs:358)
UnityEngine.GameObject:SetActive (bool)
Ehooray.UI.GamePanelManager:ShowPanel (Ehooray.UI.GamePanel) (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:106)
Ehooray.UI.GamePanelManager/<OpenAsync>d__11`1<LureActionSystem>:MoveNext () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:67)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1<LureActionSystem>:Start<Ehooray.UI.GamePanelManager/<OpenAsync>d__11`1<LureActionSystem>> (Ehooray.UI.GamePanelManager/<OpenAsync>d__11`1<LureActionSystem>&) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:242)
Ehooray.UI.GamePanelManager:OpenAsync<LureActionSystem> ()
Fishing.Strategies.NewWaitStrategy:OnEnter (Fishing.FishingManager) (at Assets/Fishing/Scripts/Battle/NewStrategies/NewWaitStrategy.cs:32)
Fishing.FishingManager/<TransitionToState>d__48:MoveNext () (at Assets/Fishing/Scripts/Battle/FishingManager.cs:149)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:Start<Fishing.FishingManager/<TransitionToState>d__48> (Fishing.FishingManager/<TransitionToState>d__48&) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:111)
Fishing.FishingManager:TransitionToState (Fishing.FishingState)
Fishing.Strategies.StartStrategy:OnUpdate (Fishing.FishingManager) (at Assets/Fishing/Scripts/Battle/Strategies/StartStrategy.cs:53)
Fishing.FishingManager:Update () (at Assets/Fishing/Scripts/Battle/FishingManager.cs:123)



---

_**Assistant**_



---

