---
description: In-depth explanation of the three fishing mini-game mechanics, including casting's timing-based click game, luring's WASD controls with 5 lure types, and reeling's tension and QTE reaction controls. Also covers 10 fish struggle styles, attribute influence systems, and UI element design for each phase.
globs: 
alwaysApply: false
---
# 钓鱼迷你游戏系统

## 钓鱼流程概览
钓鱼过程包含三个连续的迷你游戏：抛竿 → 诱饵 → 收线

## 抛竿迷你游戏
- **文件**: ](mdc:src/scripts/CastingMiniGame.js)
- **机制**: 时机为基础的点击游戏
- **UI元素**:
  - 抛竿计量表显示
  - 准确区域标识
  - 2D钓竿抛竿动画
- **控制**: 在准确区域内点击
- **结果**: 
  - 成功：诱饵落在热点（大鱼和稀有鱼更可能出现）
  - 失败：诱饵落在普通区域
- **影响因素**: 
  - 玩家抛竿精度属性
  - 鱼类探测属# 诱饵迷你游戏
- **文件**: [LuringMiniGame.js](mdc:FishingPhaser/src/scripts/LuringMiniGame.js)
- **机制**: 鱼影接近诱饵，玩家有3-4次机会钩鱼
- **UI元素**:
  - 诱饵模拟UI（右上角）
  - 鱼影游动动画
  - 鱼类兴趣计量表
  - 输入提示系统
- **控制方案**: WASD键控制诱饵移动
  - **W键**: 诱饵快速上游（1秒后）
  - **A/D键**: 诱饵向右上方游动（朝船只方向，持续1秒）
  - **冷却时间**: 所有操作有1秒冷却

### 5种诱饵类型控制
1. **旋转器**: 脉冲点击
2. **软塑料**: 拖拽和暂停
3. **飞钓**: 滑动闪烁组合
4. **波普器**: 点击和保持爆发
5. **勺形**: 圆形轨迹

### 诱饵阶段流程
1. 鱼影（小、中、大）游向诱饵
2. 鱼影行为反映鱼类侵略性和难以捕捉性. 成功保持鱼影接近，失败使鱼影游走
5. 最终定时输入钩住鱼类

## 收线迷你游戏
- **文件**: [ReelingMiniGame.js](mdc:FishingPhaser/src/scripts/ReelingMiniGame.js)
- **机制**: 基于张力的游戏配合QTE反应控制
- **UI元素**:
  - 挣扎的鱼类动画（左右快速游动）
  - 张力计量表
  - 鱼类体力条
  - QTE提示
- **控制**:
  - **鼠标按压**: 收线，消耗鱼类体力
  - **QTE输入**: 快速反应防止张力过高
- **目标**: 将鱼类体力降至0，同时保持张力不超过最大值

### 鱼类挣扎风格（10种）
- 快速冲撞、深潜、长距离冲刺等
- 每种挣扎对应特定的QTE控制
- 挣扎强度基于鱼类属性（力量、耐力、侵略性）

### QTE反应控制
- 快速点击、保持和释放、滑动链等
- 失败将导致张力计量表突然增加至90%
- 成功保持张力在安全范围内

## 属性影响系统
### 玩家属性
- **抛竿精度**: 扩大准确区域
- **诱饵成功率**: 简化输入时机
- **张力稳定性**: 扩大安全张力范围
- **QTE精度**: 延长QTE时机窗口

### 装备加成
- **钓竿**: 提升抛竿精度、张力稳定性、收线速度
- **诱饵**: 增加咬钩率、诱饵成功率、耐久度
- **比基尼助手**: 提供鱼类探测、QTE精度加成

### 鱼类影响
- **侵略性**: 影响鱼影接近速度
- **难以捕捉性**: 增加诱饵阶段失败风险
- **力量和耐力**: 影响收线阶段难度
- **大小**: 影响所需的收线时间