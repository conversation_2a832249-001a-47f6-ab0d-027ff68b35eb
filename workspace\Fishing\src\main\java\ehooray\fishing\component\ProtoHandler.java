package ehooray.fishing.component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Optional;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.base.Strings;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.response.ExceptionProto;
import ehooray.fishing.dto.proto.response.ResponseProto;
import ehooray.fishing.dto.proto.response.ResponseProto.Response.State;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.ProtoHandleMediator;

@Component
public class ProtoHandler
{
	@Autowired
	protected BeanFactory beanFactory;

	@Autowired
	protected TransactionMethodInvoker transactionMethodInvoker;
	
	@Autowired
	protected ProtoHandleMediatorContainer mediatorContainer;
	
	@Autowired
	protected ProtoHandler selfForAop;
	
	@Autowired
	protected ResponseCache responseCache;
	
	@Value("${game.responseCache.enable}")
    protected boolean responseCacheEnable;

	public ResponseProto.Response handleRequest(RequestProto.Request request, ProtoHandleMediator.Builder handleMediatorBuilder)
	{
		try
		{
		    if(responseCacheEnable)
		    {
		        Optional<ResponseProto.Response> existResponse = responseCache.find(handleMediatorBuilder.getPlayerId(), request.getRequestId());
	            if(existResponse.isPresent()) return existResponse.get();
		    }
		    
			Object controller = beanFactory.getBean(request.getController());
			Method method = controller.getClass().getDeclaredMethod(request.getMethod(), handleMediatorBuilder.getBuildClassType());
			ProtoHandleMediator mediator;
			if(method.getAnnotation(TransactionMethod.class).value() == TransactionMethod.Method.Write)
			    mediator = transactionMethodInvoker.invokeWriteMethod(controller, method, handleMediatorBuilder);
			else
			    mediator = transactionMethodInvoker.invokeReadMethod(controller, method, handleMediatorBuilder);

			ResponseProto.Response.Builder responseBuilder = ResponseProto.Response.newBuilder();
			responseBuilder.setState(State.Success);
			responseBuilder.addAllDataList(mediator.getResultList());
			responseBuilder.setRequestId(request.getRequestId());
			responseBuilder.addAllDirtyFlags(mediator.getDirtyFlags());
			ResponseProto.Response response = responseBuilder.build();
			
			// cache previous response
			if(responseCacheEnable)
			    responseCache.add(handleMediatorBuilder.getPlayerId(), response);
			return response;
		}
		catch(InvocationTargetException e)
		{
		    return selfForAop.handleRequestException(e.getCause(), Optional.of(request));
		}
		catch(Throwable e)
        {
            return selfForAop.handleRequestException(e, Optional.of(request));
        }
		finally
		{
		    handleRequestFinally();
		}
	}
	
	protected ResponseProto.Response handleRequestException(Throwable e, Optional<RequestProto.Request> request)
	{
	    ExceptionProto.Exception.Builder exceptionBuilder = ExceptionProto.Exception.newBuilder();
	    if(e instanceof ExceptionByCsvId)
	    {
	        ExceptionByCsvId exceptionByCsvId = (ExceptionByCsvId)e;
	        exceptionBuilder.setCsvId(exceptionByCsvId.getCsvId());
	        if(exceptionByCsvId.isOverrideContentExist())
	            exceptionBuilder.addAllOverrideContent(Arrays.asList(exceptionByCsvId.getOverrideContent()));
	        if(!Strings.isNullOrEmpty(e.getMessage()))
	            exceptionBuilder.setServerMessage(e.getMessage());
	    }
	    else
	    {
	        if(!Strings.isNullOrEmpty(e.getMessage()))
	            exceptionBuilder.setServerMessage(e.getMessage());
	    }


	    ResponseProto.Response.Builder responseBuilder = ResponseProto.Response.newBuilder();
        responseBuilder.setState(State.Exception);
        if(request.isPresent()) 
            responseBuilder.setRequestId(request.get().getRequestId());
        responseBuilder.setException(exceptionBuilder.build());
	    return responseBuilder.build();
	}

	protected void handleRequestFinally()
	{
	    mediatorContainer.removeMediator();
	}

	public ResponseProto.Response handleUnknownException(Throwable e)
    {
        ExceptionProto.Exception.Builder exceptionBuilder = ExceptionProto.Exception.newBuilder();
        if(!Strings.isNullOrEmpty(e.getMessage()))
            exceptionBuilder.setServerMessage(e.getMessage());

        ResponseProto.Response.Builder responseBuilder = ResponseProto.Response.newBuilder();
        responseBuilder.setState(State.Exception);
        responseBuilder.setException(exceptionBuilder.build());
        return responseBuilder.build();
    }
}
