// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/IabTransaction.proto

package ehooray.fishing.dto.proto.model;

public final class IabTransactionProto {
  private IabTransactionProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IabTransactionOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.IabTransaction)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>string transactionId = 3;</code>
     * @return The transactionId.
     */
    java.lang.String getTransactionId();
    /**
     * <code>string transactionId = 3;</code>
     * @return The bytes for transactionId.
     */
    com.google.protobuf.ByteString
        getTransactionIdBytes();

    /**
     * <code>int32 state = 4;</code>
     * @return The state.
     */
    int getState();

    /**
     * <code>string exception = 5;</code>
     * @return The exception.
     */
    java.lang.String getException();
    /**
     * <code>string exception = 5;</code>
     * @return The bytes for exception.
     */
    com.google.protobuf.ByteString
        getExceptionBytes();
  }
  /**
   * Protobuf type {@code model.IabTransaction}
   */
  public static final class IabTransaction extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.IabTransaction)
      IabTransactionOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IabTransaction.newBuilder() to construct.
    private IabTransaction(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IabTransaction() {
      transactionId_ = "";
      exception_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IabTransaction();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.IabTransactionProto.internal_static_model_IabTransaction_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.IabTransactionProto.internal_static_model_IabTransaction_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.class, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int TRANSACTIONID_FIELD_NUMBER = 3;
    private volatile java.lang.Object transactionId_;
    /**
     * <code>string transactionId = 3;</code>
     * @return The transactionId.
     */
    @java.lang.Override
    public java.lang.String getTransactionId() {
      java.lang.Object ref = transactionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionId_ = s;
        return s;
      }
    }
    /**
     * <code>string transactionId = 3;</code>
     * @return The bytes for transactionId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTransactionIdBytes() {
      java.lang.Object ref = transactionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STATE_FIELD_NUMBER = 4;
    private int state_;
    /**
     * <code>int32 state = 4;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }

    public static final int EXCEPTION_FIELD_NUMBER = 5;
    private volatile java.lang.Object exception_;
    /**
     * <code>string exception = 5;</code>
     * @return The exception.
     */
    @java.lang.Override
    public java.lang.String getException() {
      java.lang.Object ref = exception_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        exception_ = s;
        return s;
      }
    }
    /**
     * <code>string exception = 5;</code>
     * @return The bytes for exception.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExceptionBytes() {
      java.lang.Object ref = exception_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        exception_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.IabTransaction}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.IabTransaction)
        ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.IabTransactionProto.internal_static_model_IabTransaction_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.IabTransactionProto.internal_static_model_IabTransaction_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.class, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        csvId_ = 0;

        transactionId_ = "";

        state_ = 0;

        exception_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.IabTransactionProto.internal_static_model_IabTransaction_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction build() {
        ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction buildPartial() {
        ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction result = new ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction(this);
        result.entityId_ = entityId_;
        result.csvId_ = csvId_;
        result.transactionId_ = transactionId_;
        result.state_ = state_;
        result.exception_ = exception_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object transactionId_ = "";
      /**
       * <code>string transactionId = 3;</code>
       * @return The transactionId.
       */
      public java.lang.String getTransactionId() {
        java.lang.Object ref = transactionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          transactionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string transactionId = 3;</code>
       * @return The bytes for transactionId.
       */
      public com.google.protobuf.ByteString
          getTransactionIdBytes() {
        java.lang.Object ref = transactionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string transactionId = 3;</code>
       * @param value The transactionId to set.
       * @return This builder for chaining.
       */
      public Builder setTransactionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        transactionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string transactionId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransactionId() {
        
        transactionId_ = getDefaultInstance().getTransactionId();
        onChanged();
        return this;
      }
      /**
       * <code>string transactionId = 3;</code>
       * @param value The bytes for transactionId to set.
       * @return This builder for chaining.
       */
      public Builder setTransactionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        transactionId_ = value;
        onChanged();
        return this;
      }

      private int state_ ;
      /**
       * <code>int32 state = 4;</code>
       * @return The state.
       */
      @java.lang.Override
      public int getState() {
        return state_;
      }
      /**
       * <code>int32 state = 4;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 state = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object exception_ = "";
      /**
       * <code>string exception = 5;</code>
       * @return The exception.
       */
      public java.lang.String getException() {
        java.lang.Object ref = exception_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          exception_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string exception = 5;</code>
       * @return The bytes for exception.
       */
      public com.google.protobuf.ByteString
          getExceptionBytes() {
        java.lang.Object ref = exception_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          exception_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string exception = 5;</code>
       * @param value The exception to set.
       * @return This builder for chaining.
       */
      public Builder setException(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        exception_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string exception = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearException() {
        
        exception_ = getDefaultInstance().getException();
        onChanged();
        return this;
      }
      /**
       * <code>string exception = 5;</code>
       * @param value The bytes for exception to set.
       * @return This builder for chaining.
       */
      public Builder setExceptionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        exception_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.IabTransaction)
    }

    // @@protoc_insertion_point(class_scope:model.IabTransaction)
    private static final ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction();
    }

    public static ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IabTransaction>
        PARSER = new com.google.protobuf.AbstractParser<IabTransaction>() {
      @java.lang.Override
      public IabTransaction parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IabTransaction> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IabTransaction> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_IabTransaction_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_IabTransaction_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\032model/IabTransaction.proto\022\005model\"j\n\016I" +
      "abTransaction\022\020\n\010entityId\030\001 \001(\003\022\r\n\005csvId" +
      "\030\002 \001(\005\022\025\n\rtransactionId\030\003 \001(\t\022\r\n\005state\030\004" +
      " \001(\005\022\021\n\texception\030\005 \001(\tBP\n\037ehooray.fishi" +
      "ng.dto.proto.modelB\023IabTransactionProtoH" +
      "\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_IabTransaction_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_IabTransaction_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_IabTransaction_descriptor,
        new java.lang.String[] { "EntityId", "CsvId", "TransactionId", "State", "Exception", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
