using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Cysharp.Threading.Tasks;
using System.Threading;
using DG.Tweening;
using Zenject;
using Ehooray.UI;
using Fishing.UI;
using Ehooray;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/Battle/TensionCanvas")]
    public class LineTensionManager : GamePanel
    {
        enum TensionState
        {
            BeforeStart = 0,
            Pull,
            Release,
            Pause,
            End
        }
        TensionState tensionState;

        [Header("管理器引用")]
        public PlayerHpCircleManager playerHpCircleManager;
        public FishingSkillBarManager fishingSkillBarManager;

        [Header("UI組件")]
        [SerializeField]
        Button pullButton;
        [SerializeField]
        Button pauseButton;
        [SerializeField]
        Image tensionBarImage;
        [SerializeField]
        Image warningImage;
        [SerializeField]
        Image fishHPBarImage;

        [Header("狀態條圖片")]
        [SerializeField]
        Sprite stateBarGreen;
        [SerializeField]
        Sprite stateBarYellow;
        [SerializeField] 
        Sprite stateBarRed;
        [SerializeField]
        Sprite stateBarBlue;

        [Header("張力基礎參數")]
        [SerializeField]
        float defaultTension = 10;
        [SerializeField]
        float currentTension = 0;
        [SerializeField]
        int minTension = 0;
        [SerializeField]
        int maxTension = 1000;
        [SerializeField]
        float raiseSkillThresholdPercentage = 0.9f;

        [Header("張力計算參數")]
        [SerializeField]
        int tensionParameter = 100;
        [SerializeField]
        int struggleMultiplier = 100;
        [SerializeField]
        int rodControlValue = 10;
        [SerializeField]
        int baseRaiseTensionPerSecond = 100;
        [SerializeField]
        int extraRaiseTensionPerSecond = 100;
        [SerializeField]
        int minRaisePerTime = 1;

        [Header("張力變化參數")]
        public float raiseTensionPerTime = 10;
        public float reduceTensionPerTime = 5;
        [Range(0, 1)]
        public float lowAlertThreshold = 0.2f;
        [Range(0, 1)]
        public float highAlertThreshold = 0.8f;
        public float raiseWarningDuration = 0.2f;
        public float dropWarningDuration = 0.4f;

        [Header("警告動畫參數")]
        [SerializeField]
        AnimationCurve warningCurve;
        [SerializeField]
        float fillPercentage;
        [SerializeField]
        float fakePercentage;
        [SerializeField]
        float fakePercentageAmplitude;
        [SerializeField]
        float amplitudeSpeed = 5;
        [SerializeField]
        int amplitudeLimit = 2;

        float StunMF;
        float SStruggleMF;
        float MStruggleMF;
        float LStruggleMF;

        CancellationTokenSource cancellationTokenSource;

        #region Zenject Inejct
        FishingManager fishingManager;
        FishingGamePanelManager fishingGamePanelManager;
        TableContainer tableContainer;
        FishingRepositories fishingRepositories;
        FishingContainer fishingContainer;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
                                TableContainer tableContainer,
                                FishingGamePanelManager fishingGamePanelManager,
                                FishingRepositories fishingRepositories,
                                FishingContainer fishingContainer)
        {
            this.fishingManager = fishingManager;
            this.tableContainer = tableContainer;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.fishingRepositories = fishingRepositories;
            this.fishingContainer = fishingContainer;
        }

        private void Awake()
        {
            pauseButton.onClick.AddListener(OpenPausePanel);
        }
        void Start()
        {
            //Application.targetFrameRate = 30;
            DynamicGI.UpdateEnvironment();

            LoadParameter();
        }

        public void CloseRoot()
        {
            if (cancellationTokenSource != null)
                cancellationTokenSource.Cancel();

            ResetWarningImage();

            fishingSkillBarManager.CancelGetStunPower();
            playerHpCircleManager.CloseRoot();
        }

        // Update is called once per frame
        void Update()
        {
            //currentTension = 500;

        }

        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }

        public async void Play()
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Dispose();
            }
            cancellationTokenSource = new CancellationTokenSource();

            ShakeCamera.ShakeOnce(Camera.main).Forget();
            fishingSkillBarManager.SetSkillBarValue(0);

            playerHpCircleManager.Play().Forget();

            var closeupText = await fishingGamePanelManager.OpenAsync<CloseupTextManager>();
            closeupText.ShowCloseupText().Forget();

            defaultTension = maxTension / 3;
            currentTension = defaultTension;
            RefreshTensionBar();
            LetFishGo();

            try
            {
                while (!cancellationTokenSource.IsCancellationRequested)
                {
                    //Debug.Log("state = " + tensionState);
                    switch (tensionState)
                    {
                        case TensionState.BeforeStart:
                            break;
                        case TensionState.Pull:
                            //張力上升
                            RaiseTensionBar();
                            RiseSkillBar();
                            break;
                        case TensionState.Release:
                            //張力下降
                            ReduceTensionBar();
                            break;
                        case TensionState.Pause:
                            break;
                        case TensionState.End:
                            break;
                        default:
                            break;
                    }

                    RefreshTensionBar();
                    AlertTensionDanger();
                    RaiseSkillBar();
                    await UniTask.Delay(16, delayTiming: PlayerLoopTiming.FixedUpdate, cancellationToken: cancellationTokenSource.Token);
                }

            }
            catch
            {

            }

        }

        public void ResetStatus()
        {
            tensionState = TensionState.BeforeStart;
        }

        public void OnPullButtonPressed(PointerEventData data)
        {
            PullFish();
            fishingManager.cordReelManager.wheelRotate.SetPullFishSpeed();
        }

        public void PullFish()
        {
            tensionState = TensionState.Pull;
        }

        public void OnPullButtonUp(PointerEventData data)
        {
            LetFishGo();
            fishingManager.cordReelManager.wheelRotate.SetReleaseFishSpeed();
        }

        public void LetFishGo()
        {
            tensionState = TensionState.Release;
        }

        public void RefreshTensionBar()
        {
            fillPercentage = currentTension / maxTension;
            fakePercentage = fillPercentage + fakePercentageAmplitude * Mathf.Sin(Time.time * amplitudeSpeed);
            //Debug.Log("raiseTensionPerTime = " + raiseTensionPerTime + ", amplitudeLimit = " + amplitudeLimit);
            if (tensionState == TensionState.Pull && raiseTensionPerTime < amplitudeLimit)
            {
                tensionBarImage.fillAmount = fakePercentage;
            }
            else
            {
                tensionBarImage.fillAmount = fillPercentage;
            }

        }

        public void RaiseTensionBar()
        {
            currentTension += raiseTensionPerTime;
            if (currentTension >= maxTension)
            {
                currentTension = maxTension;
                //Debug.Log("線斷了");
                //tensionState = TensionState.End;
                //通知不計時
                fishingManager.TransitionToState(FishingState.Fail).Forget();
            }
        }

        public void ReduceTensionBar()
        {
            currentTension -= reduceTensionPerTime / 60f;
            if (currentTension <= minTension)
            {
                currentTension = minTension;
                //Debug.Log("魚跑了");
                //tensionState = TensionState.End;
                //通知不計時
                fishingManager.TransitionToState(FishingState.Fail).Forget();
            }
        }
        public void RiseSkillBar()
        {
            var struggleValue = GetStruggleValue() / 1000f;
            var level = fishingRepositories.playerRepository.player.level;
            var lvSkillAdd = (float)tableContainer.LevelTable.GetLvSkillAdd(level) / 1000f;
            var variableSkillAdd = tableContainer.VariableTable.SkillAdd;
            var addValue = variableSkillAdd * (1 + lvSkillAdd);
            var perAddValue = addValue * struggleValue / 60;
            fishingSkillBarManager.AddSkillBarValue(perAddValue);
        }

        public bool IsValidFight()
        {
            return currentTension > minTension && currentTension < maxTension && tensionState == TensionState.Pull;
        }

        void AlertTensionDanger()
        {
            //Debug.Log("tension danger");
            //fillPercentage = tensionBarImage.fillAmount;
            if (tensionState != TensionState.Pull && tensionState != TensionState.Release)
            {
                //Debug.Log("state wrong");
                return;
            }

            if (cancellationTokenSource.IsCancellationRequested)
            {
                //Debug.Log("cancel return");
                return;
            }

            if (fillPercentage <= lowAlertThreshold || fillPercentage >= highAlertThreshold)
            {
                //開啟警告
                //Debug.Log("fillamount = " + fillPercentage);
                DoWarningTween();
            }
            else
            {
                //關閉警告
                CloseWarning();
            }
        }

        void DoWarningTween()
        {
            bool tweening = DOTween.IsTweening(warningImage);
            if (warningImage.color.a == 0)
            {
                DOTween.Kill(warningImage);
            }
            if (tweening)
            {
                return;
            }
            //Debug.Log("start dotween");
            ShakeCamera.ShakeOnce(Camera.main, 0.01f).Forget();
            warningImage.DOColor(new Color(1, 1, 1, 0.9f), raiseWarningDuration).SetEase(warningCurve);
        }

        void CloseWarning()
        {
            bool tweening = DOTween.IsTweening(warningImage);
            if (warningImage.color.a == 0)
            {
                return;
            }
            if (tweening)
            {
                DOTween.Kill(warningImage);
            }
            warningImage.DOColor(new Color(1, 1, 1, 0), dropWarningDuration);
        }

        public void ResetWarningImage()
        {
            DOTween.Kill(warningImage);
            warningImage.color = new Color(1, 1, 1, 0);
        }

        public void SetRaisePerTime(int value)
        {
            raiseTensionPerTime = value;
            raiseTensionPerTime = Mathf.Clamp(raiseTensionPerTime, minRaisePerTime, maxTension);
        }

        public void SetReducePerTime(int value)
        {
            reduceTensionPerTime = value;
            //reduceTensionPerTime = Mathf.Clamp(raiseTensionPerTime, minRaisePerTime, maxTension);
        }

        float lastAmount = 0;
        void RaiseSkillBar()
        {

            if (fillPercentage < raiseSkillThresholdPercentage)
            {
                lastAmount = fillPercentage;
                return;
            }
            if (tensionState != TensionState.Pull)
            {
                return;
            }

            if (lastAmount < raiseSkillThresholdPercentage)
            {
                lastAmount = fillPercentage;
                fishingSkillBarManager.RaisePowerOnce();
                Debug.Log("In Rise Area");
            }
        }

        public void SetRaiseSkillThresholdPercentage(int thousandValue)
        {
            //value為千分比，要除以1000
            raiseSkillThresholdPercentage = thousandValue / 1000f;
        }

        public void SetReduceTensionPerTime(int thousandValue)
        {
            reduceTensionPerTime = thousandValue / 1000f;
        }

        public void SetExtraRaiseTensionPerSecond(int thousandValue)
        {
            extraRaiseTensionPerSecond = thousandValue;
        }

        public void SetBaseRaiseTensionPerSecond(int thousandValue)
        {
            baseRaiseTensionPerSecond = thousandValue;
        }

        public void SetRodControlValue(int thousandValue)
        {
            rodControlValue = thousandValue;
        }

        public void SetStruggleMultiplier(int thousandValue)
        {
            struggleMultiplier = thousandValue;
        }

        public void SetTensionParameter(int thousandValue)
        {
            tensionParameter = thousandValue;
        }

        public void SetRaiseTensionPerTimeWhenChangeState()
        {
            raiseTensionPerTime = CalculateRaiseTensionPerTime();
        }

        public float CalculateRaiseTensionPerTime()
        {
            //[張力增幅公式]= (基底升張力值 + 追加張力值 - 釣竿控制值) * 掙扎狀態倍率 * 張力參數
            //計算結果<基底升張力值，則為基底升張力值
            //"(追加張力值 - 釣竿控制值)" 為魚與釣竿的強度對抗，結果最低 = 0，不為負值
            float struggleRate = GetStruggleValue() / 1000f;
            float tensionRate = tensionParameter / 1000f;
            int extensionMinusRodControl = Mathf.Max(0, extraRaiseTensionPerSecond - rodControlValue);
            float result = (baseRaiseTensionPerSecond + extensionMinusRodControl) * struggleRate * tensionRate;

            return result / 60f;
        }

        void LoadParameter()
        {
            StunMF = tableContainer.VariableTable.StunMF;
            SStruggleMF = tableContainer.VariableTable.SStruggleMF;
            MStruggleMF = tableContainer.VariableTable.MStruggleMF;
            LStruggleMF = tableContainer.VariableTable.LStruggleMF;
            baseRaiseTensionPerSecond = tableContainer.VariableTable.baseRaiseTensionPerSecond;
            reduceTensionPerTime = tableContainer.VariableTable.reduceTensionPerTime;
            tensionParameter = tableContainer.VariableTable.tensionParameter;
            raiseSkillThresholdPercentage = tableContainer.VariableTable.raiseSkillThresholdPercentage;
        }

        float GetStruggleValue()
        {
            float struggleValue;
            switch (fishingManager.fish.state)
            {
                case StruggleState.Big:
                    struggleValue = LStruggleMF;
                    break;
                case StruggleState.Mid:
                    struggleValue = MStruggleMF;
                    break;
                case StruggleState.Small:
                    struggleValue = SStruggleMF;
                    break;
                case StruggleState.Stun:
                    struggleValue = StunMF;
                    break;
                default:
                    struggleValue = SStruggleMF;
                    break;

            }
            return struggleValue;
        }

        public void SetFishData(FishTableData data)
        {
            extraRaiseTensionPerSecond = data.Extension;
        }
        public void OpenPausePanel()
        {
            var runner = OpenBagPanelRunner();
            runner.RunAsync(showMask: true).Forget();
        }

        public void OpenBagPanel()
        {
            var runner = OpenBagPanelRunner();
            runner.RunAsync(showMask: true).Forget();
        }

        public AsyncProgressRunner OpenBagPanelRunner()
        {
            var runner = fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                var pausePanel = await gamePanelManager.OpenAsync<PausePanelManager>();
                Time.timeScale = 0;
                gamePanelManager.Close<FishInfoManager>();
            });
            return runner;
        }
        public void SetFishHPBar(float hpPersent)
        {
            fishHPBarImage.fillAmount = hpPersent;
            switch (fishingManager.fish.state)
            {
                case StruggleState.Big:
                    fishHPBarImage.sprite = stateBarRed;
                    break;
                case StruggleState.Mid:
                    fishHPBarImage.sprite = stateBarYellow;
                    break;
                case StruggleState.Small:
                    fishHPBarImage.sprite = stateBarGreen;
                    break;
                case StruggleState.Stun:
                    fishHPBarImage.sprite = stateBarBlue;
                    break;
                default:
                    break;
            }
        }
    }
}