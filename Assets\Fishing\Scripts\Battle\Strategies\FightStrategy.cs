using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚戰鬥狀態策略
    /// </summary>
    public class FightStrategy : FishingStrategyBase
    {
        public FightStrategy() : base(FishingState.Fight) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToFight方法中的初始化邏輯
            context.forceItem.PlayWaterEffectForFight();
            context.fishLine.StopLineAnimation();
            
            await context.fishingGamePanelManager.OpenAsync<LineTensionManager>();
            context.lineTensionManager.Play();
            
            var trigger = FishingEventTrigger.GetTrigger(context.cordReelManager.pullButton.gameObject);
            trigger.onPointerDown += context.lineTensionManager.OnPullButtonPressed;
            trigger.onPointerUp += context.lineTensionManager.OnPullButtonUp;
            
            context.cordReelManager.SetCordReelButtonColorNormal();
            context.cordReelManager.StartInteractive();
            
            context.fishInfoManager.ResetText();
            context.fishInfoManager.SetLineLengthText(context.fish.lineLength + context.EquipData.lineData.CsvData.Parameter_1);
            await context.fishingGamePanelManager.OpenAsync<FishInfoManager>();
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoFight方法中的邏輯
            context.fish.Move();
            context.fish.Leave();
            context.fishLine.StraightLine();
            context.forceItem.SetForce(40f);
            context.fishRod.KeepFacingFish();
            context.FightRotateCamera();
            
            if (context.lineTensionManager.IsValidFight())
            {
                context.fish.Fight();
            }
            
            context.fish.SetDistanceZ();
            
            // 狀態轉換邏輯
            if (context.fish.distance <= context.fish.catchDistance)
            {
                context.TransitionToState(FishingState.Catch).Forget();
                return;
            }
            
            if (context.fish.distance >= context.fish.lineLength + context.EquipData.lineData.CsvData.Parameter_1)
            {
                context.TransitionToState(FishingState.Fail).Forget();
                return;
            }
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            // 清理事件監聽
            var trigger = FishingEventTrigger.GetTrigger(context.cordReelManager.pullButton.gameObject);
            trigger.onPointerDown -= context.lineTensionManager.OnPullButtonPressed;
            trigger.onPointerUp -= context.lineTensionManager.OnPullButtonUp;
            
            return UniTask.CompletedTask;
        }
    }
} 