# 1. 遊戲概覽系統設計文檔

## 1.1 遊戲概念設計

### 核心概念
Luxury Angler 是一個融合現實釣魚機制與奢華生活主題的 2D 釣魚模擬遊戲。玩家從新手釣魚者成長為大師級釣手，體驗 50 個等級的進程，探索 5 個異域地圖，每個地圖包含 10 個釣魚點。

### 遊戲核心特色
- **奢華釣魚生活風格**: 高端船隻和異域釣魚地點
- **深度合成系統**: 透過魚卡合成機制製作釣竿、魚餌、船隻和服裝
- **動態環境系統**: 日夜循環和天氣系統影響魚類可得性
- **技能與休閒並重**: 技能導向和放置類遊戲玩法的結合

### 技術實現架構
相關檔案：
- [gameConfig.json](../src/data/gameConfig.json): 遊戲全域配置
- [TextConfig.js](../src/data/TextConfig.js): 文字配置系統
- [GameState.js](../src/scripts/GameState.js): 遊戲狀態管理器
- [GameLoop.js](../src/scripts/GameLoop.js): 遊戲主循環

## 1.2 目標受眾分析

### 主要目標群體
- 年齡：16-35 歲的休閒到中度玩家
- 偏好：模擬、生活模擬和 RPG 遊戲愛好者
- 參考遊戲：Stardew Valley、Dave the Diver
- 遊戲動機：享受進程、合成、社交元素與奢華體驗

### 玩家類型適配
1. **休閒玩家**: 
   - 自動釣魚功能
   - 簡化的UI操作
   - 清晰的進度指引

2. **中度玩家**: 
   - 技能導向的迷你遊戲
   - 複雜的合成系統
   - 社交互動機制

## 1.3 平台與技術規格

### 支援平台
- **主要平台**: 行動裝置 (iOS, Android)
- **潛在平台**: PC (Steam), Nintendo Switch

### 技術架構
- **遊戲引擎**: Phaser 3
- **圖形風格**: 手繪 2D 風格，鮮豔水域，奢華船隻，精細魚類
- **效能目標**: 60 FPS，輕量級時間/天氣模擬

相關檔案：
- [BootScene.js](../src/scenes/BootScene.js): 遊戲啟動場景
- [PreloadScene.js](../src/scenes/PreloadScene.js): 資源預載入
- [MenuScene.js](../src/scenes/MenuScene.js): 主選單場景

## 1.4 遊戲類型與機制

### 核心類型
- **模擬遊戲**: 真實的釣魚體驗
- **釣魚遊戲**: 專業的釣魚機制
- **生活模擬**: 奢華生活風格
- **卡牌收集**: 50 張魚卡系統
- **社交遊戲**: 10 位比基尼女伴系統
- **放置遊戲**: 自動釣魚選項

### 獨特賣點 (USP)
1. **奢華釣魚生活方式**: 結合高端船隻和異域地點
2. **深度合成系統**: 使用魚卡合成裝備的創新機制
3. **動態環境**: 8 個時間段和天氣系統影響遊戲體驗
4. **技能與休閒平衡**: 滿足不同類型玩家需求

## 1.5 核心功能概述

### 釣魚機制
相關檔案：
- [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚迷你遊戲主控
- [CastingMiniGame.js](../src/scripts/CastingMiniGame.js): 拋竿遊戲
- [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 誘魚遊戲
- [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js): 收線遊戲

### 合成系統
相關檔案：
- [CraftingManager.js](../src/scripts/CraftingManager.js): 合成管理器
- [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面

### 進程系統
相關檔案：
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 玩家進程管理
- [PlayerProgressionUI.js](../src/ui/PlayerProgressionUI.js): 進程介面

### 社交系統
相關檔案：
- [CabinScene.js](../src/scenes/CabinScene.js): 船艙場景
- [DialogScene.js](../src/scenes/DialogScene.js): 對話場景
- [DialogManager.js](../src/scripts/DialogManager.js): 對話管理器

## 1.6 設計原則與限制

### 設計原則
1. **易於學習，難於精通**: 簡單入門，深度挑戰
2. **視覺奢華感**: 高品質的視覺呈現
3. **漸進式揭露**: 功能逐步解鎖
4. **玩家選擇權**: 技能或休閒模式自由切換

### 技術限制
1. **行動裝置效能**: 優化記憶體使用
2. **網路依賴**: 雲端存檔功能
3. **本地化需求**: 多語言支援架構

相關檔案：
- [DataLoader.js](../src/scripts/DataLoader.js): 數據載入器
- [SaveUtility.js](../src/scripts/SaveUtility.js): 存檔工具
- [SettingsManager.js](../src/scripts/SettingsManager.js): 設定管理

## 1.7 系統間整合

### 核心系統連接
```
遊戲狀態 (GameState) 
    ↓
場景管理 (SceneManager) 
    ↓
各個功能場景 (GameScene, BoatMenuScene, etc.)
    ↓
UI系統 (各種UI組件)
```

### 數據流架構
```
配置數據 (gameConfig.json)
    ↓
遊戲狀態管理 (GameState.js)
    ↓
各系統管理器 (Manager classes)
    ↓
使用者介面 (UI classes)
```

相關檔案：
- [SceneManager.js](../src/scripts/SceneManager.js): 場景管理器
- [EventEmitter.js](../src/scripts/EventEmitter.js): 事件系統 