package ehooray.fishing.component.init;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.csv.CsvLoader;
import ehooray.fishing.component.utils.ProtoToStringConverter;

@Component
@Order(RunnerOrder.Logger)
public class LoggerInitialRunner implements ApplicationRunner
{
	@Autowired
    protected CsvLoader csvLoader;

    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected ProtoToStringConverter protoToStringConverter;

    @Value("${game.proto.class-packages}")
    protected List<String> protoClassPackages;

    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        String[] packages = protoClassPackages.toArray(String[]::new);
        protoToStringConverter.initial(packages);
    }
}
