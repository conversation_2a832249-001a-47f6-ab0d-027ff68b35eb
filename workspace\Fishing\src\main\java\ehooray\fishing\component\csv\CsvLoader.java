package ehooray.fishing.component.csv;

import java.io.File;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.poiji.bind.Poiji;

import ehooray.fishing.annotation.CsvPath;
import ehooray.fishing.pojo.csv.ACsv;

@Component
public class CsvLoader
{
    @Value("${game.csv.file-path}")
    protected String csvFolder;
    
    public ArrayList<ACsv<?>> load(String[] csvPackageNames) throws Exception
    {
        ConfigurationBuilder configBuilder = new ConfigurationBuilder();
        configBuilder.setScanners(new TypeAnnotationsScanner(), new SubTypesScanner(false));
        configBuilder.forPackages(csvPackageNames);

        Reflections reflections = new Reflections(configBuilder);
        Set<Class<?>> classes = reflections.getTypesAnnotatedWith(CsvPath.class);
        ArrayList<ACsv<?>> results = new ArrayList<ACsv<?>>(classes.size());
        for (Class<?> singleClass : classes)
        {
            String csvPath = singleClass.getAnnotation(CsvPath.class).value();
            File csvFile = new File(csvFolder + csvPath);
            
            ParameterizedType parameterizedType = (ParameterizedType) singleClass.getGenericSuperclass();
            Class<?> rowClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
            List<?> rows = Poiji.fromExcel(csvFile, rowClass);
            ACsv<?> newCsv = (ACsv<?>) singleClass.getDeclaredConstructor().newInstance();
            newCsv.setCsvPath(csvPath);
            newCsv.addRow(rows);

            results.add(newCsv);
            newCsv.onLoadFinish();
        }

        return results;
    }
}
