// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/fishrod/RepairFishRodResult.proto

package ehooray.fishing.dto.proto.response.fishrod;

public final class RepairFishRodResultProto {
  private RepairFishRodResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RepairFishRodResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.fishrod.RepairFishRodResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return Whether the fishRod field is set.
     */
    boolean hasFishRod();
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return The fishRod.
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod();
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     */
    ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumeItemsList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    int getConsumeItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.fishrod.RepairFishRodResult}
   */
  public static final class RepairFishRodResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.fishrod.RepairFishRodResult)
      RepairFishRodResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepairFishRodResult.newBuilder() to construct.
    private RepairFishRodResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepairFishRodResult() {
      consumeItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepairFishRodResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.internal_static_response_fishrod_RepairFishRodResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.internal_static_response_fishrod_RepairFishRodResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.Builder.class);
    }

    public static final int FISHROD_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return Whether the fishRod field is set.
     */
    @java.lang.Override
    public boolean hasFishRod() {
      return fishRod_ != null;
    }
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     * @return The fishRod.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
      return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
    }
    /**
     * <code>.model.FishRod fishRod = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
      return getFishRod();
    }

    public static final int CONSUMEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_;
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public int getConsumeItemsCount() {
      return consumeItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
      return consumeItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index) {
      return consumeItems_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.fishrod.RepairFishRodResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.fishrod.RepairFishRodResult)
        ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.internal_static_response_fishrod_RepairFishRodResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.internal_static_response_fishrod_RepairFishRodResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.class, ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumeItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.internal_static_response_fishrod_RepairFishRodResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult build() {
        ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult buildPartial() {
        ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult result = new ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult(this);
        int from_bitField0_ = bitField0_;
        if (fishRodBuilder_ == null) {
          result.fishRod_ = fishRod_;
        } else {
          result.fishRod_ = fishRodBuilder_.build();
        }
        if (consumeItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumeItems_ = java.util.Collections.unmodifiableList(consumeItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumeItems_ = consumeItems_;
        } else {
          result.consumeItems_ = consumeItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.FishRodProto.FishRod fishRod_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> fishRodBuilder_;
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       * @return Whether the fishRod field is set.
       */
      public boolean hasFishRod() {
        return fishRodBuilder_ != null || fishRod_ != null;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       * @return The fishRod.
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod getFishRod() {
        if (fishRodBuilder_ == null) {
          return fishRod_ == null ? ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        } else {
          return fishRodBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder setFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishRod_ = value;
          onChanged();
        } else {
          fishRodBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder setFishRod(
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder builderForValue) {
        if (fishRodBuilder_ == null) {
          fishRod_ = builderForValue.build();
          onChanged();
        } else {
          fishRodBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder mergeFishRod(ehooray.fishing.dto.proto.model.FishRodProto.FishRod value) {
        if (fishRodBuilder_ == null) {
          if (fishRod_ != null) {
            fishRod_ =
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.newBuilder(fishRod_).mergeFrom(value).buildPartial();
          } else {
            fishRod_ = value;
          }
          onChanged();
        } else {
          fishRodBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public Builder clearFishRod() {
        if (fishRodBuilder_ == null) {
          fishRod_ = null;
          onChanged();
        } else {
          fishRod_ = null;
          fishRodBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder getFishRodBuilder() {
        
        onChanged();
        return getFishRodFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder getFishRodOrBuilder() {
        if (fishRodBuilder_ != null) {
          return fishRodBuilder_.getMessageOrBuilder();
        } else {
          return fishRod_ == null ?
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod.getDefaultInstance() : fishRod_;
        }
      }
      /**
       * <code>.model.FishRod fishRod = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder> 
          getFishRodFieldBuilder() {
        if (fishRodBuilder_ == null) {
          fishRodBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.FishRodProto.FishRod, ehooray.fishing.dto.proto.model.FishRodProto.FishRod.Builder, ehooray.fishing.dto.proto.model.FishRodProto.FishRodOrBuilder>(
                  getFishRod(),
                  getParentForChildren(),
                  isClean());
          fishRod_ = null;
        }
        return fishRodBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumeItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumeItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumeItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumeItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
        if (consumeItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeItems_);
        } else {
          return consumeItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public int getConsumeItemsCount() {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.size();
        } else {
          return consumeItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);
        } else {
          return consumeItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addAllConsumeItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeItems_);
          onChanged();
        } else {
          consumeItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder clearConsumeItems() {
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder removeConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.remove(index);
          onChanged();
        } else {
          consumeItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
          int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);  } else {
          return consumeItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumeItemsOrBuilderList() {
        if (consumeItemsBuilder_ != null) {
          return consumeItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder() {
        return getConsumeItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumeItemsBuilderList() {
        return getConsumeItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumeItemsFieldBuilder() {
        if (consumeItemsBuilder_ == null) {
          consumeItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumeItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumeItems_ = null;
        }
        return consumeItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.fishrod.RepairFishRodResult)
    }

    // @@protoc_insertion_point(class_scope:response.fishrod.RepairFishRodResult)
    private static final ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult();
    }

    public static ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RepairFishRodResult>
        PARSER = new com.google.protobuf.AbstractParser<RepairFishRodResult>() {
      @java.lang.Override
      public RepairFishRodResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RepairFishRodResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepairFishRodResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto.RepairFishRodResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_fishrod_RepairFishRodResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_fishrod_RepairFishRodResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*response/fishrod/RepairFishRodResult.p" +
      "roto\022\020response.fishrod\032\023model/FishRod.pr" +
      "oto\032\026model/PlayerItem.proto\"_\n\023RepairFis" +
      "hRodResult\022\037\n\007fishRod\030\001 \001(\0132\016.model.Fish" +
      "Rod\022\'\n\014consumeItems\030\002 \003(\0132\021.model.Player" +
      "ItemB`\n*ehooray.fishing.dto.proto.respon" +
      "se.fishrodB\030RepairFishRodResultProtoH\002\252\002" +
      "\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_fishrod_RepairFishRodResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_fishrod_RepairFishRodResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_fishrod_RepairFishRodResult_descriptor,
        new java.lang.String[] { "FishRod", "ConsumeItems", });
    ehooray.fishing.dto.proto.model.FishRodProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
