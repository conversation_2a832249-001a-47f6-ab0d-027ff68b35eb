package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("IabTable.xlsx")
public class IabCsv extends ACsv<IabCsv.Row>
{
    public enum ItemType
    {
        Ignore(0),
//        Character(1),
//        Building(2),
        PlayerItem(3)
        ;

        protected int id;
        private ItemType(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<ItemType> valueOf(int id)
        {
            return Arrays.stream(ItemType.values()).filter(x-> x.getId() == id).findFirst();
        }

    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Product_ID")
        protected String productId;

        @ExcelCellName("Normalize_Price")
        protected float normalizePrice;

        @ExcelCellName("ItemType1")
        protected int itemType1;

        @ExcelCellName("ItemID_1")
        protected int itemId1;

        @ExcelCellName("ItemNum_1")
        protected int itemNum1;

        @ExcelCellName("ItemType2")
        protected int itemType2;

        @ExcelCellName("ItemID_2")
        protected int itemId2;

        @ExcelCellName("ItemNum_2")
        protected int itemNum2;

        @ExcelCellName("ItemType3")
        protected int itemType3;

        @ExcelCellName("ItemID_3")
        protected int itemId3;

        @ExcelCellName("ItemNum_3")
        protected int itemNum3;

        @ExcelCellName("ItemType4")
        protected int itemType4;

        @ExcelCellName("ItemID_4")
        protected int itemId4;

        @ExcelCellName("ItemNum_4")
        protected int itemNum4;

        @Override
        public int getId()
        {
            return id;
        }


        public int getRowIndex()
        {
            return rowIndex;
        }


        public String getProductId()
        {
            return productId;
        }


        public float getNormalizePrice()
        {
            return normalizePrice;
        }


        public int getItemId1()
        {
            return itemId1;
        }


        public int getItemNum1()
        {
            return itemNum1;
        }


        public int getItemId2()
        {
            return itemId2;
        }


        public int getItemNum2()
        {
            return itemNum2;
        }


        public int getItemId3()
        {
            return itemId3;
        }


        public int getItemNum3()
        {
            return itemNum3;
        }


        public int getItemId4()
        {
            return itemId4;
        }


        public int getItemNum4()
        {
            return itemNum4;
        }

        protected int[] itemTypes;
        public int[] getItemTypes()
        {
            if(itemTypes != null) return itemTypes;

            // atomic operation, do not assign separately
            itemTypes = new int[] { itemType1, itemType2, itemType3, itemType4 };
            return itemTypes;
        }

        protected int[] itemIds;
        public int[] getItemIds()
        {
            if(itemIds != null) return itemIds;

            // atomic operation, do not assign separately
            itemIds = new int[] { itemId1, itemId2, itemId3, itemId4 };
            return itemIds;
        }

        protected int[] itemNums;
        public int[] getItemNums()
        {
            if(itemNums != null) return itemNums;

            // atomic operation, do not assign separately
            itemNums = new int[] { itemNum1, itemNum2, itemNum3, itemNum4 };
            return itemNums;
        }

    }

}
