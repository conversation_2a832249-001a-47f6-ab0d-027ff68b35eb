package ehooray.fishing.pojo.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

public final class LocalDateTimeUtils
{
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime)
    {
        return localDateTime.atZone(ZoneId.systemDefault());
    }

    public static LocalDateTime Min(LocalDateTime a, LocalDateTime b)
    {
        return (a.isBefore(b)) ? a : b;
    }

    public static LocalDateTime Max(LocalDateTime a, LocalDateTime b)
    {
        return (a.isAfter(b)) ? a : b;
    }
    
    public static boolean isOverADay(LocalDateTime now, LocalDateTime compareDate)
    {
        return now.isAfter(getMidnight(compareDate));
    }
    
    public static LocalDateTime getMidnight(LocalDateTime dateTime)
    {
        return getMidnightOfPlusDays(dateTime, 1);
    }
    
    public static LocalDateTime getMidnightOfPlusDays(LocalDateTime dateTime, int days)
    {
        return dateTime.truncatedTo(ChronoUnit.DAYS).plusDays(days);
    }
}
