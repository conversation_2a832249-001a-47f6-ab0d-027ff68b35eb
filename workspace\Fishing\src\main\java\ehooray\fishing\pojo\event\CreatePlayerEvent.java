package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class CreatePlayerEvent extends ApplicationEvent
{
    private final PlayerEntity player;

    public CreatePlayerEvent(Object source, PlayerEntity player)
    {
        super(source);
        this.player = player;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }
}
