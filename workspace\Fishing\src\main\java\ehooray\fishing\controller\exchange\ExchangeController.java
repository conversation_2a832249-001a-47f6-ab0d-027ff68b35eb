package ehooray.fishing.controller.exchange;

import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.ExchangeProtoMap;
import ehooray.fishing.component.protomap.model.FishItemProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.dto.proto.request.exchange.ExchangeFishProto;
import ehooray.fishing.dto.proto.request.exchange.ExchangeItemProto;
import ehooray.fishing.dto.proto.response.exchange.ExchangeFishResultProto;
import ehooray.fishing.dto.proto.response.exchange.ExchangeItemResultProto;
import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.DisplayCsv;
import ehooray.fishing.pojo.csv.ExchangeCsv;
import ehooray.fishing.pojo.csv.FishCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.service.exchange.ExchangeService;
import ehooray.fishing.service.item.FishItemService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;

@Controller("ExchangeController")
public class ExchangeController
{
    @Autowired
    protected CsvContainer csvContainer;
    @Autowired
    protected ExchangeService exchangeService;
    @Autowired
    protected PlayerItemService playerItemService;
    @Autowired
    protected FishItemService fishItemService;
    @Autowired
    protected ExchangeProtoMap exchangeProtoMap;
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    @Autowired
    protected FishItemProtoMap fishItemProtoMap;
    @Autowired
    protected ScheduleCalculator scheduleCalculator;
    
    @TransactionMethod(Method.Write)
    public void getExchanges(WebSocketHandleMediator handleMediator) throws Exception
    {
        var player = handleMediator.getPlayer().get();
        var now = handleMediator.getRequestStartTime();
        
        var exchangeCsv = csvContainer.getCsv(ExchangeCsv.class);
        
        // remove not on csv exchange entities
        var missingCsvExchanges = exchangeService.getAllByPlayer(player).stream().filter(
                exchange -> exchangeCsv.getOptionalById(exchange.getCsvId()).isEmpty()).collect(Collectors.toList());
        exchangeService.deleteAll(player, missingCsvExchanges);
        // remove already out of date exchange entities
        var expiredExchanges = exchangeService.getAllByPlayer(player).stream().filter(exchange ->
        {
            return scheduleCalculator.isOutStartCycle(DisplayCsv.Type.Exchange, exchange.getCsvId(), exchange.getStartTime().get(), now);
        }).collect(Collectors.toList());
        exchangeService.deleteAll(player, expiredExchanges);
        
        // find not exist exchange rows
        var existCsvIds = exchangeService.getAllByPlayer(player).stream().map(exchange -> exchange.getCsvId()).collect(Collectors.toList());
        var candidateExchangeRows = exchangeCsv.getRows().stream().filter(row -> !existCsvIds.contains(row.getId())).collect(Collectors.toList());
        
        // filter not on schedule
        candidateExchangeRows.removeIf((row) -> !scheduleCalculator.isInCycle(DisplayCsv.Type.Exchange, row.getId(), now));
        
        // create new exchanges
        for (ExchangeCsv.Row shopRow : candidateExchangeRows)
        {
            exchangeService.create(player, shopRow, now);
        }
        
        handleMediator.addResult(exchangeProtoMap.toAnyByEntities(player.getExchanges(), now));
    }
    
    @TransactionMethod(Method.Write)
    public void exchangeItem(WebSocketHandleMediator handleMediator) throws Exception
    {
        var exchangeItem = handleMediator.getRequest().getDataList(0).unpack(ExchangeItemProto.ExchangeItem.class);
        var player = handleMediator.getPlayer().get();
        var now = handleMediator.getRequestStartTime();
        var exchangeEntity = exchangeService.findByEntityId(player, exchangeItem.getExchangeEntity()).get();
        var exchangeRow = csvContainer.getCsv(ExchangeCsv.class).getById(exchangeEntity.getCsvId());
        
        exchangeService.exchangeItem(exchangeEntity, exchangeItem.getExchangeCount());
        
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.ExchangeController_exchangeItem, exchangeEntity.getCsvId()));
        var consumeItemResult = new ArrayList<DiffItem>();
        var produceItemResult = new ArrayList<DiffItem>();
        for (int i = 0; i < exchangeItem.getExchangeCount(); i++)
        {
            // consume items
            consumeItemResult.addAll(playerItemService.consumeItems(player, exchangeRow.getRequireIds(), exchangeRow.getRequireAmounts()));
            // reward item
            var itemRow = csvContainer.getCsv(PlayerItemCsv.class).getById(exchangeRow.getExchangeId());
            produceItemResult.add(playerItemService.produceItem(player, itemRow, exchangeRow.getExchangeAmount()));
        }
        
        var resultBuilder = ExchangeItemResultProto.ExchangeItemResult.newBuilder();
        resultBuilder.setExchange(exchangeProtoMap.toBuilderByEntity(exchangeEntity, now));
        resultBuilder.addAllConsumeItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
        resultBuilder.addAllProduceItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));

        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void exchangeFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        var exchangeFish = handleMediator.getRequest().getDataList(0).unpack(ExchangeFishProto.ExchangeFish.class);
        var player = handleMediator.getPlayer().get();
        var now = handleMediator.getRequestStartTime();
        var exchangeEntity = exchangeService.findByEntityId(player, exchangeFish.getExchangeEntity()).get();
        var exchangeRow = csvContainer.getCsv(ExchangeCsv.class).getById(exchangeEntity.getCsvId());
        
        exchangeService.exchangeItem(exchangeEntity, 1);
        
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.ExchangeController_exchangeFish, exchangeEntity.getCsvId()));
        // consume fish 
        var consumeFish = fishItemService.findByEntityIds(player, exchangeFish.getFishEntitesList());
        fishItemService.exchangeFish(player, consumeFish, now);
        // reward fish
        // TODO mint NFT Fish
        var rewardFishRow = csvContainer.getCsv(FishCsv.class).getById(exchangeRow.getExchangeId());
        var rewardFish = Optional.<FishItemEntity>empty();
        if (rewardFishRow.isNFT())
            rewardFish = Optional.of(fishItemService.produceFishNFT(player, rewardFishRow));
        else
            throw new ExceptionByCsvId("not support exchange normal fish");
          
        var resultBuilder = ExchangeFishResultProto.ExchangeFishResult.newBuilder();
        resultBuilder.setExchange(exchangeProtoMap.toBuilderByEntity(exchangeEntity, now));
        resultBuilder.addAllConsumeItems(fishItemProtoMap.toProtoByEntities(consumeFish));
        if (rewardFish.isPresent())
            resultBuilder.setProduceItem(fishItemProtoMap.toProtoByEntity(rewardFish.get()));

        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
