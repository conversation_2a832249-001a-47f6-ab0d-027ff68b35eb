using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚準備狀態策略
    /// </summary>
    public class PrepareStrategy : FishingStrategyBase
    {
        public PrepareStrategy() : base(FishingState.Prepare) { }
        
        public override UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToPrepare方法中的初始化邏輯
            FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick += context.prepareManager.OnStartButtonClick;

            context.cordReelManager.OpenStartText();
            context.fishingGamePanelManager.OpenAsync<PrepareManager>().Forget();
            context.cordReelManager.wheelRotate.StopRotate();
            context.fishingGamePanelManager.OpenAsync<TopInfoManager>().Forget();
            context.prepareManager.leftArrowButton.onClick.Invoke();
            
            return UniTask.CompletedTask;
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoPrepare方法中的邏輯，目前為空
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            // 清理事件監聽
            FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick -= context.prepareManager.OnStartButtonClick;
            return UniTask.CompletedTask;
        }
    }
} 