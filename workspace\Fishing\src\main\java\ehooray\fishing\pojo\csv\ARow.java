package ehooray.fishing.pojo.csv;

import java.lang.reflect.Field;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;

import com.poiji.annotation.ExcelCellName;

import ehooray.fishing.component.lua.IToLua;
import ehooray.fishing.pojo.ExceptionByCsvId;

public abstract class ARow implements IRow, IToLua
{
    protected LuaTable toLuaRow;
    
    @Override
    public LuaTable toLua() throws Exception
    {
        if(toLuaRow != null) return toLuaRow;
        
        LuaTable table = LuaTable.tableOf();
        Field[] fields = this.getClass().getDeclaredFields();
        for(Field field : fields)
        {
            ExcelCellName excelCellNameInstance = field.getAnnotation(ExcelCellName.class);
            if(excelCellNameInstance == null) continue;
            
            field.setAccessible(true);
            String fieldName = field.getAnnotation(ExcelCellName.class).value();
            Object fieldValue = field.get(this);
            if(fieldValue == null) continue;
            
            LuaValue luaValue = objectToLua(fieldName, fieldValue);
            table.set(fieldName, luaValue);
        }
        
        // atomic operation, do not assign separately
        toLuaRow = table;
        return table;
    }
    
    protected LuaValue objectToLua(String name, Object value) throws ExceptionByCsvId
    {
        if(value.getClass() == Integer.class) return LuaValue.valueOf((int)value);
        if(value.getClass() == Long.class) return LuaValue.valueOf((long)value);
        if(value.getClass() == String.class) return LuaValue.valueOf(value.toString());

        throw new ExceptionByCsvId("not support type = " + value.getClass() + ", field name = " + name);
    }
}
