// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/iab/unity/UnityIabVerifyResult.proto

package ehooray.fishing.dto.proto.response.iab.unity;

public final class UnityIabVerifyResultProto {
  private UnityIabVerifyResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnityIabVerifyResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.iab.unity.UnityIabVerifyResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return Whether the transaction field is set.
     */
    boolean hasTransaction();
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return The transaction.
     */
    ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction();
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     */
    ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder();
  }
  /**
   * Protobuf type {@code response.iab.unity.UnityIabVerifyResult}
   */
  public static final class UnityIabVerifyResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.iab.unity.UnityIabVerifyResult)
      UnityIabVerifyResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UnityIabVerifyResult.newBuilder() to construct.
    private UnityIabVerifyResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UnityIabVerifyResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UnityIabVerifyResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.internal_static_response_iab_unity_UnityIabVerifyResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.internal_static_response_iab_unity_UnityIabVerifyResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.class, ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.Builder.class);
    }

    public static final int TRANSACTION_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction transaction_;
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return Whether the transaction field is set.
     */
    @java.lang.Override
    public boolean hasTransaction() {
      return transaction_ != null;
    }
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     * @return The transaction.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction() {
      return transaction_ == null ? ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
    }
    /**
     * <code>.model.IabTransaction transaction = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder() {
      return getTransaction();
    }

    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.iab.unity.UnityIabVerifyResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.iab.unity.UnityIabVerifyResult)
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.internal_static_response_iab_unity_UnityIabVerifyResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.internal_static_response_iab_unity_UnityIabVerifyResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.class, ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (transactionBuilder_ == null) {
          transaction_ = null;
        } else {
          transaction_ = null;
          transactionBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.internal_static_response_iab_unity_UnityIabVerifyResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult build() {
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult buildPartial() {
        ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult result = new ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult(this);
        if (transactionBuilder_ == null) {
          result.transaction_ = transaction_;
        } else {
          result.transaction_ = transactionBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction transaction_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder> transactionBuilder_;
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       * @return Whether the transaction field is set.
       */
      public boolean hasTransaction() {
        return transactionBuilder_ != null || transaction_ != null;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       * @return The transaction.
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction getTransaction() {
        if (transactionBuilder_ == null) {
          return transaction_ == null ? ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
        } else {
          return transactionBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder setTransaction(ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction value) {
        if (transactionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          transaction_ = value;
          onChanged();
        } else {
          transactionBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder setTransaction(
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder builderForValue) {
        if (transactionBuilder_ == null) {
          transaction_ = builderForValue.build();
          onChanged();
        } else {
          transactionBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder mergeTransaction(ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction value) {
        if (transactionBuilder_ == null) {
          if (transaction_ != null) {
            transaction_ =
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.newBuilder(transaction_).mergeFrom(value).buildPartial();
          } else {
            transaction_ = value;
          }
          onChanged();
        } else {
          transactionBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public Builder clearTransaction() {
        if (transactionBuilder_ == null) {
          transaction_ = null;
          onChanged();
        } else {
          transaction_ = null;
          transactionBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder getTransactionBuilder() {
        
        onChanged();
        return getTransactionFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder getTransactionOrBuilder() {
        if (transactionBuilder_ != null) {
          return transactionBuilder_.getMessageOrBuilder();
        } else {
          return transaction_ == null ?
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.getDefaultInstance() : transaction_;
        }
      }
      /**
       * <code>.model.IabTransaction transaction = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder> 
          getTransactionFieldBuilder() {
        if (transactionBuilder_ == null) {
          transactionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransaction.Builder, ehooray.fishing.dto.proto.model.IabTransactionProto.IabTransactionOrBuilder>(
                  getTransaction(),
                  getParentForChildren(),
                  isClean());
          transaction_ = null;
        }
        return transactionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.iab.unity.UnityIabVerifyResult)
    }

    // @@protoc_insertion_point(class_scope:response.iab.unity.UnityIabVerifyResult)
    private static final ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult();
    }

    public static ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnityIabVerifyResult>
        PARSER = new com.google.protobuf.AbstractParser<UnityIabVerifyResult>() {
      @java.lang.Override
      public UnityIabVerifyResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnityIabVerifyResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnityIabVerifyResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.iab.unity.UnityIabVerifyResultProto.UnityIabVerifyResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_iab_unity_UnityIabVerifyResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_iab_unity_UnityIabVerifyResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-response/iab/unity/UnityIabVerifyResul" +
      "t.proto\022\022response.iab.unity\032\032model/IabTr" +
      "ansaction.proto\"B\n\024UnityIabVerifyResult\022" +
      "*\n\013transaction\030\001 \001(\0132\025.model.IabTransact" +
      "ionBc\n,ehooray.fishing.dto.proto.respons" +
      "e.iab.unityB\031UnityIabVerifyResultProtoH\002" +
      "\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.IabTransactionProto.getDescriptor(),
        });
    internal_static_response_iab_unity_UnityIabVerifyResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_iab_unity_UnityIabVerifyResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_iab_unity_UnityIabVerifyResult_descriptor,
        new java.lang.String[] { "Transaction", });
    ehooray.fishing.dto.proto.model.IabTransactionProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
