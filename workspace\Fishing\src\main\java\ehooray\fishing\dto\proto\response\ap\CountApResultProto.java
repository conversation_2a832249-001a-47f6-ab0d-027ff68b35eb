// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/ap/CountApResult.proto

package ehooray.fishing.dto.proto.response.ap;

public final class CountApResultProto {
  private CountApResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CountApResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.ap.CountApResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     * @return Whether the apItem field is set.
     */
    boolean hasApItem();
    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     * @return The apItem.
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getApItem();
    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getApItemOrBuilder();

    /**
     * <code>int64 apCountRemainTime = 2;</code>
     * @return The apCountRemainTime.
     */
    long getApCountRemainTime();
  }
  /**
   * Protobuf type {@code response.ap.CountApResult}
   */
  public static final class CountApResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.ap.CountApResult)
      CountApResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CountApResult.newBuilder() to construct.
    private CountApResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CountApResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CountApResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.ap.CountApResultProto.internal_static_response_ap_CountApResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.ap.CountApResultProto.internal_static_response_ap_CountApResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.class, ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.Builder.class);
    }

    public static final int APITEM_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem apItem_;
    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     * @return Whether the apItem field is set.
     */
    @java.lang.Override
    public boolean hasApItem() {
      return apItem_ != null;
    }
    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     * @return The apItem.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getApItem() {
      return apItem_ == null ? ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : apItem_;
    }
    /**
     * <code>.model.PlayerItem apItem = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getApItemOrBuilder() {
      return getApItem();
    }

    public static final int APCOUNTREMAINTIME_FIELD_NUMBER = 2;
    private long apCountRemainTime_;
    /**
     * <code>int64 apCountRemainTime = 2;</code>
     * @return The apCountRemainTime.
     */
    @java.lang.Override
    public long getApCountRemainTime() {
      return apCountRemainTime_;
    }

    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.ap.CountApResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.ap.CountApResult)
        ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.ap.CountApResultProto.internal_static_response_ap_CountApResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.ap.CountApResultProto.internal_static_response_ap_CountApResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.class, ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (apItemBuilder_ == null) {
          apItem_ = null;
        } else {
          apItem_ = null;
          apItemBuilder_ = null;
        }
        apCountRemainTime_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.ap.CountApResultProto.internal_static_response_ap_CountApResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult build() {
        ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult buildPartial() {
        ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult result = new ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult(this);
        if (apItemBuilder_ == null) {
          result.apItem_ = apItem_;
        } else {
          result.apItem_ = apItemBuilder_.build();
        }
        result.apCountRemainTime_ = apCountRemainTime_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem apItem_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> apItemBuilder_;
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       * @return Whether the apItem field is set.
       */
      public boolean hasApItem() {
        return apItemBuilder_ != null || apItem_ != null;
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       * @return The apItem.
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getApItem() {
        if (apItemBuilder_ == null) {
          return apItem_ == null ? ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : apItem_;
        } else {
          return apItemBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public Builder setApItem(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (apItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          apItem_ = value;
          onChanged();
        } else {
          apItemBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public Builder setApItem(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (apItemBuilder_ == null) {
          apItem_ = builderForValue.build();
          onChanged();
        } else {
          apItemBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public Builder mergeApItem(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (apItemBuilder_ == null) {
          if (apItem_ != null) {
            apItem_ =
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.newBuilder(apItem_).mergeFrom(value).buildPartial();
          } else {
            apItem_ = value;
          }
          onChanged();
        } else {
          apItemBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public Builder clearApItem() {
        if (apItemBuilder_ == null) {
          apItem_ = null;
          onChanged();
        } else {
          apItem_ = null;
          apItemBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getApItemBuilder() {
        
        onChanged();
        return getApItemFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getApItemOrBuilder() {
        if (apItemBuilder_ != null) {
          return apItemBuilder_.getMessageOrBuilder();
        } else {
          return apItem_ == null ?
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance() : apItem_;
        }
      }
      /**
       * <code>.model.PlayerItem apItem = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getApItemFieldBuilder() {
        if (apItemBuilder_ == null) {
          apItemBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  getApItem(),
                  getParentForChildren(),
                  isClean());
          apItem_ = null;
        }
        return apItemBuilder_;
      }

      private long apCountRemainTime_ ;
      /**
       * <code>int64 apCountRemainTime = 2;</code>
       * @return The apCountRemainTime.
       */
      @java.lang.Override
      public long getApCountRemainTime() {
        return apCountRemainTime_;
      }
      /**
       * <code>int64 apCountRemainTime = 2;</code>
       * @param value The apCountRemainTime to set.
       * @return This builder for chaining.
       */
      public Builder setApCountRemainTime(long value) {
        
        apCountRemainTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 apCountRemainTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearApCountRemainTime() {
        
        apCountRemainTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.ap.CountApResult)
    }

    // @@protoc_insertion_point(class_scope:response.ap.CountApResult)
    private static final ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult();
    }

    public static ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CountApResult>
        PARSER = new com.google.protobuf.AbstractParser<CountApResult>() {
      @java.lang.Override
      public CountApResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CountApResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CountApResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.ap.CountApResultProto.CountApResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_ap_CountApResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_ap_CountApResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037response/ap/CountApResult.proto\022\013respo" +
      "nse.ap\032\026model/PlayerItem.proto\"M\n\rCountA" +
      "pResult\022!\n\006apItem\030\001 \001(\0132\021.model.PlayerIt" +
      "em\022\031\n\021apCountRemainTime\030\002 \001(\003BU\n%ehooray" +
      ".fishing.dto.proto.response.apB\022CountApR" +
      "esultProtoH\002\252\002\025Fishing.Network.Protob\006pr" +
      "oto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_ap_CountApResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_ap_CountApResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_ap_CountApResult_descriptor,
        new java.lang.String[] { "ApItem", "ApCountRemainTime", });
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
