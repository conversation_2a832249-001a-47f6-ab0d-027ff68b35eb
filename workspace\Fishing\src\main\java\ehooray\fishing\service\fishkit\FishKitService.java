package ehooray.fishing.service.fishkit;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishKitEntity;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.repository.FishKitRepository;

@Service
public class FishKitService
{
    protected final CsvContainer csvContainer;
    protected final FishKitRepository fishKitRepository;

    @Autowired
    public FishKitService(CsvContainer csvContainer, FishKitRepository fishKitRepository)
    {
        this.csvContainer = csvContainer;
        this.fishKitRepository = fishKitRepository;
    }
    
    public List<FishKitEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getFishKits();
    }
    
    public Optional<FishKitEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }

    public FishKitEntity createFishKit(PlayerEntity player, FishRodEntity fishRod, PlayerItemEntity fishLine, PlayerItemEntity bait)
    {
        var newFishKit = new FishKitEntity();
        newFishKit.setFishRod(fishRod);
        newFishKit.setFishLine(fishLine);
        newFishKit.setBait(bait);
        newFishKit.setPlayer(player);
        getAllByPlayer(player).add(newFishKit);

        fishKitRepository.save(newFishKit);
        
        return newFishKit;
    }

    public void updateFishKit(FishKitEntity fishKit, FishRodEntity fishRod, PlayerItemEntity fishLine, PlayerItemEntity bait)
    {
        fishKit.setFishRod(fishRod);
        fishKit.setFishLine(fishLine);
        fishKit.setBait(bait);

        fishKitRepository.save(fishKit);
    }
}
