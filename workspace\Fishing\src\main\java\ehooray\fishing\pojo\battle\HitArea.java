package ehooray.fishing.pojo.battle;

import java.util.Arrays;
import java.util.Optional;

public enum HitArea
{
    <PERSON>(1),
    <PERSON>(2),
    Dark(3);
    
    protected int id;
    
    private HitArea(int id)
    {
        this.id = id;
    }

    public int getId()
    {
        return id;
    }

    public static Optional<HitArea> valueOf(int id)
    {
        return Arrays.stream(HitArea.values()).filter(x -> x.getId() == id).findFirst();
    }
}
