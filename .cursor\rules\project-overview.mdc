---
description: Provides overall overview of the Luxury Angler project, including project introduction, technical architecture using Phaser 3, core game loop flow, project file structure organization, and main game features. This is the entry document for new developers to understand the project's full scope.
globs: 
alwaysApply: false
---
# Luxury Angler 项目总览

## 项目简介
Luxury Angler 是一个2D钓鱼模拟游戏，使用Phaser 3引擎开发。游戏混合了现实钓鱼机制和奢华生活主题，玩家从新手成长为钓鱼大师。

## 项目结构
- **主入口**: [main.js](mdc:src/main.js) - Phaser游戏配置和场景注册
- **场景系统**: [src/scenes/](mdc:src/scenes/) - 所有游戏场景
- **核心脚本**: [src/scripts/](mdc:src/scripts/) - 游戏逻辑和管理器
- **UI组件**: [src/ui/](mdc:src/ui/) - 用户界面组件
- **数据配置**: [src/data/](mdc:src/data/) - 游戏数据和配置文件

## 核心游戏循环
1. 标题画面 → 船只菜单（时间、天气、位置显示）
2. 旅行 → 选择地图（5个中的1个）→ 选择钓点（10个中的1个）
3. 钓鱼 → 选择模式：故事模式或练习模式
4. 钓鱼流程：抛竿 → 诱饵 → 收线
5. 社交互动、商店购买、库存管理

## 技术架构
- **引擎**: Phaser 3
- **分辨率**: 1280x720
- **物理引擎**: Arcade Physics
- **缩放模式**: FIT with CENTER_BOTH

## 游戏特色
- 50个等级，5个地图，每个地图10个钓点
- 50种鱼卡收集系统
- 合成系统：使用鱼卡制作装备
- 时间和天气系统影响鱼类出现

- 10个比基尼女孩同伴社交系统