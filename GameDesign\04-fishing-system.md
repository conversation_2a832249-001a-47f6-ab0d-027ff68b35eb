# 4. 釣魚系統設計文檔

## 4.1 釣魚機制總覽

### 4.1.1 拋竿迷你遊戲 (Cast Minigame)

拋竿迷你遊戲是釣魚流程的第一階段，決定魚餌投放的精準度。

#### 遊戲機制設計
**基本流程**:
1. 顯示拋竿計量器
2. 玩家在精準區域內點擊
3. 成功：魚餌投放到熱點（大型或稀有魚類出現機率較高）
4. 失敗：魚餌投放到普通區域

**視覺設計**:
- 2D釣竿拋竿動畫
- 魚餌帶釣線拋向水中
- 成功：魚餌落在熱點並有濺水音效
- 失敗：魚餌落在熱點外

**屬性影響**:
- **拋竿精準度**: 影響精準區域大小
- **魚類偵測**: 影響熱點可見度
- **拋竿距離**: 影響可達釣魚區域

**技術實現**:
```javascript
// 拋竿精準度計算範例
const accuracyZoneSize = baseZoneSize * (1 + player.castAccuracy / 100);
const fishDetectionChance = baseFishDetection * (1 + player.fishDetection / 100);
```

相關檔案：
- [CastingMiniGame.js](../src/scripts/CastingMiniGame.js): 拋竿遊戲邏輯

### 4.1.2 誘魚迷你遊戲 (Lure Minigame) - 增強版

誘魚迷你遊戲是核心釣魚體驗，涉及複雜的魚類行為模擬和五種魚餌控制方式。

#### 遊戲介面設計
**誘魚模擬UI** (右上角):
- 顯示魚餌在水中的移動
- 附近魚類在UI底部游泳
- 感興趣的魚會停下、冒泡、觀察魚餌5秒

**基本控制方式**:
- **上 (W)**: 魚餌快速上游1秒
- **左/右 (A/D)**: 魚餌向船隻方向（螢幕右側）游動1秒
- **所有動作**: 1秒冷卻時間

#### 魚影機制
**魚影類型**:
- **小型魚影**: 普通魚類
- **中型魚影**: 中等稀有度
- **大型魚影**: 稀有或高價值魚類

**魚影行為**:
- 游向魚餌的方式反映魚類**攻擊性**和**狡猾度**
- 繞圈、衝刺等行為模式

#### 誘魚階段系統
**階段設計**:
- 玩家有**3-4次機會**（階段）來鉤住魚
- 每階段使用特定魚餌的節奏輸入
- **成功**: 正確輸入讓魚影保持接近
- **失敗**: 錯誤讓魚影游走；全部失敗則收回魚餌

**漸進難度**:
- 後期階段更困難（更緊的時機、複雜序列）
- 根據魚類**狡猾度**和玩家**誘魚成功率**調整

#### 五種魚餌類型與控制方式

**1. 旋轉餌 (Spinner) - 脈衝點擊**
- **控制方式**: 有節奏的點擊
- **效果**: 魚餌旋轉閃光
- **適用魚類**: 攻擊性魚類

**2. 軟塑膠餌 (Soft Plastic) - 拖拽暫停**
- **控制方式**: 拖拽後暫停
- **效果**: 模擬受傷小魚
- **適用魚類**: 掠食性魚類

**3. 飛釣餌 (Fly) - 滑動閃爍組合**
- **控制方式**: 快速滑動組合
- **效果**: 模擬昆蟲落水
- **適用魚類**: 表層魚類

**4. 波扒餌 (Popper) - 點擊長按爆發**
- **控制方式**: 點擊後長按
- **效果**: 表面水花爆發
- **適用魚類**: 大型攻擊性魚類

**5. 匙型餌 (Spoon) - 圓形軌跡**
- **控制方式**: 畫圓形軌跡
- **效果**: 閃爍擺動
- **適用魚類**: 深水魚類

#### 鉤魚最終階段
**鉤魚流程**:
1. 成功完成所有階段後
2. 最終定時輸入鉤住魚
3. **反饋**: 濺水聲（成功）或撞擊聲（失敗）

#### 屬性整合系統
**玩家屬性影響**:
- **誘魚成功率**: 放寬輸入時機
- **咬餌率**: 可能減少所需階段數

**魚類屬性影響**:
- **攻擊性**: 更快的魚影接近速度
- **狡猾度**: 更高的失敗風險

**魚餌屬性加成**:
- **咬餌率**: 提高魚類興趣
- **誘魚成功率**: 改善輸入容錯
- **魚餌耐久性**: 減少失效機會
- **魚餌精準度**: 調整技能需求

技術實現：
```javascript
// 誘魚階段難度計算
const phaseDifficulty = baseDifficulty * 
    (1 + fish.elusiveness / 10) * 
    (1 - player.lureSuccess / 100) *
    (1 - lure.lureSuccessBonus / 100);
```

相關檔案：
- [LuringMiniGame.js](../src/scripts/LuringMiniGame.js): 誘魚遊戲主邏輯
- [lures.json](../src/data/lures.json): 魚餌數據定義

### 4.1.3 收線迷你遊戲 (Reel-In Minigame)

收線迷你遊戲是釣魚的最後階段，需要管理張力並應對魚類掙扎。

#### 基本遊戲機制
**視覺元素**:
- 被鉤住掙扎的魚與釣線
- 濺水效果
- 魚快速左右游動

**張力計量器**:
- 顯示當前釣線張力
- 玩家必須防止張力達到最大值
- 張力過高會導致釣線斷裂

**收線控制**:
- **滑鼠按壓**: 收線並消耗魚的體力
- 消耗體力數值會從掙扎的魚身上彈出顯示

#### 魚類體力系統
**體力條**: 顯示在魚的頂部
**掙扎循環**: 魚會進行數次掙扎直到體力歸零
**勝利條件**: 魚體力耗盡後被成功拉起

#### QTE反應控制系統
**掙扎樣式觸發QTE**:
魚類會隨機執行2-3種掙扎方式中的組合，每種掙扎需要特定的QTE回應。

**QTE失敗懲罰**:
如果未能快速輸入正確的QTE，張力計量器會突然增加到90%最大值。

#### 十種魚類掙扎樣式

**1. 快速顫動 (Rapid Thrashing)**
- **行為**: 快速左右擺動
- **QTE**: 快速點擊序列
- **風險**: 高張力增加

**2. 深潜 (Deep Dive)**
- **行為**: 向深水游動
- **QTE**: 長按住控制
- **風險**: 持續張力增加

**3. 長距離衝刺 (Long Sprint)**
- **行為**: 直線高速游走
- **QTE**: 跟隨滑動
- **風險**: 突發高張力

**4. 圓形游動 (Circular Swimming)**
- **行為**: 繞圈游動
- **QTE**: 圓形軌跡追蹤
- **風險**: 持續中等張力

**5. 跳躍 (Jumping)**
- **行為**: 躍出水面
- **QTE**: 定時釋放
- **風險**: 瞬間極高張力

**6. 底部摩擦 (Bottom Scraping)**
- **行為**: 在海底摩擦
- **QTE**: 交替點擊
- **風險**: 線損傷風險

**7. 螺旋游動 (Spiral Swimming)**
- **行為**: 螺旋形移動
- **QTE**: 螺旋手勢
- **風險**: 複雜張力變化

**8. 突然停止 (Sudden Stop)**
- **行為**: 瞬間停止掙扎
- **QTE**: 立即停止輸入
- **風險**: 玩家過度收線

**9. 之字形游動 (Zigzag Swimming)**
- **行為**: Z字形快速移動
- **QTE**: 快速方向變化
- **風險**: 不可預測張力

**10. 死亡翻滾 (Death Roll)**
- **行為**: 身體翻滾旋轉
- **QTE**: 旋轉手勢配合
- **風險**: 極高斷線風險

#### QTE反應控制類型

**1. 快速點擊 (Quick Tap)**
- **操作**: 在時間窗口內快速點擊
- **適用掙扎**: 快速顫動、底部摩擦

**2. 長按釋放 (Hold and Release)**
- **操作**: 長按後在正確時機釋放
- **適用掙扎**: 深潛、跳躍

**3. 滑動鏈 (Swipe Chain)**
- **操作**: 連續方向滑動
- **適用掙扎**: 長距離衝刺、之字形游動

**4. 軌跡追蹤 (Trace Following)**
- **操作**: 跟隨螢幕軌跡
- **適用掙扎**: 圓形游動、螺旋游動

**5. 定時輸入 (Timed Input)**
- **操作**: 精確時機的單次輸入
- **適用掙扎**: 突然停止、死亡翻滾

#### 屬性影響系統
**玩家屬性**:
- **張力穩定性**: 擴大安全張力區域
- **收線速度**: 更快的收線效率
- **QTE精準度**: 延長反應時間窗口
- **線強度**: 減少斷線風險

**魚類屬性**:
- **力量**: 影響張力產生強度
- **耐力**: 決定掙扎持續時間
- **速度**: 影響掙扎頻率
- **大小**: 影響整體難度

**釣竿屬性**:
- **張力穩定性**: 減少張力波動
- **掙扎抗性**: 減少魚類掙扎效果
- **收線速度**: 提高收線效率

技術實現：
```javascript
// 張力計算範例
const tensionIncrease = 
    fish.strength * struggleIntensity * 
    (1 - rod.tensionStability / 100) *
    (1 - player.lineStrength / 100);

// QTE時間窗口計算
const qteWindow = baseQTETime * (1 + player.qtePrecision / 100);
```

相關檔案：
- [ReelingMiniGame.js](../src/scripts/ReelingMiniGame.js): 收線遊戲邏輯
- [fish.json](../src/data/fish.json): 魚類掙扎樣式數據

## 4.2 時間系統設計

### 4.2.1 時間週期結構

**8個時間段設計**:
1. **黎明** (05:00-08:00): 晨光魚類活躍
2. **上午** (08:00-11:00): 穩定釣魚期
3. **正午** (11:00-14:00): 陽光充足，部分魚類休息
4. **下午** (14:00-17:00): 下午活動高峰
5. **黃昏** (17:00-20:00): 覓食時間，高活動期
6. **夜晚** (20:00-23:00): 夜行魚類出現
7. **深夜** (23:00-02:00): 特殊夜行魚類
8. **午夜** (02:00-05:00): 最稀有夜行魚類

### 4.2.2 時間進程機制

**時間推進方式**:
- **釣魚活動**: +30分鐘
- **旅行活動**: +1小時
- **其他活動**: 視活動類型而定

**時間影響因素**:
- 魚類出現機率
- 特定魚種的活躍時段
- 釣魚效率加成/減益
- 特殊事件觸發

### 4.2.3 魚類時間偏好系統

**範例時間偏好**:
- **月光鱸魚**: 夜晚時段 (+50% 出現機率)
- **日照金槍魚**: 正午時段 (+30% 出現機率)
- **黎明鯛魚**: 黎明時段 (+40% 出現機率)

技術實現：
```javascript
// 時間影響魚類出現機率
const timeBonus = fish.preferredTimeModifier[currentTimeSlot] || 1.0;
const spawnChance = baseFishSpawnChance * timeBonus;
```

相關檔案：
- [TimeManager.js](../src/scripts/TimeManager.js): 時間系統管理
- [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 時間顯示介面

## 4.3 天氣系統設計

### 4.3.1 天氣類型與效果

**晴天 (Sunny)**:
- **魚類偵測加成**: 提高發現魚類機率
- **視覺效果**: 明亮水面，良好能見度
- **特殊效果**: 表層魚類更活躍

**雨天 (Rainy)**:
- **咬餌率加成**: +10% 咬餌率
- **視覺效果**: 雨滴、較暗的環境
- **特殊效果**: 某些魚類在雨天更活躍

### 4.3.2 天氣變化機制

**變化週期**: 每2遊戲小時檢查天氣變化
**變化機率**: 基於現實天氣模式的權重系統
**預報系統**: 玩家可查看未來天氣預報

### 4.3.3 天氣與魚類行為

**晴天偏好魚類**:
- 表層活動魚類
- 視覺獵食者
- 光線敏感魚種

**雨天偏好魚類**:
- 深水魚類上浮
- 以昆蟲為食的魚類
- 低光環境適應魚種

技術實現：
```javascript
// 天氣影響咬餌率
const weatherBonus = weather === 'rainy' ? 1.1 : 1.0;
const finalBiteRate = baseBiteRate * weatherBonus;
```

相關檔案：
- [WeatherManager.js](../src/scripts/WeatherManager.js): 天氣系統管理
- [TimeWeatherUI.js](../src/ui/TimeWeatherUI.js): 天氣顯示介面

## 4.4 地圖選擇與釣魚地點

### 4.4.1 地圖結構設計

**5個主要地圖**:
1. **珊瑚灣 (Coral Cove)**: 熱帶淺水區域
2. **深海深淵 (Deep Abyss)**: 深海釣魚區域
3. **北極海域 (Arctic Waters)**: 冰冷海域
4. **火山島嶼 (Volcanic Island)**: 地熱活動區域
5. **神秘海溝 (Mysterious Trench)**: 未知深海區域

**每個地圖10個釣魚點**:
- 每個釣魚點有獨特的魚類組合
- 不同深度和環境特色
- 特殊釣魚條件或挑戰

### 4.4.2 故事模式 vs 任務模式

**故事模式地圖**:
- 與主要敘事線綁定
- 包含獨特魚類和挑戰
- 解鎖新功能和劇情
- 有特殊的Boss魚類

**任務模式地圖**:
- 獨立於故事的訓練場
- 排除故事基礎魚地圖
- 專注技能練習和特定魚種捕獲
- 不影響故事進程

### 4.4.3 釣魚點特色系統

**範例釣魚點**:
- **珊瑚礁 (Coral Reef)**: 彩色熱帶魚
- **深海溝渠 (Deep Trench)**: 深海巨型魚類
- **海底山丘 (Underwater Hills)**: 中型洄游魚
- **熱水口 (Thermal Vents)**: 耐熱特殊魚種

技術實現：
- [LocationManager.js](../src/scripts/LocationManager.js): 地點管理系統
- [LocationData.js](../src/data/LocationData.js): 地點數據定義
- [MapSelectionUI.js](../src/ui/MapSelectionUI.js): 地圖選擇介面

## 4.5 魚類系統設計

### 4.5.1 魚類分類結構

**魚類總覽**:
- **總數**: 50種魚類（50張魚卡）
- **類型分組**: 10個魚類類型，每類型5種魚
- **Boss魚類**: 10條Boss魚
- **分布**: 5個地圖 × 10個地點，每地點5種不同魚類

### 4.5.2 魚類類型分組範例

**1. 小型熱帶魚類型**:
- 小丑魚、天使魚、蝴蝶魚、刺尾魚、六線魚

**2. 中型珊瑚魚類型**:
- 石斑魚、鸚鵡魚、麥鯛、金鯧、海鱺

**3. 大型洄游魚類型**:
- 鮪魚、旗魚、鯊魚、海豚魚、舵魚

**4. 深海魚類型**:
- 鮟鱇魚、蝰魚、燈籠魚、琵琶魚、深海鯊

**5. 極地魚類型**:
- 北極鱈魚、冰魚、海豹魚、極地鯊、冰川鱒

### 4.5.3 Boss魚類設計

**Boss魚特色**:
- 每5等級出現一條Boss魚
- 特殊的掙扎模式組合
- 需要特定裝備或策略
- 豐厚的獎勵和成就

**範例Boss魚**:
- **等級5**: 巨型鱸魚 - 多階段掙扎
- **等級10**: 電鰻王 - 電擊QTE挑戰
- **等級50**: 海妖 - 終極Boss戰

### 4.5.4 魚類生成邏輯

**基本生成規則**:
```javascript
// 魚類生成機率計算
const spawnChance = 
    fish.baseSpawnRate *
    timeMultiplier *
    weatherMultiplier *
    locationMultiplier *
    playerLevelMultiplier *
    equipmentBonuses;
```

**稀有度影響**:
- 稀有度1-3：常見魚類，高生成率
- 稀有度4-6：中等稀有，正常生成率
- 稀有度7-10：極稀有，低生成率

技術實現：
- [FishDatabase.js](../src/scripts/FishDatabase.js): 魚類數據庫
- [FishSpawner.js](../src/scripts/FishSpawner.js): 魚類生成邏輯
- [fish.json](../src/data/fish.json): 魚類屬性數據

## 4.6 整合系統設計

### 4.6.1 釣魚系統與屬性系統整合

**屬性對釣魚的影響**:
- 拋竿階段：精準度、距離影響成功率
- 誘魚階段：咬餌率、成功率影響魚類興趣
- 收線階段：張力穩定、QTE精準度影響難度

### 4.6.2 釣魚系統與進程系統整合

**經驗值獲得**:
- 成功釣魚：基礎經驗值
- 稀有魚類：額外經驗加成
- 完美釣魚：技能表現獎勵

**等級影響**:
- 解鎖新地圖和釣魚點
- 提高基礎屬性
- 獲得新能力和特權

### 4.6.3 釣魚系統與經濟系統整合

**魚卡價值**:
- 基於稀有度的價格差異
- 時間和天氣加成影響價值
- 完美捕獲的品質獎勵

**裝備需求**:
- 更好裝備提高成功率
- 專用裝備對抗特定魚類
- 升級路徑的經濟投資

相關檔案：
- [FishingMinigames.js](../src/scripts/FishingMinigames.js): 釣魚遊戲主控制器
- [GameScene.js](../src/scenes/GameScene.js): 核心釣魚場景
- [HUDScene.js](../src/scenes/HUDScene.js): 釣魚過程中的UI顯示
- [InputManager.js](../src/scripts/InputManager.js): 輸入處理系統 