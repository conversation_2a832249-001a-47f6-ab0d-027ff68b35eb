package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class FishItemEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final FishItemEntity fish;

    public FishItemEvent(Object source, PlayerEntity player, FishItemEntity fish)
    {
        super(source);
        this.player = player;
        this.fish = fish;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }

    public FishItemEntity getFish()
    {
        return fish;
    }
    
}
