// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/Exception.proto

package ehooray.fishing.dto.proto.response;

public final class ExceptionProto {
  private ExceptionProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ExceptionOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.Exception)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string serverMessage = 1;</code>
     * @return The serverMessage.
     */
    java.lang.String getServerMessage();
    /**
     * <code>string serverMessage = 1;</code>
     * @return The bytes for serverMessage.
     */
    com.google.protobuf.ByteString
        getServerMessageBytes();

    /**
     * <pre>
     * from csv, if not found, just show server message
     * </pre>
     *
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @return A list containing the overrideContent.
     */
    java.util.List<java.lang.String>
        getOverrideContentList();
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @return The count of overrideContent.
     */
    int getOverrideContentCount();
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @param index The index of the element to return.
     * @return The overrideContent at the given index.
     */
    java.lang.String getOverrideContent(int index);
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the overrideContent at the given index.
     */
    com.google.protobuf.ByteString
        getOverrideContentBytes(int index);
  }
  /**
   * Protobuf type {@code response.Exception}
   */
  public static final class Exception extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.Exception)
      ExceptionOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Exception.newBuilder() to construct.
    private Exception(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Exception() {
      serverMessage_ = "";
      overrideContent_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Exception();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.ExceptionProto.internal_static_response_Exception_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.ExceptionProto.internal_static_response_Exception_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.ExceptionProto.Exception.class, ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder.class);
    }

    public static final int SERVERMESSAGE_FIELD_NUMBER = 1;
    private volatile java.lang.Object serverMessage_;
    /**
     * <code>string serverMessage = 1;</code>
     * @return The serverMessage.
     */
    @java.lang.Override
    public java.lang.String getServerMessage() {
      java.lang.Object ref = serverMessage_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        serverMessage_ = s;
        return s;
      }
    }
    /**
     * <code>string serverMessage = 1;</code>
     * @return The bytes for serverMessage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getServerMessageBytes() {
      java.lang.Object ref = serverMessage_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        serverMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <pre>
     * from csv, if not found, just show server message
     * </pre>
     *
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int OVERRIDECONTENT_FIELD_NUMBER = 3;
    private com.google.protobuf.LazyStringList overrideContent_;
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @return A list containing the overrideContent.
     */
    public com.google.protobuf.ProtocolStringList
        getOverrideContentList() {
      return overrideContent_;
    }
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @return The count of overrideContent.
     */
    public int getOverrideContentCount() {
      return overrideContent_.size();
    }
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @param index The index of the element to return.
     * @return The overrideContent at the given index.
     */
    public java.lang.String getOverrideContent(int index) {
      return overrideContent_.get(index);
    }
    /**
     * <pre>
     * for overriding content
     * </pre>
     *
     * <code>repeated string overrideContent = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the overrideContent at the given index.
     */
    public com.google.protobuf.ByteString
        getOverrideContentBytes(int index) {
      return overrideContent_.getByteString(index);
    }

    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.ExceptionProto.Exception prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.Exception}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.Exception)
        ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.ExceptionProto.internal_static_response_Exception_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.ExceptionProto.internal_static_response_Exception_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.ExceptionProto.Exception.class, ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.ExceptionProto.Exception.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        serverMessage_ = "";

        csvId_ = 0;

        overrideContent_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.ExceptionProto.internal_static_response_Exception_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ExceptionProto.Exception getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.ExceptionProto.Exception.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ExceptionProto.Exception build() {
        ehooray.fishing.dto.proto.response.ExceptionProto.Exception result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ExceptionProto.Exception buildPartial() {
        ehooray.fishing.dto.proto.response.ExceptionProto.Exception result = new ehooray.fishing.dto.proto.response.ExceptionProto.Exception(this);
        int from_bitField0_ = bitField0_;
        result.serverMessage_ = serverMessage_;
        result.csvId_ = csvId_;
        if (((bitField0_ & 0x00000001) != 0)) {
          overrideContent_ = overrideContent_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.overrideContent_ = overrideContent_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private java.lang.Object serverMessage_ = "";
      /**
       * <code>string serverMessage = 1;</code>
       * @return The serverMessage.
       */
      public java.lang.String getServerMessage() {
        java.lang.Object ref = serverMessage_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          serverMessage_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string serverMessage = 1;</code>
       * @return The bytes for serverMessage.
       */
      public com.google.protobuf.ByteString
          getServerMessageBytes() {
        java.lang.Object ref = serverMessage_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          serverMessage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string serverMessage = 1;</code>
       * @param value The serverMessage to set.
       * @return This builder for chaining.
       */
      public Builder setServerMessage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        serverMessage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string serverMessage = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerMessage() {
        
        serverMessage_ = getDefaultInstance().getServerMessage();
        onChanged();
        return this;
      }
      /**
       * <code>string serverMessage = 1;</code>
       * @param value The bytes for serverMessage to set.
       * @return This builder for chaining.
       */
      public Builder setServerMessageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        serverMessage_ = value;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <pre>
       * from csv, if not found, just show server message
       * </pre>
       *
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <pre>
       * from csv, if not found, just show server message
       * </pre>
       *
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * from csv, if not found, just show server message
       * </pre>
       *
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList overrideContent_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureOverrideContentIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          overrideContent_ = new com.google.protobuf.LazyStringArrayList(overrideContent_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @return A list containing the overrideContent.
       */
      public com.google.protobuf.ProtocolStringList
          getOverrideContentList() {
        return overrideContent_.getUnmodifiableView();
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @return The count of overrideContent.
       */
      public int getOverrideContentCount() {
        return overrideContent_.size();
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param index The index of the element to return.
       * @return The overrideContent at the given index.
       */
      public java.lang.String getOverrideContent(int index) {
        return overrideContent_.get(index);
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param index The index of the value to return.
       * @return The bytes of the overrideContent at the given index.
       */
      public com.google.protobuf.ByteString
          getOverrideContentBytes(int index) {
        return overrideContent_.getByteString(index);
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param index The index to set the value at.
       * @param value The overrideContent to set.
       * @return This builder for chaining.
       */
      public Builder setOverrideContent(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureOverrideContentIsMutable();
        overrideContent_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param value The overrideContent to add.
       * @return This builder for chaining.
       */
      public Builder addOverrideContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureOverrideContentIsMutable();
        overrideContent_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param values The overrideContent to add.
       * @return This builder for chaining.
       */
      public Builder addAllOverrideContent(
          java.lang.Iterable<java.lang.String> values) {
        ensureOverrideContentIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, overrideContent_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOverrideContent() {
        overrideContent_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * for overriding content
       * </pre>
       *
       * <code>repeated string overrideContent = 3;</code>
       * @param value The bytes of the overrideContent to add.
       * @return This builder for chaining.
       */
      public Builder addOverrideContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureOverrideContentIsMutable();
        overrideContent_.add(value);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.Exception)
    }

    // @@protoc_insertion_point(class_scope:response.Exception)
    private static final ehooray.fishing.dto.proto.response.ExceptionProto.Exception DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.ExceptionProto.Exception();
    }

    public static ehooray.fishing.dto.proto.response.ExceptionProto.Exception getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Exception>
        PARSER = new com.google.protobuf.AbstractParser<Exception>() {
      @java.lang.Override
      public Exception parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Exception> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Exception> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.ExceptionProto.Exception getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_Exception_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_Exception_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030response/Exception.proto\022\010response\"J\n\t" +
      "Exception\022\025\n\rserverMessage\030\001 \001(\t\022\r\n\005csvI" +
      "d\030\002 \001(\005\022\027\n\017overrideContent\030\003 \003(\tBN\n\"ehoo" +
      "ray.fishing.dto.proto.responseB\016Exceptio" +
      "nProtoH\002\252\002\025Ehooray.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_response_Exception_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_Exception_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_Exception_descriptor,
        new java.lang.String[] { "ServerMessage", "CsvId", "OverrideContent", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
