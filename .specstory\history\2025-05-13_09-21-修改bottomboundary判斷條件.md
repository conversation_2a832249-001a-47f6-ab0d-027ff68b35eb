<!-- Generated by SpecStory -->

# 修改bottomBoundary判斷條件 (2025-05-13 17:21:02)

_**User**_

bottomBoundary的判斷改成直接已某一個值來做判斷

---

_**Assistant**_



---

好的，我將修改 `LureAnimationController.cs` 腳本。

我會做以下調整：
1.  移除 `bottomBoundary` (RectTransform類型) 的 `SerializeField`。
2.  新增一個名為 `bottomBoundaryY` (float類型) 的 `SerializeField`，用於直接指定底部邊界的 Y 座標值。
3.  更新 `StartDrifting` 方法中的邏輯，使用這個新的 `bottomBoundaryY` 值來判斷魚的下沉極限。

這樣，你就可以直接在 Inspector 中設定一個數值來控制底部邊界，而不再需要指定一個 UI 元素。

我現在來準備修改程式碼。


---

```diff
      [Header("UI Elements")]
      [SerializeField] private Image fishImage;
-     [SerializeField] private RectTransform bottomBoundary; // 新增：底部邊界UI元素
+     [SerializeField] private float bottomBoundaryY = -200f; // 新增：底部邊界Y座標值 (可根據需要調整默認值)
  
      [Header("Animation Parameters")]
```

```diff
          }
  
-         if (bottomBoundary == null)
-         {
-             Debug.LogWarning("LureAnimationController: BottomBoundary is not assigned in the Inspector. Sinking behavior will be disabled.");
-         }
          StartDrifting();
      }
```

```diff
  
          // --- 下沉動畫 ---
-         if (bottomBoundary != null)
-         {
-             float fishPivotY = fishRectTransform.pivot.y;
-             float fishHeight = fishRectTransform.rect.height;
-             
-             float boundaryTopActualY = bottomBoundary.anchoredPosition.y + bottomBoundary.rect.height * (1f - bottomBoundary.pivot.y);
-             float targetSinkAnchorY = boundaryTopActualY + fishHeight * fishPivotY;
- 
+         float targetSinkAnchorY = bottomBoundaryY; // 直接使用設定的Y值作為目標
          float currentAnchorY = fishRectTransform.anchoredPosition.y;
          float distanceToSink = currentAnchorY - targetSinkAnchorY;
  
-             if (distanceToSink > 0.01f) // 只有當高於底部時才下沉
+         if (distanceToSink > 0.01f) // 只有當高於底部邊界時才下沉
          {
-                 float sinkDuration = distanceToSink / driftSpeed; // driftSpeed 是 單位/秒
-                 if (sinkDuration <= 0.001f) sinkDuration = 0.001f; // 防止持續時間為零或負
+             float sinkDuration = distanceToSink / driftSpeed;
+             if (sinkDuration <= 0.001f) sinkDuration = 0.001f;
  
              fishRectTransform.DOAnchorPosY(targetSinkAnchorY, sinkDuration)
-                     .SetEase(Ease.Linear); // 或者 Ease.InOutSine
+                 .SetEase(Ease.Linear);
          }
          else
          {
              // 如果已經在底部或以下，則將其位置固定在底部目標Y值
              fishRectTransform.anchoredPosition = new Vector2(fishRectTransform.anchoredPosition.x, targetSinkAnchorY);
-             }
-         }
-         else
-         {
-             // 如果沒有設置底部邊界，可以選擇執行舊的漂移動畫或不做任何事情
-             // 這裡我們將不執行下沉，但仍會執行水平擺動（如果需要）
          }
  
```

---

程式碼已修改。

主要的變更如下：
1.  `bottomBoundary` (RectTransform) 欄位已被移除。
2.  新增了一個名為 `bottomBoundaryY` 的 `float` 欄位，並設置了預設值 `-200f`。你可以在Unity編輯器的Inspector中調整此值。
3.  `Start()` 方法中移除了對 `bottomBoundary` 是否為空的檢查。
4.  `StartDrifting()` 方法中的下沉邏輯現在直接使用 `bottomBoundaryY` 作為目標Y座標。它會計算當前魚錨點位置與 `bottomBoundaryY` 之間的距離，如果魚高於此邊界，則執行下沉動畫；否則，將魚的位置直接設定到 `bottomBoundaryY`。

請檢查修改是否符合你的預期。

---

