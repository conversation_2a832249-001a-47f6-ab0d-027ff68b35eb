using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚捕獲狀態策略
    /// </summary>
    public class CatchStrategy : FishingStrategyBase
    {
        public CatchStrategy() : base(FishingState.Catch) { }
        
        public override UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToCatch方法中的初始化邏輯
            context.fishLine.SetTopPointToRod();
            context.cordReelManager.ResetCordReel();
            context.fishingGamePanelManager.Close<CordReelManager>();
            context.fishingGamePanelManager.Close<PausePanelManager>();
            context.lineTensionManager.CloseRoot();
            context.fishingGamePanelManager.Close<LineTensionManager>();
            context.fishingGamePanelManager.Close<FishInfoManager>();
            
            ShakeCamera.ShakeOnce(context.mainCamera).Forget();
            context.forceItem.CloseWaterEffect();
            context.forceItem.SetForce(0);
            context.fishRod.rodModel.gameObject.SetActive(false);
            context.fishLine.gameObject.SetActive(false);
            context.fish.transform.gameObject.SetActive(true);
            context.fish.PullUpAnimation();
            context.fish.PlayWaterEffect();
            context.mainCamera.GetComponent<Animator>().enabled = true;
            context.mainCamera.GetComponent<Animator>().Play("Show", 0, 0);
            context.SetBlankScreen(true);
            
            return UniTask.CompletedTask;
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoCatch方法中的邏輯，通常由動畫事件驅動，此處可以不做處理
            // 實際動畫完成後，會通過context.FinishCatchAnimate()轉到Win狀態
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 