package ehooray.fishing.component.logger;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import ehooray.fishing.config.SessionName;

@Component
@Aspect
public class WebSocketHandlerLogger
{
    @After(value = "execution(* ehooray.fishing.component.WebSocketHandler.afterConnectionEstablished(*))")
    public void afterConnectionEstablished(JoinPoint joinPoint)
    {
        try
        {
            WebSocketSession session = (WebSocketSession) joinPoint.getArgs()[0];
            long playerId = putPlayerIdToContext(session);
            LogManager.getLogger(this.getClass()).info("player " + playerId + " connect success, session id = " + session.getId());
        }
        finally
        {
            ThreadContext.remove(SessionName.PlayerId);
        }
    }

    @After(value = "execution(* ehooray.fishing.component.WebSocketHandler.afterConnectionClosed(*, *))")
    public void afterConnectionClosed(JoinPoint joinPoint)
    {
        try
        {
            WebSocketSession session = (WebSocketSession) joinPoint.getArgs()[0];
            long playerId = putPlayerIdToContext(session);
            LogManager.getLogger(this.getClass()).info("player " + playerId + " diconnect, session id = " + session.getId());
        }
        finally
        {
            ThreadContext.remove(SessionName.PlayerId);
        }
    }

    protected long putPlayerIdToContext(WebSocketSession session)
    {
        long playerId = (long) session.getAttributes().get(SessionName.PlayerId);
        ThreadContext.put(SessionName.PlayerId, String.valueOf(playerId));
        return playerId;
    }

    protected void removePlayerIdInContext()
    {
        ThreadContext.remove(SessionName.PlayerId);
    }

    @Pointcut("execution(* ehooray.fishing.component.WebSocketHandler.handleBinaryMessage_public(*, *))")
    protected void handleBinaryMessage_public()
    {
    }

    @Before("handleBinaryMessage_public()")
    protected void before_handleBinaryMessage_public(JoinPoint joinPoint) throws Exception
    {
        putPlayerIdToContext((WebSocketSession) joinPoint.getArgs()[0]);
    }

    @AfterReturning(value = "handleBinaryMessage_public()")
    protected void afterReturning_handleBinaryMessage(JoinPoint joinPoint)
    {
        removePlayerIdInContext();
    }
}