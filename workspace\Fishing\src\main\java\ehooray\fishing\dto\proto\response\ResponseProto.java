// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/Response.proto

package ehooray.fishing.dto.proto.response;

public final class ResponseProto {
  private ResponseProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.Response)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.response.Response.State state = 1;</code>
     * @return The enum numeric value on the wire for state.
     */
    int getStateValue();
    /**
     * <code>.response.Response.State state = 1;</code>
     * @return The state.
     */
    ehooray.fishing.dto.proto.response.ResponseProto.Response.State getState();

    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    java.util.List<com.google.protobuf.Any> 
        getDataListList();
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    com.google.protobuf.Any getDataList(int index);
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    int getDataListCount();
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getDataListOrBuilderList();
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
        int index);

    /**
     * <code>.response.Exception exception = 3;</code>
     * @return Whether the exception field is set.
     */
    boolean hasException();
    /**
     * <code>.response.Exception exception = 3;</code>
     * @return The exception.
     */
    ehooray.fishing.dto.proto.response.ExceptionProto.Exception getException();
    /**
     * <code>.response.Exception exception = 3;</code>
     */
    ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder getExceptionOrBuilder();

    /**
     * <code>int64 requestId = 4;</code>
     * @return The requestId.
     */
    long getRequestId();

    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @return A list containing the dirtyFlags.
     */
    java.util.List<java.lang.Integer> getDirtyFlagsList();
    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @return The count of dirtyFlags.
     */
    int getDirtyFlagsCount();
    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @param index The index of the element to return.
     * @return The dirtyFlags at the given index.
     */
    int getDirtyFlags(int index);
  }
  /**
   * Protobuf type {@code response.Response}
   */
  public static final class Response extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.Response)
      ResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Response.newBuilder() to construct.
    private Response(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Response() {
      state_ = 0;
      dataList_ = java.util.Collections.emptyList();
      dirtyFlags_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Response();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.ResponseProto.internal_static_response_Response_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.ResponseProto.internal_static_response_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.ResponseProto.Response.class, ehooray.fishing.dto.proto.response.ResponseProto.Response.Builder.class);
    }

    /**
     * Protobuf enum {@code response.Response.State}
     */
    public enum State
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <code>None = 0;</code>
       */
      None(0),
      /**
       * <code>Success = 1;</code>
       */
      Success(1),
      /**
       * <code>Exception = 2;</code>
       */
      Exception(2),
      UNRECOGNIZED(-1),
      ;

      /**
       * <code>None = 0;</code>
       */
      public static final int None_VALUE = 0;
      /**
       * <code>Success = 1;</code>
       */
      public static final int Success_VALUE = 1;
      /**
       * <code>Exception = 2;</code>
       */
      public static final int Exception_VALUE = 2;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static State valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static State forNumber(int value) {
        switch (value) {
          case 0: return None;
          case 1: return Success;
          case 2: return Exception;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<State>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          State> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<State>() {
              public State findValueByNumber(int number) {
                return State.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.ResponseProto.Response.getDescriptor().getEnumTypes().get(0);
      }

      private static final State[] VALUES = values();

      public static State valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private State(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:response.Response.State)
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private int state_;
    /**
     * <code>.response.Response.State state = 1;</code>
     * @return The enum numeric value on the wire for state.
     */
    @java.lang.Override public int getStateValue() {
      return state_;
    }
    /**
     * <code>.response.Response.State state = 1;</code>
     * @return The state.
     */
    @java.lang.Override public ehooray.fishing.dto.proto.response.ResponseProto.Response.State getState() {
      @SuppressWarnings("deprecation")
      ehooray.fishing.dto.proto.response.ResponseProto.Response.State result = ehooray.fishing.dto.proto.response.ResponseProto.Response.State.valueOf(state_);
      return result == null ? ehooray.fishing.dto.proto.response.ResponseProto.Response.State.UNRECOGNIZED : result;
    }

    public static final int DATALIST_FIELD_NUMBER = 2;
    private java.util.List<com.google.protobuf.Any> dataList_;
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.Any> getDataListList() {
      return dataList_;
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getDataListOrBuilderList() {
      return dataList_;
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    @java.lang.Override
    public int getDataListCount() {
      return dataList_.size();
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    @java.lang.Override
    public com.google.protobuf.Any getDataList(int index) {
      return dataList_.get(index);
    }
    /**
     * <code>repeated .google.protobuf.Any dataList = 2;</code>
     */
    @java.lang.Override
    public com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
        int index) {
      return dataList_.get(index);
    }

    public static final int EXCEPTION_FIELD_NUMBER = 3;
    private ehooray.fishing.dto.proto.response.ExceptionProto.Exception exception_;
    /**
     * <code>.response.Exception exception = 3;</code>
     * @return Whether the exception field is set.
     */
    @java.lang.Override
    public boolean hasException() {
      return exception_ != null;
    }
    /**
     * <code>.response.Exception exception = 3;</code>
     * @return The exception.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.response.ExceptionProto.Exception getException() {
      return exception_ == null ? ehooray.fishing.dto.proto.response.ExceptionProto.Exception.getDefaultInstance() : exception_;
    }
    /**
     * <code>.response.Exception exception = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder getExceptionOrBuilder() {
      return getException();
    }

    public static final int REQUESTID_FIELD_NUMBER = 4;
    private long requestId_;
    /**
     * <code>int64 requestId = 4;</code>
     * @return The requestId.
     */
    @java.lang.Override
    public long getRequestId() {
      return requestId_;
    }

    public static final int DIRTYFLAGS_FIELD_NUMBER = 5;
    private com.google.protobuf.Internal.IntList dirtyFlags_;
    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @return A list containing the dirtyFlags.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDirtyFlagsList() {
      return dirtyFlags_;
    }
    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @return The count of dirtyFlags.
     */
    public int getDirtyFlagsCount() {
      return dirtyFlags_.size();
    }
    /**
     * <code>repeated int32 dirtyFlags = 5;</code>
     * @param index The index of the element to return.
     * @return The dirtyFlags at the given index.
     */
    public int getDirtyFlags(int index) {
      return dirtyFlags_.getInt(index);
    }
    private int dirtyFlagsMemoizedSerializedSize = -1;

    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.ResponseProto.Response parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.ResponseProto.Response prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.Response}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.Response)
        ehooray.fishing.dto.proto.response.ResponseProto.ResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.ResponseProto.internal_static_response_Response_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.ResponseProto.internal_static_response_Response_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.ResponseProto.Response.class, ehooray.fishing.dto.proto.response.ResponseProto.Response.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.ResponseProto.Response.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = 0;

        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dataListBuilder_.clear();
        }
        if (exceptionBuilder_ == null) {
          exception_ = null;
        } else {
          exception_ = null;
          exceptionBuilder_ = null;
        }
        requestId_ = 0L;

        dirtyFlags_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.ResponseProto.internal_static_response_Response_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ResponseProto.Response getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.ResponseProto.Response.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ResponseProto.Response build() {
        ehooray.fishing.dto.proto.response.ResponseProto.Response result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ResponseProto.Response buildPartial() {
        ehooray.fishing.dto.proto.response.ResponseProto.Response result = new ehooray.fishing.dto.proto.response.ResponseProto.Response(this);
        int from_bitField0_ = bitField0_;
        result.state_ = state_;
        if (dataListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            dataList_ = java.util.Collections.unmodifiableList(dataList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.dataList_ = dataList_;
        } else {
          result.dataList_ = dataListBuilder_.build();
        }
        if (exceptionBuilder_ == null) {
          result.exception_ = exception_;
        } else {
          result.exception_ = exceptionBuilder_.build();
        }
        result.requestId_ = requestId_;
        if (((bitField0_ & 0x00000002) != 0)) {
          dirtyFlags_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.dirtyFlags_ = dirtyFlags_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private int state_ = 0;
      /**
       * <code>.response.Response.State state = 1;</code>
       * @return The enum numeric value on the wire for state.
       */
      @java.lang.Override public int getStateValue() {
        return state_;
      }
      /**
       * <code>.response.Response.State state = 1;</code>
       * @param value The enum numeric value on the wire for state to set.
       * @return This builder for chaining.
       */
      public Builder setStateValue(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.response.Response.State state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public ehooray.fishing.dto.proto.response.ResponseProto.Response.State getState() {
        @SuppressWarnings("deprecation")
        ehooray.fishing.dto.proto.response.ResponseProto.Response.State result = ehooray.fishing.dto.proto.response.ResponseProto.Response.State.valueOf(state_);
        return result == null ? ehooray.fishing.dto.proto.response.ResponseProto.Response.State.UNRECOGNIZED : result;
      }
      /**
       * <code>.response.Response.State state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(ehooray.fishing.dto.proto.response.ResponseProto.Response.State value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.response.Response.State state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.Any> dataList_ =
        java.util.Collections.emptyList();
      private void ensureDataListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dataList_ = new java.util.ArrayList<com.google.protobuf.Any>(dataList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> dataListBuilder_;

      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public java.util.List<com.google.protobuf.Any> getDataListList() {
        if (dataListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dataList_);
        } else {
          return dataListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public int getDataListCount() {
        if (dataListBuilder_ == null) {
          return dataList_.size();
        } else {
          return dataListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public com.google.protobuf.Any getDataList(int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);
        } else {
          return dataListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder setDataList(
          int index, com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.set(index, value);
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder setDataList(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder addDataList(com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder addDataList(
          int index, com.google.protobuf.Any value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(index, value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder addDataList(
          com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder addDataList(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder addAllDataList(
          java.lang.Iterable<? extends com.google.protobuf.Any> values) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dataList_);
          onChanged();
        } else {
          dataListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder clearDataList() {
        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dataListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public Builder removeDataList(int index) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.remove(index);
          onChanged();
        } else {
          dataListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public com.google.protobuf.Any.Builder getDataListBuilder(
          int index) {
        return getDataListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public com.google.protobuf.AnyOrBuilder getDataListOrBuilder(
          int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);  } else {
          return dataListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
           getDataListOrBuilderList() {
        if (dataListBuilder_ != null) {
          return dataListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dataList_);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public com.google.protobuf.Any.Builder addDataListBuilder() {
        return getDataListFieldBuilder().addBuilder(
            com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public com.google.protobuf.Any.Builder addDataListBuilder(
          int index) {
        return getDataListFieldBuilder().addBuilder(
            index, com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any dataList = 2;</code>
       */
      public java.util.List<com.google.protobuf.Any.Builder> 
           getDataListBuilderList() {
        return getDataListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getDataListFieldBuilder() {
        if (dataListBuilder_ == null) {
          dataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  dataList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          dataList_ = null;
        }
        return dataListBuilder_;
      }

      private ehooray.fishing.dto.proto.response.ExceptionProto.Exception exception_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.response.ExceptionProto.Exception, ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder, ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder> exceptionBuilder_;
      /**
       * <code>.response.Exception exception = 3;</code>
       * @return Whether the exception field is set.
       */
      public boolean hasException() {
        return exceptionBuilder_ != null || exception_ != null;
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       * @return The exception.
       */
      public ehooray.fishing.dto.proto.response.ExceptionProto.Exception getException() {
        if (exceptionBuilder_ == null) {
          return exception_ == null ? ehooray.fishing.dto.proto.response.ExceptionProto.Exception.getDefaultInstance() : exception_;
        } else {
          return exceptionBuilder_.getMessage();
        }
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public Builder setException(ehooray.fishing.dto.proto.response.ExceptionProto.Exception value) {
        if (exceptionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          exception_ = value;
          onChanged();
        } else {
          exceptionBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public Builder setException(
          ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder builderForValue) {
        if (exceptionBuilder_ == null) {
          exception_ = builderForValue.build();
          onChanged();
        } else {
          exceptionBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public Builder mergeException(ehooray.fishing.dto.proto.response.ExceptionProto.Exception value) {
        if (exceptionBuilder_ == null) {
          if (exception_ != null) {
            exception_ =
              ehooray.fishing.dto.proto.response.ExceptionProto.Exception.newBuilder(exception_).mergeFrom(value).buildPartial();
          } else {
            exception_ = value;
          }
          onChanged();
        } else {
          exceptionBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public Builder clearException() {
        if (exceptionBuilder_ == null) {
          exception_ = null;
          onChanged();
        } else {
          exception_ = null;
          exceptionBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder getExceptionBuilder() {
        
        onChanged();
        return getExceptionFieldBuilder().getBuilder();
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      public ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder getExceptionOrBuilder() {
        if (exceptionBuilder_ != null) {
          return exceptionBuilder_.getMessageOrBuilder();
        } else {
          return exception_ == null ?
              ehooray.fishing.dto.proto.response.ExceptionProto.Exception.getDefaultInstance() : exception_;
        }
      }
      /**
       * <code>.response.Exception exception = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.response.ExceptionProto.Exception, ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder, ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder> 
          getExceptionFieldBuilder() {
        if (exceptionBuilder_ == null) {
          exceptionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.response.ExceptionProto.Exception, ehooray.fishing.dto.proto.response.ExceptionProto.Exception.Builder, ehooray.fishing.dto.proto.response.ExceptionProto.ExceptionOrBuilder>(
                  getException(),
                  getParentForChildren(),
                  isClean());
          exception_ = null;
        }
        return exceptionBuilder_;
      }

      private long requestId_ ;
      /**
       * <code>int64 requestId = 4;</code>
       * @return The requestId.
       */
      @java.lang.Override
      public long getRequestId() {
        return requestId_;
      }
      /**
       * <code>int64 requestId = 4;</code>
       * @param value The requestId to set.
       * @return This builder for chaining.
       */
      public Builder setRequestId(long value) {
        
        requestId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 requestId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestId() {
        
        requestId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList dirtyFlags_ = emptyIntList();
      private void ensureDirtyFlagsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          dirtyFlags_ = mutableCopy(dirtyFlags_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @return A list containing the dirtyFlags.
       */
      public java.util.List<java.lang.Integer>
          getDirtyFlagsList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(dirtyFlags_) : dirtyFlags_;
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @return The count of dirtyFlags.
       */
      public int getDirtyFlagsCount() {
        return dirtyFlags_.size();
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @param index The index of the element to return.
       * @return The dirtyFlags at the given index.
       */
      public int getDirtyFlags(int index) {
        return dirtyFlags_.getInt(index);
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @param index The index to set the value at.
       * @param value The dirtyFlags to set.
       * @return This builder for chaining.
       */
      public Builder setDirtyFlags(
          int index, int value) {
        ensureDirtyFlagsIsMutable();
        dirtyFlags_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @param value The dirtyFlags to add.
       * @return This builder for chaining.
       */
      public Builder addDirtyFlags(int value) {
        ensureDirtyFlagsIsMutable();
        dirtyFlags_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @param values The dirtyFlags to add.
       * @return This builder for chaining.
       */
      public Builder addAllDirtyFlags(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDirtyFlagsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dirtyFlags_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dirtyFlags = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDirtyFlags() {
        dirtyFlags_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.Response)
    }

    // @@protoc_insertion_point(class_scope:response.Response)
    private static final ehooray.fishing.dto.proto.response.ResponseProto.Response DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.ResponseProto.Response();
    }

    public static ehooray.fishing.dto.proto.response.ResponseProto.Response getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Response>
        PARSER = new com.google.protobuf.AbstractParser<Response>() {
      @java.lang.Override
      public Response parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Response> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Response> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.ResponseProto.Response getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_Response_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_Response_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027response/Response.proto\022\010response\032\031goo" +
      "gle/protobuf/any.proto\032\030response/Excepti" +
      "on.proto\"\331\001\n\010Response\022\'\n\005state\030\001 \001(\0162\030.r" +
      "esponse.Response.State\022&\n\010dataList\030\002 \003(\013" +
      "2\024.google.protobuf.Any\022&\n\texception\030\003 \001(" +
      "\0132\023.response.Exception\022\021\n\trequestId\030\004 \001(" +
      "\003\022\022\n\ndirtyFlags\030\005 \003(\005\"-\n\005State\022\010\n\004None\020\000" +
      "\022\013\n\007Success\020\001\022\r\n\tException\020\002BM\n\"ehooray." +
      "fishing.dto.proto.responseB\rResponseProt" +
      "oH\002\252\002\025Ehooray.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
          ehooray.fishing.dto.proto.response.ExceptionProto.getDescriptor(),
        });
    internal_static_response_Response_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_Response_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_Response_descriptor,
        new java.lang.String[] { "State", "DataList", "Exception", "RequestId", "DirtyFlags", });
    com.google.protobuf.AnyProto.getDescriptor();
    ehooray.fishing.dto.proto.response.ExceptionProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
