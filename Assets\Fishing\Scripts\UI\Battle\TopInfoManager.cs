using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Cysharp.Threading.Tasks;
using Ehooray;
using Ehooray.UI;
using Fishing.UI;
using Zenject;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/Battle/TopUICanvas")]
    public class TopInfoManager : GamePanel
    {

        [SerializeField]
        Button backToLobbybutton;

        SceneLoadingManager sceneLoadingManager;
        FishingContainer fishingContainer;

        public TopInfoManager()
        {
        }

        [Inject]
        public void Construct(SceneLoadingManager sceneLoadingManager,
            FishingContainer fishingContainer,
            GamePanelManager gamePanelManager)
        {
            this.sceneLoadingManager = sceneLoadingManager;
            this.fishingContainer = fishingContainer;
            this.gamePanelManager = gamePanelManager;
        }

        // Start is called before the first frame update
        void Start()
        {
            backToLobbybutton.onClick.AddListener(UniTask.UnityAction(BackToLobby));
        }

        // Update is called once per frame
        void Update()
        {

        }

        public async UniTaskVoid BackToLobby()
        {
            await sceneLoadingManager.LoadSceneAsync("Lobby",(container) =>
            {
                container.BindInstance(true).WithId("isOpenMapPanel").WhenInjectedInto<LobbyBootstrap>();
            });
        }
    }
}