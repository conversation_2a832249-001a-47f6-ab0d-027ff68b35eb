// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/iab/unity/UnityIabAccept.proto

package ehooray.fishing.dto.proto.request.iab.unity;

public final class UnityIabAcceptProto {
  private UnityIabAcceptProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnityIabAcceptOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.iab.unity.UnityIabAccept)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code request.iab.unity.UnityIabAccept}
   */
  public static final class UnityIabAccept extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.iab.unity.UnityIabAccept)
      UnityIabAcceptOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UnityIabAccept.newBuilder() to construct.
    private UnityIabAccept(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UnityIabAccept() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UnityIabAccept();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.internal_static_request_iab_unity_UnityIabAccept_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.internal_static_request_iab_unity_UnityIabAccept_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.Builder.class);
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.iab.unity.UnityIabAccept}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.iab.unity.UnityIabAccept)
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAcceptOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.internal_static_request_iab_unity_UnityIabAccept_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.internal_static_request_iab_unity_UnityIabAccept_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.internal_static_request_iab_unity_UnityIabAccept_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept build() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept buildPartial() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept result = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.iab.unity.UnityIabAccept)
    }

    // @@protoc_insertion_point(class_scope:request.iab.unity.UnityIabAccept)
    private static final ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept();
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnityIabAccept>
        PARSER = new com.google.protobuf.AbstractParser<UnityIabAccept>() {
      @java.lang.Override
      public UnityIabAccept parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnityIabAccept> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnityIabAccept> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.iab.unity.UnityIabAcceptProto.UnityIabAccept getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_iab_unity_UnityIabAccept_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_iab_unity_UnityIabAccept_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&request/iab/unity/UnityIabAccept.proto" +
      "\022\021request.iab.unity\"\020\n\016UnityIabAcceptB\\\n" +
      "+ehooray.fishing.dto.proto.request.iab.u" +
      "nityB\023UnityIabAcceptProtoH\002\252\002\025Fishing.Ne" +
      "twork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_iab_unity_UnityIabAccept_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_iab_unity_UnityIabAccept_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_iab_unity_UnityIabAccept_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
