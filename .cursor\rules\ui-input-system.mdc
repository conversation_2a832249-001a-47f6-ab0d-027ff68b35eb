---
description: Comprehensive explanation of the game's UI components and input systems, including inventory interface, crafting interface, multi-platform input management, specific control schemes for the three fishing mini-games, audio system, settings system, social system UI, shop system UI, and achievement system UI design and implementation.
globs: 
alwaysApply: false
---
# UI和输入系统

## UI组件
- **[InventoryUI.js](mdc:src/ui/InventoryUI.js)** - 库存界面
  - 50个槽位（可通过船只或技能扩展到100）
  - 支持物品分类：鱼卡、装备、消耗品
  - 批量操作：出售重复物品等
  - 物品详情查看和管理功能
  
- **[CraftingUI.js](mdc:src/ui/CraftingUI.js)** - 制作界面
  - 船只工坊制作系统
  - 显示合成配方、所需材料、费用和时间
  - 支持装备升级（合并低级物品）
  - 制作队列和进度显示

## 输入系统
- **[InputManager.js](mdc:src/scripts/InputManager.js)** - 输入管理器
  - 处理键盘、鼠标、触摸输入
  - 支持WASD控制（诱饵游戏）
  - QTE反应控制系统
  - 多平台输入适配（移动端、PC、主机）

## 钓鱼迷你游戏控制
### 抛竿控制
- 鼠标点击或触摸时机控制
- 准确区域内点击到达热点
- 失败则投到普通区域

### 诱饵控制（WASD）
- **W键**：诱饵快速上游
- **A/D键**：诱饵向右上方游动（朝向船只）
- 所有操作有1秒冷却时间
- 5种诱饵类型有不同的控制模式

### 收线控制
- **鼠标按压**：收线并消耗鱼类体力
- **QTE控制**：快速输入防止张力过高
- **张力管理**：保持张力不超过最大值

## 音效系统
- **[AudioManager.js](mdc:src/scripts/AudioManager.js)** - 音频管理器
  - 海洋环境音乐
  - 水花声音效果
  - 胜利号角和成功音效
  - 支持音效和背景音乐分别控制
  - 动态音量调节

## 设置系统
- **[SettingsManager.js](mdc:src/scripts/SettingsManager.js)** - 设置管理器
  - 音量控制（主音量、音效、音乐）
  - 画质设置（分辨率、特效质量）
  - 控制方案选择
  - 自动保存玩家偏好

## 社交系统UI
- **巡航聊天室**：与10个比基尼女孩同伴互动
- **对话系统**：使用RenJS处理故事和对话
- **浪漫度计量表**：0-100点数显示
- **礼物系统**：使用鱼卡或制作物品作为礼物

## 商店系统UI
- **货币显示**：金币和宝石余额
- **商品分类**：鱼卡、装备、特殊物品
- **每日刷新**：00:00自动更新库存
- **购买确认**：防止误购的确认对话框

## 成就系统UI
- **成就分类**：故事、进度、日常/周常任务
- **进度追踪**：实时显示成就进度
- **奖励领取**：金币、宝石、鱼卡奖励

- **成就通知**：完成时的动画提示