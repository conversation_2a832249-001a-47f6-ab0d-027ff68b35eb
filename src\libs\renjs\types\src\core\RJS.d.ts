import 'pixi';
import 'p2';
import { Game, Graphics } from 'phaser-ce';
import RJSControl from './RJSControl';
import BackgroundManager from '../managers/BackgroundManager';
import CharacterManager from '../managers/CharacterManager';
import Accessibility, { AccessibilityConfig } from '../gui/a11y/Accessibility';
import AudioManager from '../managers/AudioManager';
import CGSManager from '../managers/CGSManager';
import TextManager from '../managers/TextManager';
import TweenManager from '../managers/TweenManager';
import LogicManager from '../managers/LogicManager';
import StoryManager from '../managers/StoryManager';
import Ambient from '../screen-effects/Ambient';
import Effects from '../screen-effects/Effects';
import Transition from '../screen-effects/Transition';
import RJSGUI from '../gui/RJSGUI';
import { RJSGameConfig, StoryConfig } from './RJSGameConfig';
import UserPreferences from './UserPreferences';
export default class RJS extends Game {
    gameStarted: boolean;
    control: RJSControl;
    xShots: any[];
    blackOverlay: Graphics;
    setup: any;
    story: {
        [key: string]: any;
    };
    guiSetup: any;
    gui: RJSGUI;
    tools: any;
    screenReady: boolean;
    pluginsRJS: any;
    addPlugin(name: string, cls: any): void;
    get renjsversion(): string;
    config: RJSGameConfig;
    userPreferences: UserPreferences;
    storyConfig: StoryConfig;
    storyAccessibility?: AccessibilityConfig;
    accessibility: Accessibility;
    storyLog: any[];
    interruptAction: any;
    managers: {
        background: BackgroundManager;
        character: CharacterManager;
        audio: AudioManager;
        cgs: CGSManager;
        text: TextManager;
        tween: TweenManager;
        logic: LogicManager;
        story: StoryManager;
    };
    screenEffects: {
        ambient: Ambient;
        effects: Effects;
        transition: Transition;
    };
    propertyRanges: {
        textSpeed: number[];
        autoSpeed: number[];
        bgmv: number[];
        sfxv: number[];
    };
    constructor(config: RJSGameConfig);
    launch(): void;
    setupScreen(): void;
    phaserUpdateHandler(): void;
    initStory(): Promise<any>;
    checkPlugins(signal: string, params?: any[]): Promise<any>;
    pause(): void;
    takeXShot(): void;
    unpause(): Promise<any>;
    endGame(): Promise<any>;
    start(initialVars?: {}): Promise<any>;
    skip(): void;
    auto(): void;
    save(slot?: any): Promise<any>;
    getSlotThumbnail(slot: any): string;
    loadSlot(slot: any): Promise<void>;
    waitForClick(callback?: any): void;
    asyncWait(time: number): Promise<any>;
    waitTimeout(time: any, callback?: any): void;
    waitForClickOrTimeout(time: any, callback: any): void;
    onTap(pointer: any): void;
    initInput(): void;
    lockClick(): void;
    resolveAction: () => void;
    onInterpretActions(): void;
}
