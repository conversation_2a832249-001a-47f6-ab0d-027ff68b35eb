using Cysharp.Threading.Tasks;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚失敗狀態策略
    /// </summary>
    public class FailStrategy : FishingStrategyBase
    {
        public FailStrategy() : base(FishingState.Fail) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToFail方法中的初始化邏輯
            context.fish.ChangeContinueShowFish();
            context.fish.useSkill = false;
            context.continueManager.SetFishInfo();
            context.cordReelManager.ResetCordReel();
            context.fishingGamePanelManager.Close<CordReelManager>();
            context.fishingGamePanelManager.Close<PausePanelManager>();
            context.lineTensionManager.CloseRoot();
            context.fishingGamePanelManager.Close<LineTensionManager>();
            context.fishingGamePanelManager.Close<FishInfoManager>();
            
            var panel = await context.fishingGamePanelManager.OpenAsync<ContinueManager>();
            panel.Refresh();
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoFail方法中的邏輯，目前為空
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 