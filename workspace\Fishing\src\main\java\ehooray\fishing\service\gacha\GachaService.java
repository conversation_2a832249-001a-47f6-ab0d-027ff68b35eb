package ehooray.fishing.service.gacha;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.GachaEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.GachaMachineCsv;
import ehooray.fishing.pojo.event.GachaCountEvent;
import ehooray.fishing.repository.GachaRepository;

@Service
public class GachaService
{
    public enum BuyValue
    {
        Single(1, 1),
        Ten(2, 10),
        Free(3, 1),
        ;
        protected int id;
        protected int count;
        private BuyValue(int id, int count)
        {
            this.id = id;
            this.count = count;
        }

        public int getId()
        {
            return id;
        }
        
        public int getCount()
        {
            return count;
        }

        public static Optional<BuyValue> valueOf(int id)
        {
            return Arrays.stream(BuyValue.values()).filter(x-> x.getId() == id).findFirst();
        }
    }
    
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected GachaRepository gachaRepository;
    
    @Autowired
    protected ApplicationEventPublisher publisher;
    
    public List<GachaEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getGachas();
    }

    public Optional<GachaEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }
    
    public GachaEntity create(PlayerEntity player, GachaMachineCsv.Row gachaRow, LocalDateTime now) throws Exception
    {
        GachaEntity newGacha = new GachaEntity();
        newGacha.setCsvId(gachaRow.getId());
        newGacha.setPlayer(player);
        if(gachaRow.isFree())
            newGacha.setFreeBuyTime(calculateFreeFinishTime(gachaRow, now));
        newGacha.setStartTime(now);

        getAllByPlayer(player).add(newGacha);
        gachaRepository.save(newGacha);

        return newGacha;
    }
    
    public void buyFreeGacha(GachaEntity gacha, LocalDateTime now) throws ExceptionByCsvId
    {
        GachaMachineCsv.Row gachaRow = csvContainer.getCsv(GachaMachineCsv.class).getById(gacha.getCsvId());
        
        if(!gachaRow.isFree())
            throw new ExceptionByCsvId("not free gacha");
        
        if(now.isBefore(gacha.getFreeBuyTime().get()))
            throw new ExceptionByCsvId("it's not time for free gacha");

        gacha.setFreeBuyTime(calculateFreeFinishTime(gachaRow, now));
        gachaRepository.save(gacha);
    }

    public void buyGacha(GachaEntity gacha, int count) throws ExceptionByCsvId
    {
        int newCount = gacha.getBuyCount() + count;
        gacha.setBuyCount(newCount);
        gachaRepository.save(gacha);
        
        publisher.publishEvent(new GachaCountEvent(this, gacha.getPlayer(), count));
    }
    
    public void deleteAll(PlayerEntity player, List<GachaEntity> gachaEntities)
    {
        getAllByPlayer(player).removeAll(gachaEntities);
        gachaRepository.deleteAll(gachaEntities);
    }
    
    protected LocalDateTime calculateFreeFinishTime(GachaMachineCsv.Row gachaRow, LocalDateTime now)
    {
        return now.plusHours(gachaRow.getFreeTime());
    }
}
