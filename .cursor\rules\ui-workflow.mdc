---
description: UI development workflow guide for Fishing project, covering GamePanel inheritance, Zenject dependency injection, resource loading with IResourceProvider, data management through TableContainer and Repository, network communication via Best HTTP, and complete UI development process including DOTween animations and UI manager usage.
globs: 
alwaysApply: false
---
# UI功能開發工作流程

本規則描述了Fishing項目中UI功能開發的完整工作流程和架構模式。

## UI腳本架構

### 基礎繼承結構
- 所有UI腳本都位於 [Assets/Fishing/Scripts/UI](mdc:Assets/Fishing/Scripts/UI) 目錄下
- UI腳本必須繼承 [GamePanel.cs](mdc:Assets/Ehooray/Runtime/UI/GamePanel.cs)
- 使用 [Zenject](mdc:Assets/Plugins/Zenject) 進行依賴注入，避免使用單例模式
- UI元件通過Unity UI系統進行reference
- 必須提供 `Refresh()` 方法用於刷新介面功能

### UI管理器
- 使用 [GamePanelManager.cs](mdc:Assets/Ehooray/Runtime/UI/GamePanelManager.cs) 進行基礎UI管理
- 使用 [FishingGamePanelManager.cs](mdc:Assets/Fishing/Scripts/General/FishingGamePanelManager.cs) 進行遊戲專用UI管理
- 提供UI開啟、關閉、切換等功能

## 資源載入系統

### 資源提供者
- 使用 [IResourceProvider.cs](mdc:Assets/Ehooray/Runtime/File/IResourceProvider.cs) 載入Unity資源
- 支援異步載入機制
- 提供資源快取和管理功能

### 動畫系統
- 使用 [DOTween](mdc:Assets/DOTween) 進行程式化動畫
- 使用Unity Animator進行複雜動畫狀態機
- 優先使用DOTween處理UI動畫效果

## 數據管理架構

### 遊戲配置數據
- 遊戲數據表位於 [Assets/Fishing/Scripts/Tables](mdc:Assets/Fishing/Scripts/Tables) 目錄
- 使用 [QuickSheet](mdc:Assets/QuickSheet) 套件自動生成數據表腳本
- 通過 [TableContainer.cs](mdc:Assets/Fishing/Scripts/General/TableContainer.cs) 統一存取遊戲設定資料

### 伺服器數據
- 伺服器拉取的數據存放在 [Assets/Fishing/Scripts/Repository](mdc:Assets/Fishing/Scripts/Repository) 目錄
- 提供本地緩存和數據同步功能
- 支援數據變更通知機制

## 網路通信系統

### 網路基礎設施
- 使用 [Best HTTP](mdc:Assets/Best HTTP) 作為網路通信基礎
- 業務邏輯網路代碼位於 [Assets/Fishing/Scripts/Network](mdc:Assets/Fishing/Scripts/Network) 目錄
- 支援HTTP請求和WebSocket通信

### 網路服務層
- 網路服務實現在 [Network/Service](mdc:Assets/Fishing/Scripts/Network/Service) 目錄
- 提供統一的API接口封裝
- 處理網路錯誤和重試機制

## 依賴注入架構

### Zenject配置
- 遊戲模組使用 [Zenject](mdc:Assets/Plugins/Zenject) 進行依賴注入
- 避免使用單例模式，提高代碼可測試性
- Zenject安裝器位於 [Assets/Fishing/Scripts/Installers](mdc:Assets/Fishing/Scripts/Installers) 目錄

### 安裝器類型
- [FishingProjectInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingProjectInstaller.cs): 項目級別服務註冊
- [FishingLobbyInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingLobbyInstaller.cs): 場景級別組件註冊

## 開發流程

### UI功能開發步驟
1. **創建UI腳本**: 繼承GamePanel，實現基礎UI框架
2. **依賴注入設計**: 註入需要的服務和管理器
3. **數據綁定**: 連接TableContainer配置數據和Repository伺服器數據
4. **網路交互**: 實現與伺服器的數據傳輸邏輯
5. **UI刷新機制**: 實現Refresh()方法處理數據更新
6. **動畫效果**: 使用DOTween或Animator添加UI動畫

### 數據流向
```
TableContainer (配置) ← QuickSheet生成
     ↓
   UI腳本 ← GamePanel繼承
     ↓
Repository (伺服器數據) ← Network服務
     ↓
GamePanelManager (UI管理)

