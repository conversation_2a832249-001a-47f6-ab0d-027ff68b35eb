package ehooray.fishing.unit.controller;

import static org.mockito.Mockito.spy;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.WebSocketHandleMediator;

public class BaseControllerTest
{
    protected PlayerEntity player;
    protected WebSocketHandleMediator.Builder mediatorBuilder;
    
    @BeforeEach
    protected void beforeEach()
    {
        PlayerAuthEntity playerAuth = spy(PlayerAuthEntity.class);
        playerAuth.setAccount("");
        
        player = new PlayerEntity();
        player.setPlayerAuth(playerAuth);
        player.setName("");
        player.setLevel(1);
        
        mediatorBuilder = spy(WebSocketHandleMediator.Builder.class);
        mediatorBuilder.setPlayer(Optional.of(player));
    }
    
    protected WebSocketHandleMediator buildMediator(Any anyProto)
    {
        // prepare mediator
        RequestProto.Request.Builder requestBuilder = RequestProto.Request.newBuilder();
        requestBuilder.addDataList(anyProto);
        RequestProto.Request requestProto = requestBuilder.build();
        mediatorBuilder.setRequest(requestProto);
        return (WebSocketHandleMediator)mediatorBuilder.build();
    }
}
