// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/quest/AwardQuestResult.proto

package ehooray.fishing.dto.proto.response.quest;

public final class AwardQuestResultProto {
  private AwardQuestResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AwardQuestResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.quest.AwardQuestResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Player player = 1;</code>
     * @return Whether the player field is set.
     */
    boolean hasPlayer();
    /**
     * <code>.model.Player player = 1;</code>
     * @return The player.
     */
    ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer();
    /**
     * <code>.model.Player player = 1;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder();

    /**
     * <code>.model.Quest quest = 2;</code>
     * @return Whether the quest field is set.
     */
    boolean hasQuest();
    /**
     * <code>.model.Quest quest = 2;</code>
     * @return The quest.
     */
    ehooray.fishing.dto.proto.model.QuestProto.Quest getQuest();
    /**
     * <code>.model.Quest quest = 2;</code>
     */
    ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder getQuestOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumePlayerItemsList();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    int getConsumePlayerItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumePlayerItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
        int index);

    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getRewardPlayerItemsList();
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getRewardPlayerItems(int index);
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    int getRewardPlayerItemsCount();
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getRewardPlayerItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getRewardPlayerItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.quest.AwardQuestResult}
   */
  public static final class AwardQuestResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.quest.AwardQuestResult)
      AwardQuestResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AwardQuestResult.newBuilder() to construct.
    private AwardQuestResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AwardQuestResult() {
      consumePlayerItems_ = java.util.Collections.emptyList();
      rewardPlayerItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AwardQuestResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.internal_static_response_quest_AwardQuestResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.internal_static_response_quest_AwardQuestResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.class, ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.Builder.class);
    }

    public static final int PLAYER_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.PlayerProto.Player player_;
    /**
     * <code>.model.Player player = 1;</code>
     * @return Whether the player field is set.
     */
    @java.lang.Override
    public boolean hasPlayer() {
      return player_ != null;
    }
    /**
     * <code>.model.Player player = 1;</code>
     * @return The player.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer() {
      return player_ == null ? ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
    }
    /**
     * <code>.model.Player player = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder() {
      return getPlayer();
    }

    public static final int QUEST_FIELD_NUMBER = 2;
    private ehooray.fishing.dto.proto.model.QuestProto.Quest quest_;
    /**
     * <code>.model.Quest quest = 2;</code>
     * @return Whether the quest field is set.
     */
    @java.lang.Override
    public boolean hasQuest() {
      return quest_ != null;
    }
    /**
     * <code>.model.Quest quest = 2;</code>
     * @return The quest.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.QuestProto.Quest getQuest() {
      return quest_ == null ? ehooray.fishing.dto.proto.model.QuestProto.Quest.getDefaultInstance() : quest_;
    }
    /**
     * <code>.model.Quest quest = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder getQuestOrBuilder() {
      return getQuest();
    }

    public static final int CONSUMEPLAYERITEMS_FIELD_NUMBER = 3;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumePlayerItems_;
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumePlayerItemsList() {
      return consumePlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumePlayerItemsOrBuilderList() {
      return consumePlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    @java.lang.Override
    public int getConsumePlayerItemsCount() {
      return consumePlayerItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index) {
      return consumePlayerItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
        int index) {
      return consumePlayerItems_.get(index);
    }

    public static final int REWARDPLAYERITEMS_FIELD_NUMBER = 4;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> rewardPlayerItems_;
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getRewardPlayerItemsList() {
      return rewardPlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getRewardPlayerItemsOrBuilderList() {
      return rewardPlayerItems_;
    }
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    @java.lang.Override
    public int getRewardPlayerItemsCount() {
      return rewardPlayerItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getRewardPlayerItems(int index) {
      return rewardPlayerItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getRewardPlayerItemsOrBuilder(
        int index) {
      return rewardPlayerItems_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.quest.AwardQuestResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.quest.AwardQuestResult)
        ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.internal_static_response_quest_AwardQuestResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.internal_static_response_quest_AwardQuestResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.class, ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumePlayerItemsFieldBuilder();
          getRewardPlayerItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (playerBuilder_ == null) {
          player_ = null;
        } else {
          player_ = null;
          playerBuilder_ = null;
        }
        if (questBuilder_ == null) {
          quest_ = null;
        } else {
          quest_ = null;
          questBuilder_ = null;
        }
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumePlayerItemsBuilder_.clear();
        }
        if (rewardPlayerItemsBuilder_ == null) {
          rewardPlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          rewardPlayerItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.internal_static_response_quest_AwardQuestResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult build() {
        ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult buildPartial() {
        ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult result = new ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult(this);
        int from_bitField0_ = bitField0_;
        if (playerBuilder_ == null) {
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        if (questBuilder_ == null) {
          result.quest_ = quest_;
        } else {
          result.quest_ = questBuilder_.build();
        }
        if (consumePlayerItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumePlayerItems_ = java.util.Collections.unmodifiableList(consumePlayerItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumePlayerItems_ = consumePlayerItems_;
        } else {
          result.consumePlayerItems_ = consumePlayerItemsBuilder_.build();
        }
        if (rewardPlayerItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            rewardPlayerItems_ = java.util.Collections.unmodifiableList(rewardPlayerItems_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.rewardPlayerItems_ = rewardPlayerItems_;
        } else {
          result.rewardPlayerItems_ = rewardPlayerItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.PlayerProto.Player player_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder> playerBuilder_;
      /**
       * <code>.model.Player player = 1;</code>
       * @return Whether the player field is set.
       */
      public boolean hasPlayer() {
        return playerBuilder_ != null || player_ != null;
      }
      /**
       * <code>.model.Player player = 1;</code>
       * @return The player.
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.Player getPlayer() {
        if (playerBuilder_ == null) {
          return player_ == null ? ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
        } else {
          return playerBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder setPlayer(ehooray.fishing.dto.proto.model.PlayerProto.Player value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          player_ = value;
          onChanged();
        } else {
          playerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder setPlayer(
          ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder builderForValue) {
        if (playerBuilder_ == null) {
          player_ = builderForValue.build();
          onChanged();
        } else {
          playerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder mergePlayer(ehooray.fishing.dto.proto.model.PlayerProto.Player value) {
        if (playerBuilder_ == null) {
          if (player_ != null) {
            player_ =
              ehooray.fishing.dto.proto.model.PlayerProto.Player.newBuilder(player_).mergeFrom(value).buildPartial();
          } else {
            player_ = value;
          }
          onChanged();
        } else {
          playerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = null;
          onChanged();
        } else {
          player_ = null;
          playerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder getPlayerBuilder() {
        
        onChanged();
        return getPlayerFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder getPlayerOrBuilder() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilder();
        } else {
          return player_ == null ?
              ehooray.fishing.dto.proto.model.PlayerProto.Player.getDefaultInstance() : player_;
        }
      }
      /**
       * <code>.model.Player player = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerProto.Player, ehooray.fishing.dto.proto.model.PlayerProto.Player.Builder, ehooray.fishing.dto.proto.model.PlayerProto.PlayerOrBuilder>(
                  getPlayer(),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }

      private ehooray.fishing.dto.proto.model.QuestProto.Quest quest_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.QuestProto.Quest, ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder, ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder> questBuilder_;
      /**
       * <code>.model.Quest quest = 2;</code>
       * @return Whether the quest field is set.
       */
      public boolean hasQuest() {
        return questBuilder_ != null || quest_ != null;
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       * @return The quest.
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest getQuest() {
        if (questBuilder_ == null) {
          return quest_ == null ? ehooray.fishing.dto.proto.model.QuestProto.Quest.getDefaultInstance() : quest_;
        } else {
          return questBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public Builder setQuest(ehooray.fishing.dto.proto.model.QuestProto.Quest value) {
        if (questBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          quest_ = value;
          onChanged();
        } else {
          questBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public Builder setQuest(
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder builderForValue) {
        if (questBuilder_ == null) {
          quest_ = builderForValue.build();
          onChanged();
        } else {
          questBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public Builder mergeQuest(ehooray.fishing.dto.proto.model.QuestProto.Quest value) {
        if (questBuilder_ == null) {
          if (quest_ != null) {
            quest_ =
              ehooray.fishing.dto.proto.model.QuestProto.Quest.newBuilder(quest_).mergeFrom(value).buildPartial();
          } else {
            quest_ = value;
          }
          onChanged();
        } else {
          questBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public Builder clearQuest() {
        if (questBuilder_ == null) {
          quest_ = null;
          onChanged();
        } else {
          quest_ = null;
          questBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder getQuestBuilder() {
        
        onChanged();
        return getQuestFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder getQuestOrBuilder() {
        if (questBuilder_ != null) {
          return questBuilder_.getMessageOrBuilder();
        } else {
          return quest_ == null ?
              ehooray.fishing.dto.proto.model.QuestProto.Quest.getDefaultInstance() : quest_;
        }
      }
      /**
       * <code>.model.Quest quest = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.QuestProto.Quest, ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder, ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder> 
          getQuestFieldBuilder() {
        if (questBuilder_ == null) {
          questBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.QuestProto.Quest, ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder, ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder>(
                  getQuest(),
                  getParentForChildren(),
                  isClean());
          quest_ = null;
        }
        return questBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumePlayerItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumePlayerItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumePlayerItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumePlayerItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumePlayerItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumePlayerItemsList() {
        if (consumePlayerItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumePlayerItems_);
        } else {
          return consumePlayerItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public int getConsumePlayerItemsCount() {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.size();
        } else {
          return consumePlayerItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumePlayerItems(int index) {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.get(index);
        } else {
          return consumePlayerItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder setConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.set(index, value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder setConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder addConsumePlayerItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder addConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumePlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(index, value);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder addConsumePlayerItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder addConsumePlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder addAllConsumePlayerItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumePlayerItems_);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder clearConsumePlayerItems() {
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public Builder removeConsumePlayerItems(int index) {
        if (consumePlayerItemsBuilder_ == null) {
          ensureConsumePlayerItemsIsMutable();
          consumePlayerItems_.remove(index);
          onChanged();
        } else {
          consumePlayerItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumePlayerItemsBuilder(
          int index) {
        return getConsumePlayerItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumePlayerItemsOrBuilder(
          int index) {
        if (consumePlayerItemsBuilder_ == null) {
          return consumePlayerItems_.get(index);  } else {
          return consumePlayerItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumePlayerItemsOrBuilderList() {
        if (consumePlayerItemsBuilder_ != null) {
          return consumePlayerItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumePlayerItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumePlayerItemsBuilder() {
        return getConsumePlayerItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumePlayerItemsBuilder(
          int index) {
        return getConsumePlayerItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumePlayerItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumePlayerItemsBuilderList() {
        return getConsumePlayerItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumePlayerItemsFieldBuilder() {
        if (consumePlayerItemsBuilder_ == null) {
          consumePlayerItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumePlayerItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumePlayerItems_ = null;
        }
        return consumePlayerItemsBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> rewardPlayerItems_ =
        java.util.Collections.emptyList();
      private void ensureRewardPlayerItemsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          rewardPlayerItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(rewardPlayerItems_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> rewardPlayerItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getRewardPlayerItemsList() {
        if (rewardPlayerItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardPlayerItems_);
        } else {
          return rewardPlayerItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public int getRewardPlayerItemsCount() {
        if (rewardPlayerItemsBuilder_ == null) {
          return rewardPlayerItems_.size();
        } else {
          return rewardPlayerItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getRewardPlayerItems(int index) {
        if (rewardPlayerItemsBuilder_ == null) {
          return rewardPlayerItems_.get(index);
        } else {
          return rewardPlayerItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder setRewardPlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (rewardPlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.set(index, value);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder setRewardPlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (rewardPlayerItemsBuilder_ == null) {
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder addRewardPlayerItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (rewardPlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.add(value);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder addRewardPlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (rewardPlayerItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.add(index, value);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder addRewardPlayerItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (rewardPlayerItemsBuilder_ == null) {
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.add(builderForValue.build());
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder addRewardPlayerItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (rewardPlayerItemsBuilder_ == null) {
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder addAllRewardPlayerItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (rewardPlayerItemsBuilder_ == null) {
          ensureRewardPlayerItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardPlayerItems_);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder clearRewardPlayerItems() {
        if (rewardPlayerItemsBuilder_ == null) {
          rewardPlayerItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public Builder removeRewardPlayerItems(int index) {
        if (rewardPlayerItemsBuilder_ == null) {
          ensureRewardPlayerItemsIsMutable();
          rewardPlayerItems_.remove(index);
          onChanged();
        } else {
          rewardPlayerItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getRewardPlayerItemsBuilder(
          int index) {
        return getRewardPlayerItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getRewardPlayerItemsOrBuilder(
          int index) {
        if (rewardPlayerItemsBuilder_ == null) {
          return rewardPlayerItems_.get(index);  } else {
          return rewardPlayerItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getRewardPlayerItemsOrBuilderList() {
        if (rewardPlayerItemsBuilder_ != null) {
          return rewardPlayerItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardPlayerItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addRewardPlayerItemsBuilder() {
        return getRewardPlayerItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addRewardPlayerItemsBuilder(
          int index) {
        return getRewardPlayerItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem rewardPlayerItems = 4;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getRewardPlayerItemsBuilderList() {
        return getRewardPlayerItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getRewardPlayerItemsFieldBuilder() {
        if (rewardPlayerItemsBuilder_ == null) {
          rewardPlayerItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  rewardPlayerItems_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          rewardPlayerItems_ = null;
        }
        return rewardPlayerItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.quest.AwardQuestResult)
    }

    // @@protoc_insertion_point(class_scope:response.quest.AwardQuestResult)
    private static final ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult();
    }

    public static ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<AwardQuestResult>
        PARSER = new com.google.protobuf.AbstractParser<AwardQuestResult>() {
      @java.lang.Override
      public AwardQuestResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<AwardQuestResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AwardQuestResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto.AwardQuestResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_quest_AwardQuestResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_quest_AwardQuestResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%response/quest/AwardQuestResult.proto\022" +
      "\016response.quest\032\022model/Player.proto\032\021mod" +
      "el/Quest.proto\032\026model/PlayerItem.proto\"\253" +
      "\001\n\020AwardQuestResult\022\035\n\006player\030\001 \001(\0132\r.mo" +
      "del.Player\022\033\n\005quest\030\002 \001(\0132\014.model.Quest\022" +
      "-\n\022consumePlayerItems\030\003 \003(\0132\021.model.Play" +
      "erItem\022,\n\021rewardPlayerItems\030\004 \003(\0132\021.mode" +
      "l.PlayerItemB[\n(ehooray.fishing.dto.prot" +
      "o.response.questB\025AwardQuestResultProtoH" +
      "\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.PlayerProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.QuestProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_quest_AwardQuestResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_quest_AwardQuestResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_quest_AwardQuestResult_descriptor,
        new java.lang.String[] { "Player", "Quest", "ConsumePlayerItems", "RewardPlayerItems", });
    ehooray.fishing.dto.proto.model.PlayerProto.getDescriptor();
    ehooray.fishing.dto.proto.model.QuestProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
