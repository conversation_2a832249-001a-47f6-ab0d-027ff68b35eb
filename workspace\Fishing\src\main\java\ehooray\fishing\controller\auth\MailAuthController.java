package ehooray.fishing.controller.auth;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.auth.BasicAuthTokenConverter;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.dto.proto.request.auth.MailAuthProto;
import ehooray.fishing.dto.proto.request.auth.MailRegisterProto;
import ehooray.fishing.dto.proto.response.auth.MailAuthRegisterResultProto;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.HttpProtoHandleMediator;
import ehooray.fishing.dto.proto.response.auth.MailAuthResultProto;
import ehooray.fishing.service.auth.PlayerAuthService;
import ehooray.fishing.service.player.PlayerService;


@Controller("MailAuthController")
public class MailAuthController 
{
	@Autowired
	protected PlayerAuthService playerAuthService;
	@Autowired
	protected PlayerService playerService;
	@Autowired
	protected BasicAuthTokenConverter tokenConverter;
	@Autowired
    protected CsvContainer csvContainer;
	
	@TransactionMethod(Method.Write)
	public void auth(HttpProtoHandleMediator handleMediator) throws Exception
	{
		MailAuthProto.MailAuth mailAuth = handleMediator.getRequest().getDataList(0).unpack(MailAuthProto.MailAuth.class);
		
		String account = mailAuth.getMail();
		String password = mailAuth.getPassword();
		verifyAccountPasswordFormat(account, password);
		
		MailAuthResultProto.MailAuthResult.Builder authResultBuilder = MailAuthResultProto.MailAuthResult.newBuilder();
		Optional<PlayerAuthEntity> playerAuth = playerAuthService.findByAccountAndServerId(account, mailAuth.getServerId());
		if(playerAuth.isEmpty())
		{
			authResultBuilder.setIsResgiter(false);
		}
		else
		{
		    
			if(!playerAuthService.verifyPassword(playerAuth.get(), password))
			    throw new ExceptionByCsvId("incorrect password");
			
			String newToken = playerAuthService.updateToNewToken(playerAuth.get());
			authResultBuilder.setIsResgiter(true);
			authResultBuilder.setToken(tokenConverter.encode(mailAuth.getServerId(), account, newToken));
		}
		handleMediator.addResult(Any.pack(authResultBuilder.build()));
	}
	
	protected void verifyAccountPasswordFormat(String account, String password)
	{
		// TODO: not mail type throw exception, check password length or other limitation
	}
	
	@TransactionMethod(Method.Write)
	public void register(HttpProtoHandleMediator handleMediator) throws Exception
	{
	    handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.PresetData));
		MailRegisterProto.MailRegister registerAuth = handleMediator.getRequest().getDataList(0).unpack(MailRegisterProto.MailRegister.class);
		
		LocalDateTime now = handleMediator.getRequestStartTime();
		MailAuthProto.MailAuth mailAuth = registerAuth.getMailAuth();
		String account = mailAuth.getMail();
        String password = mailAuth.getPassword();
		verifyAccountPasswordFormat(account, password);
		Optional<PlayerAuthEntity> findPlayerAuth = playerAuthService.findByAccountAndServerId(account, mailAuth.getServerId());
		if(findPlayerAuth.isPresent())
			throw new ExceptionByCsvId("duplcated account");
		
		PlayerAuthEntity playerAuth = playerAuthService.createPlayerAuth(mailAuth.getServerId(), account, password, now);
		PlayerEntity newPlayer = playerService.createPlayer(playerAuth, registerAuth.getPlayerName());
		// make sure player list of playerAuth is sync for player cache
		playerAuthService.addPlayer(playerAuth, newPlayer);
		String newToken = playerAuthService.updateToNewToken(playerAuth);
		
		MailAuthRegisterResultProto.MailAuthRegisterResult.Builder registerResultBuilder = MailAuthRegisterResultProto.MailAuthRegisterResult.newBuilder();
		registerResultBuilder.setToken(tokenConverter.encode(mailAuth.getServerId(), account, newToken));
		handleMediator.addResult(Any.pack(registerResultBuilder.build()));
	}
}
