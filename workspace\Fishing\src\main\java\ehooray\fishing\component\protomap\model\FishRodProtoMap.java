package ehooray.fishing.component.protomap.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.FishRodProto;
import ehooray.fishing.entity.FishRodEntity;

@Component
public class FishRodProtoMap
{

    public FishRodProto.FishRod.Builder toBuilderByEntity(FishRodEntity fishRod)
    {
    	FishRodProto.FishRod.Builder builder = FishRodProto.FishRod.newBuilder();
        builder.setEntityId(fishRod.getId());
        builder.setCsvId(fishRod.getCsvId());
        builder.setStrength(fishRod.getStrength());
        builder.setControl(fishRod.getControl());
        builder.setLucky(fishRod.getLucky());
        builder.setDqr(fishRod.getDqr());
        builder.setTimes(fishRod.getTimes());
        builder.setIsLock(fishRod.isLock());
        builder.setLevel(fishRod.getLevel());

        return builder;
    }

    public Any toAnyByEntity(FishRodEntity fishRod)
    {
        return Any.pack(toBuilderByEntity(fishRod).build());
    }

    public ArrayList<Any> toAnyByEntities(List<FishRodEntity> fishRods)
    {
        ArrayList<Any> fishRodProtos = new ArrayList<>(fishRods.size());
        for(FishRodEntity fishRod : fishRods)
        	fishRodProtos.add(toAnyByEntity(fishRod));

        return fishRodProtos;
    }
    
    public FishRodProto.FishRod toProtoByEntity(FishRodEntity fishRod)
    {
        return toBuilderByEntity(fishRod).build();
    }
    
    public ArrayList<FishRodProto.FishRod> toProtoByEntities(List<FishRodEntity> fishRods)
    {
        var result = new ArrayList<FishRodProto.FishRod>(fishRods.size());
        for (var fishRod : fishRods)
            result.add(toProtoByEntity(fishRod));
        
        return result;
    }
}
