// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/iab/unity/UnityIabVerify.proto

package ehooray.fishing.dto.proto.request.iab.unity;

public final class UnityIabVerifyProto {
  private UnityIabVerifyProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnityIabVerifyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.iab.unity.UnityIabVerify)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>string receipt = 2;</code>
     * @return The receipt.
     */
    java.lang.String getReceipt();
    /**
     * <code>string receipt = 2;</code>
     * @return The bytes for receipt.
     */
    com.google.protobuf.ByteString
        getReceiptBytes();
  }
  /**
   * Protobuf type {@code request.iab.unity.UnityIabVerify}
   */
  public static final class UnityIabVerify extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.iab.unity.UnityIabVerify)
      UnityIabVerifyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UnityIabVerify.newBuilder() to construct.
    private UnityIabVerify(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UnityIabVerify() {
      receipt_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UnityIabVerify();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.internal_static_request_iab_unity_UnityIabVerify_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.internal_static_request_iab_unity_UnityIabVerify_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int RECEIPT_FIELD_NUMBER = 2;
    private volatile java.lang.Object receipt_;
    /**
     * <code>string receipt = 2;</code>
     * @return The receipt.
     */
    @java.lang.Override
    public java.lang.String getReceipt() {
      java.lang.Object ref = receipt_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        receipt_ = s;
        return s;
      }
    }
    /**
     * <code>string receipt = 2;</code>
     * @return The bytes for receipt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getReceiptBytes() {
      java.lang.Object ref = receipt_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receipt_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.iab.unity.UnityIabVerify}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.iab.unity.UnityIabVerify)
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerifyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.internal_static_request_iab_unity_UnityIabVerify_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.internal_static_request_iab_unity_UnityIabVerify_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        receipt_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.internal_static_request_iab_unity_UnityIabVerify_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify build() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify buildPartial() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify result = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify(this);
        result.entityId_ = entityId_;
        result.receipt_ = receipt_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object receipt_ = "";
      /**
       * <code>string receipt = 2;</code>
       * @return The receipt.
       */
      public java.lang.String getReceipt() {
        java.lang.Object ref = receipt_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          receipt_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string receipt = 2;</code>
       * @return The bytes for receipt.
       */
      public com.google.protobuf.ByteString
          getReceiptBytes() {
        java.lang.Object ref = receipt_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          receipt_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string receipt = 2;</code>
       * @param value The receipt to set.
       * @return This builder for chaining.
       */
      public Builder setReceipt(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        receipt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string receipt = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceipt() {
        
        receipt_ = getDefaultInstance().getReceipt();
        onChanged();
        return this;
      }
      /**
       * <code>string receipt = 2;</code>
       * @param value The bytes for receipt to set.
       * @return This builder for chaining.
       */
      public Builder setReceiptBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        receipt_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.iab.unity.UnityIabVerify)
    }

    // @@protoc_insertion_point(class_scope:request.iab.unity.UnityIabVerify)
    private static final ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify();
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnityIabVerify>
        PARSER = new com.google.protobuf.AbstractParser<UnityIabVerify>() {
      @java.lang.Override
      public UnityIabVerify parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnityIabVerify> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnityIabVerify> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.iab.unity.UnityIabVerifyProto.UnityIabVerify getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_iab_unity_UnityIabVerify_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_iab_unity_UnityIabVerify_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&request/iab/unity/UnityIabVerify.proto" +
      "\022\021request.iab.unity\"3\n\016UnityIabVerify\022\020\n" +
      "\010entityId\030\001 \001(\003\022\017\n\007receipt\030\002 \001(\tB\\\n+ehoo" +
      "ray.fishing.dto.proto.request.iab.unityB" +
      "\023UnityIabVerifyProtoH\002\252\002\025Fishing.Network" +
      ".Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_iab_unity_UnityIabVerify_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_iab_unity_UnityIabVerify_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_iab_unity_UnityIabVerify_descriptor,
        new java.lang.String[] { "EntityId", "Receipt", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
