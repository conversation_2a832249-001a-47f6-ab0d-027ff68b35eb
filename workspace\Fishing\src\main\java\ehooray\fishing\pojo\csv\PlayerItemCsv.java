package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import org.springframework.util.StringUtils;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("ItemTable.xlsx")
public class PlayerItemCsv extends ACsv<PlayerItemCsv.Row>
{
    public enum Tag
    {
        CashCoin(2),
        FreeCashCoin(3),
        GameCoin(4),
        <PERSON>p(5),
        <PERSON>(101),
        <PERSON>NFT(102),
        <PERSON><PERSON><PERSON>(201),
        <PERSON>t(301),
        FishLine(401),
        Box(3001),
        ;

        protected int id;

        private Tag(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<Tag> valueOf(int id)
        {
            return Arrays.stream(Tag.values()).filter(x -> x.getId() == id).findFirst();
        }
    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("Tag")
        protected int tag;

        @ExcelCellName("Parameter_1")
        protected String parameter1;
        
        @ExcelCellName("Parameter_2")
        protected String parameter2;
        
        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }

        public int getTag()
        {
            return tag;
        }
        
        public Optional<Tag> getTagEnum()
        {
            return Tag.valueOf(getTag());
        }
        
        protected String[] paramters;
        protected String[] getParameters()
        {
            if(paramters != null) return paramters;
            
            // atomic operation, do not assign separately
            paramters = new String[] 
            { 
                StringUtils.hasText(parameter1) ? parameter1 : "0", 
                StringUtils.hasText(parameter2) ? parameter2 : "0"
            };
            return paramters;
        }
        
        public int getParametersCount()
        {
            return getParameters().length;
        }
        
        public int getParameterByInt(int index)
        {
            return Integer.parseInt(getParameters()[index]);
        }
    }

    // another utility function at here
    
    protected HashMap<Integer, List<Row>> tagMap;
    
    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();
        
        Collection<Row> rows = getRows();
        tagMap = new HashMap<Integer, List<Row>>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsWithTag = tagMap.get(row.getTag());
            if(rowsWithTag == null)
            {
                rowsWithTag = new ArrayList<Row>();
                tagMap.put(row.getTag(), rowsWithTag);
            }
            rowsWithTag.add(row);
        }
    }
    
    public List<Row> findAllByTag(int tag)
    {
        return tagMap.get(tag);
    }
    
    public Row findByTag(int tag)
    {
        return findAllByTag(tag).get(0);
    }
}
