package ehooray.fishing.log.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import ehooray.fishing.pojo.ExceptionByCsvId;

@Entity
//@formatter:off
@Table
(
  name = "fish_item_log",
  indexes =
  {
      @Index(name = "csvId", columnList = "csvId"),
      @Index(name = "playerId", columnList = "playerId"),
      @Index(name = "createDate", columnList = "createDate"),
  }
)
//@formatter:on
public class FishItemLogEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected int fromSystem;
    protected int behaviour;
    protected int behaviourInput1;
    protected int behaviourInput2;
    protected long playerId;
    
    @Temporal(TemporalType.TIMESTAMP)// for performance
    @Column(nullable = false)
    protected Date createDate;

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }

    public void setFromSystem(int fromSystem)
    {
        this.fromSystem = fromSystem;
    }

    public void setBehaviour(int behaviour)
    {
        this.behaviour = behaviour;
    }
    
    public void setBehaviourInput(int index, int behaviourInput)
    {
        switch (index)
        {
            case 0: behaviourInput1 = behaviourInput; break;
            case 1: behaviourInput2 = behaviourInput; break;

            default: throw new ExceptionByCsvId("Not support exception");
        }
    }

    public void setPlayerId(long playerId)
    {
        this.playerId = playerId;
    }
    
    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }
}
