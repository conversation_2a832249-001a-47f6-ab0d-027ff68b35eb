package ehooray.fishing.controller.quest;

import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.component.ConditionComponentFilter;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.AchievementProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.component.protomap.model.PlayerProtoMap;
import ehooray.fishing.component.protomap.model.QuestProtoMap;
import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.dto.proto.request.quest.AwardQuestProto;
import ehooray.fishing.dto.proto.response.quest.AwardQuestResultProto;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.QuestEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.DisplayCsv;
import ehooray.fishing.pojo.csv.QuestCsv;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.player.PlayerService;
import ehooray.fishing.service.quest.QuestService;

@Controller("QuestController")
public class QuestController
{
    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected QuestProtoMap questMap;
    
    @Autowired
    protected AchievementProtoMap achievementMap;

    @Autowired
    protected QuestService questService;

    @Autowired
    protected PlayerService playerService;

    @Autowired
    protected PlayerItemService playerItemService;

    @Autowired
    protected PlayerProtoMap playerProtoMap;

    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    
    @Autowired
    protected ScheduleCalculator scheduleCalculator;
    
    @Autowired
    protected ConditionComponentFilter conditionComponentFilter;

    @TransactionMethod(Method.Write)
    public void getQuests(WebSocketHandleMediator handleMediator) throws Exception
    {
        PlayerEntity player = handleMediator.getPlayer().get();
        var nowTime = handleMediator.getRequestStartTime();
        
        var questCsv = csvContainer.getCsv(QuestCsv.class);
        
        // remove not on csv quest
        var missingCsvQuests = questService.getAllByPlayer(player).stream().filter(
                quest -> questCsv.getOptionalById(quest.getCsvId()).isEmpty()).collect(Collectors.toList());
        questService.deleteAll(player, missingCsvQuests);
        // remove already out of date quests
        var expiredQuests = questService.getAllByPlayer(player).stream().filter(quest->
            scheduleCalculator.isOutStartCycle(DisplayCsv.Type.Quest, quest.getCsvId(), quest.getStartTime(), nowTime)).collect(Collectors.toList());
        questService.deleteAll(player, expiredQuests);
   
        // find not exist quest rows
        var existCsvIds = questService.getAllByPlayer(player).stream().map(quest -> quest.getCsvId()).collect(Collectors.toList());
        var candidateQuestRows = questCsv.getRows().stream().filter(row -> !existCsvIds.contains(row.getId())).collect(Collectors.toList());
        
        // filter not on schedule
        candidateQuestRows.removeIf(row -> !scheduleCalculator.isInCycle(DisplayCsv.Type.Quest, row.getId(), nowTime));
        
        // filter unlock rows
        conditionComponentFilter.removeFalsedRows(player, candidateQuestRows, row -> row.getUnlockCondition());
        
        // create new Quest
        for (var questRow : candidateQuestRows)
        {
            questService.newQuest(player, questRow, nowTime);
        }
        
        handleMediator.addResult(questMap.toAnyByEntities(player.getQuests()));
    }
    
    @TransactionMethod(Method.Read)
    public void getAchievements(WebSocketHandleMediator handleMediator) throws Exception
    {
        PlayerEntity player = handleMediator.getPlayer().get();
        
        var achievements = player.getAchievements().stream().collect(Collectors.toList());
        handleMediator.addResult(achievementMap.toAnyByEntities(achievements));
    }

    @TransactionMethod(Method.Write)
    public void awardQuest(WebSocketHandleMediator handleMediator) throws Exception
    {
        AwardQuestProto.AwardQuest awardQuest = handleMediator.getRequest().getDataList(0)
                .unpack(AwardQuestProto.AwardQuest.class);

        PlayerEntity player = handleMediator.getPlayer().get();
        Optional<QuestEntity> quest = questService.findByEntityId(player, awardQuest.getQuestEntityId());

        QuestCsv.Row questRow = csvContainer.getCsv(QuestCsv.class).getById(quest.get().getCsvId());

        // award quest
        questService.awardQuest(player, quest.get());
        
        // consume require player item
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.QuestController_awardQuest, questRow.getId()));
        ArrayList<PlayerItemService.DiffItem> consumeItemResult = playerItemService.consumeItems(player, questRow.getConsumeIds(), questRow.getConsumeValues());
        // consume target require player item
        consumeItemResult.addAll(questService.consumeHaveItemTargets(player, quest.get()));

        // product item
        ArrayList<PlayerItemService.DiffItem> produceItemResult = playerItemService.produceItems(player, questRow.getRewardIds(), questRow.getRewardValues());
        
        // give exp to player
        PlayerService.DiffExp playerDiffExpResult = playerService.addExpToPlayer(player, questRow.getExpReward());

        AwardQuestResultProto.AwardQuestResult.Builder resultBuilder = AwardQuestResultProto.AwardQuestResult.newBuilder();
        resultBuilder.setPlayer(playerProtoMap.toBuilderByDiffExp(playerDiffExpResult));
        resultBuilder.setQuest(questMap.toBuilderByEntity(quest.get()).build());
        resultBuilder.addAllConsumePlayerItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
        resultBuilder.addAllRewardPlayerItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
