using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚等待狀態策略
    /// </summary>
    public class WaitStrategy : FishingStrategyBase
    {
        public WaitStrategy() : base(FishingState.Wait) { }
        
        public override UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToWait方法中的初始化邏輯
            context.cordReelManager.wheelRotate.SetWaitFishSpeed();
            context.baitPath.StopBaitAnimation();
            context.fishRod.HideBaitObject();
            
            return UniTask.CompletedTask;
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoWait方法中的邏輯
            context.fish.Wait();
            context.forceItem.PlayWaterEffectForWait();
            context.forceItem.SetForce(5);
            if (context.fish.waitTime <= 0)
            {
                context.TransitionToState(FishingState.Fight).Forget();
            }
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 