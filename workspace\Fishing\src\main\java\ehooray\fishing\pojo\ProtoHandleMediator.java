package ehooray.fishing.pojo;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.pojo.LogSource;

public class ProtoHandleMediator
{
	protected RequestProto.Request request;
	protected List<Any> resultList = new ArrayList<>();
	protected Optional<PlayerEntity> player;
	protected LocalDateTime requestStartTime;
	protected List<Integer> dirtyFlags = new ArrayList<Integer>();
	protected LogSource logSource;

    protected ProtoHandleMediator() {}

	public RequestProto.Request getRequest()
	{
		return request;
	}
	public void addResult(Any result)
	{
		resultList.add(result);
	}
	public void addResult(List<Any> result)
    {
        resultList.addAll(result);
    }
	public void addDirtyFlag(int flag)
	{
	    if(dirtyFlags.contains(flag)) return;
	    
	    dirtyFlags.add(flag);
	}
	public List<Any> getResultList()
	{
		return resultList;
	}
	public List<Integer> getDirtyFlags()
    {
        return dirtyFlags;
    }

	public Optional<PlayerEntity> getPlayer()
    {
        return player;
    }

	public LocalDateTime getRequestStartTime()
    {
        return requestStartTime;
    }
	
	public void setLogSource(LogSource logSource)
    {
        this.logSource = logSource;
    }
    
    public LogSource getLogSource()
    {
        return logSource;
    }

	public static class Builder
	{
		protected ProtoHandleMediator instance;
		protected long playerId = -1;

		public Builder(ProtoHandleMediator instance)
		{
		    this.instance = instance;
		}

		public void setRequest(RequestProto.Request request)
		{
			instance.request = request;
		}

		public ProtoHandleMediator build()
		{
			return instance;
		}

		public Class<?> getBuildClassType()
		{
		    return instance.getClass();
		}

        public void setPlayerId(long playerId)
        {
            this.playerId = playerId;
        }

        public long getPlayerId()
        {
            return playerId;
        }

        public boolean haveSetPlayerId()
        {
            return playerId > 0;
        }

        public void setPlayer(Optional<PlayerEntity> player)
        {
            instance.player = player;
        }

        public void setRequestStartTime(LocalDateTime requestStartTime)
        {
            instance.requestStartTime = requestStartTime;
        }
	}

}
