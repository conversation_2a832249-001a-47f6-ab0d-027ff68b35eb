// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/fishitem/SellFish.proto

package ehooray.fishing.dto.proto.request.fishitem;

public final class SellFishProto {
  private SellFishProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SellFishOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.fishitem.SellFish)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @return A list containing the entityIds.
     */
    java.util.List<java.lang.Long> getEntityIdsList();
    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @return The count of entityIds.
     */
    int getEntityIdsCount();
    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @param index The index of the element to return.
     * @return The entityIds at the given index.
     */
    long getEntityIds(int index);
  }
  /**
   * Protobuf type {@code request.fishitem.SellFish}
   */
  public static final class SellFish extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.fishitem.SellFish)
      SellFishOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SellFish.newBuilder() to construct.
    private SellFish(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SellFish() {
      entityIds_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SellFish();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.internal_static_request_fishitem_SellFish_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.internal_static_request_fishitem_SellFish_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.class, ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.Builder.class);
    }

    public static final int ENTITYIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList entityIds_;
    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @return A list containing the entityIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getEntityIdsList() {
      return entityIds_;
    }
    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @return The count of entityIds.
     */
    public int getEntityIdsCount() {
      return entityIds_.size();
    }
    /**
     * <code>repeated int64 entityIds = 1;</code>
     * @param index The index of the element to return.
     * @return The entityIds at the given index.
     */
    public long getEntityIds(int index) {
      return entityIds_.getLong(index);
    }
    private int entityIdsMemoizedSerializedSize = -1;

    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.fishitem.SellFish}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.fishitem.SellFish)
        ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFishOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.internal_static_request_fishitem_SellFish_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.internal_static_request_fishitem_SellFish_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.class, ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.internal_static_request_fishitem_SellFish_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish build() {
        ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish buildPartial() {
        ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish result = new ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          entityIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.entityIds_ = entityIds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList entityIds_ = emptyLongList();
      private void ensureEntityIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          entityIds_ = mutableCopy(entityIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @return A list containing the entityIds.
       */
      public java.util.List<java.lang.Long>
          getEntityIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(entityIds_) : entityIds_;
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @return The count of entityIds.
       */
      public int getEntityIdsCount() {
        return entityIds_.size();
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @param index The index of the element to return.
       * @return The entityIds at the given index.
       */
      public long getEntityIds(int index) {
        return entityIds_.getLong(index);
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The entityIds to set.
       * @return This builder for chaining.
       */
      public Builder setEntityIds(
          int index, long value) {
        ensureEntityIdsIsMutable();
        entityIds_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @param value The entityIds to add.
       * @return This builder for chaining.
       */
      public Builder addEntityIds(long value) {
        ensureEntityIdsIsMutable();
        entityIds_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @param values The entityIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllEntityIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureEntityIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, entityIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 entityIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityIds() {
        entityIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.fishitem.SellFish)
    }

    // @@protoc_insertion_point(class_scope:request.fishitem.SellFish)
    private static final ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish();
    }

    public static ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SellFish>
        PARSER = new com.google.protobuf.AbstractParser<SellFish>() {
      @java.lang.Override
      public SellFish parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SellFish> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SellFish> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.fishitem.SellFishProto.SellFish getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_fishitem_SellFish_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_fishitem_SellFish_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037request/fishitem/SellFish.proto\022\020reque" +
      "st.fishitem\"\035\n\010SellFish\022\021\n\tentityIds\030\001 \003" +
      "(\003BU\n*ehooray.fishing.dto.proto.request." +
      "fishitemB\rSellFishProtoH\002\252\002\025Fishing.Netw" +
      "ork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_fishitem_SellFish_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_fishitem_SellFish_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_fishitem_SellFish_descriptor,
        new java.lang.String[] { "EntityIds", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
