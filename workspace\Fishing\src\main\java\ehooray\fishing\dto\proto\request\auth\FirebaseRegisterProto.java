// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/auth/FirebaseRegister.proto

package ehooray.fishing.dto.proto.request.auth;

public final class FirebaseRegisterProto {
  private FirebaseRegisterProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FirebaseRegisterOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.auth.FirebaseRegister)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     * @return Whether the firebaseAuth field is set.
     */
    boolean hasFirebaseAuth();
    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     * @return The firebaseAuth.
     */
    ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getFirebaseAuth();
    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     */
    ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder getFirebaseAuthOrBuilder();

    /**
     * <code>string playerName = 2;</code>
     * @return The playerName.
     */
    java.lang.String getPlayerName();
    /**
     * <code>string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();
  }
  /**
   * Protobuf type {@code request.auth.FirebaseRegister}
   */
  public static final class FirebaseRegister extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.auth.FirebaseRegister)
      FirebaseRegisterOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FirebaseRegister.newBuilder() to construct.
    private FirebaseRegister(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FirebaseRegister() {
      playerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FirebaseRegister();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.internal_static_request_auth_FirebaseRegister_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.internal_static_request_auth_FirebaseRegister_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.class, ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.Builder.class);
    }

    public static final int FIREBASEAUTH_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth firebaseAuth_;
    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     * @return Whether the firebaseAuth field is set.
     */
    @java.lang.Override
    public boolean hasFirebaseAuth() {
      return firebaseAuth_ != null;
    }
    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     * @return The firebaseAuth.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getFirebaseAuth() {
      return firebaseAuth_ == null ? ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.getDefaultInstance() : firebaseAuth_;
    }
    /**
     * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder getFirebaseAuthOrBuilder() {
      return getFirebaseAuth();
    }

    public static final int PLAYERNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object playerName_;
    /**
     * <code>string playerName = 2;</code>
     * @return The playerName.
     */
    @java.lang.Override
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        playerName_ = s;
        return s;
      }
    }
    /**
     * <code>string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.auth.FirebaseRegister}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.auth.FirebaseRegister)
        ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegisterOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.internal_static_request_auth_FirebaseRegister_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.internal_static_request_auth_FirebaseRegister_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.class, ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (firebaseAuthBuilder_ == null) {
          firebaseAuth_ = null;
        } else {
          firebaseAuth_ = null;
          firebaseAuthBuilder_ = null;
        }
        playerName_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.internal_static_request_auth_FirebaseRegister_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister build() {
        ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister buildPartial() {
        ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister result = new ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister(this);
        if (firebaseAuthBuilder_ == null) {
          result.firebaseAuth_ = firebaseAuth_;
        } else {
          result.firebaseAuth_ = firebaseAuthBuilder_.build();
        }
        result.playerName_ = playerName_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth firebaseAuth_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder> firebaseAuthBuilder_;
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       * @return Whether the firebaseAuth field is set.
       */
      public boolean hasFirebaseAuth() {
        return firebaseAuthBuilder_ != null || firebaseAuth_ != null;
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       * @return The firebaseAuth.
       */
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getFirebaseAuth() {
        if (firebaseAuthBuilder_ == null) {
          return firebaseAuth_ == null ? ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.getDefaultInstance() : firebaseAuth_;
        } else {
          return firebaseAuthBuilder_.getMessage();
        }
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public Builder setFirebaseAuth(ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth value) {
        if (firebaseAuthBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          firebaseAuth_ = value;
          onChanged();
        } else {
          firebaseAuthBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public Builder setFirebaseAuth(
          ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder builderForValue) {
        if (firebaseAuthBuilder_ == null) {
          firebaseAuth_ = builderForValue.build();
          onChanged();
        } else {
          firebaseAuthBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public Builder mergeFirebaseAuth(ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth value) {
        if (firebaseAuthBuilder_ == null) {
          if (firebaseAuth_ != null) {
            firebaseAuth_ =
              ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.newBuilder(firebaseAuth_).mergeFrom(value).buildPartial();
          } else {
            firebaseAuth_ = value;
          }
          onChanged();
        } else {
          firebaseAuthBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public Builder clearFirebaseAuth() {
        if (firebaseAuthBuilder_ == null) {
          firebaseAuth_ = null;
          onChanged();
        } else {
          firebaseAuth_ = null;
          firebaseAuthBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder getFirebaseAuthBuilder() {
        
        onChanged();
        return getFirebaseAuthFieldBuilder().getBuilder();
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder getFirebaseAuthOrBuilder() {
        if (firebaseAuthBuilder_ != null) {
          return firebaseAuthBuilder_.getMessageOrBuilder();
        } else {
          return firebaseAuth_ == null ?
              ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.getDefaultInstance() : firebaseAuth_;
        }
      }
      /**
       * <code>.request.auth.FirebaseAuth firebaseAuth = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder> 
          getFirebaseAuthFieldBuilder() {
        if (firebaseAuthBuilder_ == null) {
          firebaseAuthBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder>(
                  getFirebaseAuth(),
                  getParentForChildren(),
                  isClean());
          firebaseAuth_ = null;
        }
        return firebaseAuthBuilder_;
      }

      private java.lang.Object playerName_ = "";
      /**
       * <code>string playerName = 2;</code>
       * @return The playerName.
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          playerName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string playerName = 2;</code>
       * @return The bytes for playerName.
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string playerName = 2;</code>
       * @param value The playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string playerName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerName() {
        
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <code>string playerName = 2;</code>
       * @param value The bytes for playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        playerName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.auth.FirebaseRegister)
    }

    // @@protoc_insertion_point(class_scope:request.auth.FirebaseRegister)
    private static final ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister();
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FirebaseRegister>
        PARSER = new com.google.protobuf.AbstractParser<FirebaseRegister>() {
      @java.lang.Override
      public FirebaseRegister parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FirebaseRegister> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FirebaseRegister> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.FirebaseRegisterProto.FirebaseRegister getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_auth_FirebaseRegister_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_auth_FirebaseRegister_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#request/auth/FirebaseRegister.proto\022\014r" +
      "equest.auth\032\037request/auth/FirebaseAuth.p" +
      "roto\"X\n\020FirebaseRegister\0220\n\014firebaseAuth" +
      "\030\001 \001(\0132\032.request.auth.FirebaseAuth\022\022\n\npl" +
      "ayerName\030\002 \001(\tBY\n&ehooray.fishing.dto.pr" +
      "oto.request.authB\025FirebaseRegisterProtoH" +
      "\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.getDescriptor(),
        });
    internal_static_request_auth_FirebaseRegister_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_auth_FirebaseRegister_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_auth_FirebaseRegister_descriptor,
        new java.lang.String[] { "FirebaseAuth", "PlayerName", });
    ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
