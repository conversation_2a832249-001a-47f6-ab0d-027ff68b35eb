package ehooray.fishing.component.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.protobuf.Any;

import ehooray.fishing.component.WebSocketHandler;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.init.CheckServerRunner;
import ehooray.fishing.component.logger.CheckMaintainScheduleLogger;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto;
import ehooray.fishing.entity.ServerInfoEntity;
import ehooray.fishing.pojo.csv.LanguageCsv;
import ehooray.fishing.service.server.ServerInfoService;

@Component
public class CheckMaintainSchedule
{
    public enum State
    {
        NotInitial,
        Nothing,
        Finish
    }
    
    @Autowired
    protected ServerInfoService serverInfoService;

    @Autowired
    protected WebSocketHandler webSocketHandler;

    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected CheckServerRunner checkServerRunner;
    
    @Scheduled(fixedDelayString = "${game.check-maintain-schedule}")
    @Transactional(readOnly = true, timeoutString = "${game.transactional.timeout}")
    public State check()
    {
        if(!checkServerRunner.isInitialFinished()) return State.NotInitial;
        
        ServerInfoEntity myServerInfo = serverInfoService.myServerInfo().get();
        if(!myServerInfo.isMaintain()) return State.Nothing;

        
        CloseSessionRequestProto.CloseSessionRequest.Builder closeSessionRequest = CloseSessionRequestProto.CloseSessionRequest.newBuilder();
        closeSessionRequest.setCsvId(csvContainer.getCsv(LanguageCsv.class).getMaintainRow().getId());
        RequestProto.Request.Builder requestBuilder = RequestProto.Request.newBuilder();
        requestBuilder.setController("MaintainService");
        requestBuilder.setMethod("OnMaintain");
        requestBuilder.addDataList(Any.pack(closeSessionRequest.build()));
        
        webSocketHandler.closeAllSessions(requestBuilder.build(), CheckMaintainScheduleLogger.class.getCanonicalName());
        
        return State.Finish;
    }
}
