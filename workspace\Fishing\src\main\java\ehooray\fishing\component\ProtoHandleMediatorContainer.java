package ehooray.fishing.component;

import java.util.HashMap;

import org.springframework.stereotype.Component;

import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.ProtoHandleMediator;

@Component
public class ProtoHandleMediatorContainer
{
    // Note: because there won't have same id threads, use hashmap directly
    protected HashMap<Long, ProtoHandleMediator> threadIdMediatorMap = new HashMap<>();

    public void addMediator(ProtoHandleMediator mediator) throws Exception
    {
        long threadId = Thread.currentThread().getId();
        if(threadIdMediatorMap.containsKey(threadId))
            throw new ExceptionByCsvId("already exist same thread id = " + threadId);

        threadIdMediatorMap.put(threadId, mediator);
    }

    public void removeMediator()
    {
        threadIdMediatorMap.remove(Thread.currentThread().getId());
    }
    
    public ProtoHandleMediator getMediator()
    {
        return threadIdMediatorMap.get(Thread.currentThread().getId());
    }
}
