---
description: Detailed description of the scene system architecture based on Phaser.Scene, including initialization scenes (boot, preload, main menu), core game scenes (boat menu, main game, HUD), functional scenes (shop, settings), and scene management, transitions, and data passing mechanisms between scenes.
globs: 
alwaysApply: false
---
# 场景系统

## 场景结构
所有场景继承自Phaser.Scene，按游戏流程组织：

### 初始化场景
- **[BootScene.js](mdc:src/scenes/BootScene.js)** - 引导场景，初始化基础资源
- **[PreloadScene.js](mdc:src/scenes/PreloadScene.js)** - 预加载场景，加载游戏资源
- **[MenuScene.js](mdc:src/scenes/MenuScene.js)** - 主菜单场景

### 核心游戏场景  
- **[BoatMenuScene.js](mdc:src/scenes/BoatMenuScene.js)** - 船只菜单场景
  - 游戏的中心枢纽
  - 显示时间、天气、位置信息
  - 提供旅行、钓鱼、聊天、商店、库存选项
  - 处理玩家在船上的所有主要决策
  
- **[GameScene.js](mdc:src/scenes/GameScene.js)** - 主游戏场景
  - 钓鱼迷你游戏的主要场景
  - 处理抛竿、诱饵、收线三个阶段
  - 管理鱼类AI和玩家交互
  - 显示钓鱼环境和动画
  
- **[HUDScene.js](mdc:src/scenes/HUDScene.js)** - HUD场景
  - 游戏界面覆盖层
  - 显示玩家状态、时间、天气等信息
  - 提供快速访问按钮和通知

### 功能场景
- **[ShopScene.js](mdc:src/scenes/ShopScene.js)** - 商店场景
  - 只在起始港口可访问
  - 购买鱼卡、装备、宝石
  - 每日00:00刷新库存
  
- **[SettingsScene.js](mdc:src/scenes/SettingsScene.js)** - 设置场景
  - 音量控制、画质设置
  - 游戏偏好配置

## 场景管理
- **场景注册**：在[main.js](mdc:src/main.js)中注册所有场景
- **场景切换**：使用[SceneManager.js](mdc:src/scripts/SceneManager.js)进行场景切换
- **场景叠加**：支持场景叠加（如HUD覆盖在GameScene上）
- **数据传递**：场景间通过事件系统和GameState传递数据

## 游戏流程
1. **BootScene** → **PreloadScene** → **MenuScene**
2. **MenuScene** → **BoatMenuScene**（游戏中心）
3. **BoatMenuScene** ↔ **GameScene**（钓鱼时）
4. **BoatMenuScene** ↔ **ShopScene**（在港口时）
5. **任意场景** ↔ **SettingsScene**（设置覆盖）
6. **HUDScene** 始终作为覆盖层显示

## 场景状态管理
- 每个场景维护自己的局部状态
- 全局状态通过[GameState.js](mdc:src/scripts/GameState.js)管理
- 场景间通信使用Phaser的事件系统

- 重要数据自动保存到云端或本地存储