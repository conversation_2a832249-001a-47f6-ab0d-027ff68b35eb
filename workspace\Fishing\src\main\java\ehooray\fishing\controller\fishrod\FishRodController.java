package ehooray.fishing.controller.fishrod;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.FishRodProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.dto.proto.request.fishrod.LockFishRodProto;
import ehooray.fishing.dto.proto.request.fishrod.RepairFishRodProto;
import ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto;
import ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto;
import ehooray.fishing.dto.proto.response.fishrod.RepairFishRodResultProto;
import ehooray.fishing.dto.proto.response.fishrod.SellFishRodResultProto;
import ehooray.fishing.dto.proto.response.fishrod.UpgradeFishRodResultProto;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.RodCsv;
import ehooray.fishing.pojo.csv.UpgradeCsv;
import ehooray.fishing.service.fishrod.FishRodService;
import ehooray.fishing.service.item.PlayerItemService;

@Controller("FishRodController")
public class FishRodController 
{
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected FishRodProtoMap fishRodProtoMap;
    
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    
    @Autowired
    protected FishRodService fishRodService;
    
    @Autowired
    protected PlayerItemService playerItemService;
    
    @TransactionMethod(Method.Read)
	public void getFishRods(WebSocketHandleMediator handleMediator) throws Exception
	{
        // response
        var player = handleMediator.getPlayer().get();
		handleMediator.addResult(fishRodProtoMap.toAnyByEntities(player.getFishRods()));
	}
    
    @TransactionMethod(Method.Write)
    public void lockFishRod(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var lockFishRod = handleMediator.getRequest().getDataList(0).unpack(LockFishRodProto.LockFishRod.class);
        var player = handleMediator.getPlayer().get();
        var fishRod = fishRodService.findByEntityId(player, lockFishRod.getEntityId()).get();
        
        // action
        fishRodService.lockFishRod(fishRod, lockFishRod.getIsLock());
        
        // response
        handleMediator.addResult(fishRodProtoMap.toAnyByEntities(List.of(fishRod)));
    }
    
    @TransactionMethod(Method.Write)
    public void repairFishRod(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var repairFishRod = handleMediator.getRequest().getDataList(0).unpack(RepairFishRodProto.RepairFishRod.class);
        var player = handleMediator.getPlayer().get();
        var fishRod = fishRodService.findByEntityId(player, repairFishRod.getEntityId()).get();
        var fishCsvRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
        var coinCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.GameCoin.getId());
        
        // action
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.FishRodController_repairFishRod, fishCsvRow.getId()));
        // consume coin : amount = (|max-current| * perFee) * Discount
        // discount = 1 - (Durability Quality and Reliability)*0.2 %
        var consumeItemResult = new ArrayList<PlayerItemService.DiffItem>();
        int consumeItemAmount = Math.abs(fishCsvRow.getTimes() - fishRod.getTimes()) * fishCsvRow.getPerFee();
        consumeItemAmount = Math.round(consumeItemAmount * fishRod.getRepairDiscount());
        var diffItem = playerItemService.consumeItem(player, coinCsvRow, consumeItemAmount);
        consumeItemResult.add(diffItem);
        // repair
        fishRodService.repairFishRod(fishRod, fishCsvRow);
        
        // response
        var resultBuilder = RepairFishRodResultProto.RepairFishRodResult.newBuilder();
        resultBuilder.setFishRod(fishRodProtoMap.toProtoByEntity(fishRod));
        resultBuilder.addAllConsumeItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void sellFishRod(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var sellFishRod = handleMediator.getRequest().getDataList(0).unpack(SellFishRodProto.SellFishRod.class);
        var player = handleMediator.getPlayer().get();
        var fishRod = fishRodService.findByEntityId(player, sellFishRod.getEntityId()).get(); 
        var fishCsvRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
        var coinCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.GameCoin.getId());

        // action
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.FishRodController_sellFishRod, fishCsvRow.getId()));
        // sell(delete entity)
        fishRodService.sellFishRod(player, fishRod);
        // add coin
        var produceItemResult = new ArrayList<PlayerItemService.DiffItem>();
        produceItemResult.add(playerItemService.produceItem(player, coinCsvRow, fishCsvRow.getSell()));
        
        // response
        var resultBuilder = SellFishRodResultProto.SellFishRodResult.newBuilder();
        resultBuilder.setFishRod(fishRodProtoMap.toProtoByEntity(fishRod));
        resultBuilder.addAllProduceItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void upgradeFishRod(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var upgradeFishRod = handleMediator.getRequest().getDataList(0).unpack(UpgradeFishRodProto.UpgradeFishRod.class);
        var player = handleMediator.getPlayer().get();
        var fishRod = fishRodService.findByEntityId(player, upgradeFishRod.getTargetEntityId()).get();
        var materials = fishRodService.findByEntityIds(player, upgradeFishRod.getMarteialsList());
        var coinCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.GameCoin.getId());
        var rodRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
        var upgradeRow = csvContainer.getCsv(UpgradeCsv.class).findByRareAndLv(rodRow.getRare(), fishRod.getLevel()).get();
        
        // action
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.FishRodController_upgradeFishRod, rodRow.getId()));
        // consume coin
        var consumeItemResult = new ArrayList<PlayerItemService.DiffItem>();
        consumeItemResult.add(playerItemService.consumeItem(player, coinCsvRow, upgradeRow.getUpgradeCost()));
        // remove materials and upgrade fish rod
        boolean success = fishRodService.upgradeFishRod(player, fishRod, materials);
        
        // response
        var resultBuilder = UpgradeFishRodResultProto.UpgradeFishRodResult.newBuilder();
        resultBuilder.setIsSuccess(success);
        resultBuilder.setFishRod(fishRodProtoMap.toProtoByEntity(fishRod));
        resultBuilder.addAllConsumeItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
        resultBuilder.addAllConsumeFishRods(fishRodProtoMap.toProtoByEntities(materials));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
