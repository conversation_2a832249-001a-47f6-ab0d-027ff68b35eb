<!-- Generated by SpecStory -->

# Generate description for cursor rule (2025-05-29 17:58:58)

_**User**_

@assets-structure.mdc @fishing-battle-scene.mdc @fishing-project.mdc 請生成description for cursor rule

---

_**Assistant**_



---

根据这三个 cursor rule 文件的内容，我为您生成以下描述：

## assets-structure
**描述：** Unity钓鱼游戏项目Assets目录结构指南，详细说明了游戏核心资源、第三方插件、环境素材和服务集成等各个目录的用途和功能。

## fishing-battle-scene
**描述：** 钓鱼战斗场景中的核心对象和组件功能说明，包括FishingManager状态机、钓鱼竿系统、鱼类行为、张力机制和UI管理器等关键游戏玩法元素。

## fishing-project
**描述：** Fishing项目的整体架构和技术栈文档，涵盖Zenject依赖注入、BestHTTP网络通信、XLua脚本集成、Naninovel故事系统等核心技术的使用方式和项目结构组织。

这些 cursor rule 为开发团队提供了完整的项目导航和架构理解，有助于快速定位代码和理解系统设计。

---

_**User**_

to english

---

_**Assistant**_



---

Based on the three cursor rule files, I'll generate the following descriptions in English:

## assets-structure
**Description:** Unity fishing game project Assets directory structure guide, detailing the purposes and functions of various directories including game core resources, third-party plugins, environment assets, and service integrations.

## fishing-battle-scene
**Description:** Core objects and component functionality in the fishing battle scene, including FishingManager state machine, fishing rod system, fish behavior, tension mechanics, and UI managers for key gameplay elements.

## fishing-project
**Description:** Overall architecture and technology stack documentation for the Fishing project, covering core technologies such as Zenject dependency injection, BestHTTP network communication, XLua script integration, Naninovel story system usage, and project structure organization.

These cursor rules provide the development team with complete project navigation and architectural understanding, helping to quickly locate code and understand system design.

---

_**User**_

請幫我製作工作流程的cursor rule /Generate Cursor Rules 

做介面功能: 在@UI 底下會新增腳本，腳本會繼承 @GamePanel.cs ，使用zenject依賴注入其他功能腳本，UI元件通常使用Unity UI做reference，會提供Refresh()方法做刷新介面功能，其他使用開啟介面功能開關會使用 @GamePanelManager.cs 和@FishingGamePanelManager.cs 

讀取一些unity資源會使用 @IResourceProvider.cs ，動畫使用 dotween或是 unity animator

讀取遊戲資料會在 @Tables ，這些腳本是由套件@QuickSheet 產生，然後存取遊戲設定資料在@TableContainer.cs 

網路功能會使用 @Best HTTP 協助，但是要新增業務邏輯網路會在@Network ，從遊戲server拉取的資料會在@Repository 

原則上遊戲模組功能使用@Zenject 來使用，不使用單例，定義那些模組的zenject installer會放在@Installers 

遊戲玩法核心邏輯基本都放在@Battle 

所以通常都是有兩部分
1.做UI，做功能會使用網路和伺服器傳輸資料互動，看是要做什麼功能；一般來說UI，玩家身上的某個物品資料可以關連到 @TableContainer.cs 讀取設定資料，而真正從資料庫拉取回來的資料是在 @Repository ，請server做好功能後回應更新
2.做遊戲核心玩法，遊戲狀態機(中間會和UI互動)，和遊戲伺服器傳輸通常在開始和結束
3

---

_**Assistant**_



---

我將為您創建一個詳細的工作流程 cursor rule，涵蓋UI功能開發和遊戲核心玩法的工作流程。

---

```diff
- 
+ # UI功能開發工作流程
+ 
+ 本規則描述了Fishing項目中UI功能開發的完整工作流程和架構模式。
+ 
+ ## UI腳本架構
+ 
+ ### 基礎繼承結構
+ - 所有UI腳本都位於 [Assets/Fishing/Scripts/UI](mdc:Assets/Fishing/Scripts/UI) 目錄下
+ - UI腳本必須繼承 [GamePanel.cs](mdc:Assets/Ehooray/Runtime/UI/GamePanel.cs)
+ - 使用 [Zenject](mdc:Assets/Plugins/Zenject) 進行依賴注入，避免使用單例模式
+ - UI元件通過Unity UI系統進行reference
+ - 必須提供 `Refresh()` 方法用於刷新介面功能
+ 
+ ### UI管理器
+ - 使用 [GamePanelManager.cs](mdc:Assets/Ehooray/Runtime/UI/GamePanelManager.cs) 進行基礎UI管理
+ - 使用 [FishingGamePanelManager.cs](mdc:Assets/Fishing/Scripts/General/FishingGamePanelManager.cs) 進行遊戲專用UI管理
+ - 提供UI開啟、關閉、切換等功能
+ 
+ ## 資源載入系統
+ 
+ ### 資源提供者
+ - 使用 [IResourceProvider.cs](mdc:Assets/Ehooray/Runtime/File/IResourceProvider.cs) 載入Unity資源
+ - 支援異步載入機制
+ - 提供資源快取和管理功能
+ 
+ ### 動畫系統
+ - 使用 [DOTween](mdc:Assets/DOTween) 進行程式化動畫
+ - 使用Unity Animator進行複雜動畫狀態機
+ - 優先使用DOTween處理UI動畫效果
+ 
+ ## 數據管理架構
+ 
+ ### 遊戲配置數據
+ - 遊戲數據表位於 [Assets/Fishing/Scripts/Tables](mdc:Assets/Fishing/Scripts/Tables) 目錄
+ - 使用 [QuickSheet](mdc:Assets/QuickSheet) 套件自動生成數據表腳本
+ - 通過 [TableContainer.cs](mdc:Assets/Fishing/Scripts/General/TableContainer.cs) 統一存取遊戲設定資料
+ 
+ ### 伺服器數據
+ - 伺服器拉取的數據存放在 [Assets/Fishing/Scripts/Repository](mdc:Assets/Fishing/Scripts/Repository) 目錄
+ - 提供本地緩存和數據同步功能
+ - 支援數據變更通知機制
+ 
+ ## 網路通信系統
+ 
+ ### 網路基礎設施
+ - 使用 [Best HTTP](mdc:Assets/Best HTTP) 作為網路通信基礎
+ - 業務邏輯網路代碼位於 [Assets/Fishing/Scripts/Network](mdc:Assets/Fishing/Scripts/Network) 目錄
+ - 支援HTTP請求和WebSocket通信
+ 
+ ### 網路服務層
+ - 網路服務實現在 [Network/Service](mdc:Assets/Fishing/Scripts/Network/Service) 目錄
+ - 提供統一的API接口封裝
+ - 處理網路錯誤和重試機制
+ 
+ ## 依賴注入架構
+ 
+ ### Zenject配置
+ - 遊戲模組使用 [Zenject](mdc:Assets/Plugins/Zenject) 進行依賴注入
+ - 避免使用單例模式，提高代碼可測試性
+ - Zenject安裝器位於 [Assets/Fishing/Scripts/Installers](mdc:Assets/Fishing/Scripts/Installers) 目錄
+ 
+ ### 安裝器類型
+ - [FishingProjectInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingProjectInstaller.cs): 項目級別服務註冊
+ - [FishingLobbyInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingLobbyInstaller.cs): 場景級別組件註冊
+ 
+ ## 開發流程
+ 
+ ### UI功能開發步驟
+ 1. **創建UI腳本**: 繼承GamePanel，實現基礎UI框架
+ 2. **依賴注入設計**: 註入需要的服務和管理器
+ 3. **數據綁定**: 連接TableContainer配置數據和Repository伺服器數據
+ 4. **網路交互**: 實現與伺服器的數據傳輸邏輯
+ 5. **UI刷新機制**: 實現Refresh()方法處理數據更新
+ 6. **動畫效果**: 使用DOTween或Animator添加UI動畫
+ 
+ ### 數據流向
+ ```
+ TableContainer (配置) ← QuickSheet生成
+      ↓
+    UI腳本 ← GamePanel繼承
+      ↓
+ Repository (伺服器數據) ← Network服務
+      ↓
+ GamePanelManager (UI管理)
+ 
```

---

現在我創建第二個cursor rule來描述遊戲核心玩法的工作流程：

---

```diff
- 
+ # 遊戲核心玩法開發工作流程
+ 
+ 本規則描述了Fishing項目中遊戲核心玩法的開發工作流程和架構模式。
+ 
+ ## 核心玩法架構
+ 
+ ### 戰鬥系統核心
+ - 遊戲核心玩法邏輯位於 [Assets/Fishing/Scripts/Battle](mdc:Assets/Fishing/Scripts/Battle) 目錄
+ - 主要控制器: [FishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/FishingManager.cs) - 管理釣魚遊戲狀態機
+ - 新版張力系統: [NewFishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/NewFishingManager.cs) - 處理新釣魚玩法
+ - 魚類行為系統: [Fish.cs](mdc:Assets/Fishing/Scripts/Battle/Fish.cs) - 控制魚的AI和挣扎行為
+ 
+ ### 狀態機設計
+ 釣魚系統包含以下狀態流程:
+ 1. **Prepare**: 準備狀態
+ 2. **BeforeStart**: 開始前準備
+ 3. **Start**: 抛竿開始
+ 4. **Wait**: 等待魚上鉤
+ 5. **Fight**: 與魚搏鬥
+ 6. **Catch**: 成功捕獲
+ 7. **Fail**: 釣魚失敗
+ 8. **Win**: 釣魚成功
+ 
+ ## 遊戲組件系統
+ 
+ ### 釣魚設備組件
+ - **釣魚竿系統**: [FishRod.cs](mdc:Assets/Fishing/Scripts/Battle/FishRod.cs) - 控制釣魚竿物理和動畫
+ - **釣魚線系統**: [FishLine.cs](mdc:Assets/Fishing/Scripts/Battle/FishLine.cs) - 管理釣魚線的物理表現
+ - **魚餌系統**: [BaitLine.cs](mdc:Assets/Fishing/Scripts/Battle/BaitLine.cs) - 處理魚餌的行為
+ - **力度指示器**: [FishRodForceItem.cs](mdc:Assets/Fishing/Scripts/Battle/FishRodForceItem.cs) - 顯示釣魚力度
+ 
+ ### 輸入控制系統
+ - **手勢檢測**: [ThrowGestureDetector.cs](mdc:Assets/Fishing/Scripts/Battle/ThrowGestureDetector.cs) - 檢測抛竿手勢
+ - **兩點抛竿**: [ThrowBaitTwoPoint.cs](mdc:Assets/Fishing/Scripts/Battle/ThrowBaitTwoPoint.cs) - 處理雙點觸控抛竿
+ 
+ ## 戰略模式系統
+ 
+ ### 釣魚策略架構
+ - 策略基類: [FishingStrategyBase.cs](mdc:Assets/Fishing/Scripts/Battle/FishingStrategyBase.cs)
+ - 策略介面: [IFishingStrategy.cs](mdc:Assets/Fishing/Scripts/Battle/IFishingStrategy.cs)
+ - 舊版策略: [Assets/Fishing/Scripts/Battle/Strategies](mdc:Assets/Fishing/Scripts/Battle/Strategies) 目錄
+ - 新版策略: [Assets/Fishing/Scripts/Battle/NewStrategies](mdc:Assets/Fishing/Scripts/Battle/NewStrategies) 目錄
+ 
+ ### 策略依賴注入
+ - 策略安裝器: [FishingStrategyInstaller.cs](mdc:Assets/Fishing/Scripts/Battle/FishingStrategyInstaller.cs)
+ - 新策略安裝器: [NewFishingStrategyInstaller.cs](mdc:Assets/Fishing/Scripts/Battle/NewFishingStrategyInstaller.cs)
+ 
+ ## 數據管理
+ 
+ ### 戰鬥數據結構
+ - **魚類數據**: [FishData.cs](mdc:Assets/Fishing/Scripts/Battle/FishData.cs) - 定義魚的屬性
+ - **裝備數據**: [BattleEquipeData.cs](mdc:Assets/Fishing/Scripts/Battle/BattleEquipeData.cs) - 戰鬥裝備配置
+ - **單例容器**: [FishingSingleton.cs](mdc:Assets/Fishing/Scripts/Battle/FishingSingleton.cs) - 戰鬥場景單例管理
+ 
+ ### 核心組件
+ - **釣魚竿節點**: [FishRodPoint.cs](mdc:Assets/Fishing/Scripts/Battle/FishRodPoint.cs) - 釣魚竿控制點
+ - **釣魚線虛擬**: [FishLineDummy.cs](mdc:Assets/Fishing/Scripts/Battle/FishLineDummy.cs) - 釣魚線輔助組件
+ 
+ ## 與UI系統交互
+ 
+ ### UI-戰鬥交互模式
+ 1. **狀態通知**: 戰鬥系統通過Signal通知UI狀態變化
+ 2. **數據同步**: UI監聽戰鬥數據變化並實時更新顯示
+ 3. **用戶輸入**: UI接收用戶操作並傳遞給戰鬥系統
+ 4. **視覺反饋**: 戰鬥系統觸發UI動畫和特效
+ 
+ ### 張力系統UI
+ - 張力管理器位於UI系統中，與戰鬥系統緊密配合
+ - 提供實時張力值顯示和警告功能
+ - 支援挣扎係數和張力閾值配置
+ 
+ ## 網路通信模式
+ 
+ ### 戰鬥網路流程
+ 1. **戰鬥開始**: 向伺服器發送開始釣魚請求
+ 2. **狀態同步**: 關鍵狀態變化時與伺服器同步
+ 3. **結果提交**: 戰鬥結束時提交結果到伺服器
+ 4. **獎勵獲取**: 接收伺服器返回的獎勵數據
+ 
+ ### 數據持久化
+ - 戰鬥結果存儲到Repository
+ - 玩家進度和統計數據更新
+ - 成就和任務進度檢查
+ 
+ ## 開發流程
+ 
+ ### 核心玩法開發步驟
+ 1. **狀態機設計**: 定義遊戲流程和狀態轉換
+ 2. **組件實現**: 開發各個遊戲組件的邏輯
+ 3. **策略模式**: 實現不同的遊戲策略和玩法變化
+ 4. **UI集成**: 與UI系統建立通信和數據綁定
+ 5. **網路集成**: 實現與伺服器的數據交換
+ 6. **測試優化**: 進行玩法測試和性能優化
+ 
+ ### 架構原則
+ - 使用Zenject進行依賴注入，避免硬編碼依賴
+ - 狀態機模式管理複雜的遊戲流程
+ - 策略模式支援不同玩法變化
+ - Signal系統實現組件間解耦通信
+ - 數據驅動設計，通過配置表控制遊戲行為
+ 
```

---

我已經成功為您創建了兩個詳細的 cursor rules：

## 1. UI功能開發工作流程 (`ui-workflow.mdc`)
這個規則涵蓋了：
- **UI腳本架構**: GamePanel繼承、Zenject依賴注入、UI管理器使用
- **資源載入系統**: IResourceProvider使用、DOTween動畫
- **數據管理**: TableContainer配置數據、Repository伺服器數據
- **網路通信**: Best HTTP基礎、Network業務邏輯
- **開發流程**: 完整的UI開發步驟和數據流向

## 2. 遊戲核心玩法開發工作流程 (`battle-workflow.mdc`)
這個規則涵蓋了：
- **核心玩法架構**: FishingManager狀態機、戰鬥系統組件
- **狀態機設計**: 釣魚遊戲的完整狀態流程
- **戰略模式系統**: 策略模式實現和依賴注入
- **UI-戰鬥交互**: Signal通信、張力系統
- **網路通信模式**: 戰鬥流程中的伺服器交互
- **開發流程**: 核心玩法開發的架構原則

這兩個 cursor rules 將幫助開發團隊：
1. 快速理解項目的工作流程和架構模式
2. 遵循統一的開發規範和最佳實踐
3. 明確各個系統之間的交互關係
4. 提高代碼的一致性和可維護性

您現在可以在開發過程中引用這些規則，它們將指導AI助手更好地理解您的項目結構和開發需求。

---

_**User**_

請為這兩個rule填寫description

---

_**Assistant**_



---

我來為這兩個 cursor rules 填寫描述：

## ui-workflow
**Description:** UI development workflow guide for Fishing project, covering GamePanel inheritance, Zenject dependency injection, resource loading with IResourceProvider, data management through TableContainer and Repository, network communication via Best HTTP, and complete UI development process including DOTween animations and UI manager usage.

## battle-workflow  
**Description:** Core gameplay development workflow for Fishing project, detailing battle system architecture with FishingManager state machine, fishing components (rod, line, fish AI), strategy pattern implementation, UI-battle interaction through Signal system, network communication flow, and comprehensive development process for fishing game mechanics.

這些描述簡潔地概括了每個 cursor rule 的核心內容和用途，幫助開發者快速理解這些工作流程指南的適用場景。

---

