using Cysharp.Threading.Tasks;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚勝利狀態策略
    /// </summary>
    public class WinStrategy : FishingStrategyBase
    {
        public WinStrategy() : base(FishingState.Win) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToWin方法中的初始化邏輯
            var WinFishResult = await context.fishingContainer.networkService.battleService.WinFish();
            await context.fishingContainer.networkService.questService.GetAchievements();

            context.fish.continueTimes = 0;
            context.fish.SetFishData();
            context.fish.SetFishToShowPoint();
            await context.fishingGamePanelManager.OpenAsync<GameEndManager>();

            context.gameEndManager.SetFishInfoText(WinFishResult);
            context.fish.line.gameObject.SetActive(true);
            context.mainCamera.transform.forward = context.fish.showTarget.transform.position + new Vector3(0.15f, 0, 0) - context.mainCamera.transform.position;
            context.SetBlankScreen(false);
            context.fishData.UpdataFishData(WinFishResult.NextFishCsvId);
            context.lineTensionManager.fishingSkillBarManager.ResetSkillBar();
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoWin方法中的邏輯，目前為空
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 