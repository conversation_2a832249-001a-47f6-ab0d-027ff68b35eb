---
description: Core objects and component functionality in the fishing battle scene, including FishingManager state machine, fishing rod system, fish behavior, tension mechanics, and UI managers for key gameplay elements.
globs: 
alwaysApply: false
---
# 钓鱼场景对象功能说明

本规则文件描述了钓鱼战斗场景(Battle Scene)中的主要对象及其功能。

## 核心管理器

- **FishingManager**: 钓鱼系统的主要控制器，位于[FishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/FishingManager.cs)，负责管理整个钓鱼游戏流程，包含以下状态:
  - Prepare: 准备状态
  - BeforeStart: 开始前准备
  - Start: 抛竿开始
  - Wait: 等待鱼上钩
  - Fight: 与鱼搏斗
  - Catch: 成功捕获
  - Fail: 钓鱼失败
  - Win: 钓鱼成功

- **NewFishingManager**: 釣魚張力新玩法的主要控制器，參考[FishingManager.cs](mdc:Assets/Fishing/Scripts/Battle/FishingManager.cs)
- **BattleLevelManager**: 战斗关卡管理器，控制战斗场景的整体流程

## 钓鱼系统组件

- **FishRod**: 钓鱼竿对象，包含以下子对象:
  - Rod: 钓鱼竿模型
  - RodAnimate: 钓鱼竿动画控制器
  - TopPoint: 钓鱼竿顶点，用于连接钓鱼线
  - BaitObj: 鱼饵对象挂载点
  - 节点系统(node_0到node_6): 控制钓鱼竿的弯曲形态

- **FishLine**: 钓鱼线对象，通过多个控制点(Points)构成弯曲的钓鱼线，连接钓鱼竿和鱼饵
  - Point_1到Point_5: 构成钓鱼线的控制点

- **Fish**: 鱼对象，负责鱼的行为控制，详细实现在[Fish.cs](mdc:Assets/Fishing/Scripts/Battle/Fish.cs)，包含:
  - 鱼的挣扎状态: 大挣扎(Big)、中挣扎(Mid)、小挣扎(Small)和晕眩(Stun)
  - 生命值和距离控制
  - 移动点控制
  - 水面特效

- **FishRodForceItem**: 钓鱼力度指示器，显示钓鱼时的力度和水面效果
  - WaterRipples_Stationairy: 静止水波纹效果
  - WaterSplash_Ring: 水花环效果
  - WaterSplash_Collision: 水面碰撞效果

- **Bait**: 鱼饵对象
  - BaitPath: 鱼饵路径控制
  - MiddlePoint: 鱼饵中间控制点

- **CatchPoint**: 捕获点，确定鱼的捕获位置

## 环境对象

- **WaterPlane**: 水面平面，使用StylizedWater2插件渲染水面效果
- **Main Camera**: 主摄像机，用于游戏主视角
- **UICamera**: UI专用摄像机
- **Directional Light**: 场景主光源

## UI系统

- **WatchFishCanvas**: 观察鱼的UI画布
- **BlankScreenCanvas**: 空白屏幕过渡效果
- **EventSystem**: Unity事件系统，处理输入事件

## 其他管理器

- **SoundManager**: 声音管理器
  - Sound: 背景音乐管理
  - Sfx: 音效管理

- **WatchFishCamera**: 观察鱼的专用摄像机，用于鱼的特写展示

## 张力系统

场景中的[LineTensionManager](mdc:Assets/Fishing/Scripts/UI/Battle/LineTensionBar/LineTensionManager.cs)组件控制钓鱼线张力，是钓鱼玩法的核心机制:
- 控制钓鱼线的张力值
- 挣扎系数倍率
- 张力增减速率
- 警告阈值

[NewLineTensionManager.cs](mdc:Assets/Fishing/Scripts/UI/Battle/LineTensionBar/NewLineTensionManager.cs)組件是釣魚玩法核心機制，[LineTensionManager.cs](mdc:Assets/Fishing/Scripts/UI/Battle/LineTensionBar/LineTensionManager.cs)即將棄用

