using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Cysharp.Threading.Tasks;
using Zenject;
using Ehooray.UI;
using Ehooray;
using Fishing.Network;
using Fishing.Network.Proto;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/Battle/ThrowBaitCanvas")]
    public class ThrowBaitManager : GamePanel
    {
        enum HitArea
        {
            Gray = 1,
            Light = 2,
            Dark = 3
        }
        public Button pullButton;

        [SerializeField]
        Image hitBarImage;
        [SerializeField]
        Image backgroundImage;
        [SerializeField]
        Image SpotImage;

        [SerializeField]
        Vector2 rightEndPoint;
        [SerializeField]
        Vector2 leftEndPoint;
        [SerializeField]
        Vector2 spotPoint;

        [SerializeField]
        int runWay = 1;
        [SerializeField]
        int speed = 1;

        [SerializeField]
        float keepSpace = 50;

        HitArea fishSizeThrowBaitBonus;

        CancellationTokenSource cancellationTokenSource;

        #region Zenject Inejct
        FishingManager fishingManager;
        TableContainer tableContainer;
        ServiceContainer serviceContainer;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
            TableContainer tableContainer,
            ServiceContainer serviceContainer
            )
        {
            this.fishingManager = fishingManager;
            this.tableContainer = tableContainer;
            this.serviceContainer = serviceContainer;
        }

        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }

        public void CloseRoot()
        {
            if (cancellationTokenSource != null)
                cancellationTokenSource.Cancel();
            //cancellationTokenSource.Dispose();
        }

        public void OnPullButtonPressed(PointerEventData data)
        {
            //cancellationTokenSource.Cancel();
            UniTask.Void(async () =>
            {

                SetFishSizeThrowBaitBonus();
                Debug.Log("hitbar.x = " + hitBarImage.rectTransform.localPosition.x);
                var startFish = new StartFish(){ HitArea = (int)fishSizeThrowBaitBonus};
                await serviceContainer.battleService.StartFish(startFish);
                //回報拋竿
                Debug.Log("throw the bait");
                fishingManager.TransitionToState(FishingState.Start).Forget();
                fishingManager.cordReelManager.CloseTapText();
                fishingManager.cordReelManager.StopInteractive();
                fishingManager.cordReelManager.SetCordReelButtonTranslucent();
                fishingManager.cordReelManager.wheelRotate.SetThrowBaitSpeed();
            });


        }

        public async void Play()
        {
            //Debug.Log("throw bait play");
            fishingManager.cordReelManager.OpenTapText();
            RandomSpotPosition();

            float xLength = backgroundImage.rectTransform.rect.width / 2;
            rightEndPoint = new Vector2(backgroundImage.rectTransform.localPosition.x + xLength, backgroundImage.rectTransform.localPosition.y);
            leftEndPoint = new Vector2(backgroundImage.rectTransform.localPosition.x - xLength, backgroundImage.rectTransform.localPosition.y);

            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Dispose();
            }
            cancellationTokenSource = new CancellationTokenSource();

            await AsyncHitBarMove(cancellationTokenSource.Token);

        }

        async UniTask AsyncHitBarMove(CancellationToken cancelToken)
        {
            try
            {
                while (!cancellationTokenSource.IsCancellationRequested)
                {
                    DoHitBarMove();
                    await UniTask.Delay(16, delayTiming: PlayerLoopTiming.FixedUpdate);
                }

            }
            catch
            {

            }

        }

        void DoHitBarMove()
        {
            float moveDelta = speed * Time.fixedDeltaTime * runWay;
            hitBarImage.rectTransform.localPosition = new Vector3(hitBarImage.rectTransform.localPosition.x + moveDelta, hitBarImage.rectTransform.localPosition.y, 0);
            float barX = hitBarImage.rectTransform.localPosition.x;

            if (barX > rightEndPoint.x)
            {
                runWay *= -1;
                hitBarImage.rectTransform.localPosition = new Vector3(rightEndPoint.x, rightEndPoint.y, 0);
            }
            else if (barX < leftEndPoint.x)
            {
                runWay *= -1;
                hitBarImage.rectTransform.localPosition = new Vector3(leftEndPoint.x, leftEndPoint.y, 0);
            }
        }

        void RandomSpotPosition()
        {
            float backgroundWidth = backgroundImage.rectTransform.rect.width;
            float spotWidth = SpotImage.rectTransform.rect.width;
            float randomSpace = (backgroundWidth - spotWidth) / 2 - keepSpace;
            float randomDistance = Random.Range(-randomSpace, randomSpace);
            SpotImage.rectTransform.localPosition = backgroundImage.rectTransform.localPosition + new Vector3(randomDistance, 0, 0);
        }

        void SetFishSizeThrowBaitBonus()
        {
            float spotWidth = SpotImage.rectTransform.rect.width;
            float hitBarWidth = hitBarImage.rectTransform.rect.width;

            float hitBarDistance = Mathf.Abs(SpotImage.rectTransform.localPosition.x - hitBarImage.rectTransform.localPosition.x);
            float greatDistance = Mathf.Abs(hitBarWidth) / 2;
            float goodDistance = Mathf.Abs(spotWidth - hitBarWidth) / 2;

            if (hitBarDistance < greatDistance)
            {
                fishSizeThrowBaitBonus = HitArea.Dark;
            }
            else if (hitBarDistance < goodDistance)
            {
                fishSizeThrowBaitBonus = HitArea.Light;
            }
            else
            {
                fishSizeThrowBaitBonus = HitArea.Gray;
            }
        }

        public void SetFishData(FishTableData data)
        {
            speed = data.Speed;
        }
    }
}