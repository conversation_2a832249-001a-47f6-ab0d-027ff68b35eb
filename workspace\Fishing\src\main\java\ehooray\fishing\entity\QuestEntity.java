package ehooray.fishing.entity;

import java.time.LocalDateTime;
import java.util.List;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table
(
    name = "quest"
)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class QuestEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected boolean isAward;
    protected LocalDateTime startTime;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "quest", fetch = FetchType.LAZY)
    protected List<QuestTargetEntity> targets;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    protected PlayerEntity player;
    
    public void setId(long id)
    {
        this.id = id;
    }
    public long getId()
    {
        return id;
    }
    
    public int getCsvId()
    {
        return csvId;
    }
    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }
    
    public boolean getIsAward()
    {
        return isAward;
    }
    
    public void setIsAward(boolean isAward)
    {
        this.isAward = isAward;
    }
    
    public LocalDateTime getStartTime()
    {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime)
    {
        this.startTime = startTime;
    }
    
    public List<QuestTargetEntity> getTargets()
    {
        return targets;
    }

    public void setTargets(List<QuestTargetEntity> targets)
    {
        this.targets = targets;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
}
