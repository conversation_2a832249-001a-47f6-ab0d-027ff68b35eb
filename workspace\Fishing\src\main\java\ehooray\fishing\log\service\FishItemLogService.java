package ehooray.fishing.log.service;

import java.time.ZoneId;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.ProtoHandleMediatorContainer;
import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.entity.FishItemLogEntity;
import ehooray.fishing.log.repository.FishItemLogRepository;
import ehooray.fishing.pojo.event.FishItemEvent;
import ehooray.fishing.pojo.event.SellFishEvent;

@Service
public class FishItemLogService
{
    @Autowired
    protected FishItemLogRepository fishItemLogRepository;
    
    @Autowired
    protected ProtoHandleMediatorContainer protoHandleMediatorContainer;
    
    @EventListener
    public void createLog(FishItemEvent fishItemEvent)
    {
        createLog(fishItemEvent.getPlayer(), fishItemEvent.getFish());
    }
    
    @EventListener
    public void createLog(SellFishEvent sellFishEvent)
    {
        createLog(sellFishEvent.getPlayer(), sellFishEvent.getFishItem());
    }
    
    protected FishItemLogEntity createLog(PlayerEntity player, FishItemEntity fishItem)
    {
        var mediator = protoHandleMediatorContainer.getMediator();
        
        var log = new FishItemLogEntity();
        log.setCsvId(fishItem.getCsvId());
        log.setFromSystem(mediator.getLogSource().getBehaviour().getSystemId());
        log.setBehaviour(mediator.getLogSource().getBehaviour().getId());
        log.setPlayerId(player.getId());
        for(int i = 0, iMax = mediator.getLogSource().getBehaviourInputs().length; i < iMax; i++)
            log.setBehaviourInput(i, mediator.getLogSource().getBehaviourInputs()[i]);
        var now = mediator.getRequestStartTime();
        log.setCreateDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        
        fishItemLogRepository.save(log);
        
        return log;
    }
}
