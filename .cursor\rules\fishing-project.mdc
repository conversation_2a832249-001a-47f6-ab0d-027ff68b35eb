---
description: Overall architecture and technology stack documentation for the Fishing project, covering core technologies such as Zenject dependency injection, BestHTTP network communication, XLua script integration, Naninovel story system usage, and project structure organization.
globs: 
alwaysApply: false
---
# Fishing项目架构与技术栈

## 项目概述
Fishing项目是一个基于Unity开发的钓鱼游戏，使用了多种现代化技术和框架来构建游戏核心功能。项目的主要代码和资源位于[Assets/Fishing](mdc:Assets/Fishing)目录下。

## 核心技术栈

### 依赖注入
- **Zenject**: 项目使用Zenject框架进行依赖注入和控制反转(IoC)，简化了组件间的耦合。
  - 所有服务和管理器通过[FishingProjectInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingProjectInstaller.cs)进行注册
  - 场景级别的组件通过[FishingLobbyInstaller.cs](mdc:Assets/Fishing/Scripts/Installers/FishingLobbyInstaller.cs)进行注册

### 网络通信
- **BestHTTP**: 项目使用BestHTTP包来处理HTTP请求和WebSocket通信
  - HTTP请求实现: [HttpMethod.cs](mdc:Assets/Ehooray/Runtime/Network/Method/HttpMethod.cs)
  - WebSocket实现: [WebSocketMethod.cs](mdc:Assets/Ehooray/Runtime/Network/Method/WebSocketMethod.cs)
  - 网络管理器: [NetworkManager.cs](mdc:Assets/Ehooray/Runtime/Network/NetworkManager.cs)
  - 网络节点: [NetworkNode.cs](mdc:Assets/Ehooray/Runtime/Network/NetworkNode.cs)

### 数据处理
- 使用Protocol Buffers (Protobuf)作为数据序列化格式
- 数据存储在[Repository](mdc:Assets/Fishing/Scripts/Repository)目录下的仓库类中
- 服务层位于[Network/Service](mdc:Assets/Fishing/Scripts/Network/Service)目录

### 脚本和故事系统
- **XLua**: 使用XLua框架集成Lua脚本
  - Lua机器: [LuaMachine.cs](mdc:Assets/Fishing/Scripts/Lua/LuaMachine.cs)
  - Lua组件: [LuaComponent.cs](mdc:Assets/Fishing/Scripts/Lua/LuaComponent.cs)
  - Lua缓存: [LuaCacheContainer.cs](mdc:Assets/Fishing/Scripts/Lua/LuaCacheContainer.cs)

- **Naninovel**: 用于游戏内的视觉小说和故事系统
  - 自定义命令在[NaniCommands](mdc:Assets/Fishing/Scripts/NaniCommands)目录下
  - 故事结束事件: [StoryEndEvent.cs](mdc:Assets/Fishing/Scripts/NaniCommands/StoryEndEvent.cs)

### 游戏框架
- 使用Signal系统进行事件通信
- 使用**UniTask**进行异步操作
- 集成**Firebase**服务进行身份验证
- 集成**Parse**后端系统(Firebase內部)

### 项目结构
- **Scripts**: 包含所有游戏逻辑代码
  - **Installers**: Zenject依赖注入配置
  - **Network**: 网络通信相关代码
  - **Repository**: 数据仓库
  - **System**: 核心系统实现
  - **UI**: 用户界面组件
  - **Fish**: 钓鱼核心玩法
  - **Battle**: 战斗系统
  - **Lua**: Lua脚本集成
  - **NaniCommands**: Naninovel命令扩展

### 启动流程
游戏主要通过以下Bootstrap类进行初始化:
- [LobbyBootstrap.cs](mdc:Assets/Fishing/Scripts/LobbyBootstrap.cs): 负责大厅场景初始化
- [FishingGameBootstrap.cs](mdc:Assets/Fishing/Scripts/FishingGameBootstrap.cs): 负责游戏核心玩法初始化


