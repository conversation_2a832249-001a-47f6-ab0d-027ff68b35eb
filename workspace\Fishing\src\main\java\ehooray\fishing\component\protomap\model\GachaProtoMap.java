package ehooray.fishing.component.protomap.model;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.GachaProto;
import ehooray.fishing.entity.GachaEntity;

@Component
public class GachaProtoMap
{
    public GachaProto.Gacha.Builder toBuilderByEntity(GachaEntity gacha)
    {
        GachaProto.Gacha.Builder builder = GachaProto.Gacha.newBuilder();
        builder.setEntityId(gacha.getId());
        builder.setCsvId(gacha.getCsvId());
        builder.setBuyCount(gacha.getBuyCount());
        if(gacha.getFreeBuyTime().isPresent())
            builder.setFreeBuyTime(gacha.getFreeBuyTime().get().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        return builder;
    }

    public Any toAnyByEntity(GachaEntity gacha)
    {
        return Any.pack(toBuilderByEntity(gacha).build());
    }

    public ArrayList<Any> toAnyByEntities(List<GachaEntity> gachas)
    {
        ArrayList<Any> gachaProtos = new ArrayList<>(gachas.size());
        for(GachaEntity gacha : gachas)
            gachaProtos.add(toAnyByEntity(gacha));

        return gachaProtos;
    }
}
