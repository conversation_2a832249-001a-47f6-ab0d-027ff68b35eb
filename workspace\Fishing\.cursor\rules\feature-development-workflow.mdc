---
description: Step-by-step guide for developing new features in the fishing game server, detailing the layered architecture approach from <PERSON><PERSON><PERSON> to Controller, including code examples, naming conventions, and development best practices for maintaining code quality and consistency.
globs: 
alwaysApply: false
---
# 新功能开发流程指南

## 开发流程概述

在钓鱼游戏服务器中新增功能时，需要按照以下分层架构进行开发，确保代码组织清晰且维护性良好。

## 标准开发步骤

### 1. 数据层设计 (@Entity)
**位置**: `src/main/java/ehooray/fishing/entity/`
- 创建数据库实体类，定义表结构
- 使用 JPA 注解进行字段映射
- 建立实体间的关联关系
- **参考示例**: [PlayerEntity.java](mdc:src/main/java/ehooray/fishing/entity/PlayerEntity.java), [QuestEntity.java](mdc:src/main/java/ehooray/fishing/entity/QuestEntity.java)

### 2. 数据访问层 (@Repository)
**位置**: `src/main/java/ehooray/fishing/repository/`
- 继承 JpaRepository 接口
- 定义自定义查询方法
- 处理数据库 CRUD 操作
- **参考示例**: [PlayerRepository.java](mdc:src/main/java/ehooray/fishing/repository/PlayerRepository.java), [QuestRepository.java](mdc:src/main/java/ehooray/fishing/repository/QuestRepository.java)

### 3. 协议定义 (@Proto)
**位置**: `src/main/java/ehooray/fishing/dto/proto/`
- 定义 Protobuf 消息格式
- 区分请求和响应消息
- 编译生成 Java 类作为传输载体
- **目录结构**:
  - `request/` - 客户端请求消息
  - `response/` - 服务器响应消息
  - `model/` - 通用数据模型
- **参考**: [BaseProto.java](mdc:src/main/java/ehooray/fishing/dto/proto/BaseProto.java)

### 4. 业务逻辑层 (@Service)
**位置**: `src/main/java/ehooray/fishing/service/{模块名}/`
- 实现核心业务逻辑
- 与数据库进行交互
- 处理数据转换和验证
- 管理事务边界
- **职责**: 
  - 数据库操作调用
  - 业务规则验证
  - 数据转换处理
  - 复杂逻辑计算

### 5. 控制器层 (@Controller)
**位置**: `src/main/java/ehooray/fishing/controller/{模块名}/`
- 创建 ProtoHandler 处理 WebSocket 消息
- 包含一些业务逻辑处理
- 调用 Service 层方法
- 处理协议消息的序列化/反序列化
- **命名规范**: `{功能名}ProtoHandler.java`
- **参考**: [AuthProtoHandler.java](mdc:src/main/java/ehooray/fishing/controller/AuthProtoHandler.java)

### 6. 工具组件 (@Component)
**位置**: `src/main/java/ehooray/fishing/component/`
- 创建可复用的工具类和组件
- 实现跨模块的通用功能
- 提供额外的辅助功能
- **包含子目录**:
  - `utils/` - 工具类
  - `trigger/` - 触发器组件
  - `schedule/` - 定时任务
  - `auth/` - 认证组件
  - `logger/` - 日志组件
  - `component/` - 基础组件
- **参考**: [WebSocketHandler.java](mdc:src/main/java/ehooray/fishing/component/WebSocketHandler.java)

## 开发示例：新增排行榜功能

### 步骤 1: 创建实体
```java
// src/main/java/ehooray/fishing/entity/RankingEntity.java
@Entity
@Table(name = "ranking")
public class RankingEntity extends AEntity {
    @Column(name = "player_id")
    private Long playerId;
    
    @Column(name = "score")
    private Integer score;
    
    @Column(name = "rank_type")
    private String rankType;
    
    // getters, setters...
}
```

### 步骤 2: 创建 Repository
```java
// src/main/java/ehooray/fishing/repository/RankingRepository.java
public interface RankingRepository extends JpaRepository<RankingEntity, Long> {
    List<RankingEntity> findByRankTypeOrderByScoreDesc(String rankType);
    Optional<RankingEntity> findByPlayerIdAndRankType(Long playerId, String rankType);
}
```

### 步骤 3: 定义 Proto 消息
```proto
// ranking.proto
message GetRankingRequest {
    string rank_type = 1;
    int32 limit = 2;
}

message GetRankingResponse {
    repeated RankingData rankings = 1;
}

message RankingData {
    int64 player_id = 1;
    string player_name = 2;
    int32 score = 3;
    int32 rank = 4;
}
```

### 步骤 4: 实现 Service
```java
// src/main/java/ehooray/fishing/service/ranking/RankingService.java
@Service
@Transactional
public class RankingService {
    
    @Autowired
    private RankingRepository rankingRepository;
    
    @Autowired
    private PlayerRepository playerRepository;
    
    public List<RankingEntity> getRankingByType(String rankType, int limit) {
        // 与数据库交互的业务逻辑
        return rankingRepository.findByRankTypeOrderByScoreDesc(rankType)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    public void updatePlayerScore(Long playerId, String rankType, Integer score) {
        // 更新玩家排行榜分数
    }
}
```

### 步骤 5: 创建 Controller
```java
// src/main/java/ehooray/fishing/controller/ranking/RankingProtoHandler.java
@Component
public class RankingProtoHandler {
    
    @Autowired
    private RankingService rankingService;
    
    @ProtoMethod
    public GetRankingResponse getRanking(GetRankingRequest request) {
        // 处理 WebSocket 消息和业务逻辑
        List<RankingEntity> rankings = rankingService.getRankingByType(
            request.getRankType(), request.getLimit());
        
        // 转换为响应消息
        return buildResponse(rankings);
    }
}
```

### 步骤 6: 添加工具组件（如需要）
```java
// src/main/java/ehooray/fishing/component/ranking/RankingCalculator.java
@Component
public class RankingCalculator {
    
    public Integer calculateFishingScore(PlayerEntity player) {
        // 排行榜分数计算逻辑
        return score;
    }
}
```

## 开发注意事项

### 层级职责分离
- **Controller**: 协议处理 + 部分业务逻辑
- **Service**: 核心业务逻辑 + 数据库交互
- **Repository**: 纯数据访问层
- **Entity**: 数据表映射
- **Component**: 工具类和可复用组件

### 命名约定
- Entity: `{名称}Entity.java`
- Repository: `{名称}Repository.java`
- Service: `{名称}Service.java`
- ProtoHandler: `{名称}ProtoHandler.java`
- Component: `{功能}Component.java`

### 依赖注入
- 使用 `@Autowired` 或构造函数注入
- Service 注入 Repository
- Controller 注入 Service
- 避免循环依赖

### 事务管理
- 在 Service 层使用 `@Transactional`
- 读操作使用 `readOnly = true`
- 复杂操作确保事务一致性

### WebSocket 消息处理
- 使用 `@ProtoMethod` 注解标记处理方法
- 参考现有的 ProtoHandler 实现方式
- 确保消息的序列化/反序列化正确

### 数据库设计原则
- 继承 [AEntity](mdc:src/main/java/ehooray/fishing/entity/AEntity.java) 基类获得通用字段
- 合理设计索引提升查询性能
- 考虑数据迁移和版本兼容性

## 常用工具组件

### 已有组件参考
- **WebSocket处理**: [WebSocketHandler.java](mdc:src/main/java/ehooray/fishing/component/WebSocketHandler.java)
- **协议处理**: [ProtoHandler.java](mdc:src/main/java/ehooray/fishing/component/ProtoHandler.java)
- **响应缓存**: [ResponseCache.java](mdc:src/main/java/ehooray/fishing/component/ResponseCache.java)
- **事务调用**: [TransactionMethodInvoker.java](mdc:src/main/java/ehooray/fishing/component/TransactionMethodInvoker.java)
- **组件触发**: [TriggeredComponentCollector.java](mdc:src/main/java/ehooray/fishing/component/component/TriggeredComponentCollector.java)

### 开发流程检查清单
- [ ] Entity 创建并配置数据库映射
- [ ] Repository 接口定义并实现查询方法
- [ ] Proto 消息定义并编译生成 Java 类
- [ ] Service 实现业务逻辑并管理事务
- [ ] Controller 处理协议消息并调用 Service
- [ ] Component 创建必要的工具组件
- [ ] 单元测试编写并通过
- [ ] 集成测试验证功能正常

