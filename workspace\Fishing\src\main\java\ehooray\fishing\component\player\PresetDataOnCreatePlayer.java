package ehooray.fishing.component.player;

import java.util.Comparator;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.PresetDataCsv;
import ehooray.fishing.pojo.csv.RodCsv;
import ehooray.fishing.pojo.event.CreatePlayerEvent;
import ehooray.fishing.service.fishkit.FishKitService;
import ehooray.fishing.service.fishrod.FishRodService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.player.PlayerService;

@Component
public class PresetDataOnCreatePlayer
{
    final static int FishKitNum = 5; // TODO preset FishKitNum variableCsv
    
    @Autowired
    protected CsvContainer csvContainer;
    @Autowired
    protected PlayerItemService playerItemService;
    @Autowired
    protected FishRodService fishRodService;
    @Autowired
    protected FishKitService fishKitService;
    @Autowired
    protected PlayerService playerService;
    
    @EventListener
    public void onEvent(CreatePlayerEvent event) throws Exception
    {
        PresetDataCsv presetDataCsv = csvContainer.getCsv(PresetDataCsv.class);
        
        for (PresetDataCsv.Row row : presetDataCsv.getRows())
            addItemByPresetDataRow(event.getPlayer(), row);
        createPresetFishKit(event.getPlayer());
    }
    
    protected void addItemByPresetDataRow(PlayerEntity player, PresetDataCsv.Row presetDataRow) throws Exception
    {
        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
        RodCsv fishRodCsv = csvContainer.getCsv(RodCsv.class);
        
        switch (presetDataRow.getType().get())
        {
            case PlayerItem:
                playerItemService.produceItem(player, playerItemCsv.getById(presetDataRow.getItemId()), presetDataRow.getCount());
                break;
            case FishRod:
                fishRodService.create(player, fishRodCsv.getById(presetDataRow.getItemId()));
                break;
        }
    }
    
    protected void createPresetFishKit(PlayerEntity player)
    {
        var fishRod = fishRodService.getAllByPlayer(player).stream().sorted(Comparator.comparing(FishRodEntity::getCsvId)).findFirst().get();
        var playerItems = playerItemService.getAllByPlayer(player);
        Optional<PlayerItemEntity> fishLine = playerItems.stream().filter(playerItem -> filterByItemTag(playerItem, PlayerItemCsv.Tag.FishLine)).findFirst();
        Optional<PlayerItemEntity> bait = playerItems.stream().filter(playerItem -> filterByItemTag(playerItem, PlayerItemCsv.Tag.Bait)).findFirst();
        
        for (int i = 0; i < FishKitNum; i++)
            fishKitService.createFishKit(player, fishRod, fishLine.get(), bait.get());
        
        playerService.activeFishKit(player, fishKitService.getAllByPlayer(player).get(0));
    }
    
    private boolean filterByItemTag(PlayerItemEntity playerItem, PlayerItemCsv.Tag itemTag)
    {
        var csvRow = csvContainer.getCsv(PlayerItemCsv.class).getById(playerItem.getCsvId());
        return csvRow.getTag() == itemTag.getId();
    }
}
