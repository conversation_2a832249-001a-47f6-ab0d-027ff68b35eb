package ehooray.fishing.controller.time;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.protomap.model.ServerTimeProtoMap;
import ehooray.fishing.pojo.WebSocketHandleMediator;

@Controller("ServerTimeController")
public class ServerTimeController
{
    @Autowired
    protected ServerTimeProtoMap serverTimeMap;
    
    @TransactionMethod(Method.Read)
	public void getTime(WebSocketHandleMediator handleMediator) throws Exception
	{
        LocalDateTime now = handleMediator.getRequestStartTime();
        handleMediator.addResult(serverTimeMap.toAny(now));
	}
}
