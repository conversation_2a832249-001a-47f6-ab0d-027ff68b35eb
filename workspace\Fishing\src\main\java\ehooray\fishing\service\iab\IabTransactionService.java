package ehooray.fishing.service.iab;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.IabTransactionEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.IabCsv;
import ehooray.fishing.pojo.event.IabAcceptEvent;
import ehooray.fishing.pojo.iab.IabTransactionStateEnum;
import ehooray.fishing.repository.IabTransactionRepository;

@Service
public class IabTransactionService
{
    @Autowired
    protected IabTransactionRepository iabRepository;

    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected ApplicationEventPublisher publisher;

    public IabTransactionEntity create(PlayerEntity player, IabCsv.Row iabRow,
            String transactionId, int platformId, int platformState, LocalDateTime now) throws Exception
    {
        IabTransactionEntity newIabTransaction = new IabTransactionEntity();
        newIabTransaction.setCsvId(iabRow.getId());
        newIabTransaction.setPlayerId(player.getId());
        newIabTransaction.setTransactionId(transactionId);
        newIabTransaction.setPlatformId(platformId);
        newIabTransaction.setCreateTime(now);
        newIabTransaction.setPlatformState(platformState);
        newIabTransaction.setState(IabTransactionStateEnum.Create.getId());
        
        iabRepository.save(newIabTransaction);
        return newIabTransaction;
    }
    
    public void clientCancel(IabTransactionEntity iabTransaction, int state, String exception, LocalDateTime now) throws Exception
    {
        IabTransactionStateEnum stateEnum = IabTransactionStateEnum.valueOf(state).get();
        if(stateEnum != IabTransactionStateEnum.PlayerCancel 
        && stateEnum != IabTransactionStateEnum.ClientServiceFail)
            throw new ExceptionByCsvId("invalid transaction state = " + stateEnum);
        
        iabTransaction.setState(stateEnum.getId());
        iabTransaction.setClientCancelTime(now);
        if(stateEnum == IabTransactionStateEnum.ClientServiceFail)
            iabTransaction.setException(exception);
        
        iabRepository.save(iabTransaction);
    }
    
    public void verifySuccess(IabTransactionEntity iabTransaction, IabCsv.Row iabRow, String transactionId, int platformState, 
             LocalDateTime purchaseTime, LocalDateTime now, String receipt) throws Exception
    {
        if(iabTransaction.getState() != IabTransactionStateEnum.Create.getId())
            throw new ExceptionByCsvId("invalid iab transaction state, current = " + iabTransaction.getState());
        
        if(Strings.isNullOrEmpty(iabTransaction.getTransactionId()))
            iabTransaction.setTransactionId(transactionId);
        iabTransaction.setPlatformState(platformState);
        iabTransaction.setNormalizePrice(iabRow.getNormalizePrice());
        iabTransaction.setPurchaseTime(purchaseTime);
        iabTransaction.setVerifyTime(now);
        iabTransaction.setState(IabTransactionStateEnum.WaitPlayerAccept.getId());
        iabTransaction.setReceipt(receipt);
        
        iabRepository.save(iabTransaction);
    }
    
    public void verifyFail(IabTransactionEntity iabTransaction, String transactionId, int platformState, LocalDateTime now, String receipt, String exception)
    {
        if(Strings.isNullOrEmpty(iabTransaction.getTransactionId()))
            iabTransaction.setTransactionId(transactionId);
        iabTransaction.setPlatformState(platformState);
        iabTransaction.setVerifyTime(now);
        iabTransaction.setState(IabTransactionStateEnum.VerifyFail.getId());
        iabTransaction.setReceipt(receipt);
        iabTransaction.setException(exception);
        
        iabRepository.save(iabTransaction);
    }
    
    public void accept(PlayerEntity player, IabTransactionEntity iabTransaction, LocalDateTime now) throws ExceptionByCsvId
    {
        if(iabTransaction.getState() != IabTransactionStateEnum.WaitPlayerAccept.getId())
            throw new ExceptionByCsvId("invalid iab transaction state, current = " + iabTransaction.getState());
        
        iabTransaction.setAcceptTime(now);
        iabTransaction.setState(IabTransactionStateEnum.PlayerAccepted.getId());
        
        iabRepository.save(iabTransaction);
        publisher.publishEvent(new IabAcceptEvent(this, player, iabTransaction));
        
    }
    
    public Optional<IabTransactionEntity> findByEntityId(PlayerEntity player, long id)
    {
        return iabRepository.findByIdAndPlayerId(id, player.getId());
    }
    
    public List<IabTransactionEntity> findByState(PlayerEntity player, int state)
    {
        return iabRepository.findByStateAndPlayerId(state, player.getId());
    }
}
