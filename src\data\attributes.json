{"playerAttributes": {"levelingAttributes": {"castAccuracy": {"id": "castAccuracy", "name": "Cast Accuracy", "description": "Increases hotspot target zone size", "baseValue": 50, "maxValue": 100, "improvementPerLevel": 1, "maxLevelBonus": 20, "category": "fishing", "unit": "%"}, "energy": {"id": "energy", "name": "Energy", "description": "Stamina for performing actions", "baseValue": 100, "maxValue": 200, "improvementPerLevel": 2, "maxLevelBonus": 100, "category": "core", "unit": "points"}, "biteRate": {"id": "biteRate", "name": "Bite Rate", "description": "Likelihood of fish biting the lure", "baseValue": 30, "maxValue": 80, "improvementPerLevel": 0.5, "maxLevelBonus": 25, "category": "fishing", "unit": "%"}, "rareFishChance": {"id": "rareFish<PERSON><PERSON>ce", "name": "Rare Fish Chance", "description": "Probability of encountering rare fish", "baseValue": 5, "maxValue": 25, "improvementPerLevel": 0.2, "maxLevelBonus": 10, "category": "fishing", "unit": "%"}, "qtePrecision": {"id": "qtePrecision", "name": "QTE Precision", "description": "Extends QTE timing windows", "baseValue": 50, "maxValue": 100, "improvementPerLevel": 0.5, "maxLevelBonus": 25, "category": "skill", "unit": "%"}, "lureSuccess": {"id": "lureSuccess", "name": "<PERSON>re <PERSON>", "description": "Success rate of luring inputs", "baseValue": 60, "maxValue": 95, "improvementPerLevel": 0.5, "maxLevelBonus": 25, "category": "fishing", "unit": "%"}, "lineStrength": {"id": "lineStrength", "name": "Line Strength", "description": "Reduces line break chance during fights", "baseValue": 70, "maxValue": 95, "improvementPerLevel": 0.3, "maxLevelBonus": 15, "category": "fishing", "unit": "%"}, "castingRange": {"id": "castingRange", "name": "Casting Range", "description": "Maximum distance for casting lures", "baseValue": 50, "maxValue": 100, "improvementPerLevel": 0.5, "maxLevelBonus": 25, "category": "fishing", "unit": "meters"}, "boatSpeed": {"id": "boatSpeed", "name": "Boat Speed", "description": "Reduces travel time between locations", "baseValue": 100, "maxValue": 130, "improvementPerLevel": 0.3, "maxLevelBonus": 15, "category": "travel", "unit": "%"}, "coinGain": {"id": "coinGain", "name": "Coin <PERSON>", "description": "Increases coins earned from activities", "baseValue": 100, "maxValue": 130, "improvementPerLevel": 0.3, "maxLevelBonus": 15, "category": "economy", "unit": "%"}}, "fishingRodAttributes": {"rodCastAccuracy": {"id": "rodCastAccuracy", "name": "<PERSON> A<PERSON>uracy", "description": "Equipment bonus to casting precision", "baseValue": 0, "maxValue": 50, "category": "equipment", "unit": "%"}, "tensionStability": {"id": "tensionStability", "name": "Tension Stability", "description": "Widens safe tension zone during fights", "baseValue": 0, "maxValue": 60, "category": "equipment", "unit": "%"}, "rodRareFishChance": {"id": "rodRareFishChance", "name": "<PERSON> Chance", "description": "Equipment bonus to rare fish encounters", "baseValue": 0, "maxValue": 40, "category": "equipment", "unit": "%"}, "rodCastingRange": {"id": "rodCastingRange", "name": "Rod Casting Range", "description": "Equipment bonus to casting distance", "baseValue": 0, "maxValue": 70, "category": "equipment", "unit": "%"}, "reelSpeed": {"id": "reelSpeed", "name": "<PERSON><PERSON>", "description": "Faster reeling during fish fights", "baseValue": 0, "maxValue": 40, "category": "equipment", "unit": "%"}, "struggleResistance": {"id": "struggleResistance", "name": "Struggle Resistance", "description": "Reduces fish struggle intensity", "baseValue": 0, "maxValue": 35, "category": "equipment", "unit": "%"}}, "lureAttributes": {"lureBiteRate": {"id": "lureBiteRate", "name": "Lure Bite Rate", "description": "Equipment bonus to fish bite probability", "baseValue": 0, "maxValue": 100, "category": "equipment", "unit": "%"}, "lureLureSuccess": {"id": "lureLureSuccess", "name": "Lure Success Rate", "description": "Equipment bonus to luring minigame", "baseValue": 0, "maxValue": 80, "category": "equipment", "unit": "%"}, "lureDurability": {"id": "lureDurability", "name": "Lure Durability", "description": "Reduces lure failure chance", "baseValue": 0, "maxValue": 50, "category": "equipment", "unit": "%"}, "lurePrecision": {"id": "lurePrecision", "name": "Lure Precision", "description": "Tightens input timing windows", "baseValue": 0, "maxValue": 45, "category": "equipment", "unit": "%"}}, "companionAttributes": {"romanceEffectiveness": {"id": "romanceEffectiveness", "name": "Romance Effectiveness", "description": "Increases romance points gained", "baseValue": 100, "maxValue": 200, "category": "social", "unit": "%"}, "chatroomAffinity": {"id": "chatroomAffinity", "name": "Chatroom Affinity", "description": "Boosts chatroom interaction success", "baseValue": 100, "maxValue": 180, "category": "social", "unit": "%"}, "entertainmentMoodBoost": {"id": "entertainmentMoodBoost", "name": "Entertainment Mood Boost", "description": "Enhances companion mood improvements", "baseValue": 100, "maxValue": 150, "category": "social", "unit": "%"}, "companionFishDetection": {"id": "companionFishDetection", "name": "Companion Fish Detection", "description": "Companions help spot high-value fish", "baseValue": 0, "maxValue": 25, "category": "fishing", "unit": "%"}, "companionEnergy": {"id": "companionEnergy", "name": "Companion Energy Boost", "description": "Companions provide energy bonuses", "baseValue": 0, "maxValue": 50, "category": "core", "unit": "points"}, "companionQtePrecision": {"id": "companionQtePrecision", "name": "Companion QTE Precision", "description": "Companions extend QTE timing windows", "baseValue": 0, "maxValue": 20, "category": "skill", "unit": "%"}}, "boatAttributes": {"craftingEfficiency": {"id": "craftingEfficiency", "name": "Crafting Efficiency", "description": "Reduces crafting time and costs", "baseValue": 0, "maxValue": 50, "category": "crafting", "unit": "%"}, "autoFishingYield": {"id": "autoFishingYield", "name": "Auto-Fishing Yield", "description": "Increases auto-fishing rewards", "baseValue": 0, "maxValue": 80, "category": "automation", "unit": "%"}, "boatFishDetection": {"id": "boatFishDetection", "name": "Boat Fish Detection", "description": "Enhanced fish detection systems", "baseValue": 0, "maxValue": 35, "category": "fishing", "unit": "%"}, "hotspotStability": {"id": "hotspotStability", "name": "Hotspot Stability", "description": "Increases fishing hotspot duration", "baseValue": 0, "maxValue": 70, "category": "fishing", "unit": "%"}, "companionSlotCapacity": {"id": "companionSlotCapacity", "name": "Companion Slot Capacity", "description": "Number of companions that can join", "baseValue": 1, "maxValue": 6, "category": "social", "unit": "slots"}, "autoFishingEfficiency": {"id": "autoFishingEfficiency", "name": "Auto-Fishing Efficiency", "description": "Reduces auto-fishing time requirements", "baseValue": 0, "maxValue": 50, "category": "automation", "unit": "%"}, "boatDurability": {"id": "boatDurability", "name": "Boat Durability", "description": "Reduces penalties in adverse conditions", "baseValue": 100, "maxValue": 200, "category": "survival", "unit": "%"}, "fishtankStorage": {"id": "fishtankStorage", "name": "Fishtank Storage", "description": "Expands Fish Card storage capacity", "baseValue": 10, "maxValue": 100, "category": "storage", "unit": "cards"}}}, "fishAttributes": {"size": {"id": "size", "name": "Size", "description": "Physical size of the fish (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "physical"}, "aggressiveness": {"id": "aggressiveness", "name": "Aggressiveness", "description": "How likely the fish is to attack lures (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "behavior"}, "elusiveness": {"id": "elusiveness", "name": "Elusiveness", "description": "How difficult the fish is to catch (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "behavior"}, "strength": {"id": "strength", "name": "Strength", "description": "Fighting power during the reel-in phase (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "combat"}, "rarity": {"id": "rarity", "name": "<PERSON><PERSON>", "description": "How rare the fish is to encounter (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "spawn"}, "weight": {"id": "weight", "name": "Weight", "description": "Physical weight of the fish in kilograms", "minValue": 0.1, "maxValue": 500, "category": "physical", "unit": "kg"}, "speed": {"id": "speed", "name": "Speed", "description": "Swimming speed affecting escape attempts (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "movement"}, "depthPreference": {"id": "depthPreference", "name": "Depth Preference", "description": "Preferred water depth for spawning (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "habitat"}, "baitPreference": {"id": "baitPreference", "name": "Bait Preference", "description": "Attraction to specific lure types (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "behavior"}, "endurance": {"id": "endurance", "name": "Endurance", "description": "How long the fish can fight before tiring (1-10 scale)", "minValue": 1, "maxValue": 10, "category": "combat"}, "activeTimePeriod": {"id": "activeTimePeriod", "name": "Active Time Period", "description": "Time periods when fish is most active", "category": "temporal", "possibleValues": ["dawn", "morning", "noon", "afternoon", "evening", "dusk", "night", "midnight"]}, "weatherPreference": {"id": "weatherPreference", "name": "Weather Preference", "description": "Preferred weather conditions for activity", "category": "environmental", "possibleValues": ["sunny", "cloudy", "rainy", "stormy", "any"]}}, "attributeModifiers": {"weatherEffects": {"sunny": {"fishDetection": 10, "biteRate": 0, "visibility": 20}, "cloudy": {"fishDetection": 0, "biteRate": 5, "visibility": 0}, "rainy": {"fishDetection": -5, "biteRate": 10, "visibility": -10}, "stormy": {"fishDetection": -15, "biteRate": 15, "visibility": -25, "rareFishChance": 5}}, "timeEffects": {"dawn": {"biteRate": 15, "rareFishChance": 5}, "morning": {"biteRate": 10, "fishDetection": 5}, "noon": {"biteRate": 0, "fishDetection": 10}, "afternoon": {"biteRate": 5, "fishDetection": 5}, "evening": {"biteRate": 10, "rareFishChance": 3}, "dusk": {"biteRate": 20, "rareFishChance": 8}, "night": {"biteRate": 25, "rareFishChance": 10, "fishDetection": -10}, "midnight": {"biteRate": 30, "rareFishChance": 15, "fishDetection": -20}}, "depthEffects": {"shallow": {"castAccuracy": 10, "biteRate": 5}, "medium": {"castAccuracy": 0, "biteRate": 0}, "deep": {"castAccuracy": -5, "biteRate": -5, "rareFishChance": 10}, "abyss": {"castAccuracy": -15, "biteRate": -10, "rareFishChance": 25}}}}