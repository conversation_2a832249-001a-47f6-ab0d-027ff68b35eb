package ehooray.fishing.entity;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

// Note: this entity may always create and never remove, so don't use @OneToMany and l2cache.
@Entity
//@formatter:off
@Table
(
    name = "iab_transaction",
    indexes =
    {
        @Index(name = "csvId", columnList = "csvId"),
        @Index(name = "platformId", columnList = "platformId"),
        @Index(name = "state", columnList = "state"),
        @Index(name = "playerId", columnList = "playerId"),
        @Index(name = "createTime", columnList = "createTime"),
        @Index(name = "transactionId", columnList = "transactionId"),
    }
)
//@formatter:on
public class IabTransactionEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    @Column(unique=true)
    protected String transactionId;
    protected long playerId;
    protected int csvId;
    protected int platformId;
    protected int platformState;
    protected int state;
    protected boolean isTest;
    protected float normalizePrice;
    
    protected LocalDateTime createTime;
    protected LocalDateTime purchaseTime;
    protected LocalDateTime verifyTime;
    protected LocalDateTime acceptTime;
    protected LocalDateTime clientCancelTime;

    @Column(length=8192)
    private String receipt;
    protected String exception;

    public String getTransactionId()
    {
        return transactionId;
    }

    public void setTransactionId(String transactionId)
    {
        this.transactionId = transactionId;
    }

    public long getPlayerId()
    {
        return playerId;
    }

    public void setPlayerId(long playerId)
    {
        this.playerId = playerId;
    }

    public int getCsvId()
    {
        return csvId;
    }

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }

    public LocalDateTime getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime)
    {
        this.createTime = createTime;
    }

    public LocalDateTime getPurchaseTime()
    {
        return purchaseTime;
    }

    public void setPurchaseTime(LocalDateTime purchaseTime)
    {
        this.purchaseTime = purchaseTime;
    }

    public int getPlatformId()
    {
        return platformId;
    }

    public void setPlatformId(int platformId)
    {
        this.platformId = platformId;
    }

    public int getState()
    {
        return state;
    }

    public void setState(int state)
    {
        this.state = state;
    }

    public String getReceipt()
    {
        return receipt;
    }

    public void setReceipt(String receipt)
    {
        this.receipt = receipt;
    }

    public LocalDateTime getAcceptTime()
    {
        return acceptTime;
    }

    public void setAcceptTime(LocalDateTime acceptTime)
    {
        this.acceptTime = acceptTime;
    }

    public long getId()
    {
        return id;
    }

    public void setId(long id)
    {
        this.id = id;
    }

    public boolean isTest()
    {
        return isTest;
    }

    public void setTest(boolean isTest)
    {
        this.isTest = isTest;
    }

    public int getPlatformState()
    {
        return platformState;
    }

    public void setPlatformState(int platformState)
    {
        this.platformState = platformState;
    }

    public LocalDateTime getVerifyTime()
    {
        return verifyTime;
    }

    public void setVerifyTime(LocalDateTime verifyTime)
    {
        this.verifyTime = verifyTime;
    }

    public float getNormalizePrice()
    {
        return normalizePrice;
    }

    public void setNormalizePrice(float normalizePrice)
    {
        this.normalizePrice = normalizePrice;
    }

    public String getException()
    {
        return exception;
    }

    public void setException(String exception)
    {
        this.exception = exception;
    }

    public LocalDateTime getClientCancelTime()
    {
        return clientCancelTime;
    }

    public void setClientCancelTime(LocalDateTime clientCancelTime)
    {
        this.clientCancelTime = clientCancelTime;
    }

}
