package ehooray.fishing.pojo.iab.unity;

public class UnityIabReceiptVerifyException extends Exception
{
    private static final long serialVersionUID = 1438192239057384175L;
    protected int platformState;
    protected String transactionId;
    public UnityIabReceiptVerifyException(String transactionId, int platformState, String message)
    {
        super("[" + platformState + "]" + message);
        this.transactionId = transactionId;
        this.platformState = platformState;
    }
    public int getPlatformState()
    {
        return platformState;
    }
    public String getTransactionId()
    {
        return transactionId;
    }


}
