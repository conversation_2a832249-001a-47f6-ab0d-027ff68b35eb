package ehooray.fishing.repository;


import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.CrudRepository;

import ehooray.fishing.entity.IabTransactionEntity;


public interface IabTransactionRepository extends CrudRepository<IabTransactionEntity, Long>
{
    Optional<IabTransactionEntity> findByIdAndPlayerId(long id, long playerId);
    List<IabTransactionEntity> findByStateAndPlayerId(int state, long playerId);
}
