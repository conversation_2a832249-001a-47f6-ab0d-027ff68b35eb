<!-- Generated by SpecStory -->

# 修改漂移效果至底部 (2025-05-13 15:45:31)

_**User**_

StartDrifting的部分原本是在中心點飄移，請改成逐漸向下沉，直到碰到Bottom的image為止

---

_**Assistant**_



---

好的，我将修改 `LureAnimationController.cs` 中的 `StartDrifting` 方法，使其控制鱼饵逐渐向下沉，直到碰到一个指定的 "Bottom" 图像为止。同时，鱼饵在下沉过程中以及到达底部后，会保持水平方向的轻微摆动。

为了实现这个功能，我需要做以下修改：
1.  在 `LureAnimationController` 类中添加一个新的 `SerializeField` 字段 `bottomBoundary`，用于引用代表底部的 `RectTransform`。您需要在 Unity 编辑器中将相应的UI元素拖拽到这个字段上。
2.  添加一个新的 `SerializeField` 字段 `horizontalSwaySegmentDuration` 来控制水平摆动一个单程所需的时间。
3.  修改 `driftSpeed` 的含义，使其代表垂直下沉的速度（单位/秒）。`driftMagnitude` 仍用于控制水平摆动的幅度。
4.  重写 `StartDrifting` 方法的逻辑：
    *   停止所有先前由该脚本控制的动画。
    *   计算鱼饵下沉的目标Y轴位置，使其底部边缘与 `bottomBoundary` 的顶部边缘对齐。
    *   如果鱼饵尚未到达底部，则启动一个 `DOAnchorPosY` 动画使其下沉。
    *   启动一个新的水平摆动动画序列（存储在 `driftSequence` 中），使鱼饵围绕其中心X轴位置进行左右摆动。这个摆动会持续进行，无论鱼饵是否在下沉或已到达底部。
5.  更新 `OnQTESuccess` 方法中暂停动画的逻辑，使用 `fishRectTransform?.DOPause()` 来暂停所有在 `fishRectTransform` 上的动画（包括下沉和摆动）。

以下是修改后的 `LureAnimationController.cs` 代码。请注意，您需要在Unity编辑器中为新增的 `bottomBoundary` 字段指定一个 `RectTransform`。



---

```diff
      [Header("UI Elements")]
      [SerializeField] private Image fishImage;
+     [SerializeField] private RectTransform bottomBoundary; // 新增：底部邊界UI元素
  
      [Header("Animation Parameters")]
-     [SerializeField] private float driftSpeed = 1f;
-     [SerializeField] private float driftMagnitude = 10f;
+     [SerializeField] private float driftSpeed = 50f; // 修改含義：垂直下沉速度 (單位/秒)
+     [SerializeField] private float driftMagnitude = 10f; // 水平擺動幅度
+     [SerializeField] private float horizontalSwaySegmentDuration = 1f; // 新增：水平擺動單程時間
      [SerializeField] private float dashDuration = 0.2f;
      [SerializeField] private float returnDuration = 0.5f;
      [SerializeField] private float dashDistance = 100f;
  
      private RectTransform fishRectTransform;
-     private Vector2 centerPosition;
-     private Sequence driftSequence;
-     private bool isAnimating = false; // 防止並發動畫
+     private Vector2 centerPosition; // 初始中心位置，QTE返回點
+     private Sequence driftSequence; // 用於水平擺動動畫
+     private bool isAnimating = false; // 防止並發QTE動畫
  
      void Start()
      {
          fishRectTransform = fishImage.rectTransform;
+         if (fishRectTransform != null)
+         {
              centerPosition = fishRectTransform.anchoredPosition;
+         }
+         else
+         {
+             Debug.LogError("LureAnimationController: fishImage.rectTransform is null.");
+             // 可以設置一個默認的centerPosition或者禁用組件
+             centerPosition = Vector2.zero;
+             enabled = false; 
+             return;
+         }
+ 
+         if (bottomBoundary == null)
+         {
+             Debug.LogWarning("LureAnimationController: BottomBoundary is not assigned in the Inspector. Sinking behavior will be disabled.");
+         }
          StartDrifting();
      }
```

```diff
      private void StartDrifting()
      {
-         if (isAnimating) return; // 如果其他動畫正在播放，則不開始漂移
+         if (isAnimating) return; // 如果QTE動畫正在播放，則不開始漂移/下沉
  
-         // 確保在啟動新的漂移動畫序列之前，停止之前的序列
+         // 確保在啟動新的動畫序列之前，停止之前的序列和所有相關tween
          driftSequence?.Kill();
- 
-         // 使用 DOTween 創建循環漂移動畫
+         fishRectTransform?.DOKill(); // 停止所有在fishRectTransform上的tween (包括下沉和舊的擺動)
+ 
+         if (fishRectTransform == null) return;
+ 
+         // --- 下沉動畫 ---
+         if (bottomBoundary != null)
+         {
+             float fishPivotY = fishRectTransform.pivot.y;
+             float fishHeight = fishRectTransform.rect.height;
+             
+             float boundaryTopActualY = bottomBoundary.anchoredPosition.y + bottomBoundary.rect.height * (1f - bottomBoundary.pivot.y);
+             float targetSinkAnchorY = boundaryTopActualY + fishHeight * fishPivotY;
+ 
+             float currentAnchorY = fishRectTransform.anchoredPosition.y;
+             float distanceToSink = currentAnchorY - targetSinkAnchorY;
+ 
+             if (distanceToSink > 0.01f) // 只有當高於底部時才下沉
+             {
+                 float sinkDuration = distanceToSink / driftSpeed; // driftSpeed 是 單位/秒
+                 if (sinkDuration <= 0.001f) sinkDuration = 0.001f; // 防止持續時間為零或負
+ 
+                 fishRectTransform.DOAnchorPosY(targetSinkAnchorY, sinkDuration)
+                     .SetEase(Ease.Linear); // 或者 Ease.InOutSine
+             }
+             else
+             {
+                 // 如果已經在底部或以下，則將其位置固定在底部目標Y值
+                 fishRectTransform.anchoredPosition = new Vector2(fishRectTransform.anchoredPosition.x, targetSinkAnchorY);
+             }
+         }
+         else
+         {
+             // 如果沒有設置底部邊界，可以選擇執行舊的漂移動畫或不做任何事情
+             // 這裡我們將不執行下沉，但仍會執行水平擺動（如果需要）
+         }
+ 
+         // --- 水平擺動動畫 (圍繞 centerPosition.x) ---
+         // 即使在下沉或已到達底部，也應進行水平擺動
          driftSequence = DOTween.Sequence();
-         driftSequence.Append(fishRectTransform.DOAnchorPos(centerPosition + new Vector2(Random.Range(-driftMagnitude, driftMagnitude), Random.Range(-driftMagnitude, driftMagnitude)), 1f / driftSpeed).SetEase(Ease.InOutSine));
-         driftSequence.Append(fishRectTransform.DOAnchorPos(centerPosition + new Vector2(Random.Range(-driftMagnitude, driftMagnitude), Random.Range(-driftMagnitude, driftMagnitude)), 1f / driftSpeed).SetEase(Ease.InOutSine));
-         driftSequence.SetLoops(-1, LoopType.Yoyo); // 無限循環
+         // 擺動路徑: center.x -> center.x + magnitude -> center.x - magnitude -> center.x + magnitude ...
+         driftSequence.Append(fishRectTransform.DOAnchorPosX(centerPosition.x + driftMagnitude, horizontalSwaySegmentDuration).SetEase(Ease.InOutSine));
+         driftSequence.Append(fishRectTransform.DOAnchorPosX(centerPosition.x - driftMagnitude, horizontalSwaySegmentDuration).SetEase(Ease.InOutSine));
+         driftSequence.SetLoops(-1, LoopType.Yoyo); // Yoyo循環使其在兩個 Append 點之間來回擺動
          driftSequence.Play();
      }
```

```diff
      public void OnQTESuccess(Vector2 direction)
      {
-         if (isAnimating || fishRectTransform == null) return; // 如果動畫正在運行或尚未初始化，則阻止啟動新動畫
+         if (isAnimating || fishRectTransform == null) return;
  
          isAnimating = true;
-         driftSequence?.Pause(); // 在 QTE 動畫期間暫停漂移
+         // driftSequence?.Pause(); // 舊的只暫停擺動
+         fishRectTransform?.DOPause(); // 暫停所有在此RectTransform上的DOTween動畫 (包括下沉和擺動)
  
          Vector2 dashTargetPosition = centerPosition + direction.normalized * dashDistance;
  
-         // 在啟動新的移動 tween 之前停止任何現有的 tween
-         fishRectTransform.DOKill();
+         fishRectTransform.DOKill(); // 在衝刺前停止所有當前動畫
  
-         // 動畫序列：衝刺出去，然後返回中心，然後恢復漂移
          DOTween.Sequence()
-             .Append(fishRectTransform.DOAnchorPos(dashTargetPosition, dashDuration).SetEase(Ease.OutQuad)) // 衝刺出去
-             .Append(fishRectTransform.DOAnchorPos(centerPosition, returnDuration).SetEase(Ease.InCubic)) // 返回中心
+             .Append(fishRectTransform.DOAnchorPos(dashTargetPosition, dashDuration).SetEase(Ease.OutQuad))
+             .Append(fishRectTransform.DOAnchorPos(centerPosition, returnDuration).SetEase(Ease.InCubic))
              .OnComplete(() =>
              {
                  isAnimating = false;
-                 StartDrifting(); // 僅在返回中心後恢復漂移
+                 StartDrifting(); // QTE結束後，恢復下沉和擺動狀態
              })
              .Play();
      }
  
-     // 可選：如果需要，立即重置位置的方法
      public void ResetPosition()
      {
          if (fishRectTransform == null) return;
-         isAnimating = false;
-         fishRectTransform.DOKill(); // 停止任何當前動畫
-         driftSequence?.Kill();     // 停止漂移動畫序列
+         
+         isAnimating = false; // 重置時，確保isAnimating標記也復位
+         fishRectTransform.DOKill(); // 停止任何當前動畫（包括下沉和擺動）
+         // driftSequence?.Kill(); // StartDrifting會處理舊的driftSequence
+ 
          fishRectTransform.anchoredPosition = centerPosition;
-         StartDrifting(); // 從中心重新開始漂移
+         StartDrifting(); // 從中心重新開始下沉和擺動
      }
  }
```

---

代码已按您的要求修改完毕。

主要改动包括：
*   添加了 `bottomBoundary` (RectTransform) 和 `horizontalSwaySegmentDuration` (float) 两个新的可配置字段。请记得在Unity编辑器中为 `bottomBoundary` 分配一个UI元素作为底部边界。
*   `driftSpeed` 现在控制垂直下沉的速度。
*   `StartDrifting` 方法已更新，现在会使鱼饵向下移动直到接触 `bottomBoundary`，同时会围绕其初始中心X轴位置进行水平摆动。
*   `OnQTESuccess` 现在会使用 `fishRectTransform?.DOPause()` 来暂停所有相关动画。
*   `ResetPosition` 方法也会正确地停止所有动画并从中心位置重新开始下沉和摆动。
*   在 `Start()` 方法中添加了对 `fishImage.rectTransform` 和 `bottomBoundary` 的空值检查，以增加代码的健壮性。

请将此更新后的脚本应用到您的项目中，并在Unity编辑器中配置好 `bottomBoundary` 字段。

---

