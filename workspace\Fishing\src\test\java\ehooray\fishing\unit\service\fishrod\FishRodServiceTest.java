package ehooray.fishing.unit.service.fishrod;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import java.util.ArrayList;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.context.ApplicationEventPublisher;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.csv.RodCsv;
import ehooray.fishing.repository.FishRodRepository;
import ehooray.fishing.service.fishrod.FishRodService;

public class FishRodServiceTest
{
    CsvContainer csvContainer;
    FishRodService fishRodService;

    
    @BeforeEach
    public void createService()
    { 
        csvContainer = mock(CsvContainer.class);
        var fishRodRepository = mock(FishRodRepository.class);
        var publisher = mock(ApplicationEventPublisher.class);
        fishRodService = new FishRodService(csvContainer, fishRodRepository, publisher);
    }
    
    @Test
    public void testCreateRod() throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, false);
        
        RodCsv.Row rowData = mock(RodCsv.Row.class);
        when(rowData.getPointMin()).thenReturn(65); 
        when(rowData.getPointMax()).thenReturn(90); 
        when(rowData.getStrength()).thenReturn(40); 
        when(rowData.getControl()).thenReturn(0); 
        when(rowData.getLuck()).thenReturn(0); 
        when(rowData.getDqr()).thenReturn(0); 
        
        // act
        var newRod = fishRodService.create(player, rowData);
        
        // assert
        int pointTotal = newRod.getStrength()+newRod.getControl()+newRod.getLucky()+newRod.getDqr();
        assertThat(pointTotal).isGreaterThanOrEqualTo(65).isLessThanOrEqualTo(90);
        assertThat(rowData.getStrength()).isGreaterThanOrEqualTo(40);
    }
    
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    public void testLockFishRod(boolean isLock) throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, false);
      
        // act
        var findFishRod = fishRodService.findByEntityId(player, fishRodId);
        fishRodService.lockFishRod(findFishRod.get(), isLock);
        
        // assert
        assertThat(findFishRod.isPresent()).isTrue();
        assertThat(findFishRod.get().isLock()).isEqualTo(isLock);
    }
    
    @ParameterizedTest
    @ValueSource(ints = {999})
    public void testRepair(int times) throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, false);
        
        RodCsv.Row rowData = mock(RodCsv.Row.class);
        when(rowData.getTimes()).thenReturn(times); 
        
        // act
        var findFishRod = fishRodService.findByEntityId(player, fishRodId);
        fishRodService.repairFishRod(findFishRod.get(), rowData);
        
        // assert
        assertThat(findFishRod.isPresent()).isTrue();
        assertThat(findFishRod.get().getTimes()).isEqualTo(times);
    }
    
    @Test
    public void sell_whenLock() throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;
        boolean isLock = true;
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, isLock);
        
        RodCsv.Row rowData = mock(RodCsv.Row.class);
        when(rowData.getSell()).thenReturn(300); 
                
        // assert
        assertThatThrownBy(()-> {
            // act
            var findFishRod = fishRodService.findByEntityId(player, fishRodId);
            fishRodService.sellFishRod(player, findFishRod.get());
        }).isInstanceOfAny(Exception.class)
          .hasMessageContaining("lock");
    }
    
    @Test
    public void sell_price300() throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;     
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, false);
        
        RodCsv rodCsv = spy(RodCsv.class);
        RodCsv.Row rowData = mock(RodCsv.Row.class);
        when(rowData.getId()).thenReturn(fishRodCsvId); 
        when(rowData.getSell()).thenReturn(300); 
        rodCsv.addRow(rowData);
        when(csvContainer.getCsv(RodCsv.class)).thenReturn(rodCsv);
        
        // act
        var findFishRod = fishRodService.findByEntityId(player, fishRodId);
        fishRodService.sellFishRod(player, findFishRod.get());
        
        // assert
        assertThat(fishRodService.findByEntityId(player, fishRodId).isEmpty()).isTrue();
    }
    
    @Test
    public void sell_price0() throws Exception
    {
        // arrange
        long fishRodId = 1;
        int fishRodCsvId = 1000;     
        var player = makePlayerWithRod(fishRodId, fishRodCsvId, false);
        
        RodCsv rodCsv = spy(RodCsv.class);
        RodCsv.Row rowData = mock(RodCsv.Row.class);
        when(rowData.getId()).thenReturn(fishRodCsvId); 
        when(rowData.getSell()).thenReturn(0); 
        rodCsv.addRow(rowData);
        when(csvContainer.getCsv(RodCsv.class)).thenReturn(rodCsv);
        
        // assert
        assertThatThrownBy(()-> {
            // act
            var findFishRod = fishRodService.findByEntityId(player, fishRodId);
            fishRodService.sellFishRod(player, findFishRod.get());
        }).isInstanceOfAny(Exception.class)
          .hasMessageContaining("prize is 0");
    }
    
    private PlayerEntity makePlayerWithRod(long fishRodId, int fishRodCsvId, boolean lock)
    {
        var fishRods = new ArrayList<FishRodEntity>();
        var newFishRod = new FishRodEntity();
        newFishRod.setId(fishRodId);
        newFishRod.setCsvId(fishRodCsvId);
        newFishRod.setLock(lock);
        fishRods.add(newFishRod);
        var player = new PlayerEntity();
        player.setFishRods(fishRods);
        newFishRod.setPlayer(player);
        
        return player;
    }
}
