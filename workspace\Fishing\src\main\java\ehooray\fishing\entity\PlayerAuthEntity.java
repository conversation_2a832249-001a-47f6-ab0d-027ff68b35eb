package ehooray.fishing.entity;

import java.time.LocalDateTime;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

// Note: do not cache this entity, because token validate need the newest data.
@Entity
//@formatter:off
@Table
(
    name = "player_auth",
    uniqueConstraints = @UniqueConstraint(columnNames = {"account", "serverId"}),
    indexes = 
    {
        @Index(name = "account", columnList = "account"),
        @Index(name = "serverId", columnList = "serverId"),
        @Index(name = "token", columnList = "token"),
    }
)
//@formatter:on
public class PlayerAuthEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    @Column(length = 64)
    protected String account;
    protected String password;
    @Column(length = 64)
    protected String token;
    protected LocalDateTime createDate;
    protected int serverId;
    protected String sessionId;
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "playerAuth")
    protected List<PlayerEntity> players;

    public String getAccount()
    {
        return account;
    }

    public void setAccount(String account)
    {
        this.account = account;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public LocalDateTime getCreateDate()
    {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate)
    {
        this.createDate = createDate;
    }

    public long getId()
    {
        return id;
    }

    public List<PlayerEntity> getPlayers()
    {
        return players;
    }

    public void setPlayers(List<PlayerEntity> players)
    {
        this.players = players;
    }

    public int getServerId()
    {
        return serverId;
    }

    public void setServerId(int serverId)
    {
        this.serverId = serverId;
    }

    public String getSessionId()
    {
        return sessionId;
    }

    public void setSessionId(String sessionId)
    {
        this.sessionId = sessionId;
    }
}
