// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/Gacha.proto

package ehooray.fishing.dto.proto.model;

public final class GachaProto {
  private GachaProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GachaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.Gacha)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>int32 buyCount = 3;</code>
     * @return The buyCount.
     */
    int getBuyCount();

    /**
     * <code>int64 freeBuyTime = 4;</code>
     * @return The freeBuyTime.
     */
    long getFreeBuyTime();
  }
  /**
   * Protobuf type {@code model.Gacha}
   */
  public static final class Gacha extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.Gacha)
      GachaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Gacha.newBuilder() to construct.
    private Gacha(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Gacha() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Gacha();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.GachaProto.internal_static_model_Gacha_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.GachaProto.internal_static_model_Gacha_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.GachaProto.Gacha.class, ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int BUYCOUNT_FIELD_NUMBER = 3;
    private int buyCount_;
    /**
     * <code>int32 buyCount = 3;</code>
     * @return The buyCount.
     */
    @java.lang.Override
    public int getBuyCount() {
      return buyCount_;
    }

    public static final int FREEBUYTIME_FIELD_NUMBER = 4;
    private long freeBuyTime_;
    /**
     * <code>int64 freeBuyTime = 4;</code>
     * @return The freeBuyTime.
     */
    @java.lang.Override
    public long getFreeBuyTime() {
      return freeBuyTime_;
    }

    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.GachaProto.Gacha prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.Gacha}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.Gacha)
        ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.GachaProto.internal_static_model_Gacha_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.GachaProto.internal_static_model_Gacha_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.GachaProto.Gacha.class, ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.GachaProto.Gacha.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        csvId_ = 0;

        buyCount_ = 0;

        freeBuyTime_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.GachaProto.internal_static_model_Gacha_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.GachaProto.Gacha getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.GachaProto.Gacha.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.GachaProto.Gacha build() {
        ehooray.fishing.dto.proto.model.GachaProto.Gacha result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.GachaProto.Gacha buildPartial() {
        ehooray.fishing.dto.proto.model.GachaProto.Gacha result = new ehooray.fishing.dto.proto.model.GachaProto.Gacha(this);
        result.entityId_ = entityId_;
        result.csvId_ = csvId_;
        result.buyCount_ = buyCount_;
        result.freeBuyTime_ = freeBuyTime_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private int buyCount_ ;
      /**
       * <code>int32 buyCount = 3;</code>
       * @return The buyCount.
       */
      @java.lang.Override
      public int getBuyCount() {
        return buyCount_;
      }
      /**
       * <code>int32 buyCount = 3;</code>
       * @param value The buyCount to set.
       * @return This builder for chaining.
       */
      public Builder setBuyCount(int value) {
        
        buyCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 buyCount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyCount() {
        
        buyCount_ = 0;
        onChanged();
        return this;
      }

      private long freeBuyTime_ ;
      /**
       * <code>int64 freeBuyTime = 4;</code>
       * @return The freeBuyTime.
       */
      @java.lang.Override
      public long getFreeBuyTime() {
        return freeBuyTime_;
      }
      /**
       * <code>int64 freeBuyTime = 4;</code>
       * @param value The freeBuyTime to set.
       * @return This builder for chaining.
       */
      public Builder setFreeBuyTime(long value) {
        
        freeBuyTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 freeBuyTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFreeBuyTime() {
        
        freeBuyTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.Gacha)
    }

    // @@protoc_insertion_point(class_scope:model.Gacha)
    private static final ehooray.fishing.dto.proto.model.GachaProto.Gacha DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.GachaProto.Gacha();
    }

    public static ehooray.fishing.dto.proto.model.GachaProto.Gacha getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Gacha>
        PARSER = new com.google.protobuf.AbstractParser<Gacha>() {
      @java.lang.Override
      public Gacha parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Gacha> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Gacha> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.GachaProto.Gacha getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Gacha_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Gacha_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021model/Gacha.proto\022\005model\"O\n\005Gacha\022\020\n\010e" +
      "ntityId\030\001 \001(\003\022\r\n\005csvId\030\002 \001(\005\022\020\n\010buyCount" +
      "\030\003 \001(\005\022\023\n\013freeBuyTime\030\004 \001(\003BG\n\037ehooray.f" +
      "ishing.dto.proto.modelB\nGachaProtoH\002\252\002\025F" +
      "ishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_Gacha_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_Gacha_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Gacha_descriptor,
        new java.lang.String[] { "EntityId", "CsvId", "BuyCount", "FreeBuyTime", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
