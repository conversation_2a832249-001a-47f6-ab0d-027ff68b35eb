package ehooray.fishing.log.service;

import java.time.ZoneId;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.ProtoHandleMediatorContainer;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.entity.FishRodLogEntity;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.log.repository.FishRodLogRepository;
import ehooray.fishing.pojo.event.FishRodEvent;
import ehooray.fishing.pojo.event.UpgradeRodEvent;

@Service
public class FishRodLogService
{
    @Autowired
    protected FishRodLogRepository fishRodLogRepository;
    
    @Autowired
    protected ProtoHandleMediatorContainer protoHandleMediatorContainer;
    
    @EventListener
    public void createLog(FishRodEvent fishRodEvent)
    {
        // maybe createRod, sellRod, repairRod (source from controller method) 
        var mediator = protoHandleMediatorContainer.getMediator();
        createLog(fishRodEvent.getPlayer(), fishRodEvent.getFishRod(), mediator.getLogSource());
    }
    
    @EventListener
    public void createLog(UpgradeRodEvent upgradeRodEvent)
    {
        var mediator = protoHandleMediatorContainer.getMediator();
        createLog(upgradeRodEvent.getPlayer(), upgradeRodEvent.getFishRod(), mediator.getLogSource());
        
        // remove materials log
        var logSource = new LogSource(SystemBehaviourEnum.FishRodController_upgradeFishRod_removeMaterials, mediator.getLogSource());
        for(var material : upgradeRodEvent.getMaterials())
            createLog(upgradeRodEvent.getPlayer(), material, logSource);
    }
    
    protected FishRodLogEntity createLog(PlayerEntity player, FishRodEntity fishRod, LogSource logSource)
    {
        var mediator = protoHandleMediatorContainer.getMediator();
        
        var log = new FishRodLogEntity();
        log.setCsvId(fishRod.getCsvId());
        log.setFromSystem(logSource.getBehaviour().getSystemId());
        log.setBehaviour(logSource.getBehaviour().getId());
        log.setPlayerId(player.getId());
        for(int i = 0, iMax = logSource.getBehaviourInputs().length; i < iMax; i++)
            log.setBehaviourInput(i, logSource.getBehaviourInputs()[i]);
        var now = mediator.getRequestStartTime();
        log.setCreateDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        
        fishRodLogRepository.save(log);
        
        return log;
    }
}
