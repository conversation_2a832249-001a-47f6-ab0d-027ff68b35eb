package ehooray.fishing.component.lua.cache;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.lua.ILuaCacheComponent;
import ehooray.fishing.pojo.lua.CatchExceptionTwoArgFunction;
import ehooray.fishing.service.player.PlayerService;
import ehooray.fishing.service.quest.QuestService;

@Component
public class QuestServiceForLua implements ILuaCacheComponent
{

    @Autowired
    protected PlayerService playerService;
    @Autowired
    protected QuestService questService;
    
    protected LuaTable cache;
    
    @Override
    public void initial() throws Exception
    {
        cache = LuaValue.tableOf();
        cache.set(FindByCsvId.class.getSimpleName(), new FindByCsvId());
    }

    @Override
    public LuaTable toLua() throws Exception
    {
        return cache;
    }
    
    public class FindByCsvId extends CatchExceptionTwoArgFunction
    {
        @Override
        public LuaValue doCall(LuaValue playerId, LuaValue questCsvId) throws Exception
        {
            var playerEntity = playerService.findByEntityId(playerId.tolong()).get();
            var questOption = questService.findByCsvId(playerEntity, questCsvId.toint());
            if (questOption.isPresent())
                return questOption.get().toLua();
            else
                return LuaValue.NIL;
        }
        
    }

}
