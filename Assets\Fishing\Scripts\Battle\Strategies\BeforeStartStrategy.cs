using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚開始前狀態策略
    /// </summary>
    public class BeforeStartStrategy : FishingStrategyBase
    {
        public BeforeStartStrategy() : base(FishingState.BeforeStart) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToBeforeStart方法中的初始化邏輯
            context.EquipData.SetEquipData();
            if (!context.CheckPlayerItem())
                return;

            context.fish.ChangeFishModel().Forget();

            context.cordReelManager.CloseStartText();
            context.fishingGamePanelManager.Close<PrepareManager>();
            context.fishingGamePanelManager.Close<TopInfoManager>();

            FishingEventTrigger.GetTrigger(context.prepareManager.startButton.gameObject).onPointerClick -= context.prepareManager.OnStartButtonClick;
            FishingEventTrigger.GetTrigger(context.throwBaitManager.pullButton.gameObject).onPointerDown += context.throwBaitManager.OnPullButtonPressed;

            context.fish.continueTimes = context.fishData.RetryCount;
            context.fish.stunTimes = context.fish.continueTimes;
            context.fish.SetFishData();

            // 重置甩動檢測器，確保可以檢測拋竿動作
            if (context.GetComponent<ThrowGestureDetector>() != null)
            {
                context.GetComponent<ThrowGestureDetector>().ResetDetector();
            }

            await context.fishingGamePanelManager.OpenAsync<ThrowBaitManager>();
            context.throwBaitManager.Play();

            await context.fishingGamePanelManager.OpenAsync<CordReelManager>();
            if (context.lineTensionManager != null)
                context.lineTensionManager.SetRodControlValue(context.EquipData.rodData.control);
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 原DoBeforeStart方法中的邏輯，目前為空
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            // 在退出此狀態時清理資源或事件
            FishingEventTrigger.GetTrigger(context.throwBaitManager.pullButton.gameObject).onPointerDown -= context.throwBaitManager.OnPullButtonPressed;

            return UniTask.CompletedTask;
        }
    }
} 