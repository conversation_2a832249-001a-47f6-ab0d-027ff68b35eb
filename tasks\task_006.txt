# Task ID: 6
# Title: Core Fishing Mechanics - Reel-In Minigame
# Status: pending
# Dependencies: 5
# Priority: high
# Description: Implement the tension-based Reel-In Minigame with a tension meter, fish stamina, QTEs for 10 fish struggle styles, and visual feedback of a struggling fish.
# Details:
Create a Phaser scene for Reel-In Minigame. UI: Tension meter, Fish Stamina bar on fish. Visuals: Hooked fish struggling, splashing water, line connecting to rod. Fish swims left/right randomly. Logic: Mouse press reels, reduces fish stamina (popup number), increases tension. Tension decreases when not reeling. Line snaps if tension maxes. Player Tension Stability widens safe zone; Reel Speed affects stamina reduction. Fish performs 2-3 struggle styles (e.g., Rapid Thrashing, Deep Dive) triggering QTEs (Quick Tap, Hold/Release, Swipe Chain). QTE failure increases tension to 90%. Fish caught when stamina is 0. 
```javascript
// ReelInMinigameScene.js
// update() {
//   if (input.isMouseDown) { fish.stamina -= player.getEffectiveAttribute('reelSpeed'); tension += X; }
//   else { tension -= Y; }
//   if (tension >= MAX_TENSION) { /* line_snap_event */ }
//   if (fish.stamina <= 0) { /* fish_caught_event */ }
//   checkForQTETRIGGER(fish.currentStruggleStyle);
// }
// onQTEFail() { tension = MAX_TENSION * 0.9; }
```

# Test Strategy:
Test tension meter mechanics (increase/decrease, snapping). Verify fish stamina reduction and catch condition. Implement and test a few QTE types and struggle styles. Validate player attribute effects (Tension Stability, Reel Speed). Playtest for balance and engagement.
