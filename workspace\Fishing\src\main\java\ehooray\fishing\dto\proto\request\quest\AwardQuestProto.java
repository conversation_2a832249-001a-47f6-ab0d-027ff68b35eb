// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/quest/AwardQuest.proto

package ehooray.fishing.dto.proto.request.quest;

public final class AwardQuestProto {
  private AwardQuestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AwardQuestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.quest.AwardQuest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 questEntityId = 1;</code>
     * @return The questEntityId.
     */
    long getQuestEntityId();
  }
  /**
   * Protobuf type {@code request.quest.AwardQuest}
   */
  public static final class AwardQuest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.quest.AwardQuest)
      AwardQuestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AwardQuest.newBuilder() to construct.
    private AwardQuest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AwardQuest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AwardQuest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.internal_static_request_quest_AwardQuest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.internal_static_request_quest_AwardQuest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.class, ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.Builder.class);
    }

    public static final int QUESTENTITYID_FIELD_NUMBER = 1;
    private long questEntityId_;
    /**
     * <code>int64 questEntityId = 1;</code>
     * @return The questEntityId.
     */
    @java.lang.Override
    public long getQuestEntityId() {
      return questEntityId_;
    }

    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.quest.AwardQuest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.quest.AwardQuest)
        ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.internal_static_request_quest_AwardQuest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.internal_static_request_quest_AwardQuest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.class, ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        questEntityId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.internal_static_request_quest_AwardQuest_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest build() {
        ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest buildPartial() {
        ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest result = new ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest(this);
        result.questEntityId_ = questEntityId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long questEntityId_ ;
      /**
       * <code>int64 questEntityId = 1;</code>
       * @return The questEntityId.
       */
      @java.lang.Override
      public long getQuestEntityId() {
        return questEntityId_;
      }
      /**
       * <code>int64 questEntityId = 1;</code>
       * @param value The questEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setQuestEntityId(long value) {
        
        questEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 questEntityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestEntityId() {
        
        questEntityId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.quest.AwardQuest)
    }

    // @@protoc_insertion_point(class_scope:request.quest.AwardQuest)
    private static final ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest();
    }

    public static ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<AwardQuest>
        PARSER = new com.google.protobuf.AbstractParser<AwardQuest>() {
      @java.lang.Override
      public AwardQuest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<AwardQuest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AwardQuest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.quest.AwardQuestProto.AwardQuest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_quest_AwardQuest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_quest_AwardQuest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\036request/quest/AwardQuest.proto\022\rreques" +
      "t.quest\"#\n\nAwardQuest\022\025\n\rquestEntityId\030\001" +
      " \001(\003BT\n\'ehooray.fishing.dto.proto.reques" +
      "t.questB\017AwardQuestProtoH\002\252\002\025Fishing.Net" +
      "work.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_quest_AwardQuest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_quest_AwardQuest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_quest_AwardQuest_descriptor,
        new java.lang.String[] { "QuestEntityId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
