package ehooray.fishing.service.exchange;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.ExchangeEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.ExchangeCsv;
import ehooray.fishing.repository.ExchangeRepository;
import ehooray.fishing.repository.FishItemRepository;
import ehooray.fishing.repository.PlayerItemRepository;

@Service
public class ExchangeService
{
    @Autowired
    protected CsvContainer csvContainer;
    @Autowired
    protected ExchangeRepository exchangeRepository;
    @Autowired
    protected PlayerItemRepository playerItemRepository;
    @Autowired
    protected FishItemRepository fishItemRepository;
    
    public List<ExchangeEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getExchanges();
    }
    
    public Optional<ExchangeEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }
    
    public ExchangeEntity create(PlayerEntity player, ExchangeCsv.Row exchangeRow, LocalDateTime now)
    {
        var newExchange = new ExchangeEntity();
        newExchange.setCsvId(exchangeRow.getId());
        newExchange.setStartTime(now);
        newExchange.setPlayer(player);
        
        getAllByPlayer(player).add(newExchange);
        exchangeRepository.save(newExchange);

        return newExchange;
    }

    public void exchangeItem(ExchangeEntity exchange, int exchangeCount) throws ExceptionByCsvId
    {
        var exchangeRow = csvContainer.getCsv(ExchangeCsv.class).getById(exchange.getCsvId());
        int newCount = exchange.getBuyCount() + exchangeCount;
        checkExchangeLimit(exchangeRow, newCount);
        
        exchange.setBuyCount(newCount);
        exchangeRepository.save(exchange);
    }
    
    public void deleteAll(PlayerEntity player, List<ExchangeEntity> shopEntities)
    {
        getAllByPlayer(player).removeAll(shopEntities);
        exchangeRepository.deleteAll(shopEntities);
    }
    
    protected void checkExchangeLimit(ExchangeCsv.Row exchangeRow, int count) throws ExceptionByCsvId
    {
        if(exchangeRow.ignoreExchangeLimit()) return;

        if(count > exchangeRow.getExchangeLimit())
            throw new ExceptionByCsvId("exchange count over limit, count = " + count + ", limit = " + exchangeRow.getExchangeLimit());
    }
}
