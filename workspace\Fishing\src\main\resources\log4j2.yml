configuration:
    status: warn
    properties:
        property: 
        - name: "logPattern"
          value: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%t] [%X{PlayerId}] [%X{RequestId}] %class{1}:%M:%L - %m%n"
    appenders:
        console:
            name: "console"
            patternLayout:
                pattern: ${logPattern}
        rollingFile:
            - name: "infoRolling"
              fileName: "logs/info-current.log"
              filePattern: "logs/info-%d{yyyy-MM-dd}-%i.log"
              patternLayout:
                  pattern: ${logPattern} 
              policies:
                  timeBasedTriggeringPolicy: # every day will rolling
                      interval: 1                      
                  sizeBasedTriggeringPolicy:
                      size: "1 GB"
              defaultRolloverStrategy:
                  max: 10
                  delete:
                      basePath: "logs"
                      ifFileName:
                          glob: "info-*.log"
                      ifLastModified:
                          age: "180D"
            - name: "exceptionRolling"
              fileName: "logs/exception-current.log"
              filePattern: "logs/exception-%d{yyyy-MM-dd}-%i.log"
              patternLayout:
                  pattern: ${logPattern} 
              policies:
                  timeBasedTriggeringPolicy: # every day will rolling
                      interval: 1
                  sizeBasedTriggeringPolicy:
                      size: "1 GB"
              defaultRolloverStrategy:
                  max: 10
                  delete:
                      basePath: "logs"
                      ifFileName:
                          glob: "exception-*.log"
                      ifLastModified:
                          age: "180D"
                          
            - name: "checkMaintainRolling"
              fileName: "logs/check-maintain-current.log"
              filePattern: "logs/check-maintain-%d{yyyy-MM-dd}-%i.log"
              patternLayout:
                  pattern: ${logPattern} 
              policies:
                  timeBasedTriggeringPolicy: # every day will rolling
                      interval: 1
                  sizeBasedTriggeringPolicy:
                      size: "1 GB"
              defaultRolloverStrategy:
                  max: 10
                  delete:
                      basePath: "logs"
                      ifFileName:
                          glob: "check-maintain-*.log"
                      ifLastModified:
                          age: "180D"
            - name: "checkClientPatchRolling"
              fileName: "logs/check-client-patch-current.log"
              filePattern: "logs/check-client-patch-%d{yyyy-MM-dd}-%i.log"
              patternLayout:
                  pattern: ${logPattern} 
              policies:
                  timeBasedTriggeringPolicy: # every day will rolling
                      interval: 1
                  sizeBasedTriggeringPolicy:
                      size: "1 GB"
              defaultRolloverStrategy:
                  max: 10
                  delete:
                      basePath: "logs"
                      ifFileName:
                          glob: "check-client-patch-*.log"
                      ifLastModified:
                          age: "180D"
            
        
    loggers:
        root:
            level: "debug"
            appenderRef:
            - ref: "console"
              level: "info"
            - ref: "infoRolling"
              level: "info"
            - ref: "exceptionRolling"
              level: "warn"
        logger: 
            # entity manager log
            - name: "org.springframework.orm.jpa"
              level: "debug"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "exceptionRolling"
                    level: "warn"
                    
            # jpa sql statement log     
            - name: "org.hibernate.SQL"
              level: "debug"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "exceptionRolling"
                    level: "warn"
            - name: "org.hibernate.type.descriptor.sql.BasicBinder"
              level: "trace"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "exceptionRolling"
                    level: "warn"
            # http security
            - name: "org.springframework.security"
              level: "debug"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "exceptionRolling"
                    level: "warn"
            # our schedule logger
            - name: "ehooray.fishing.component.logger.CheckMaintainScheduleLogger"
              level: "debug"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "checkMaintainRolling"
                    level: "debug"
                  - ref: "exceptionRolling"
                    level: "warn"
            - name: "ehooray.fishing.component.logger.CheckClientPatchScheduleLogger"
              level: "debug"
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "checkClientPatchRolling"
                    level: "debug"
                  - ref: "exceptionRolling"
                    level: "warn"
            # JTA        
            - name: "com.atomikos.jdbc.AbstractDataSourceBean"
              level: "warn"# Slf4jLogger will always output info when level
              appenderRef:
                  - ref: "console"
                    level: "warn"
                  - ref: "infoRolling"
                    level: "warn"
                  - ref: "exceptionRolling"
                    level: "warn"

