// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/fishrod/SellFishRod.proto

package ehooray.fishing.dto.proto.request.fishrod;

public final class SellFishRodProto {
  private SellFishRodProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SellFishRodOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.fishrod.SellFishRod)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();
  }
  /**
   * Protobuf type {@code request.fishrod.SellFishRod}
   */
  public static final class SellFishRod extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.fishrod.SellFishRod)
      SellFishRodOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SellFishRod.newBuilder() to construct.
    private SellFishRod(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SellFishRod() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SellFishRod();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.internal_static_request_fishrod_SellFishRod_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.internal_static_request_fishrod_SellFishRod_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.class, ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.fishrod.SellFishRod}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.fishrod.SellFishRod)
        ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRodOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.internal_static_request_fishrod_SellFishRod_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.internal_static_request_fishrod_SellFishRod_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.class, ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.internal_static_request_fishrod_SellFishRod_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod build() {
        ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod buildPartial() {
        ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod result = new ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod(this);
        result.entityId_ = entityId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.fishrod.SellFishRod)
    }

    // @@protoc_insertion_point(class_scope:request.fishrod.SellFishRod)
    private static final ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod();
    }

    public static ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SellFishRod>
        PARSER = new com.google.protobuf.AbstractParser<SellFishRod>() {
      @java.lang.Override
      public SellFishRod parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SellFishRod> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SellFishRod> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.fishrod.SellFishRodProto.SellFishRod getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_fishrod_SellFishRod_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_fishrod_SellFishRod_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!request/fishrod/SellFishRod.proto\022\017req" +
      "uest.fishrod\"\037\n\013SellFishRod\022\020\n\010entityId\030" +
      "\001 \001(\003BW\n)ehooray.fishing.dto.proto.reque" +
      "st.fishrodB\020SellFishRodProtoH\002\252\002\025Fishing" +
      ".Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_fishrod_SellFishRod_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_fishrod_SellFishRod_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_fishrod_SellFishRod_descriptor,
        new java.lang.String[] { "EntityId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
