// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/fishrod/UpgradeFishRod.proto

package ehooray.fishing.dto.proto.request.fishrod;

public final class UpgradeFishRodProto {
  private UpgradeFishRodProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UpgradeFishRodOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.fishrod.UpgradeFishRod)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 targetEntityId = 1;</code>
     * @return The targetEntityId.
     */
    long getTargetEntityId();

    /**
     * <code>repeated int64 marteials = 2;</code>
     * @return A list containing the marteials.
     */
    java.util.List<java.lang.Long> getMarteialsList();
    /**
     * <code>repeated int64 marteials = 2;</code>
     * @return The count of marteials.
     */
    int getMarteialsCount();
    /**
     * <code>repeated int64 marteials = 2;</code>
     * @param index The index of the element to return.
     * @return The marteials at the given index.
     */
    long getMarteials(int index);
  }
  /**
   * Protobuf type {@code request.fishrod.UpgradeFishRod}
   */
  public static final class UpgradeFishRod extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.fishrod.UpgradeFishRod)
      UpgradeFishRodOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpgradeFishRod.newBuilder() to construct.
    private UpgradeFishRod(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpgradeFishRod() {
      marteials_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpgradeFishRod();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.internal_static_request_fishrod_UpgradeFishRod_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.internal_static_request_fishrod_UpgradeFishRod_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.class, ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.Builder.class);
    }

    public static final int TARGETENTITYID_FIELD_NUMBER = 1;
    private long targetEntityId_;
    /**
     * <code>int64 targetEntityId = 1;</code>
     * @return The targetEntityId.
     */
    @java.lang.Override
    public long getTargetEntityId() {
      return targetEntityId_;
    }

    public static final int MARTEIALS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList marteials_;
    /**
     * <code>repeated int64 marteials = 2;</code>
     * @return A list containing the marteials.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getMarteialsList() {
      return marteials_;
    }
    /**
     * <code>repeated int64 marteials = 2;</code>
     * @return The count of marteials.
     */
    public int getMarteialsCount() {
      return marteials_.size();
    }
    /**
     * <code>repeated int64 marteials = 2;</code>
     * @param index The index of the element to return.
     * @return The marteials at the given index.
     */
    public long getMarteials(int index) {
      return marteials_.getLong(index);
    }
    private int marteialsMemoizedSerializedSize = -1;

    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.fishrod.UpgradeFishRod}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.fishrod.UpgradeFishRod)
        ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRodOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.internal_static_request_fishrod_UpgradeFishRod_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.internal_static_request_fishrod_UpgradeFishRod_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.class, ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        targetEntityId_ = 0L;

        marteials_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.internal_static_request_fishrod_UpgradeFishRod_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod build() {
        ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod buildPartial() {
        ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod result = new ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod(this);
        int from_bitField0_ = bitField0_;
        result.targetEntityId_ = targetEntityId_;
        if (((bitField0_ & 0x00000001) != 0)) {
          marteials_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.marteials_ = marteials_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private long targetEntityId_ ;
      /**
       * <code>int64 targetEntityId = 1;</code>
       * @return The targetEntityId.
       */
      @java.lang.Override
      public long getTargetEntityId() {
        return targetEntityId_;
      }
      /**
       * <code>int64 targetEntityId = 1;</code>
       * @param value The targetEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetEntityId(long value) {
        
        targetEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 targetEntityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetEntityId() {
        
        targetEntityId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList marteials_ = emptyLongList();
      private void ensureMarteialsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          marteials_ = mutableCopy(marteials_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @return A list containing the marteials.
       */
      public java.util.List<java.lang.Long>
          getMarteialsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(marteials_) : marteials_;
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @return The count of marteials.
       */
      public int getMarteialsCount() {
        return marteials_.size();
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @param index The index of the element to return.
       * @return The marteials at the given index.
       */
      public long getMarteials(int index) {
        return marteials_.getLong(index);
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @param index The index to set the value at.
       * @param value The marteials to set.
       * @return This builder for chaining.
       */
      public Builder setMarteials(
          int index, long value) {
        ensureMarteialsIsMutable();
        marteials_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @param value The marteials to add.
       * @return This builder for chaining.
       */
      public Builder addMarteials(long value) {
        ensureMarteialsIsMutable();
        marteials_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @param values The marteials to add.
       * @return This builder for chaining.
       */
      public Builder addAllMarteials(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureMarteialsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, marteials_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 marteials = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMarteials() {
        marteials_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.fishrod.UpgradeFishRod)
    }

    // @@protoc_insertion_point(class_scope:request.fishrod.UpgradeFishRod)
    private static final ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod();
    }

    public static ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UpgradeFishRod>
        PARSER = new com.google.protobuf.AbstractParser<UpgradeFishRod>() {
      @java.lang.Override
      public UpgradeFishRod parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UpgradeFishRod> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpgradeFishRod> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.fishrod.UpgradeFishRodProto.UpgradeFishRod getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_fishrod_UpgradeFishRod_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_fishrod_UpgradeFishRod_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$request/fishrod/UpgradeFishRod.proto\022\017" +
      "request.fishrod\";\n\016UpgradeFishRod\022\026\n\016tar" +
      "getEntityId\030\001 \001(\003\022\021\n\tmarteials\030\002 \003(\003BZ\n)" +
      "ehooray.fishing.dto.proto.request.fishro" +
      "dB\023UpgradeFishRodProtoH\002\252\002\025Fishing.Netwo" +
      "rk.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_fishrod_UpgradeFishRod_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_fishrod_UpgradeFishRod_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_fishrod_UpgradeFishRod_descriptor,
        new java.lang.String[] { "TargetEntityId", "Marteials", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
