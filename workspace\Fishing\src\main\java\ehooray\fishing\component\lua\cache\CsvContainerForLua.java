package ehooray.fishing.component.lua.cache;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.lua.ILuaCacheComponent;
import ehooray.fishing.pojo.csv.ACsv;
import ehooray.fishing.pojo.lua.CatchExceptionOneArgFunction;

@Component
public class CsvContainerForLua implements ILuaCacheComponent
{
    @Autowired
    protected CsvContainer csvContainer;

    protected LuaTable cache;

    @Override
    public void initial() throws Exception
    {
        cache = LuaValue.tableOf();
        for (ACsv<?> csv : csvContainer.getCsvCollection())
            cache.set(csv.getClass().getSimpleName(), csvToLuaTable(csv));
    }

    protected LuaTable csvToLuaTable(ACsv<?> csv) throws Exception
    {
        LuaTable luaTable = LuaValue.tableOf();
        // put new function for lua callback at here
        luaTable.set(GetById.class.getSimpleName(), new GetById(csv));

        return luaTable;
    }

    @Override
    public LuaTable toLua()
    {
        return cache;
    }

    public class GetById extends CatchExceptionOneArgFunction
    {
        protected ACsv<?> csv;
        protected GetById(ACsv<?> csv) { this.csv = csv; }
        @Override
        public LuaValue doCall(LuaValue arg) throws Exception
        {
            return csv.getById(arg.toint()).toLua();
        }
    }

}
