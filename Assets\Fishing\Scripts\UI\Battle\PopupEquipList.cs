using Cysharp.Threading.Tasks;
using Ehooray;
using Ehooray.UI;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.UI;
using Fishing.UI;
using Zenject;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/PrepareCanvas/PopupEquipList")]
    public class PopupEquipList : GamePanel
    {
        public Button closeButton;
        public Button confirmButton;

        public List<EquipList> equipList;
        private CancellationToken cancellationTokenDestroy;
        private FishKitData currentSelectKit;

        #region Zenject Inject
        FishingContainer fishingContainer;
        FishingRepositories fishingRepositories;
        IResourceProvider resourceProvider;
        AssetPathConfig assetPathConfig;
        FishingGamePanelManager fishingGamePanelManager;
        BattleEquipeData battleEquipeData;
        TextIDConfig textIDConfig;
        #endregion

        [Inject]
        public void Construct(FishingRepositories fishingRepositories,
                                IResourceProvider resourceProvider,
                                AssetPathConfig assetPathConfig,
                                FishingContainer fishingContainer,
                                FishingGamePanelManager fishingGamePanelManager,
                                BattleEquipeData battleEquipeData,
                                TextIDConfig textIDConfig)
        {
            this.fishingRepositories = fishingRepositories;
            this.resourceProvider = resourceProvider;
            this.assetPathConfig = assetPathConfig;
            this.fishingContainer = fishingContainer;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.battleEquipeData = battleEquipeData;
            this.textIDConfig = textIDConfig;
        }
        private void Awake()
        {
            closeButton.onClick.AddListener(Close);
            cancellationTokenDestroy = this.GetCancellationTokenOnDestroy();
            confirmButton.onClick.AddListener(Confirm);

        }
        public void Close()
        {
            gamePanelManager.Close<PopupEquipList>();
        }
        public void Refresh()
        {
            var allKit = fishingRepositories.fishKitRepository.GetFishKits();
            int i = 0;
            equipList[0].title.text = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.Kit1).Content;
            equipList[1].title.text = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.Kit2).Content;
            equipList[2].title.text = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.Kit3).Content;
            equipList[3].title.text = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.Kit4).Content;
            equipList[4].title.text = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.Kit5).Content;
            foreach (var kit in allKit.Values)
            {
                var rod = fishingRepositories.fishRodRepository.GetFishRod(kit.fishRodEntityId);
                var fishline = fishingRepositories.playerItemRepository.GetPlayerItem(kit.fishLineCsvId);
                var bait = fishingRepositories.playerItemRepository.GetPlayerItem(kit.baitCsvId);

                SetIcon(equipList[i].rodIcon.PlayerItemIcon, rod.CsvData.Icon);
                SetIcon(equipList[i].lineIcon.PlayerItemIcon, fishline.CsvData.Icon);
                SetIcon(equipList[i].baitIcon.PlayerItemIcon, bait.CsvData.Icon);
                equipList[i].rodIcon.IsLock(rod.isLock);

                equipList[i].rodIcon.SetRare((ItemTableDataExtension.Rare)rod.CsvData.Rare);
                equipList[i].lineIcon.SetRare((ItemTableDataExtension.Rare)fishline.CsvData.Rare);
                equipList[i].baitIcon.SetRare((ItemTableDataExtension.Rare)bait.CsvData.Rare);

                equipList[i].rodIcon.RewardCountText.text = $"{rod.times}/{rod.CsvData.Times}";
                equipList[i].lineIcon.RewardCountText.text = $"{fishline.finalCount}";
                equipList[i].baitIcon.RewardCountText.text = $"{bait.finalCount}";

                if (kit.isActive)
                {
                    equipList[i].thisButton.onClick.Invoke();
                    var activeEquipment = fishingContainer.tableContainer.LanguageTable.GetData(textIDConfig.ActiveEquipment).Content;
                    equipList[i].title.text = activeEquipment;
                    currentSelectKit = kit;
                }
                //var btn = equipList[i];
                equipList[i].SetSelectEvent(()=> 
                {
                    currentSelectKit = kit;
                });
                i++;
            }
        }
        public void SetIcon(Image img, string path)
        {
            UniTask.Void(async () =>
            {
                var cacheAsset = await resourceProvider.LoadResourceAsync<Sprite>(assetPathConfig.PictureItemPath + path).AttachExternalCancellation(cancellationToken: cancellationTokenDestroy);
                if (img != null) // 可能會非同步途中會被destroy
                {
                    img.sprite = cacheAsset.Object;
                }
            });
        }
        public void Confirm() 
        {
            if (fishingRepositories.fishKitRepository.GetFishKit(currentSelectKit.entityId).isActive)
            {
                Close();
                return;
            }
            UniTask.Void(async()=> 
            {
                await ActiveFishKitAsync();
                Close();
                if (fishingGamePanelManager.TryGetGamePanel(out PrepareManager prepareManager))
                    prepareManager.RefreshIcon();
                battleEquipeData.SetEquipData();
                battleEquipeData.LoadRodModel();
            });
        }

        public async UniTask ActiveFishKitAsync()
        {
            var activeFishKit = new  Network.Proto.ActiveFishKit();
            activeFishKit.FishKit = currentSelectKit.entityId;
            await fishingContainer.networkService.fishKitService.ActiveFishKit(activeFishKit);
            var result = await fishingContainer.networkService.fishKitService.GetActiveFishKit();
            foreach (var kit in fishingRepositories.fishKitRepository.GetFishKits().Values)
            {
                if (kit.entityId == result.EntityId)
                    kit.isActive = true;
                else
                    kit.isActive = false;
            }
        }
    }
}
