package ehooray.fishing.component.init;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.LocalDateTime;

import org.apache.http.conn.util.InetAddressUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import ehooray.fishing.service.server.ServerInfoService;

@Component
@Order(RunnerOrder.CheckServer)
public class CheckServerRunner implements ApplicationRunner
{
    @Value("${game.check-server.check-ip-url}")
    protected String checkServerIpUrl;

    @Autowired
    protected ServerInfoService serverInfoService;
    
    protected boolean isInitialFinished;

    @Override
    @Transactional(rollbackFor = Throwable.class, timeoutString = "${game.transactional.timeout}")
    public void run(ApplicationArguments args) throws Exception
    {
        // send request
        HttpRequest httpRequest = HttpRequest.newBuilder()
              .uri(new URI(checkServerIpUrl))
              .build();

        HttpResponse<String> response = HttpClient.newHttpClient().send(httpRequest, BodyHandlers.ofString());

        String ip = response.body().trim();
        if(InetAddressUtils.isIPv4Address(ip))
            serverInfoService.setMyIp(ip, LocalDateTime.now());
        else
            throw new Exception("parsing result error : " + response.toString() + ", body = " + response.body());
        
        isInitialFinished = true;
    }
    
    public boolean isInitialFinished()
    {
        return isInitialFinished;
    }

}
