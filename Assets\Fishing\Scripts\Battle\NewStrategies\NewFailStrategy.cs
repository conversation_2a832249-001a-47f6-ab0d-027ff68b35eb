using Cysharp.Threading.Tasks;
using Fishing.UI;
using UnityEngine;

namespace Fishing.NewStrategies
{
    /// <summary>
    /// 新釣魚失敗狀態策略
    /// 處理新張力系統下的失敗情況和UI處理
    /// </summary>
    public class NewFailStrategy : FishingStrategyBase
    {
        public NewFailStrategy() : base(FishingState.Fail) { }
        
        public override async UniTask OnEnter(FishingManager context)
        {
            // 設置魚的狀態
            context.fish.ChangeContinueShowFish();
            context.fish.useSkill = false;
            context.continueManager.SetFishInfo();
            
            // 處理UI元素
            context.cordReelManager.ResetCordReel();
            context.fishingGamePanelManager.Close<CordReelManager>();
            context.fishingGamePanelManager.Close<PausePanelManager>();
            
            // 關閉新的張力管理器
            if (context.fishingGamePanelManager.TryGetGamePanel(out NewLineTensionManager newLineTensionManager))
            {
                newLineTensionManager.CloseRoot();
                context.fishingGamePanelManager.Close<NewLineTensionManager>();
            }
            
            context.fishingGamePanelManager.Close<FishInfoManager>();
            
            // 打開繼續面板
            var panel = await context.fishingGamePanelManager.OpenAsync<ContinueManager>();
            panel.Refresh();
            
            // 可在此添加新的失敗特效或音效
        }
        
        public override void OnUpdate(FishingManager context)
        {
            // 失敗狀態無需特殊更新邏輯
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 