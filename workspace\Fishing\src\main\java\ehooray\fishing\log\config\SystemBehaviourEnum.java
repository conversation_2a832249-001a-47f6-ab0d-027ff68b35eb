package ehooray.fishing.log.config;

import java.util.Arrays;
import java.util.Optional;

public enum SystemBehaviourEnum
{
    // ap
    ApCounterController_count(10001),
    
    // FishRod
    FishRodController_repairFishRod(20001),
    FishRodController_upgradeFishRod(20002),
    FishRodController_upgradeFishRod_removeMaterials(20003),
    FishRodController_sellFishRod(20004),
    
    // Battle
    BattleController_winFish(30001),
    BattleController_failFish(30002),
    BattleController_retryFish(30003),
    
    // Gacha
    GachaController_buyGacha(40001),
    
    // FishItem
    FishItemController_sellFish(50001),
    
    // Shop
    ShopController_buyRegularItem(60001),
    
    // Quest
    QuestController_awardQuest(70001),
    
    // Exchange
    ExchangeController_exchangeItem(80001),
    ExchangeController_exchangeFish(80002),
    
    // Preset
    PresetData(90001),
    
    ;
    
    protected int id;
    private SystemBehaviourEnum(int id)
    {
        this.id = id;
    }
    
    public int getId()
    {
        return id;
    }
    
    public int getSystemId()
    {
        return id / 10000;
    }
    
    public static Optional<SystemBehaviourEnum> valueOf(int id)
    {
        return Arrays.stream(SystemBehaviourEnum.values()).filter(x-> x.getId() == id).findFirst();
    }
}
