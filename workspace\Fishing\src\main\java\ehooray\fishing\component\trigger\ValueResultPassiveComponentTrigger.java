package ehooray.fishing.component.trigger;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.component.BasePassiveComponent;
import ehooray.fishing.pojo.csv.ComponentCsv;


@Component
public class ValueResultPassiveComponentTrigger extends BaseTrigger
{
    @Autowired
    protected BeanFactory beanFactory;

    /**
     * Trigger all component rows with BasePassiveComponent implementation,
     * then compute value result
     * @return list of BasePassiveComponent implementation invoke result
     */
    @Override
    public Object invoke(BaseTrigger.Input input, List<ComponentCsv.Row> componentRows) throws Exception
    {
        List<Object[]> results = new ArrayList<>();
        for(ComponentCsv.Row componentRow : componentRows)
        {
            Class<?> invoker = ComponentCsv.Main.valueOf(componentRow.getMain()).get().getInvoker();
            BasePassiveComponent passiveComponent = (BasePassiveComponent)beanFactory.getBean(invoker);

            Object[] result = (Object[])passiveComponent.invoke(input, componentRow);
            results.add(result);
        }
        return results;
    }

}
