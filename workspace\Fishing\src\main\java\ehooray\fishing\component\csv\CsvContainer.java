  package ehooray.fishing.component.csv;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import org.springframework.stereotype.Component;

import ehooray.fishing.pojo.csv.ACsv;

@Component
public class CsvContainer
{
    protected HashMap<Class<?>, ACsv<?>> csvMap = new HashMap<Class<?>, ACsv<?>>();
    
    // key = file Name, value = script string
    protected HashMap<String, String> luaMap = new HashMap<String, String>();
    
    public void addCsv(List<ACsv<?>> csvList)
    {
        for(ACsv<?> csv : csvList)
            csvMap.put(csv.getClass(), csv);
    }
    
    @SuppressWarnings("unchecked")
    public <TCsv> TCsv getCsv(Class<TCsv> csvClass)
    {
        return (TCsv)csvMap.get(csvClass);
    }
    
    public Collection<ACsv<?>> getCsvCollection()
    {
        return csvMap.values();
    }
    
    public void addLua(String fileName, String luaScript)
    {
        luaMap.put(fileName, luaScript);
    }
    
    public String getLua(String fileName)
    {
        return luaMap.get(fileName);
    }
}
