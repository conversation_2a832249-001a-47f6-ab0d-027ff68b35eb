package ehooray.fishing.component.protomap.model;

import org.springframework.stereotype.Component;

import com.google.common.base.Strings;

import ehooray.fishing.dto.proto.model.IabTransactionProto;
import ehooray.fishing.entity.IabTransactionEntity;

@Component
public class IabTransactionProtoMap
{

    public IabTransactionProto.IabTransaction.Builder toBuilderByEntity(IabTransactionEntity iabTransaction)
    {
        IabTransactionProto.IabTransaction.Builder builder = IabTransactionProto.IabTransaction.newBuilder();
        builder.setEntityId(iabTransaction.getId());
        builder.setCsvId(iabTransaction.getCsvId());
        builder.setState(iabTransaction.getState());
        if(!Strings.isNullOrEmpty(iabTransaction.getTransactionId()))
            builder.setTransactionId(iabTransaction.getTransactionId());
        if(!Strings.isNullOrEmpty(iabTransaction.getException()))
            builder.setException(iabTransaction.getException());

        return builder;
    }


}
