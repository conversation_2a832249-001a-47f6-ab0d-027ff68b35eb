package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
//@formatter:off
@Table
(
    name = "player_item",
    indexes = 
    {
        @Index(name = "csvId", columnList = "csvId"),
    }
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class PlayerItemEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected int count;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    private PlayerEntity player;// do not get player by child
    
    public long getId()
    {
        return id;
    }

    public int getCsvId()
    {
        return csvId;
    }

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }

    public int getCount()
    {
        return count;
    }

    public void setCount(int count)
    {
        this.count = count;
    }
    
    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
}
