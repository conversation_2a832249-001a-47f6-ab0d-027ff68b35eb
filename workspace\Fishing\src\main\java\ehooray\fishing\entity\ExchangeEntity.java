package ehooray.fishing.entity;

import java.time.LocalDateTime;
import java.util.Optional;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
//@formatter:off
@Table
(
  name = "exchange",
  indexes =
  {
      @Index(name = "csvId", columnList = "csvId"),
  }
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class ExchangeEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    protected int buyCount;
    protected LocalDateTime startTime;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    protected PlayerEntity player;

    public long getId()
    {
        return id;
    }
    
    public void setId(long id)
    {
        this.id = id;
    }
    
    public int getCsvId()
    {
        return csvId;
    }

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }
    
    public int getBuyCount()
    {
        return buyCount;
    }

    public void setBuyCount(int buyCount)
    {
        this.buyCount = buyCount;
    }
    
    public Optional<LocalDateTime> getStartTime()
    {
        return Optional.ofNullable(startTime);
    }

    public void setStartTime(LocalDateTime startTime)
    {
        this.startTime = startTime;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }

    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
}
