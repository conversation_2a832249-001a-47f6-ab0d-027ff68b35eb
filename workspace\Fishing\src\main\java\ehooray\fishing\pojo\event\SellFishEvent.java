package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.FishItemEntity;
import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class SellFishEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final FishItemEntity fishItem;

    public SellFishEvent(Object source, PlayerEntity player, FishItemEntity fishItem)
    {
        super(source);
        this.player = player;
        this.fishItem = fishItem;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }

    public FishItemEntity getFishItem()
    {
        return fishItem;
    }
}
