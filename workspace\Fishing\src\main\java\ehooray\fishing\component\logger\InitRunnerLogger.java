package ehooray.fishing.component.logger;

import org.apache.logging.log4j.LogManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.service.server.ServerInfoService;

@Component
@Aspect
public class InitRunnerLogger
{
    @Autowired 
    protected ServerInfoService serverInfoService;
    
    @After(value = "execution(* org.springframework.boot.ApplicationRunner.run(*))")
    public void afterAllRun(JoinPoint joinPoint)
    {
        LogManager.getLogger(this.getClass()).info(joinPoint.getThis() + " init finished");
    }
    
    @After(value = "execution(* ehooray.fishing.component.init.CheckServerRunner.run(*))")
    public void afterRun(JoinPoint joinPoint)
    {
        LogManager.getLogger(this.getClass()).info("My ip = " + serverInfoService.getMyIp());
    }
}