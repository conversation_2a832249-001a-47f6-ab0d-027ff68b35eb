package ehooray.fishing.service.shop;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
//import ehooray.fishing.component.shop.ShopQuestTimeCalculator;
//import ehooray.fishing.entity.CharacterEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.ShopEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.CommodityCsv;
import ehooray.fishing.pojo.csv.ShopCsv;
import ehooray.fishing.pojo.event.BuyShopItemEvent;
//import ehooray.fishing.pojo.event.GainShopQuestEvent;
import ehooray.fishing.repository.ShopRepository;

@Service
public class ShopService
{

//    public enum QuestState
//    {
//        Idle(0),
//        InQuest(1),
//        Finish(2)
//        ;
//        protected int id;
//        private QuestState(int id)
//        {
//            this.id = id;
//        }
//
//        public int getId()
//        {
//            return id;
//        }
//
//        public static Optional<QuestState> valueOf(int id)
//        {
//            return Arrays.stream(QuestState.values()).filter(x-> x.getId() == id).findFirst();
//        }
//    }


    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected ShopRepository shopRepository;

//    @Autowired
//    protected ShopQuestTimeCalculator questTimeCalculator;
    
    @Autowired
    protected ApplicationEventPublisher publisher;

    public List<ShopEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getShops();
    }

    public Optional<ShopEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }

    public ShopEntity create(PlayerEntity player, ShopCsv.Row shopRow, CommodityCsv.Row commodityRow, LocalDateTime now) throws Exception
    {
        ShopEntity nweShop = new ShopEntity();
        nweShop.setCsvId(shopRow.getId());
        nweShop.setPlayer(player);
        nweShop.setCommodityCsvId(commodityRow.getId());
        nweShop.setStartTime(now);
        //nweShop.setQuestState(QuestState.Idle.getId());

        getAllByPlayer(player).add(nweShop);
        shopRepository.save(nweShop);

        return nweShop;
    }

    public void deleteAll(PlayerEntity player, List<ShopEntity> shopEntities)
    {
        getAllByPlayer(player).removeAll(shopEntities);
        shopRepository.deleteAll(shopEntities);
    }


    protected void checkBuyLimit(CommodityCsv.Row commodityRow, int count) throws ExceptionByCsvId
    {
        if(commodityRow.ignoreBuyLimit()) return;

        if(count > commodityRow.getBuyLimit())
            throw new ExceptionByCsvId("purchased count over limit, count = " + count + ", limit = " + commodityRow.getBuyLimit());
    }

    public void buyShopItem(ShopEntity shop, int buyCount) throws ExceptionByCsvId
    {
        CommodityCsv.Row commodityRow = csvContainer.getCsv(CommodityCsv.class).getById(shop.getCommodityCsvId());
        int newCount = shop.getBuyCount() + buyCount;
        checkBuyLimit(commodityRow, newCount);

        shop.setBuyCount(newCount);
        shopRepository.save(shop);
        
        publisher.publishEvent(new BuyShopItemEvent(this, shop.getPlayer()));
    }

//    public void startQuest(
//            PlayerEntity player,
//            ShopEntity shop, CharacterEntity dispatchCharacter,
//            LocalDateTime now) throws Exception
//    {
//        if(shop.getQuestState() != QuestState.Idle.getId()
//        && shop.getQuestState() != QuestState.Finish.getId())
//            throw new ExceptionByCsvId("shop quest state is incorrect");
//
//        shop.setDispatchCharacterId(dispatchCharacter.getId());
//        shop.setQuestFinishTime(questTimeCalculator.calculate(player, shop, dispatchCharacter, now));
//        shop.setQuestState(QuestState.InQuest.getId());
//
//        shopRepository.save(shop);
//    }
//
//    public void gainByQuest(ShopEntity shop, LocalDateTime now) throws Exception
//    {
//        if(shop.getQuestState() != QuestState.InQuest.getId())
//            throw new ExceptionByCsvId("shop quest state is incorrect");
//
//        if(now.isBefore(shop.getQuestFinishTime().get()))
//            throw new ExceptionByCsvId("it's not time for gaining shop quest");
//
//        int newCount = shop.getBuyCount() + 1;
//        shop.setBuyCount(newCount);
//        shop.setDispatchCharacterId(0);
//        shop.setQuestFinishTime(null);
//        shop.setQuestState(QuestState.Finish.getId());
//
//        shopRepository.save(shop);
//        
//        publisher.publishEvent(new GainShopQuestEvent(this, shop.getPlayer()));
//    }
//
//    public void questSpeedUp(ShopEntity shop, LocalDateTime now) throws Exception
//    {
//        if(shop.getQuestState() != QuestState.InQuest.getId())
//            throw new ExceptionByCsvId("shop quest state is incorrect");
//
//        if(now.isAfter(shop.getQuestFinishTime().get()))
//            throw new ExceptionByCsvId("it's not time for shop quest speed up");
//
//        shop.setQuestFinishTime(now);
//
//        shopRepository.save(shop);
//    }

}
