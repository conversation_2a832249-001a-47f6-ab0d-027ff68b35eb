package ehooray.fishing.service.auth;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.auth.BasicAuthTokenConverter;
import ehooray.fishing.entity.PlayerAuthEntity;

@Service
public class HttpSecurityUserDetailService implements UserDetailsService
{
    @Autowired
    protected PlayerAuthService playerAuthService;
    
    @Autowired
    protected BasicAuthTokenConverter tokenConverter;

    @Override
    public UserDetails loadUserByUsername(String accountCombination) throws UsernameNotFoundException
    {
        Pair<Integer, String> accountCombinationValues = tokenConverter.unpackAccountCombination(accountCombination);
        int serverId = accountCombinationValues.getValue0();
        String account = accountCombinationValues.getValue1();
        Optional<PlayerAuthEntity> playerAuth = playerAuthService.findByAccountAndServerId(account, serverId);
        if (playerAuth.isEmpty()) throw new BadCredentialsException(account + " not found");

        return new User(account, playerAuth.get().getToken(), getAuthorities(Arrays.asList("Player")));
    }

    protected List<GrantedAuthority> getAuthorities(List<String> roles)
    {
        ArrayList<GrantedAuthority> authorities = new ArrayList<GrantedAuthority>();
        for (String role : roles)
            authorities.add(new SimpleGrantedAuthority(role));
        
        return authorities;
    }
}
