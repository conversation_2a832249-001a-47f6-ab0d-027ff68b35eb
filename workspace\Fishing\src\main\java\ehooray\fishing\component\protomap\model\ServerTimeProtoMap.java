package ehooray.fishing.component.protomap.model;

import java.time.LocalDateTime;
import java.time.ZoneId;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.ServerTimeProto;

@Component
public class ServerTimeProtoMap
{

    public ServerTimeProto.ServerTime.Builder toBuilder(LocalDateTime now)
    {
        ServerTimeProto.ServerTime.Builder builder = ServerTimeProto.ServerTime.newBuilder();
        builder.setTime(now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        return builder;
    }

    public Any toAny(LocalDateTime now)
    {
        return Any.pack(toBuilder(now).build());
    }

}
