using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Zenject;
using Cysharp.Threading.Tasks;

namespace Fishing
{
    public class AnimationEvent : MonoBehaviour
    {
        #region Zenject Inject
        FishingManager fishingManager;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager)
        {
            this.fishingManager = fishingManager;
        }
        public void FinishCatchAnimate()
        {
            fishingManager.FinishCatchAnimate().Forget();
        }
    }
}


