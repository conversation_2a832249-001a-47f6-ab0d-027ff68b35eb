package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table
(
    name = "achievement"
)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class AchievementEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int questType;
    protected int parameter_1;
    protected int parameter_2;
    protected int parameter_3;
    protected int parameter_4;
    protected int parameter_5;
    protected int targetCount;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    protected PlayerEntity player;
    
    public void setId(long id)
    {
        this.id = id;
    }
    public long getId()
    {
        return id;
    }    
    
    public int getQuestType()
    {
        return questType;
    }
    public void setQuestType(int questType)
    {
        this.questType = questType;
    }
    
    public int getParameter_1()
    {
        return parameter_1;
    }
    public void setParameter_1(int parameter_1)
    {
        this.parameter_1 = parameter_1;
    }
    public int getParameter_2()
    {
        return parameter_2;
    }
    public void setParameter_2(int parameter_2)
    {
        this.parameter_2 = parameter_2;
    }
    public int getParameter_3()
    {
        return parameter_3;
    }
    public void setParameter_3(int parameter_3)
    {
        this.parameter_3 = parameter_3;
    }
    public int getParameter_4()
    {
        return parameter_4;
    }
    public void setParameter_4(int parameter_4)
    {
        this.parameter_4 = parameter_4;
    }
    public int getParameter_5()
    {
        return parameter_5;
    }
    public void setParameter_5(int parameter_5)
    {
        this.parameter_5 = parameter_5;
    }
    
    public int getTargetCount()
    {
        return targetCount;
    }

    public void setTargetCount(int targetCount)
    {
        this.targetCount = targetCount;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
}
