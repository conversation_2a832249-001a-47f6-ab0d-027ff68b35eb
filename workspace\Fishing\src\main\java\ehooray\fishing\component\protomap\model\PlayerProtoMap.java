package ehooray.fishing.component.protomap.model;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.PlayerProto;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.service.player.PlayerService;

@Component
public class PlayerProtoMap
{

    public PlayerProto.Player.Builder toBuilderByEntity(PlayerEntity player)
    {
        PlayerProto.Player.Builder builder = PlayerProto.Player.newBuilder();
        builder.setAccount(player.getPlayerAuth().getAccount());
        builder.setEntityId(player.getId());
        builder.setFinalExp(player.getExp());
        builder.setFinalLevel(player.getLevel());
        builder.setName(player.getName());
        builder.setServerId(player.getPlayerAuth().getServerId());
        
        return builder;
    }
    
    public PlayerProto.Player.Builder toBuilderByDiffExp(PlayerService.DiffExp diffExp)
    {
        PlayerProto.Player.Builder builder = toBuilderByEntity(diffExp.getPlayer());
        builder.setDiffExp(diffExp.getDiffExp());
        builder.setDiffLevel(diffExp.getDiffLevel());
        
        return builder;
    }
    
    public Any toAnyByEntity(PlayerEntity player)
    {
        return Any.pack(toBuilderByEntity(player).build());
    }

}
