<!-- Generated by SpecStory -->

# Debugging InvalidOperationException in Unity (2025-05-12 15:33:32)

_**User**_

InvalidOperationException: Sequence contains no matching element
System.Linq.Enumerable.First[TSource] (System.Collections.Generic.IEnumerable`1[T] source, System.Func`2[T,TResult] predicate) (at <df4e462ab02c4b6493ccedf77f247b33>:0)
Ehooray.UI.GamePanelManager.LoadAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:46)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Ehooray.UI.GamePanelManager.OpenAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:65)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Fishing.NewFishingGameBootstrap.PreloadTemporaryPanel[T] () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:96)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource.GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:211)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.PreloadUIPanels () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:86)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.InitializeAsync () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:61)
UnityEngine.Debug:LogException(Exception)
Cysharp.Threading.Tasks.UniTaskScheduler:PublishUnobservedTaskException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskScheduler.cs:90)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoidMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskVoidMethodBuilder.cs:53)
Fishing.<InitializeAsync>d__13:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:73)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoid`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:104)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Fishing.<PreloadUIPanels>d__14:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:91)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(AsyncUnit) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetResult() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:204)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetResult() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:70)
Fishing.<PreloadTemporaryPanel>d__15`1:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:99)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.UI.<OpenAsync>d__11`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:69)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.UI.<LoadAsync>d__10`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:59)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__5`1:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceProvider.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__21`1:MoveNext() (at Assets/Ehooray/Runtime/File/ResourceProvider.cs:69)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySignalCompletion(UniTaskStatus) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:909)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:794)
Ehooray.ResourceRunner`1:SetResult(Resource`1) (at Assets/Ehooray/Runtime/File/ResourceRunner.cs:48)
Ehooray.<RunAsync>d__4:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceLoader.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:194)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:PostLateUpdate() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:163)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:130)



---

_**Assistant**_



---

让我查看一下相关文件，找出错误原因。

---

Read file: Assets/Ehooray/Runtime/UI/GamePanelManager.cs

---

现在让我查看启动Bootstrap文件：

---

Read file: Assets/Fishing/Scripts/NewFishingGameBootstrap.cs

---

我想检查游戏面板的实现和AssetInfoAttribute的定义：

---

<details>
            <summary>Grep search for "class AssetInfoAttribute" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Ehooray/Runtime/File/AssetInfo.cs` | L5 | `public class AssetInfoAttribute : Attribute` |

</details>

---

Read file: Assets/Ehooray/Runtime/File/AssetInfo.cs

---

现在我需要检查PausePanelManager类，看看它是否正确设置了AssetInfoAttribute：

---

<details>
            <summary>Grep search for "class PausePanelManager" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/UI/Battle/PausePanelManager.cs` | L14 | `public class PausePanelManager : GamePanel` |

</details>

---

Read file: Assets/Fishing/Scripts/UI/Battle/PausePanelManager.cs

---

我想查看游戏的其他面板类是否都有AssetInfoAttribute：

---

Could not find file 'Assets/Fishing/Scripts/UI/Battle/GameEndManager.cs'. Did you mean one of:
- Assets/Fishing/Scripts/UI/Battle/GameEnd/GameEndManager.cs

---

Read file: Assets/Fishing/Scripts/UI/Battle/GameEnd/GameEndManager.cs

---

<details>
            <summary>Grep search for "class FishInfoManager" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/UI/Battle/FishInfo/FishInfoManager.cs` | L27 | `public class FishInfoManager : GamePanel` |

</details>

---

<no message>

---

Read file: Assets/Fishing/Scripts/UI/Battle/FishInfo/FishInfoManager.cs

---

让我检查一下那个缺少AssetInfoAttribute的面板：

---

<details>
            <summary>Grep search for "class ThrowBaitManager" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/UI/Battle/ThrowBaitBar/ThrowBaitManager.cs` | L16 | `public class ThrowBaitManager : GamePanel` |

</details>

---

<no message>

---

Read file: Assets/Fishing/Scripts/UI/Battle/ThrowBaitBar/ThrowBaitManager.cs

---

让我们检查一下CanvasScalar组件在面板上的存在情况：

---

<details>
            <summary>Grep search for "class NewLineTensionManager" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/UI/Battle/LineTensionBar/NewLineTensionManager.cs` | L16 | `public class NewLineTensionManager : GamePanel` |
| `file:///d%3A/Work/fishing-client/.specstory/history/2025-04-28_06-37-%E9%87%A3%E9%AD%9A%E5%B0%88%E6%A1%88%E5%8A%9F%E8%83%BD%E5%B7%AE%E7%95%B0%E6%AA%A2%E8%A6%96.md` | L966 | `+     public class NewLineTensionManager : GamePanel` |

</details>

---

<no message>

---

Read file: Assets/Fishing/Scripts/UI/Battle/LineTensionBar/NewLineTensionManager.cs

---

让我们检查LureActionSystem面板，看是否它缺少AssetInfoAttribute：

---

<details>
            <summary>Grep search for "class LureActionSystem" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/Fish/LureActionSystem.cs` | L8 | `public class LureActionSystem : GamePanel` |
| `file:///d%3A/Work/fishing-client/.specstory/history/2025-05-07_09-32-%E6%B7%BB%E5%8A%A0qte%E7%B3%BB%E7%B5%B1%E7%9A%84%E8%A8%8E%E8%AB%96.md` | L256 | `+ public class LureActionSystem : MonoBehaviour` |
| `file:///d%3A/Work/fishing-client/.specstory/history/2025-05-07_09-32-%E6%B7%BB%E5%8A%A0qte%E7%B3%BB%E7%B5%B1%E7%9A%84%E8%A8%8E%E8%AB%96.md` | L558 | `public class LureActionSystem : MonoBehaviour` |
| `file:///d%3A/Work/fishing-client/Assets/Fishing/Scripts/Fish/LureActionSystem.cs` | L8 | `public class LureActionSystem : GamePanel` |

</details>

---

<no message>

---

Read file: Assets/Fishing/Scripts/Fish/LureActionSystem.cs

---

我找到了问题所在。检查下`LureActionSystem`类：

---

Read file: Assets/Fishing/Scripts/Fish/LureActionSystem.cs

---

现在我已经找到问题所在，需要添加适当的`AssetInfoAttribute`到`LureActionSystem`类：

---

```diff
  using Ehooray.UI;
  using Fishing;
+ using Ehooray;
  
+ [AssetInfo("Prefabs/UI/Battle/LureActionCanvas")]
  public class LureActionSystem : GamePanel
  {
```

---

根据错误信息和代码分析，我找到了问题所在：`LureActionSystem`类缺少了`AssetInfoAttribute`特性，这导致在`GamePanelManager.LoadAsync<TGamePanel>`方法中执行`attributes.First((attr) => attr is AssetInfoAttribute)`语句时抛出了`Sequence contains no matching element`异常。

我已经修复了这个问题，通过为`LureActionSystem`类添加了`AssetInfoAttribute`特性，并指定了资源路径为`"Prefabs/UI/Battle/LureActionCanvas"`。你需要确保这个路径是正确的，指向游戏中对应的面板预制体。

现在当游戏尝试加载这个面板时，它将能够正确找到资源，而不会抛出异常。如果你想要确认这个修复是否有效，可以重新运行游戏来测试。

如果其他面板也遇到类似问题，也需要检查它们是否都正确添加了`AssetInfoAttribute`特性，并且资源路径是否正确。

---

_**User**_

@LureActionSystem.cs Exception: Resource `Prefabs/UI/Battle/LureActionCanvas` failed to load.
Ehooray.ResourceProvider.HandleResourceLoaded[T] (Ehooray.Resource`1[TResource] resource) (at Assets/Ehooray/Runtime/File/ResourceProvider.cs:222)
Ehooray.ResourceProvider.LoadResourceAsync[T] (System.String path) (at Assets/Ehooray/Runtime/File/ResourceProvider.cs:67)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Ehooray.AddressableResourceProvider.LoadResourceAsync[T] (System.String path) (at Assets/Ehooray/Runtime/File/AddressableResourceProvider.cs:43)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Ehooray.UI.GamePanelManager.LoadAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:51)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Ehooray.UI.GamePanelManager.OpenAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:65)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource`1[T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:255)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Fishing.NewFishingGameBootstrap.PreloadTemporaryPanel[T] () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:96)
Cysharp.Threading.Tasks.UniTask+ExceptionResultSource.GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.Factory.cs:211)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.PreloadUIPanels () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:86)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.InitializeAsync () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:61)
UnityEngine.Debug:LogException(Exception)
Cysharp.Threading.Tasks.UniTaskScheduler:PublishUnobservedTaskException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskScheduler.cs:90)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoidMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskVoidMethodBuilder.cs:53)
Fishing.<InitializeAsync>d__13:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:73)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoid`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:104)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Fishing.<PreloadUIPanels>d__14:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:91)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(AsyncUnit) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetResult() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:204)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetResult() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:70)
Fishing.<PreloadTemporaryPanel>d__15`1:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:99)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.UI.<OpenAsync>d__11`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:69)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(PausePanelManager) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.UI.<LoadAsync>d__10`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:59)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__5`1:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceProvider.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__21`1:MoveNext() (at Assets/Ehooray/Runtime/File/ResourceProvider.cs:69)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySignalCompletion(UniTaskStatus) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:909)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:794)
Ehooray.ResourceRunner`1:SetResult(Resource`1) (at Assets/Ehooray/Runtime/File/ResourceRunner.cs:48)
Ehooray.<RunAsync>d__4:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceLoader.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:194)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:PostLateUpdate() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:163)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:130)



---

_**Assistant**_



---

看起来我们解决了属性缺失的问题，但现在有一个新的错误：资源路径无法找到。错误信息显示 `Resource 'Prefabs/UI/Battle/LureActionCanvas' failed to load`，这意味着系统无法找到指定路径下的预制体。

我们需要修正`AssetInfoAttribute`中的路径，确保它指向正确的预制体位置。根据错误信息，我们需要检查并修改LureActionSystem类中的资源路径。

请尝试以下步骤：

1. 在Unity编辑器中找到实际的LureActionSystem预制体
2. 检查其正确路径
3. 修改AssetInfoAttribute以匹配实际路径

让我为LureActionSystem.cs文件做出必要的修改：

---

```diff
  using Ehooray;
  
- [AssetInfo("Prefabs/UI/Battle/LureActionCanvas")]
+ [AssetInfo("Prefabs/UI/Battle/LureAction")]
  public class LureActionSystem : GamePanel
  {
```

---

我们修改了资源路径，将`LureActionCanvas`改为`LureAction`，因为通常预制体可能会省略"Canvas"后缀。

如果这个修改后仍然无法找到资源，你需要在Unity编辑器中执行以下步骤找到确切的路径：

1. 在Project视图中搜索"LureAction"相关的预制体
2. 右键点击找到的预制体，选择"Copy Path"获取其相对路径
3. 确认预制体是否在Addressables系统中注册
4. 使用正确的路径再次修改代码

还有一种可能性是，这个预制体需要先被创建并添加到Addressables系统中。如果是这种情况，你需要：

1. 创建一个带有LureActionSystem组件的预制体
2. 将其保存到Addressables目录中
3. 确保路径与代码中的AssetInfo路径匹配

请尝试运行游戏，看是否修复了问题。如果仍有问题，可能需要检查资源管理系统的配置或其他游戏面板是如何正确加载的。

---

_**User**_

ZenjectException: Unable to resolve 'CordReelManager' while building object with type 'LureActionSystem'. Object graph:
LureActionSystem

Zenject.DiContainer.Resolve (Zenject.InjectContext context) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:1044)
Zenject.DiContainer.CallInjectMethodsTopDown (System.Object injectable, System.Type injectableType, Zenject.InjectTypeInfo typeInfo, System.Collections.Generic.List`1[T] extraArgs, Zenject.InjectContext context, System.Object concreteIdentifier, System.Boolean isDryRun) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:1501)
Zenject.DiContainer.InjectExplicitInternal (System.Object injectable, System.Type injectableType, System.Collections.Generic.List`1[T] extraArgs, Zenject.InjectContext context, System.Object concreteIdentifier) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:1638)
Zenject.DiContainer.InjectExplicit (System.Object injectable, System.Type injectableType, System.Collections.Generic.List`1[T] extraArgs, Zenject.InjectContext context, System.Object concreteIdentifier) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:1466)
Zenject.DiContainer.InjectGameObjectForComponentExplicit (UnityEngine.GameObject gameObject, System.Type componentType, System.Collections.Generic.List`1[T] extraArgs, Zenject.InjectContext context, System.Object concreteIdentifier) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:2363)
Zenject.DiContainer.InstantiatePrefabForComponentExplicit (System.Type componentType, UnityEngine.Object prefab, System.Collections.Generic.List`1[T] extraArgs, Zenject.InjectContext context, System.Object concreteIdentifier, Zenject.GameObjectCreationParameters gameObjectBindInfo) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:3392)
Zenject.DiContainer.InstantiatePrefabForComponentExplicit (System.Type componentType, UnityEngine.Object prefab, System.Collections.Generic.List`1[T] extraArgs, Zenject.GameObjectCreationParameters gameObjectBindInfo) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:3371)
Zenject.DiContainer.InstantiatePrefabForComponent (System.Type concreteType, UnityEngine.Object prefab, System.Collections.Generic.IEnumerable`1[T] extraArgs, Zenject.GameObjectCreationParameters creationInfo) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:2173)
Zenject.DiContainer.InstantiatePrefabForComponent (System.Type concreteType, UnityEngine.Object prefab, UnityEngine.Transform parentTransform, System.Collections.Generic.IEnumerable`1[T] extraArgs) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:2162)
Zenject.DiContainer.InstantiatePrefabForComponent[T] (UnityEngine.Object prefab, UnityEngine.Transform parentTransform) (at Assets/Plugins/Zenject/Source/Main/DiContainer.cs:2117)
Ehooray.UI.GamePanelFactory.Create (UnityEngine.Object prefab, UnityEngine.Transform transform) (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:140)
Zenject.IFactoryProvider`3[TParam1,TParam2,TContract].GetAllInstancesWithInjectSplit (Zenject.InjectContext context, System.Collections.Generic.List`1[T] args, System.Action& injectAction, System.Collections.Generic.List`1[T] buffer) (at Assets/Plugins/Zenject/Source/Providers/IFactoryProvider.cs:153)
Zenject.IProviderExtensions.GetAllInstances (Zenject.IProvider creator, Zenject.InjectContext context, System.Collections.Generic.List`1[T] args, System.Collections.Generic.List`1[T] buffer) (at Assets/Plugins/Zenject/Source/Providers/IProviderExtensions.cs:31)
Zenject.IProviderExtensions.GetInstance (Zenject.IProvider creator, Zenject.InjectContext context, System.Collections.Generic.List`1[T] args) (at Assets/Plugins/Zenject/Source/Providers/IProviderExtensions.cs:83)
Zenject.PlaceholderFactoryBase`1[TValue].CreateInternal (System.Collections.Generic.List`1[T] extraArgs) (at Assets/Plugins/Zenject/Source/Factories/PlaceholderFactoryBase.cs:32)
Rethrow as ZenjectException: Error during construction of type 'GamePanel' via GamePanel.Factory.Create method!
Zenject.PlaceholderFactoryBase`1[TValue].CreateInternal (System.Collections.Generic.List`1[T] extraArgs) (at Assets/Plugins/Zenject/Source/Factories/PlaceholderFactoryBase.cs:45)
Zenject.PlaceholderFactory`3[TParam1,TParam2,TValue].Create (TParam1 param1, TParam2 param2) (at Assets/Plugins/Zenject/Source/Factories/PlaceholderFactory.cs:73)
Ehooray.UI.GamePanelManager.LoadAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:53)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2[TStateMachine,T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:342)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Ehooray.UI.GamePanelManager.OpenAsync[TGamePanel] () (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:65)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2[TStateMachine,T].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:342)
Cysharp.Threading.Tasks.UniTask`1+Awaiter[T].GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:653)
Fishing.NewFishingGameBootstrap.PreloadTemporaryPanel[T] () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:96)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.PreloadUIPanels () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:86)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1[TResult].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:244)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1[TStateMachine].GetResult (System.Int16 token) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:218)
Cysharp.Threading.Tasks.UniTask+Awaiter.GetResult () (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:312)
Fishing.NewFishingGameBootstrap.InitializeAsync () (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:61)
UnityEngine.Debug:LogException(Exception)
Cysharp.Threading.Tasks.UniTaskScheduler:PublishUnobservedTaskException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskScheduler.cs:90)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoidMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskVoidMethodBuilder.cs:53)
Fishing.<InitializeAsync>d__13:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:73)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskVoid`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:104)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Fishing.<PreloadUIPanels>d__14:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:91)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:210)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:59)
Fishing.<PreloadTemporaryPanel>d__15`1:MoveNext() (at Assets/Fishing/Scripts/NewFishingGameBootstrap.cs:98)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:334)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:186)
Ehooray.UI.<OpenAsync>d__11`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:68)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:167)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:334)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetException(Exception) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:186)
Ehooray.UI.<LoadAsync>d__10`1:MoveNext() (at Assets/Ehooray/Runtime/UI/GamePanelManager.cs:58)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__5`1:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceProvider.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder`1:SetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/AsyncUniTaskMethodBuilder.cs:201)
Ehooray.<LoadResourceAsync>d__21`1:MoveNext() (at Assets/Ehooray/Runtime/File/ResourceProvider.cs:69)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation(Object) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTask.cs:21)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySignalCompletion(UniTaskStatus) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:909)
Cysharp.Threading.Tasks.UniTaskCompletionSource`1:TrySetResult(Resource`1) (at ./Library/PackageCache/com.cysharp.unitask/Runtime/UniTaskCompletionSource.cs:794)
Ehooray.ResourceRunner`1:SetResult(Resource`1) (at Assets/Ehooray/Runtime/File/ResourceRunner.cs:48)
Ehooray.<RunAsync>d__4:MoveNext() (at Assets/Ehooray/Runtime/File/AddressableResourceLoader.cs:44)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`1:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/CompilerServices/StateMachineRunner.cs:189)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:194)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:PostLateUpdate() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:163)
Cysharp.Threading.Tasks.Internal.ContinuationQueue:Run() (at ./Library/PackageCache/com.cysharp.unitask/Runtime/Internal/ContinuationQueue.cs:130)



---

_**Assistant**_



---

