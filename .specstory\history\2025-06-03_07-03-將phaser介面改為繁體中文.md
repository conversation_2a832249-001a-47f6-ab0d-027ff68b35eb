<!-- Generated by SpecStory -->

# 將phaser介面改為繁體中文 (2025-06-03 15:03:11)

_**User**_

請幫我把phaser介面資訊顯示從英文改為繁體中文

---

_**Assistant**_

我来帮您将Phaser界面信息从英文改为繁体中文。首先让我查看一下项目的结构，了解需要修改的文件。

---

<details>
            <summary>Listed current directory • **36** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `.vscode` |
| 📄 `Luxury_Angler_GDD_Unity_zhtw.txt` |
| 📄 `package-lock.json` |
| 📁 `node_modules` |
| 📄 `Luxury_Angler_GDD(updated).txt` |
| 📄 `EQUIPMENT_SLOTS_REFERENCE.md` |
| 📄 `test-quest-system.html` |
| 📄 `test-map-selection.html` |
| 📄 `test-inventory-fix.html` |
| 📄 `test-fish-database.html` |
| 📄 `test-chatroom-fix.html` |
| 📄 `test-equipment-enhancement.html` |
| 📄 `test-cabin-dialog-fix.html` |
| 📄 `test-cabin-audio-fix.html` |
| 📄 `test-bikini-assistant-chatroom.html` |
| 📄 `test-boatmenu-fix.html` |
| 📄 `test-album-system.html` |
| 📁 `src` |
| 📄 `package.json` |
| 📁 `public` |
| 📁 `docs` |
| 📄 `RENJS_QUEST_INTEGRATION.md` |
| 📄 `BIKINI_ASSISTANT_CHATROOM.md` |
| 📄 `todo.md` |
| 📄 `Luxury_Angler_GDD_Unity.txt` |
| 📄 `test-casting.html` |
| 📁 `.specstory` |
| 📁 `.cursor` |
| 📄 `.gitignore` |
| 📄 `.cursorindexingignore` |
| 📄 `test-simple.html` |
| 📄 `vite.config.js` |
| 📄 `index.html` |
| 📄 `README.md` |
| 📄 `Editor _ Mermaid Chart-2025-05-24-214451.png` |

</details>

---

<details>
            <summary>Listed directory src • **7** results</summary>
        
| Name |
|-------|
| 📁 `ui` |
| 📁 `scripts` |
| 📁 `scenes` |
| 📁 `data` |
| 📄 `main.js` |
| 📁 `libs` |
| 📁 `assets` |

</details>

---

<details>
            <summary>Listed directory src/scenes • **12** results</summary>
        
| Name |
|-------|
| 📄 `SettingsScene.js` |
| 📄 `GameScene.js` |
| 📄 `BoatMenuScene.js` |
| 📄 `ShopScene.js` |
| 📄 `QuestScene.js` |
| 📄 `HUDScene.js` |
| 📄 `MenuScene.js` |
| 📄 `CabinScene.js` |
| 📄 `DialogScene.js` |
| 📄 `AlbumScene.js` |
| 📄 `PreloadScene.js` |
| 📄 `BootScene.js` |

</details>

---

<details>
            <summary>Listed directory src/ui • **13** results</summary>
        
| Name |
|-------|
| 📄 `UITheme.js` |
| 📄 `TimeWeatherUI.js` |
| 📄 `InventoryUI.js` |
| 📄 `PlayerProgressionUI.js` |
| 📄 `EquipmentEnhancementUI.js` |
| 📄 `CraftingUI.js` |
| 📄 `RenJsDebugUI.js` |
| 📄 `ShopUI.js` |
| 📄 `LoadingStateManager.js` |
| 📄 `MapSelectionUI.js` |
| 📄 `AudioSettingsUI.js` |
| 📄 `AchievementPopupSystem.js` |
| 📄 `FishCollectionUI.js` |

</details>

---

现在让我查看一些关键文件，了解当前的文本内容，然后进行中文化。首先查看UITheme.js，这个文件可能包含了通用的UI文本：

---

Read file: src/ui/UITheme.js

---

现在让我查看一些主要场景文件，了解需要翻译的文本内容。先从MenuScene开始，然后查看其他重要场景：

---

Read file: src/scenes/BoatMenuScene.js

---

让我搜索一些关键的文本内容，了解需要翻译的具体文本：

---

<details>
            <summary>Grep search for "LUXURY ANGLER|Premium Fishing|BOAT MENU|SETTINGS|EXIT|Use WASD" • **8** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/UITheme.js` | L1 | `* Centralized UI Theme System for Luxury Angler` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/MapSelectionUI.js` | L386 | `// Update the boat menu status` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L13 | `// Animation settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L14 | `this.animationSettings = {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L195 | `duration: this.animationSettings.rotation.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L196 | `ease: this.animationSettings.rotation.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L197 | `repeat: this.animationSettings.rotation.repeat` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L461 | `duration: this.animationSettings.fadeIn.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L462 | `ease: this.animationSettings.fadeIn.ease` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L478 | `duration: this.animationSettings.fadeOut.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L479 | `ease: this.animationSettings.fadeOut.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L499 | `duration: this.animationSettings.fadeIn.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L500 | `ease: this.animationSettings.fadeIn.ease` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L513 | `duration: this.animationSettings.fadeOut.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/LoadingStateManager.js` | L514 | `ease: this.animationSettings.fadeOut.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/InventoryUI.js` | L41 | `// Grid settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L1 | `* Audio Settings UI - User interface for managing audio settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L4 | `* - Audio quality settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L8 | `export class AudioSettingsUI {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L30 | `console.log('AudioSettingsUI: Audio settings interface initialized');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L34 | `* Initialize the audio settings UI` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L39 | `this.createAdvancedSettings();` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L71 | `const title = this.scene.add.text(0, -220, 'Audio Settings', {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L190 | `this.enhancedAudio.baseAudio.updateAudioSettings(key, percentage);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L239 | `this.enhancedAudio.baseAudio.updateAudioSettings('muted', newMuted);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L254 | `* Create advanced settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L256 | `createAdvancedSettings() {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L259 | `// Advanced settings title` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L260 | `const advancedTitle = this.scene.add.text(-280, startY, 'Advanced Settings', {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L267 | `const advancedSettings = [` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L273 | `advancedSettings.forEach((setting, index) => {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L279 | `this.createAudioQualitySelector(-280, startY + 30 + (advancedSettings.length * 40));` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L286 | `const isEnabled = this.enhancedAudio.enhancedSettings[key];` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L315 | `const newValue = !this.enhancedAudio.enhancedSettings[key];` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L316 | `this.enhancedAudio.enhancedSettings[key] = newValue;` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L325 | `console.log(`AudioSettingsUI: ${key} set to ${newValue}`);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L343 | `const currentQuality = this.enhancedAudio.enhancedSettings.audioQuality;` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L367 | `this.enhancedAudio.enhancedSettings.audioQuality = quality;` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L369 | `console.log(`AudioSettingsUI: Audio quality set to ${quality}`);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L587 | `console.log(`AudioSettingsUI: Playing test audio - ${type}: ${sample}`);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L656 | `console.log('AudioSettingsUI: Refreshing audio quality display');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L660 | `* Show the audio settings UI` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L676 | `console.log('AudioSettingsUI: Audio settings panel opened');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L680 | `* Hide the audio settings UI` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L693 | `// Save settings when closing` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L694 | `this.enhancedAudio.saveAudioSettings();` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L698 | `console.log('AudioSettingsUI: Audio settings panel closed');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L713 | `* Destroy the audio settings UI` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L729 | `console.log('AudioSettingsUI: Destroyed');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AudioSettingsUI.js` | L733 | `export default AudioSettingsUI;` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L16 | `// Animation settings` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L17 | `this.animationSettings = {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L256 | `duration: this.animationSettings.slideIn.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L257 | `ease: this.animationSettings.slideIn.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L274 | `duration: this.animationSettings.celebration.duration / 2,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L275 | `ease: this.animationSettings.celebration.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L283 | `duration: this.animationSettings.glow.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L284 | `ease: this.animationSettings.glow.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L384 | `* Remove popup with exit animation` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L392 | `duration: this.animationSettings.slideOut.duration,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/ui/AchievementPopupSystem.js` | L393 | `ease: this.animationSettings.slideOut.ease,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | Lundefined | `export class SettingsManager {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L2 | `this.settings = this.getDefaultSettings();` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L3 | `this.loadSettings();` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L5 | `// Event listeners for settings changes` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L8 | `console.log('SettingsManager: Initialized with settings:', this.settings);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L11 | `getDefaultSettings() {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L69 | `loadSettings() {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L71 | `const savedSettings = localStorage.getItem('luxuryAngler_settings');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L72 | `if (savedSettings) {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L73 | `const parsed = JSON.parse(savedSettings);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L75 | `// Merge with defaults to ensure all settings exist` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L76 | `this.settings = this.mergeSettings(this.getDefaultSettings(), parsed);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L78 | `console.log('SettingsManager: Settings loaded from localStorage');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L79 | `this.emit('settingsLoaded', this.settings);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L83 | `console.error('SettingsManager: Failed to load settings:', error);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L84 | `this.settings = this.getDefaultSettings();` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L89 | `saveSettings() {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L91 | `const settingsData = {` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L92 | `settings: this.settings,` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L97 | `localStorage.setItem('luxuryAngler_settings', JSON.stringify(settingsData));` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L98 | `console.log('SettingsManager: Settings saved to localStorage');` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/scripts/SettingsManager.js` | L99 | `this.emit('settingsSaved', this.settings);` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/main.js` | L8 | `import SettingsScene from './scenes/SettingsScene.js';` |
| `file:///d%3A/dev/Fishing/FishingPhaser/src/main.js` | L34 | `SettingsScene,` |

</details>

---

现在让我创建一个中文化的文本配置文件，然后修改相关的场景文件。首先创建一个中文文本配置文件：

---

```diff
- 
+ /**
+  * 繁體中文文本配置
+  * 集中管理遊戲中所有顯示文本的中文翻譯
+  */
+ export const TextConfig = {
+     // 主選單
+     menu: {
+         title: '豪華釣手',
+         subtitle: '頂級釣魚體驗',
+         boatMenu: '船艙選單',
+         settings: '設定',
+         exit: '離開',
+         instructions: '使用 WASD 移動，空白鍵拋竿'
+     },
+     
+     // 船艙選單
+     boatMenu: {
+         title: '船艙選單',
+         startFishing: '開始釣魚',
+         inventory: '背包',
+         crafting: '製作',
+         shop: '商店',
+         cabin: '船艙',
+         quest: '任務',
+         collection: '收藏',
+         travel: '旅行',
+         tournament: '錦標賽',
+         returnToPort: '返回港口',
+         player: '玩家',
+         welcomeBack: '歡迎回到釣魚之旅！',
+         errorRecovery: '由於錯誤從釣魚返回。遊戲狀態可能已恢復。'
+     },
+     
+     // 設定選單
+     settings: {
+         title: '遊戲設定',
+         audio: '音效設定',
+         graphics: '圖形設定',
+         controls: '控制設定',
+         language: '語言設定',
+         back: '返回',
+         save: '儲存',
+         reset: '重設',
+         volume: '音量',
+         master: '主音量',
+         music: '音樂',
+         sfx: '音效',
+         quality: '品質',
+         fullscreen: '全螢幕',
+         vsync: '垂直同步'
+     },
+     
+     // 音效設定
+     audioSettings: {
+         title: '音效設定',
+         advanced: '進階設定',
+         quality: '音質',
+         low: '低',
+         medium: '中',
+         high: '高',
+         ultra: '超高',
+         spatialAudio: '空間音效',
+         reverbEffects: '混響效果',
+         dynamicRange: '動態範圍',
+         testAudio: '測試音效'
+     },
+     
+     // 背包系統
+     inventory: {
+         title: '背包',
+         equipment: '裝備',
+         items: '物品',
+         fish: '魚類',
+         lures: '魚餌',
+         tools: '工具',
+         materials: '材料',
+         empty: '空的',
+         weight: '重量',
+         capacity: '容量',
+         sort: '排序',
+         filter: '篩選',
+         sell: '出售',
+         use: '使用',
+         equip: '裝備',
+         unequip: '卸下'
+     },
+     
+     // 製作系統
+     crafting: {
+         title: '製作',
+         recipes: '配方',
+         materials: '材料',
+         craft: '製作',
+         craftAll: '全部製作',
+         required: '需要',
+         available: '可用',
+         insufficient: '材料不足',
+         success: '製作成功',
+         failed: '製作失敗',
+         categories: {
+             lures: '魚餌',
+             equipment: '裝備',
+             tools: '工具',
+             consumables: '消耗品'
+         }
+     },
+     
+     // 商店系統
+     shop: {
+         title: '商店',
+         buy: '購買',
+         sell: '出售',
+         price: '價格',
+         quantity: '數量',
+         total: '總計',
+         money: '金錢',
+         insufficient: '金錢不足',
+         purchased: '購買成功',
+         sold: '出售成功',
+         categories: {
+             equipment: '裝備',
+             lures: '魚餌',
+             tools: '工具',
+             materials: '材料'
+         }
+     },
+     
+     // 釣魚系統
+     fishing: {
+         casting: '拋竿',
+         luring: '誘餌',
+         reeling: '收線',
+         caught: '釣到了',
+         escaped: '逃脫了',
+         perfect: '完美',
+         good: '良好',
+         fair: '普通',
+         poor: '糟糕',
+         tension: '張力',
+         stamina: '體力',
+         patience: '耐心',
+         clickToCast: '點擊拋竿',
+         useWASD: '使用 WASD 控制',
+         holdSpace: '按住空白鍵'
+     },
+     
+     // 魚類收藏
+     collection: {
+         title: '魚類收藏',
+         caught: '已釣到',
+         notCaught: '未釣到',
+         rarity: '稀有度',
+         size: '大小',
+         weight: '重量',
+         location: '地點',
+         time: '時間',
+         weather: '天氣',
+         bait: '魚餌',
+         common: '普通',
+         uncommon: '不常見',
+         rare: '稀有',
+         epic: '史詩',
+         legendary: '傳說'
+     },
+     
+     // 任務系統
+     quest: {
+         title: '任務',
+         active: '進行中',
+         completed: '已完成',
+         available: '可接受',
+         locked: '已鎖定',
+         objective: '目標',
+         reward: '獎勵',
+         progress: '進度',
+         accept: '接受',
+         complete: '完成',
+         abandon: '放棄'
+     },
+     
+     // 錦標賽系統
+     tournament: {
+         title: '錦標賽',
+         join: '參加',
+         leave: '離開',
+         leaderboard: '排行榜',
+         rank: '排名',
+         score: '分數',
+         timeLeft: '剩餘時間',
+         prize: '獎品',
+         entry: '報名',
+         fee: '費用',
+         requirements: '要求'
+     },
+     
+     // 玩家進度
+     player: {
+         level: '等級',
+         experience: '經驗值',
+         skill: '技能',
+         stats: '統計',
+         achievements: '成就',
+         profile: '個人資料',
+         fishing: '釣魚',
+         crafting: '製作',
+         trading: '交易',
+         exploration: '探索'
+     },
+     
+     // 通用按鈕和操作
+     common: {
+         ok: '確定',
+         cancel: '取消',
+         yes: '是',
+         no: '否',
+         confirm: '確認',
+         close: '關閉',
+         open: '開啟',
+         save: '儲存',
+         load: '載入',
+         delete: '刪除',
+         edit: '編輯',
+         search: '搜尋',
+         filter: '篩選',
+         sort: '排序',
+         refresh: '重新整理',
+         help: '說明',
+         info: '資訊',
+         warning: '警告',
+         error: '錯誤',
+         success: '成功',
+         loading: '載入中...',
+         processing: '處理中...',
+         connecting: '連接中...',
+         disconnected: '已斷線',
+         retry: '重試',
+         skip: '跳過',
+         next: '下一個',
+         previous: '上一個',
+         continue: '繼續',
+         pause: '暫停',
+         resume: '繼續',
+         start: '開始',
+         stop: '停止',
+         reset: '重設'
+     },
+     
+     // 時間和天氣
+     timeWeather: {
+         time: '時間',
+         weather: '天氣',
+         sunny: '晴天',
+         cloudy: '多雲',
+         rainy: '雨天',
+         stormy: '暴風雨',
+         foggy: '霧天',
+         dawn: '黎明',
+         morning: '早晨',
+         noon: '中午',
+         afternoon: '下午',
+         evening: '傍晚',
+         night: '夜晚',
+         midnight: '午夜'
+     },
+     
+     // 狀態訊息
+     status: {
+         gameStateError: '遊戲狀態錯誤',
+         connectionLost: '連接中斷',
+         savingGame: '儲存遊戲中...',
+         gameLoaded: '遊戲已載入',
+         autoSaveEnabled: '自動儲存已啟用',
+         lowEnergy: '體力不足',
+         inventoryFull: '背包已滿',
+         insufficientFunds: '資金不足',
+         itemNotFound: '找不到物品',
+         actionNotAllowed: '不允許此操作',
+         featureNotAvailable: '功能暫不可用'
+     },
+     
+     // 教學和提示
+     tutorial: {
+         welcome: '歡迎來到豪華釣手！',
+         castingTip: '點擊並拖拽來調整拋竿力度和方向',
+         luringTip: '使用 WASD 鍵移動魚餌吸引魚類',
+         reelingTip: '保持張力平衡，避免魚線斷裂',
+         inventoryTip: '定期整理背包，出售不需要的物品',
+         craftingTip: '收集材料製作更好的裝備',
+         questTip: '完成任務獲得經驗值和獎勵'
+     }
+ };
+ 
+ export default TextConfig; 
```

---

現在讓我修改MenuScene.js文件，使用中文文本配置：

---

```diff
  import GameState from '../scripts/GameState.js';
  import UITheme from '../ui/UITheme.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export default class MenuScene extends Phaser.Scene {
```

```diff
  
          // Title using UITheme
-         const title = UITheme.createText(this, width / 2, height / 4, 'LUXURY ANGLER', 'headerLarge');
+         const title = UITheme.createText(this, width / 2, height / 4, TextConfig.menu.title, 'headerLarge');
          title.setOrigin(0.5);
  
          // Subtitle using UITheme
-         const subtitle = UITheme.createText(this, width / 2, height / 4 + 60, 'Premium Fishing Experience', 'bodyLarge');
+         const subtitle = UITheme.createText(this, width / 2, height / 4 + 60, TextConfig.menu.subtitle, 'bodyLarge');
          subtitle.setOrigin(0.5);
          subtitle.setColor(UITheme.colors.textSecondary);
  
          // Menu buttons using UITheme
-         this.createButton(width / 2, height / 2 - 40, 'BOAT MENU', () => {
+         this.createButton(width / 2, height / 2 - 40, TextConfig.menu.boatMenu, () => {
              this.gameState.audioManager?.playSFX('button');
              this.scene.start('BoatMenuScene');
          });
  
-         this.createButton(width / 2, height / 2 + 40, 'SETTINGS', () => {
+         this.createButton(width / 2, height / 2 + 40, TextConfig.menu.settings, () => {
              this.gameState.audioManager?.playSFX('button');
              this.sceneManager.goToSettings(this);
          });
  
-         this.createButton(width / 2, height / 2 + 120, 'EXIT', () => {
+         this.createButton(width / 2, height / 2 + 120, TextConfig.menu.exit, () => {
              this.gameState.audioManager?.playSFX('button');
              console.log('Exit clicked');
```

```diff
          decorativePanel.setAlpha(0.8);
          
-         const instructionText = UITheme.createText(this, width / 2, height - 50, 'Use WASD to move, SPACE to cast', 'bodyMedium');
+         const instructionText = UITheme.createText(this, width / 2, height - 50, TextConfig.menu.instructions, 'bodyMedium');
          instructionText.setOrigin(0.5);
          instructionText.setColor(UITheme.colors.textSecondary);
```

---

現在讓我修改BoatMenuScene.js文件，將其中的英文文本替換為中文：

---

Read file: src/scenes/BoatMenuScene.js

---

讓我修改BoatMenuScene.js文件，將英文按鈕文本替換為中文：

---

```diff
  import { ShopUI } from '../ui/ShopUI.js';
  import { LoadingStateManager } from '../ui/LoadingStateManager.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export default class BoatMenuScene extends Phaser.Scene {
```

```diff
          
          // Travel button
-         this.buttons.travel = this.createActionButton(50 + buttonSpacing * 0, buttonY, 'TRAVEL', () => {
+         this.buttons.travel = this.createActionButton(50 + buttonSpacing * 0, buttonY, TextConfig.boatMenu.travel, () => {
              this.openTravelMenu();
          }, 0x00aaff);
          
          // Fish button
-         this.buttons.fish = this.createActionButton(50 + buttonSpacing * 1, buttonY, 'FISH', () => {
+         this.buttons.fish = this.createActionButton(50 + buttonSpacing * 1, buttonY, TextConfig.boatMenu.startFishing, () => {
              this.startFishing();
          }, 0x00cc66);
          
          // Cabin button
-         this.buttons.cabin = this.createActionButton(50 + buttonSpacing * 2, buttonY, 'CABIN', () => {
+         this.buttons.cabin = this.createActionButton(50 + buttonSpacing * 2, buttonY, TextConfig.boatMenu.cabin, () => {
              this.openCabin();
          }, 0xff9900);
          
          // Quest button - NEW
-         this.buttons.quest = this.createActionButton(50 + buttonSpacing * 3, buttonY, 'QUEST', () => {
+         this.buttons.quest = this.createActionButton(50 + buttonSpacing * 3, buttonY, TextConfig.boatMenu.quest, () => {
              this.openQuest();
          }, 0x9933ff);
          
          // Inventory button
-         this.buttons.inventory = this.createActionButton(50 + buttonSpacing * 4, buttonY, 'INVENTORY', () => {
+         this.buttons.inventory = this.createActionButton(50 + buttonSpacing * 4, buttonY, TextConfig.boatMenu.inventory, () => {
              this.openInventory();
          }, 0xcc66ff);
          
          // Shop button
-         this.buttons.shop = this.createActionButton(50 + buttonSpacing * 5, buttonY, 'SHOP', () => {
+         this.buttons.shop = this.createActionButton(50 + buttonSpacing * 5, buttonY, TextConfig.boatMenu.shop, () => {
              this.openShop();
          }, 0xffcc00);
          
          // Tournament button (positioned to avoid overlap, only visible in tournament mode)
-         this.buttons.tournament = this.createActionButton(width - 140, buttonY - 60, 'TOURNAMENT', () => {
+         this.buttons.tournament = this.createActionButton(width - 140, buttonY - 60, TextConfig.boatMenu.tournament, () => {
              this.openTournamentMenu();
          }, 0xff6600);
          
          // Mode toggle button - positioned below main buttons with proper spacing
-         this.modeButton = this.createActionButton(width / 2, buttonY + 80, 'STORY MODE', () => {
+         this.modeButton = this.createActionButton(width / 2, buttonY + 80, '故事模式', () => {
              this.toggleMode();
          }, 0x9933cc);
          
          // Return to port button (conditional) - positioned below mode button
-         this.returnButton = this.createActionButton(width / 2, buttonY + 130, 'RETURN TO PORT', () => {
+         this.returnButton = this.createActionButton(width / 2, buttonY + 130, TextConfig.boatMenu.returnToPort, () => {
              this.returnToPort();
          }, 0xff3333);
```

---

現在讓我修改BoatMenuScene.js中的其他英文文本，包括進度顯示和狀態訊息：

---

Read file: src/scenes/BoatMenuScene.js

---

讓我修改BoatMenuScene.js中的進度顯示文本：

---

```diff
          progressHeader.fillRoundedRect(panelX, panelY, panelWidth, 25, { tl: 15, tr: 15, bl: 0, br: 0 });
          
-         this.add.text(panelX + panelWidth / 2, panelY + 12, 'PROGRESSION STATUS', {
+         this.add.text(panelX + panelWidth / 2, panelY + 12, '進度狀態', {
              fontSize: '16px',
              fill: '#ffffff',
```

```diff
          
          // Progress bars with improved styling - positioned within the left panel
-         this.levelProgressBar = this.createProgressBar(panelX + 10, panelY + 35, panelWidth - 20, 'Level Progress', 0x00aaff);
-         this.collectionProgressBar = this.createProgressBar(panelX + 10, panelY + 55, panelWidth - 20, 'Fish Collection', 0xff9900);
+         this.levelProgressBar = this.createProgressBar(panelX + 10, panelY + 35, panelWidth - 20, '等級進度', 0x00aaff);
+         this.collectionProgressBar = this.createProgressBar(panelX + 10, panelY + 55, panelWidth - 20, '魚類收藏', 0xff9900);
      }
  
```

---

現在讓我查看並修改一些狀態訊息和錯誤訊息：

---

Read file: src/scenes/BoatMenuScene.js

---

讓我修改BoatMenuScene.js中的錯誤訊息和歡迎訊息：

---

```diff
              if (data.errorRecovery) {
                  console.log('BoatMenuScene: Running in error recovery mode');
-                 this.showErrorMessage('Returned from fishing due to an error. Game state may have been restored.');
+                 this.showErrorMessage(TextConfig.boatMenu.errorRecovery);
              }
              
```

```diff
          } catch (gameStateError) {
              console.error('BoatMenuScene: Critical error with GameState:', gameStateError);
-             this.showErrorMessage('Game state error: ' + gameStateError.message);
+             this.showErrorMessage(TextConfig.status.gameStateError + ': ' + gameStateError.message);
              
              // Try to recover by reloading the page as last resort
```

```diff
          if (data && data.returnedFromFishing && !data.errorRecovery) {
              this.time.delayedCall(1000, () => {
-                 this.showSuccessMessage('Welcome back from your fishing trip!');
+                 this.showSuccessMessage(TextConfig.boatMenu.welcomeBack);
              });
          }
```

---

現在讓我修改BoatMenuScene.js中的標題文本：

---

```diff
          titleGlow.fillCircle(width / 2, 60, 120);
          
-         const title = this.add.text(width / 2, 60, 'LUXURY ANGLER', {
+         const title = this.add.text(width / 2, 60, TextConfig.menu.title, {
              fontSize: '36px',
              fill: '#ffffff',
```

---

現在讓我修改SettingsScene.js文件：

---

Read file: src/scenes/SettingsScene.js

---

讓我修改SettingsScene.js文件，將英文文本替換為中文：

---

```diff
  import InputManager from '../scripts/InputManager.js';
  import UITheme from '../ui/UITheme.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export default class SettingsScene extends Phaser.Scene {
```

```diff
  
          // Title using UITheme
-         const title = UITheme.createText(this, width / 2, 60, 'SETTINGS', 'headerLarge');
+         const title = UITheme.createText(this, width / 2, 60, TextConfig.settings.title, 'headerLarge');
          title.setOrigin(0.5);
  
```

```diff
  
          // Back button
-         this.createButton(width / 2, height - 60, 'BACK', () => {
+         this.createButton(width / 2, height - 60, TextConfig.settings.back, () => {
              this.scene.start('MenuScene');
          });
```

```diff
  
          // Controls tab
-         this.controlsTab = this.createTab(width / 2 - (tabWidth / 2 + 10), tabY, tabWidth, tabHeight, 'CONTROLS', () => {
+         this.controlsTab = this.createTab(width / 2 - (tabWidth / 2 + 10), tabY, tabWidth, tabHeight, TextConfig.settings.controls, () => {
              this.switchTab('controls');
          });
  
          // Audio tab
-         this.audioTab = this.createTab(width / 2 + (tabWidth / 2 + 10), tabY, tabWidth, tabHeight, 'AUDIO', () => {
+         this.audioTab = this.createTab(width / 2 + (tabWidth / 2 + 10), tabY, tabWidth, tabHeight, TextConfig.settings.audio, () => {
              this.switchTab('audio');
          });
```

---

現在讓我繼續修改SettingsScene.js中的控制設定文本：

---

```diff
  
          // Instructions using UITheme
-         const instructions = UITheme.createText(this, width / 2, y, 'Click on a key binding to change it', 'bodyLarge');
+         const instructions = UITheme.createText(this, width / 2, y, '點擊按鍵綁定來更改設定', 'bodyLarge');
          instructions.setOrigin(0.5);
          instructions.setColor(UITheme.colors.textSecondary);
```

```diff
          // Create binding rows
          const actions = [
-             { key: 'moveUp', label: 'Move Up' },
-             { key: 'moveDown', label: 'Move Down' },
-             { key: 'moveLeft', label: 'Move Left' },
-             { key: 'moveRight', label: 'Move Right' },
-             { key: 'cast', label: 'Cast/Reel' },
-             { key: 'menu', label: 'Menu' },
-             { key: 'inventory', label: 'Inventory' },
-             { key: 'shop', label: 'Shop' }
+             { key: 'moveUp', label: '向上移動' },
+             { key: 'moveDown', label: '向下移動' },
+             { key: 'moveLeft', label: '向左移動' },
+             { key: 'moveRight', label: '向右移動' },
+             { key: 'cast', label: '拋竿/收線' },
+             { key: 'menu', label: '選單' },
+             { key: 'inventory', label: '背包' },
+             { key: 'shop', label: '商店' }
          ];
  
```

---

現在讓我查看並修改一些UI組件文件。先查看InventoryUI.js：

---

Read file: src/ui/InventoryUI.js

---

讓我修改InventoryUI.js的標題文本：

---

```diff
  import UITheme from './UITheme.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export class InventoryUI {
```

```diff
  
          // Title using UITheme
-         const title = UITheme.createText(this.scene, this.width / 2, 30, 'INVENTORY', 'headerLarge');
+         const title = UITheme.createText(this.scene, this.width / 2, 30, TextConfig.inventory.title, 'headerLarge');
          title.setOrigin(0.5);
          this.container.add(title);
```

---

現在讓我查看並修改CraftingUI.js：

---

Read file: src/ui/CraftingUI.js

---

讓我修改CraftingUI.js的標題文本：

---

```diff
  import UITheme from './UITheme.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export class CraftingUI {
```

```diff
  
          // Title using UITheme
-         const title = UITheme.createText(this.scene, this.width / 2, 30, 'BOAT WORKSHOP', 'headerLarge');
+         const title = UITheme.createText(this.scene, this.width / 2, 30, TextConfig.crafting.title, 'headerLarge');
          title.setOrigin(0.5);
          this.container.add(title);
```

---

現在讓我查看並修改ShopUI.js：

---

Read file: src/ui/ShopUI.js

---

讓我修改ShopUI.js的標題文本：

---

```diff
  import { gameDataLoader } from '../scripts/DataLoader.js';
+ import TextConfig from '../data/TextConfig.js';
  
  export class ShopUI {
```

```diff
  
          // Title
-         const title = this.scene.add.text(this.x + this.width / 2, this.y + 25, '🏪 FISHING SHOP', {
+         const title = this.scene.add.text(this.x + this.width / 2, this.y + 25, '🏪 ' + TextConfig.shop.title, {
              fontSize: '20px',
              fill: '#ffffff',
```

```diff
  
          // Money text
-         this.moneyText = this.scene.add.text(this.x + this.width / 2, this.y + 75, `💰 Money: ${this.gameState.player.money} coins`, {
+         this.moneyText = this.scene.add.text(this.x + this.width / 2, this.y + 75, `💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`, {
              fontSize: '16px',
              fill: '#ffffff',
```

```diff
              
              // Update money display
-             this.moneyText.setText(`💰 Money: ${this.gameState.player.money} coins`);
+             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
              
              // Show success notification
```

```diff
          
          // Update money display
-         this.moneyText.setText(`💰 Money: ${this.gameState.player.money} coins`);
+         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
          
          // Refresh shop content
```

```diff
              
              // Update money display
-             this.moneyText.setText(`💰 Money: ${this.gameState.player.money} coins`);
+             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
              
              // Update fish display
```

```diff
          
          // Update money display
-         this.moneyText.setText(`💰 Money: ${this.gameState.player.money} coins`);
+         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
          
          // Mark game state as dirty for saving
```

---

現在讓我修改ShopUI.js中的商店分類文本：

---

Read file: src/ui/ShopUI.js

---

我來繼續完成遊戲的中文化工作。讓我先檢查當前的進度，然後繼續修改剩餘的文件。

---

Read file: src/ui/ShopUI.js

---

我看到ShopUI.js已經部分中文化了，但還有一些商店分類需要繼續修改。讓我繼續完成這個文件的中文化：

---

```diff
  
          // Shop categories using data-driven equipment
-         currentY = this.createShopSection('RODS', currentY, gameDataLoader.getAllRods(), itemSpacing);
+         currentY = this.createShopSection(TextConfig.shop.categories.rods, currentY, gameDataLoader.getAllRods(), itemSpacing);
          currentY += sectionSpacing;
          
-         currentY = this.createShopSection('LURES', currentY, gameDataLoader.getAllLures(), itemSpacing);
+         currentY = this.createShopSection(TextConfig.shop.categories.lures, currentY, gameDataLoader.getAllLures(), itemSpacing);
          currentY += sectionSpacing;
          
-         currentY = this.createShopSection('BOATS', currentY, gameDataLoader.getAllBoats(), itemSpacing);
+         currentY = this.createShopSection(TextConfig.shop.categories.boats, currentY, gameDataLoader.getAllBoats(), itemSpacing);
          currentY += sectionSpacing;
          
-         currentY = this.createShopSection('ITEMS', currentY, this.getConsumableItems(), itemSpacing);
+         currentY = this.createShopSection(TextConfig.shop.categories.items, currentY, this.getConsumableItems(), itemSpacing);
          currentY += sectionSpacing;
          
```

---

現在讓我檢查並修改ShopUI.js中的其他需要中文化的文本：

---

Read file: src/ui/ShopUI.js

---

讓我修改ShopUI.js中的購買功能和魚類庫存部分的文本：

---

```diff
  
          // Money text
-         this.moneyText = this.scene.add.text(this.x + this.width / 2, this.y + 75, `💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`, {
+         this.moneyText = this.scene.add.text(this.x + this.width / 2, this.y + 75, `💰 ${TextConfig.shop.money}: ${this.gameState.player.money} ${TextConfig.shop.currency}`, {
              fontSize: '16px',
              fill: '#ffffff',
```

```diff
          try {
              if (!this.gameState.spendMoney(item.cost)) {
-                 this.showNotification('Not enough money!', 0xe74c3c);
+                 this.showNotification(TextConfig.shop.messages.notEnoughMoney, 0xe74c3c);
                  return;
              }
```

```diff
              
              // Update money display
-             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
+             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} ${TextConfig.shop.currency}`);
              
              // Show success notification
-             this.showNotification(`Purchased: ${item.name}!`, 0x27ae60);
+             this.showNotification(`${TextConfig.shop.messages.purchased}: ${item.name}!`, 0x27ae60);
              
              // Refresh shop display
```

```diff
          } catch (error) {
              console.error('ShopUI: Error purchasing item:', error);
-             this.showNotification('Purchase failed!', 0xe74c3c);
+             this.showNotification(TextConfig.shop.messages.purchaseFailed, 0xe74c3c);
          }
      }
```

```diff
      show() {
          if (!this.gameState.canAccessShop()) {
-             this.scene.showErrorMessage('Shop only available at Starting Port!');
+             this.scene.showErrorMessage(TextConfig.shop.messages.shopOnlyAtPort);
              return;
          }
```

```diff
          
          // Update money display
-         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
+         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} ${TextConfig.shop.currency}`);
          
          // Refresh shop content
```

```diff
          this.scrollContainer.add(titleBg);
          
-         const sectionTitle = this.scene.add.text(this.x + this.width / 2, currentY + 2, 'YOUR FISH INVENTORY', {
+         const sectionTitle = this.scene.add.text(this.x + this.width / 2, currentY + 2, TextConfig.shop.fishInventory.title, {
              fontSize: '18px',
              fill: '#ffffff',
```

```diff
          // If no fish, show message
          if (fishInventory.length === 0) {
-             const noFishText = this.scene.add.text(this.x + this.width / 2, currentY, 'No fish caught yet!\nGo fishing to collect fish to sell.', {
+             const noFishText = this.scene.add.text(this.x + this.width / 2, currentY, TextConfig.shop.fishInventory.noFish, {
                  fontSize: '14px',
                  fill: '#888888',
```

```diff
              this.scrollContainer.add(testFishButton);
              
-             const testFishText = this.scene.add.text(this.x + this.width / 2, currentY + 42, 'ADD TEST FISH', {
+             const testFishText = this.scene.add.text(this.x + this.width / 2, currentY + 42, TextConfig.shop.fishInventory.addTestFish, {
                  fontSize: '12px',
                  fill: '#ffffff',
```

```diff
          this.scrollContainer.add(sellAllButton);
          
-         const sellAllText = this.scene.add.text(this.x + this.width / 2, currentY + 12, 'SELL ALL FISH', {
+         const sellAllText = this.scene.add.text(this.x + this.width / 2, currentY + 12, TextConfig.shop.fishInventory.sellAll, {
              fontSize: '12px',
              fill: '#ffffff',
```

---

現在讓我檢查並修改ShopUI.js中的魚類銷售功能部分：

---

Read file: src/ui/ShopUI.js

---

讓我修改ShopUI.js中的魚類銷售功能部分的文本：

---



---

讓我重新嘗試修改這些特定的文本：

---

```diff
          this.scrollContainer.add(sellButton);
          
-         const sellButtonText = this.scene.add.text(this.x + this.width - 55, itemY, 'SELL', {
+         const sellButtonText = this.scene.add.text(this.x + this.width - 55, itemY, TextConfig.shop.fishInventory.sell, {
              fontSize: '10px',
              fill: '#ffffff',
```

---

```diff
              
              // Update money display
-             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
+             this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} ${TextConfig.shop.currency}`);
              
              // Update fish display
              const newCount = fishGroup.count - quantity;
              if (newCount <= 0) {
                  // Remove the item if no more fish of this type
-                 fishText.setText(`${fish.name} - SOLD OUT`);
+                 fishText.setText(`${fish.name} - ${TextConfig.shop.fishInventory.soldOut}`);
                  fishText.setColor('#666666');
                  infoText.setText('');
```

```diff
              
              // Show notification
-             this.showNotification(`Sold ${fish.name} x${quantity} for $${saleValue}!`, 0x27ae60);
+             this.showNotification(`${TextConfig.shop.fishInventory.sold} ${fish.name} x${quantity} ${TextConfig.shop.fishInventory.for} $${saleValue}!`, 0x27ae60);
          }
      }
```

```diff
          
          // Update money display
-         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} 金幣`);
+         this.moneyText.setText(`💰 ${TextConfig.shop.money}: ${this.gameState.player.money} ${TextConfig.shop.currency}`);
          
          // Mark game state as dirty for saving
```

---

```diff
          
          if (fishInventory.length === 0) {
-             this.showNotification('No fish to sell!', 0xe74c3c);
+             this.showNotification(TextConfig.shop.fishInventory.noFishToSell, 0xe74c3c);
              return;
          }
```

```diff
          
          // Show notification
-         this.showNotification(`Sold all ${totalFishCount} fish for $${totalValue}!`, 0x27ae60);
+         this.showNotification(`${TextConfig.shop.fishInventory.soldAll} ${totalFishCount} ${TextConfig.shop.fishInventory.fishFor} $${totalValue}!`, 0x27ae60);
          
          // Refresh the shop to update display
```

```diff
          
          // Show notification
-         this.showNotification('Added test fish to inventory!', 0x3498db);
+         this.showNotification(TextConfig.shop.fishInventory.addedTestFish, 0x3498db);
          
          // Refresh the shop to show the new fish
```

---

現在讓我檢查其他需要中文化的UI組件。讓我先看看還有哪些文件需要處理：

---

<details>
            <summary>Listed directory src/ui • **13** results</summary>
        
| Name |
|-------|
| 📄 `ShopUI.js` |
| 📄 `CraftingUI.js` |
| 📄 `InventoryUI.js` |
| 📄 `UITheme.js` |
| 📄 `TimeWeatherUI.js` |
| 📄 `PlayerProgressionUI.js` |
| 📄 `EquipmentEnhancementUI.js` |
| 📄 `RenJsDebugUI.js` |
| 📄 `LoadingStateManager.js` |
| 📄 `MapSelectionUI.js` |
| 📄 `AudioSettingsUI.js` |
| 📄 `AchievementPopupSystem.js` |
| 📄 `FishCollectionUI.js` |

</details>

---

讓我檢查TimeWeatherUI.js，這個組件可能包含時間和天氣相關的文本：

---

Read file: src/ui/TimeWeatherUI.js

---

我看到TimeWeatherUI.js包含很多需要中文化的文本。讓我修改這個文件：

---

```diff
  // TimeWeatherUI.js - Time & Weather Display UI Component
+ import TextConfig from '../data/TextConfig.js';
+ 
  export class TimeWeatherUI {
      constructor(scene, timeManager, weatherManager, gameState) {
```

---

```diff
          
          // Header
-         this.headerText = this.scene.add.text(this.config.width / 2, 15, 'TIME & WEATHER', {
+         this.headerText = this.scene.add.text(this.config.width / 2, 15, TextConfig.timeWeather.title, {
              fontSize: '16px',
              fontWeight: 'bold',
```

---

```diff
          
          // Weather section header
-         this.weatherHeader = this.scene.add.text(15, startY, 'Weather', {
+         this.weatherHeader = this.scene.add.text(15, startY, TextConfig.timeWeather.weather, {
              fontSize: '12px',
              fontWeight: 'bold',
```

```diff
          
          // Conditions header
-         this.conditionsHeader = this.scene.add.text(15, startY, 'Fishing Conditions', {
+         this.conditionsHeader = this.scene.add.text(15, startY, TextConfig.timeWeather.fishingConditions, {
              fontSize: '12px',
              fontWeight: 'bold',
```

```diff
          
          // Activity level bar
-         this.activityLabel = this.scene.add.text(15, startY + 20, 'Activity:', {
+         this.activityLabel = this.scene.add.text(15, startY + 20, TextConfig.timeWeather.activity + ':', {
              fontSize: '10px',
              fill: '#cccccc'
```

```diff
          
          // Bite rate indicator
-         this.biteLabel = this.scene.add.text(15, startY + 35, 'Bite Rate:', {
+         this.biteLabel = this.scene.add.text(15, startY + 35, TextConfig.timeWeather.biteRate + ':', {
              fontSize: '10px',
              fill: '#cccccc'
```

---

現在讓我檢查並修改TimeWeatherUI.js中的時間控制按鈕和其他動態文本：

---

Read file: src/ui/TimeWeatherUI.js

---

讓我修改TimeWeatherUI.js中的時間控制按鈕和動態文本：

---

```diff
          
          // Pause/Resume button
-         this.pauseButton = this.scene.add.text(15, controlsY, '⏸️ Pause', {
+         this.pauseButton = this.scene.add.text(15, controlsY, '⏸️ ' + TextConfig.timeWeather.pause, {
              fontSize: '11px',
              fill: '#4a90e2',
```

```diff
          
          // Forecast toggle
-         this.forecastButton = this.scene.add.text(this.config.width - 15, controlsY, '📅 Forecast', {
+         this.forecastButton = this.scene.add.text(this.config.width - 15, controlsY, '📅 ' + TextConfig.timeWeather.forecast, {
              fontSize: '11px',
              fill: '#4a90e2',
```

---

```diff
          // Update period
          const period = this.timeManager.getCurrentPeriodName();
-         this.periodText.setText(period);
+         const periodTranslation = TextConfig.timeWeather.periods[period] || period;
+         this.periodText.setText(periodTranslation);
          
          // Update period icon
```

```diff
          
          // Update speed indicator
-         this.speedText.setText(`Speed: ${this.timeManager.timeSpeed}x`);
+         this.speedText.setText(`${TextConfig.timeWeather.speed}: ${this.timeManager.timeSpeed}x`);
          
          // Update pause button
-         this.pauseButton.setText(this.timeManager.paused ? '▶️ Resume' : '⏸️ Pause');
+         this.pauseButton.setText(this.timeManager.paused ? '▶️ ' + TextConfig.timeWeather.resume : '⏸️ ' + TextConfig.timeWeather.pause);
      }
      
```

---

```diff
          // Update optimal indicator
          if (optimal.isOptimal) {
-             this.optimalIndicator.setText('⭐ OPTIMAL CONDITIONS ⭐');
+             this.optimalIndicator.setText('⭐ ' + TextConfig.timeWeather.optimalConditions + ' ⭐');
              this.optimalIndicator.setFill('#ffff00');
          } else if (optimal.rating >= 80) {
-             this.optimalIndicator.setText('Good Fishing Conditions');
+             this.optimalIndicator.setText(TextConfig.timeWeather.goodConditions);
              this.optimalIndicator.setFill('#90ee90');
          } else {
```

---

```diff
      showPeriodChangeNotification(newPeriod) {
          const periodName = this.timeManager.getCurrentPeriodName();
+         const periodTranslation = TextConfig.timeWeather.periods[periodName] || periodName;
          const notification = this.scene.add.text(
              this.scene.cameras.main.centerX,
              this.scene.cameras.main.centerY - 100,
-             `${periodName} has begun`,
+             `${periodTranslation}${TextConfig.timeWeather.hasBegun}`,
              {
                  fontSize: '20px',
```

---

現在讓我檢查PlayerProgressionUI.js，這個文件可能包含玩家進度相關的文本：

---

Read file: src/ui/PlayerProgressionUI.js

---

PlayerProgressionUI.js是一個很大的文件，包含很多需要中文化的文本。讓我先導入TextConfig並修改主要的標題和標籤：

---

```diff
  // PlayerProgressionUI.js - Comprehensive UI for player progression and skill trees
+ import TextConfig from '../data/TextConfig.js';
+ 
  export class PlayerProgressionUI {
      constructor(scene, x = 50, y = 50, width = 900, height = 700) {
```

```diff
              }
              
-             const notification = this.scene.add.text(this.width / 2, this.height / 2, 
+             const notification = this.scene.add.container(this.width / 2, this.height / 2, 
                  `${skill.name} upgraded to level ${newLevel}!`, {
                  fontSize: '18px',
```

---

```diff
  
          // Title
-         this.titleText = this.scene.add.text(this.width / 2, 25, 'PLAYER PROGRESSION', {
+         this.titleText = this.scene.add.text(this.width / 2, 25, TextConfig.playerProgression.title, {
              fontSize: '24px',
              fontWeight: 'bold',
```

---

```diff
      createTabButtons() {
          const tabs = [
-             { id: 'overview', name: 'Overview', icon: '📊' },
-             { id: 'achievements', name: 'Achievements', icon: '🏆' },
-             { id: 'casting', name: 'Casting', icon: '🎯' },
-             { id: 'fishing', name: 'Fishing', icon: '🎣' },
-             { id: 'reeling', name: 'Reeling', icon: '⚡' },
-             { id: 'luring', name: 'Luring', icon: '🌟' },
-             { id: 'exploration', name: 'Explorer', icon: '🗺️' }
+             { id: 'overview', name: TextConfig.playerProgression.tabs.overview, icon: '📊' },
+             { id: 'achievements', name: TextConfig.playerProgression.tabs.achievements, icon: '🏆' },
+             { id: 'casting', name: TextConfig.playerProgression.tabs.casting, icon: '🎯' },
+             { id: 'fishing', name: TextConfig.playerProgression.tabs.fishing, icon: '🎣' },
+             { id: 'reeling', name: TextConfig.playerProgression.tabs.reeling, icon: '⚡' },
+             { id: 'luring', name: TextConfig.playerProgression.tabs.luring, icon: '🌟' },
+             { id: 'exploration', name: TextConfig.playerProgression.tabs.exploration, icon: '🗺️' }
          ];
  
```

---

現在讓我修改PlayerProgressionUI.js中的概覽標籤內容：

---

```diff
  
          // Player level and experience
-         this.levelText = this.scene.add.text(20, 20, 'Level: 1', {
+         this.levelText = this.scene.add.text(20, 20, TextConfig.playerProgression.level + ': 1', {
              fontSize: '20px',
              fontWeight: 'bold',
```

```diff
          this.overviewContent.add(this.levelText);
  
-         this.experienceText = this.scene.add.text(20, 50, 'Experience: 0 / 100', {
+         this.experienceText = this.scene.add.text(20, 50, TextConfig.playerProgression.experience + ': 0 / 100', {
              fontSize: '16px',
              fill: '#ffffff'
```

```diff
  
          // Skill points
-         this.skillPointsText = this.scene.add.text(20, 110, 'Skill Points: 0', {
+         this.skillPointsText = this.scene.add.text(20, 110, TextConfig.playerProgression.skillPoints + ': 0', {
              fontSize: '18px',
              fontWeight: 'bold',
```

```diff
  
          // Statistics
-         this.statsTitle = this.scene.add.text(20, 150, 'PROGRESSION STATISTICS', {
+         this.statsTitle = this.scene.add.text(20, 150, TextConfig.playerProgression.statistics, {
              fontSize: '16px',
              fontWeight: 'bold',
```

```diff
  
          // Next level reward
-         this.nextRewardTitle = this.scene.add.text(450, 20, 'NEXT LEVEL REWARD', {
+         this.nextRewardTitle = this.scene.add.text(450, 20, TextConfig.playerProgression.nextLevelReward, {
              fontSize: '16px',
              fontWeight: 'bold',
```

```diff
  
          // Available skill upgrades
-         this.upgradesTitle = this.scene.add.text(450, 200, 'AVAILABLE UPGRADES', {
+         this.upgradesTitle = this.scene.add.text(450, 200, TextConfig.playerProgression.availableUpgrades, {
              fontSize: '16px',
              fontWeight: 'bold',
```

---

```diff
  
          // Achievements title
-         this.achievementsTitle = this.scene.add.text(20, 20, 'ACHIEVEMENTS', {
+         this.achievementsTitle = this.scene.add.text(20, 20, TextConfig.playerProgression.achievementsTitle, {
              fontSize: '20px',
              fontWeight: 'bold',
```

---

現在讓我檢查FishCollectionUI.js，這個文件可能包含魚類收藏相關的文本：

---

Read file: src/ui/FishCollectionUI.js

---



---

