package ehooray.fishing.component.init;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;

@Component
@Order(RunnerOrder.Firebase)
public class FirebaseInitialRunner implements ApplicationRunner, DisposableBean
{
	@Value("${game.firebase.key-path}")
    private Resource keyResource;
	
	@Override
    public void run(ApplicationArguments args) throws Exception
    {
        FirebaseOptions options = FirebaseOptions.builder()
                .setCredentials(GoogleCredentials.fromStream(keyResource.getInputStream()))
                .build();

        
        FirebaseApp.initializeApp(options);
    }
	
	@Override
    public void destroy() throws Exception
    {
        FirebaseApp.getInstance().delete();
    }
}
