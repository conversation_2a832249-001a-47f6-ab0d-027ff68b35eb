package ehooray.fishing.service.player;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

import org.javatuples.Triplet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.AchievementEntity;
import ehooray.fishing.entity.FishKitEntity;
import ehooray.fishing.entity.FishRodEntity;
//import ehooray.formosa.entity.AchievementEntity;
//import ehooray.formosa.entity.BuildingEntity;
//import ehooray.formosa.entity.CharacterEntity;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.entity.QuestEntity;
import ehooray.fishing.entity.SpotEntity;
//import ehooray.formosa.entity.PlayerItemEntity;
//import ehooray.formosa.entity.QuestEntity;
import ehooray.fishing.pojo.csv.LevelCsv;
import ehooray.fishing.pojo.csv.LevelCsv.ExpType;
import ehooray.fishing.pojo.event.CreatePlayerEvent;
//import ehooray.formosa.pojo.event.CreatePlayerEvent;
import ehooray.fishing.pojo.event.PlayerLevelUpEvent;
import ehooray.fishing.repository.PlayerRepository;
//import ehooray.formosa.service.building.BuildingService;
//import ehooray.formosa.service.character.CharacterService;
//import ehooray.formosa.service.item.PlayerItemService;

@Service
public class PlayerService
{
    static final int ToLv1Exp = 0;
    
    @Autowired
    protected ApplicationEventPublisher publisher;
    
    protected final CsvContainer csvContainer;
    protected final PlayerRepository playerRepository;

    public PlayerService(CsvContainer csvContainer, PlayerRepository playerRepository)
    {
        this.csvContainer = csvContainer;
        this.playerRepository = playerRepository;
    }

    public Optional<PlayerEntity> findByEntityId(long id)
    {
        return playerRepository.findById(id);
    }

    public Optional<PlayerEntity> findByPlayerAuthId(long id)
    {
        return playerRepository.findByPlayerAuthId(id);
    }

    public PlayerEntity createPlayer(PlayerAuthEntity playerAuth, String name) throws Exception
    {
        PlayerEntity player = new PlayerEntity();
        player.setName(name);
        player.setPlayerItems(new ArrayList<PlayerItemEntity>());
        player.setQuests(new ArrayList<QuestEntity>());
        player.setAchievements(new ArrayList<AchievementEntity>());
        player.setFishRods(new ArrayList<FishRodEntity>());
        player.setFishKits(new ArrayList<FishKitEntity>());
        player.setSpots(new ArrayList<SpotEntity>());
        player.setPlayerAuth(playerAuth);
        
        playerRepository.save(player);

        addExpToPlayer(player, ToLv1Exp);
        publisher.publishEvent(new CreatePlayerEvent(this, player));

        return player;
    }

    public DiffExp addExpToPlayer(PlayerEntity player, int exp) throws Exception
    {
        LevelCsv levelCsv = csvContainer.getCsv(LevelCsv.class);
        int currentLevel = player.getLevel();
        Triplet<LevelCsv.Row, Integer, Integer> nextLevelResult = levelCsv
                .findNextLevelByExp(currentLevel, levelCsv.getMaxLevel(), player.getExp(), exp, ExpType.Player);
        int nextLevel = nextLevelResult.getValue0().getLevel();

        player.setLevel(nextLevel);
        player.setExp(nextLevelResult.getValue1());

        DiffExp diffExp = new DiffExp();
        diffExp.diffExp = nextLevelResult.getValue2();
        diffExp.diffLevel = nextLevel - currentLevel;
        diffExp.player = player;

        playerRepository.save(player);
        
        publisher.publishEvent(new PlayerLevelUpEvent(this, player));

        return diffExp;
    } 
    
    public void battleSpot(PlayerEntity player, SpotEntity spot)
    {
        player.setBattleSpot(spot);
        playerRepository.save(player);
    } 
    
    public void activeFishKit(PlayerEntity player, FishKitEntity fishKit)
    {
        player.setActiveFishKit(fishKit);
        playerRepository.save(player);
    }
    
    public void setApLastCountDate(PlayerEntity player, LocalDateTime time)
    {
        player.setApLastCountDate(time);
        playerRepository.save(player);
    }

    public class DiffExp
    {
        protected int diffLevel;
        protected int diffExp;
        protected PlayerEntity player;

        public int getDiffLevel()
        {
            return diffLevel;
        }

        public int getDiffExp()
        {
            return diffExp;
        }

        public PlayerEntity getPlayer()
        {
            return player;
        }
    }

}
