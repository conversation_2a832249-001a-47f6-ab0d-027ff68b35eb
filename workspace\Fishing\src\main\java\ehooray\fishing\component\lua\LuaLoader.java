package ehooray.fishing.component.lua;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class LuaLoader
{
    @Value("${game.lua.file-path}")
    protected String luaFolder;

    @Value("${game.lua.file-ext}")
    protected String luaFileExtension;

    public String load(String fileName) throws Exception
    {
        File luaFile = new File(luaFolder + fileName + luaFileExtension);
        String luaScript = FileUtils.readFileToString(luaFile);

        return luaScript;
    }

    public Map<String, String> loadFiles(List<String> files) throws Exception
    {
        Map<String, String> resultMap = new HashMap<>();
        for (String file : files)
        {
            String script = load(file);
            resultMap.put(file, script);
        }
        return resultMap;
    }
}
