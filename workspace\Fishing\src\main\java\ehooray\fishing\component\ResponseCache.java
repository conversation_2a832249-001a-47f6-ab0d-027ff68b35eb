package ehooray.fishing.component;

import java.util.LinkedList;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ehooray.fishing.dto.proto.response.ResponseProto;

@Component
public class ResponseCache
{
    @Value("${game.responseCache.capacity}")
    protected int responseCacheCapacity;
    
    @Autowired
    protected ResponseCache selfForAop;

    public void add(long playerId, ResponseProto.Response response)
    {
        LinkedList<ResponseProto.Response> cache = selfForAop.getCache(playerId);
        synchronized (cache)// only one thread can get this cache instance at the same time
        {
            if(cache.size() >= responseCacheCapacity)
                cache.poll();
            cache.offer(response);
        }
    }

    public Optional<ResponseProto.Response> find(long playerId, long requestId)
    {
        LinkedList<ResponseProto.Response> cache = selfForAop.getCache(playerId);
        synchronized (cache)// only one thread can get this cache instance at the same time
        {
            for(int i = cache.size() - 1; i >= 0; i--)
            {
                ResponseProto.Response response = cache.get(i);
                if(response.getRequestId() == requestId)
                    return Optional.of(response);
            }
        }
        return Optional.empty();
    }

    @Cacheable("responseCache")// same as aop restriction, need public and should call by outside.
    public LinkedList<ResponseProto.Response> getCache(long playerId)
    {
        return new LinkedList<ResponseProto.Response>();
    }
}
