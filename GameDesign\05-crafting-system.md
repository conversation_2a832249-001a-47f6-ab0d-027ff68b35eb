# 5. 合成系統設計文檔

## 5.1 合成機制總覽

### 5.1.1 船隻工作坊合成系統

合成系統是《Luxury Angler》的核心進程機制，讓玩家將捕獲的魚卡轉化為更強力的裝備。

#### 基本合成流程
1. **進入船隻工作坊**: 從船隻選單存取合成介面
2. **選擇合成配方**: 從60個可用配方中選擇
3. **放入材料**: 使用魚卡（1-5星）作為主要材料
4. **支付成本**: 消耗金幣（200-16,000）
5. **等待完成**: 合成時間（2-30分鐘）
6. **獲得物品**: 產出釣竿、魚餌、船隻或服裝

#### 合成材料系統
**魚卡分級**:
- **1星魚卡**: 基礎材料，用於初級合成
- **2星魚卡**: 普通材料，中級合成主料
- **3星魚卡**: 優質材料，高級合成配方
- **4星魚卡**: 稀有材料，精英級裝備
- **5星魚卡**: 傳說材料，大師級裝備

**材料消耗機制**:
- 合成完成後魚卡被消耗
- 某些配方需要特定種類的魚卡
- 高級合成可能需要多種不同魚卡

技術實現：
- [CraftingManager.js](../src/scripts/CraftingManager.js): 合成系統核心邏輯
- [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面實現

### 5.1.2 升級合成機制

**裝備升級路徑**:
- **基礎級** → **精英級** → **大師級**
- 每級需要合成2個低級物品
- 升級成本和時間遞增
- 屬性大幅提升

**升級配方範例**:
```
精英竹製釣竿 = 2 × 竹製釣竿 + 1,000金幣 + 12分鐘
大師竹製釣竿 = 2 × 精英竹製釣竿 + 8,000金幣 + 22分鐘
```

## 5.2 詳細合成配方系統 (60個配方)

### 5.2.1 釣竿系列 (15個配方)

釣竿是主要戰鬥裝備，提供拋竿精準度、張力穩定性等關鍵屬性。

#### 基礎釣竿配方 (5個)
**1. 竹製釣竿**
- **材料**: 2張鯡魚卡
- **成本**: 200金幣
- **時間**: 2分鐘
- **屬性**: +5% 拋竿精準度
- **描述**: 傳統竹製，輕巧耐用

**2. 玻璃纖維釣竿**
- **材料**: 2張鱸魚卡
- **成本**: 400金幣
- **時間**: 4分鐘
- **屬性**: +8% 拋竿精準度, +5% 張力穩定性
- **描述**: 現代材料，彈性佳

**3. 碳纖維釣竿**
- **材料**: 2張鱒魚卡
- **成本**: 800金幣
- **時間**: 6分鐘
- **屬性**: +12% 拋竿精準度, +8% 張力穩定性
- **描述**: 高科技材料，極輕且強韌

**4. 鋼製釣竿**
- **材料**: 2張梭魚卡
- **成本**: 1,200金幣
- **時間**: 8分鐘
- **屬性**: +15% 拋竿精準度, +12% 張力穩定性, +5% 線強度
- **描述**: 重型釣竿，適合大型魚類

**5. 鈦合金釣竿**
- **材料**: 2張鮭魚卡
- **成本**: 2,000金幣
- **時間**: 10分鐘
- **屬性**: +20% 拋竿精準度, +18% 張力穩定性, +10% 稀有魚機率
- **描述**: 頂級材料，完美平衡

#### 精英釣竿配方 (5個)
升級合成，需要2個基礎釣竿 + 額外成本

**6. 精英竹製釣竿**
- **材料**: 2支竹製釣竿
- **成本**: 1,000金幣
- **時間**: 12分鐘
- **屬性**: +12% 拋竿精準度, +8% 張力穩定性

**7. 精英玻璃纖維釣竿**
- **材料**: 2支玻璃纖維釣竿
- **成本**: 2,000金幣
- **時間**: 14分鐘
- **屬性**: +18% 拋竿精準度, +15% 張力穩定性

**8. 精英碳纖維釣竿**
- **材料**: 2支碳纖維釣竿
- **成本**: 3,000金幣
- **時間**: 16分鐘
- **屬性**: +22% 拋竿精準度, +18% 張力穩定性, +8% 拋竿距離

**9. 精英鋼製釣竿**
- **材料**: 2支鋼製釣竿
- **成本**: 4,000金幣
- **時間**: 18分鐘
- **屬性**: +25% 拋竿精準度, +22% 張力穩定性, +12% 線強度

**10. 精英鈦合金釣竿**
- **材料**: 2支鈦合金釣竿
- **成本**: 6,000金幣
- **時間**: 20分鐘
- **屬性**: +28% 拋竿精準度, +25% 張力穩定性, +15% 稀有魚機率

#### 大師釣竿配方 (5個)
終極級釣竿，需要2個精英釣竿

**11. 大師竹製釣竿**
- **材料**: 2支精英竹製釣竿
- **成本**: 8,000金幣
- **時間**: 22分鐘
- **屬性**: +20% 拋竿精準度, +15% 張力穩定性, +10% 收線速度

**12. 大師玻璃纖維釣竿**
- **材料**: 2支精英玻璃纖維釣竿
- **成本**: 10,000金幣
- **時間**: 24分鐘
- **屬性**: +25% 拋竿精準度, +20% 張力穩定性, +12% 收線速度

**13. 大師碳纖維釣竿**
- **材料**: 2支精英碳纖維釣竿
- **成本**: 12,000金幣
- **時間**: 26分鐘
- **屬性**: +30% 拋竿精準度, +25% 張力穩定性, +15% 拋竿距離

**14. 大師鋼製釣竿**
- **材料**: 2支精英鋼製釣竿
- **成本**: 14,000金幣
- **時間**: 28分鐘
- **屬性**: +32% 拋竿精準度, +28% 張力穩定性, +18% 線強度

**15. 大師鈦合金釣竿**
- **材料**: 2支精英鈦合金釣竿
- **成本**: 16,000金幣
- **時間**: 30分鐘
- **屬性**: +35% 拋竿精準度, +30% 張力穩定性, +20% 稀有魚機率, +15% 收線速度

### 5.2.2 魚餌系列 (15個配方)

魚餌系列專注於提升咬餌率和誘魚成功率，對應五種魚餌類型。

#### 基礎魚餌配方 (5個)
**16. 基礎旋轉餌**
- **材料**: 2張鱸魚卡
- **成本**: 300金幣
- **時間**: 2分鐘
- **屬性**: +20% 咬餌率
- **適用**: 攻擊性魚類

**17. 軟蟲餌**
- **材料**: 2張鯰魚卡
- **成本**: 500金幣
- **時間**: 4分鐘
- **屬性**: +25% 咬餌率, +10% 誘魚成功率
- **適用**: 掠食性魚類

**18. 飛釣餌**
- **材料**: 2張鱒魚卡
- **成本**: 700金幣
- **時間**: 6分鐘
- **屬性**: +30% 咬餌率, +15% 誘魚成功率
- **適用**: 表層魚類

**19. 波扒餌**
- **材料**: 2張梭魚卡
- **成本**: 900金幣
- **時間**: 8分鐘
- **屬性**: +35% 咬餌率, +20% 誘魚成功率
- **適用**: 大型攻擊性魚類

**20. 匙型餌**
- **材料**: 2張鮭魚卡
- **成本**: 1,100金幣
- **時間**: 10分鐘
- **屬性**: +40% 咬餌率, +25% 誘魚成功率
- **適用**: 深水魚類

#### 精英魚餌配方 (5個)
**21-25. 精英系列魚餌**
- 需要2個對應基礎魚餌
- 成本範圍: 1,500-3,500金幣
- 時間範圍: 12-20分鐘
- 屬性提升: +15-25% 各項數值

#### 大師魚餌配方 (5個)
**26-30. 大師系列魚餌**
- 需要2個對應精英魚餌
- 成本範圍: 4,000-6,000金幣
- 時間範圍: 22-30分鐘
- 屬性提升: +25-40% 各項數值
- 額外效果: 魚餌耐久性 +10-15%

### 5.2.3 船隻系列 (15個配方)

船隻提供整體遊戲體驗提升，包括合成效率、自動釣魚和儲存空間。

#### 基礎船隻配方 (5個)
**31. 划艇**
- **材料**: 3張沙丁魚卡
- **成本**: 500金幣
- **時間**: 5分鐘
- **屬性**: +3% 船隻速度, +5格魚缸儲存
- **描述**: 小型個人船隻

**32. 小艇**
- **材料**: 3張鱸魚卡
- **成本**: 1,000金幣
- **時間**: 7分鐘
- **屬性**: +5% 船隻速度, +8% 合成效率, +10格魚缸儲存
- **描述**: 實用小型船隻

**33. 快艇**
- **材料**: 3張鮪魚卡
- **成本**: 2,000金幣
- **時間**: 9分鐘
- **屬性**: +8% 船隻速度, +12% 合成效率, +15格魚缸儲存
- **描述**: 高速移動船隻

**34. 遊艇**
- **材料**: 3張旗魚卡
- **成本**: 4,000金幣
- **時間**: 11分鐘
- **屬性**: +12% 船隻速度, +15% 合成效率, +20格魚缸儲存, +1伴侶槽
- **描述**: 奢華休閒船隻

**35. 豪華郵輪**
- **材料**: 3張鯊魚卡
- **成本**: 6,000金幣
- **時間**: 13分鐘
- **屬性**: +15% 船隻速度, +20% 合成效率, +25格魚缸儲存, +2伴侶槽
- **描述**: 頂級奢華船隻

#### 精英船隻配方 (5個)
**36-40. 精英系列船隻**
- 需要2艘對應基礎船隻
- 成本範圍: 2,000-12,000金幣
- 時間範圍: 15-23分鐘
- 新增屬性: 自動釣魚效率、熱點穩定性

#### 大師船隻配方 (5個)
**41-45. 大師系列船隻**
- 需要2艘對應精英船隻
- 成本範圍: 10,000-16,000金幣
- 時間範圍: 25-33分鐘
- 頂級屬性: 最大自動釣魚產量、完美合成效率

### 5.2.4 服裝系列 (15個配方)

服裝提供各種輔助加成，包括能量、社交和特殊能力。

#### 基礎服裝配方 (5個)
**46. 漁夫帽**
- **材料**: 2張鱒魚卡
- **成本**: 400金幣
- **時間**: 3分鐘
- **屬性**: +5% 能量, +3% 咬餌率
- **描述**: 經典漁夫裝備

**47. 太陽眼鏡**
- **材料**: 2張梭魚卡
- **成本**: 600金幣
- **時間**: 5分鐘
- **屬性**: +8% 魚類偵測, +5% 拋竿精準度
- **描述**: 時尚實用配件

**48. 釣魚背心**
- **材料**: 2張鮭魚卡
- **成本**: 800金幣
- **時間**: 7分鐘
- **屬性**: +10% 能量, +5格庫存空間
- **描述**: 多功能釣魚背心

**49. 比基尼上衣**
- **材料**: 2張鱸魚卡
- **成本**: 1,000金幣
- **時間**: 9分鐘
- **屬性**: +12% 浪漫效果, +8% 娛樂心情加成
- **描述**: 度假風格服裝

**50. 涼鞋**
- **材料**: 2張鱸魚卡
- **成本**: 1,200金幣
- **時間**: 11分鐘
- **屬性**: +5% 船隻速度, +8% 能量
- **描述**: 舒適輕便鞋履

#### 精英服裝配方 (5個)
**51-55. 精英系列服裝**
- 需要2件對應基礎服裝
- 成本範圍: 1,500-3,500金幣
- 時間範圍: 13-21分鐘
- 增強社交和實用屬性

#### 大師服裝配方 (5個)
**56-60. 大師系列服裝**
- 需要2件對應精英服裝
- 成本範圍: 4,000-6,000金幣
- 時間範圍: 23-31分鐘
- 頂級時尚和功能性

## 5.3 合成系統機制設計

### 5.3.1 時間管理系統

**合成時間計算**:
```javascript
// 基礎合成時間修正
const craftingTime = baseCraftingTime * 
    (1 - player.craftingEfficiency / 100) *
    (1 - boat.craftingBonus / 100) *
    (1 - companion.craftingAssist / 100);
```

**時間階層**:
- **快速合成** (2-5分鐘): 基礎裝備
- **標準合成** (6-15分鐘): 中級裝備
- **長時間合成** (16-33分鐘): 高級裝備

### 5.3.2 成本平衡系統

**金幣成本設計**:
- **早期** (200-1,200金幣): 容易獲得，快速進步
- **中期** (1,500-6,000金幣): 需要策略規劃
- **後期** (8,000-16,000金幣): 重大投資決策

**材料稀缺性**:
- 普通魚卡: 容易獲得，大量需求
- 稀有魚卡: 限量供應，珍貴材料

### 5.3.3 配方解鎖系統

**解鎖條件**:
- **玩家等級**: 達到特定等級解鎖新配方
- **前置裝備**: 需要製作基礎版本才能升級
- **故事進程**: 某些配方綁定劇情進展
- **成就獎勵**: 完成特定成就解鎖特殊配方

### 5.3.4 品質控制系統

**合成成功率**:
- 基礎配方: 100% 成功率
- 精英配方: 95% 成功率，5% 機率額外屬性
- 大師配方: 90% 成功率，10% 機率完美品質

**失敗處理**:
- 材料損失50%
- 金幣退回75%
- 獲得"合成經驗"減少未來失敗率

## 5.4 合成UI與使用者體驗

### 5.4.1 合成介面設計

**主要元素**:
- **配方列表**: 可用配方分類顯示
- **材料槽位**: 拖放魚卡到對應位置
- **成本顯示**: 金幣和時間需求
- **進度條**: 合成進行中的視覺反饋
- **完成通知**: 合成結束的提醒系統

**篩選與搜尋**:
- 按裝備類型篩選
- 按材料需求篩選
- 按解鎖狀態篩選
- 搜尋特定配方名稱

### 5.4.2 合成預覽系統

**屬性比較**:
- 顯示當前裝備 vs 合成結果
- 屬性變化以顏色標示
- 總體戰力評估

**材料檢查**:
- 自動檢查所需材料是否充足
- 缺少材料以紅色標示
- 推薦獲得途徑提示

### 5.4.3 批量合成功能

**快速合成**:
- 一鍵合成多個同類物品
- 自動選擇最佳材料組合
- 批量完成通知

**合成佇列**:
- 安排多個合成任務
- 自動依序執行
- 佇列管理介面

技術實現：
- [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面實現
- [InventoryUI.js](../src/ui/InventoryUI.js): 材料選擇介面

## 5.5 合成系統平衡與經濟整合

### 5.5.1 經濟循環設計

**資源流動**:
```
釣魚獲得魚卡 → 出售部分魚卡獲得金幣 → 保留魚卡用於合成 → 
合成裝備提升釣魚效率 → 捕獲更好魚類 → 更高經濟收益
```

**投資回報**:
- 短期投資：基礎裝備，快速見效
- 中期投資：精英裝備，穩定收益
- 長期投資：大師裝備，最大化效益

### 5.5.2 進程驅動設計

**能力解鎖曲線**:
- 等級1-10：學習基本合成
- 等級11-25：掌握精英合成
- 等級26-50：精通大師合成

**挑戰適配**:
- 新手友善：簡單配方，明確指引
- 中級挑戰：資源管理，選擇策略
- 高級精通：最適化配置，完美時機

### 5.5.3 社交整合

**伴侶協助**:
- 特定伴侶提供合成加成
- 關係等級影響協助效果
- 伴侶專長對應不同裝備類型

**競爭元素**:
- 合成排行榜
- 限時合成挑戰
- 稀有配方競逐

相關檔案：
- [CraftingManager.js](../src/scripts/CraftingManager.js): 合成系統核心
- [CraftingUI.js](../src/ui/CraftingUI.js): 合成介面
- [InventoryManager.js](../src/scripts/InventoryManager.js): 材料管理
- [PlayerProgression.js](../src/scripts/PlayerProgression.js): 配方解鎖
- [EquipmentEnhancer.js](../src/scripts/EquipmentEnhancer.js): 裝備屬性計算 