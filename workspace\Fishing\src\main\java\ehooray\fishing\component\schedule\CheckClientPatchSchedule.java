package ehooray.fishing.component.schedule;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.protobuf.Any;

import ehooray.fishing.ApplicationMain;
import ehooray.fishing.component.WebSocketHandler;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.init.CheckServerRunner;
import ehooray.fishing.component.logger.CheckClientPatchScheduleLogger;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto;
import ehooray.fishing.entity.ServerInfoEntity;
import ehooray.fishing.pojo.csv.LanguageCsv;
import ehooray.fishing.service.server.ServerInfoService;

@Component
public class CheckClientPatchSchedule
{
    public enum State
    {
        NotInitial,
        Nothing,
        Finish
    }

    @Autowired
    protected ServerInfoService serverInfoService;

    @Autowired
    protected WebSocketHandler webSocketHandler;

    @Autowired
    protected CheckServerRunner checkServerRunner;

    @Autowired
    protected CsvContainer csvContainer;

    @Value("${game.csv.class-packages}")
    protected List<String> csvClassPackages;

    protected boolean isProcessing;

    @Scheduled(fixedDelayString = "${game.check-client-patch-schedule}")
    @Transactional(rollbackFor = Throwable.class, timeoutString = "${game.transactional.timeout}")
    public State check() throws Exception
    {
        if(!checkServerRunner.isInitialFinished()) return State.NotInitial;

        ServerInfoEntity myServerInfo = serverInfoService.myServerInfo().get();
        if(!myServerInfo.isClientPatch()) return State.Nothing;


        CloseSessionRequestProto.CloseSessionRequest.Builder closeSessionRequest = CloseSessionRequestProto.CloseSessionRequest.newBuilder();
        closeSessionRequest.setCsvId(csvContainer.getCsv(LanguageCsv.class).getClientPatchRow().getId());
        RequestProto.Request.Builder requestBuilder = RequestProto.Request.newBuilder();
        requestBuilder.setController("MaintainService");
        requestBuilder.setMethod("OnPatch");
        requestBuilder.addDataList(Any.pack(closeSessionRequest.build()));

        webSocketHandler.closeAllSessions(requestBuilder.build(), CheckClientPatchScheduleLogger.class.getCanonicalName());
        serverInfoService.finishClientPatch(myServerInfo, LocalDateTime.now());

        ApplicationMain.restart();

        return State.Finish;

    }
}
