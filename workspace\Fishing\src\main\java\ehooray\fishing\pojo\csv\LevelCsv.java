package ehooray.fishing.pojo.csv;

import java.util.HashMap;
import java.util.Optional;

import org.javatuples.Triplet;
import java.util.Collection;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;
import ehooray.fishing.pojo.ExceptionByCsvId;

@CsvPath("LevelTable.xlsx")
public class LevelCsv extends ACsv<LevelCsv.Row>
{
    public enum ExpType
    {
        Player,
//        Port,
//        Rare1,
//        Rare2,
//        Rare3,
//        Rare4,
    }
    
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("Level")
        protected int level;
        
        @ExcelCellName("PlayerExp")
        protected int playerExp;
        
//        @ExcelCellName("PortExp")
//        protected int portExp;
//        
//        @ExcelCellName("RareExp_1")
//        protected int rareExp1;
//        
//        @ExcelCellName("RareExp_2")
//        protected int rareExp2;
//        
//        @ExcelCellName("RareExp_3")
//        protected int rareExp3;
//        
//        @ExcelCellName("RareExp_4")
//        protected int rareExp4;
        
        @ExcelCellName("Ap")
        protected int ap;
        
        @Override
        public int getId()
        {
            return id;
        }

        public int getRowIndex()
        {
            return rowIndex;
        }

        public int getLevel()
        {
            return level;
        }

        public int getPlayerExp()
        {
            return playerExp;
        }

//        public int getRareExp1()
//        {
//            return rareExp1;
//        }
//
//        public int getRareExp2()
//        {
//            return rareExp2;
//        }
//
//        public int getRareExp3()
//        {
//            return rareExp3;
//        }
//
//        public int getRareExp4()
//        {
//            return rareExp4;
//        }
//
//        public int getPortExp()
//        {
//            return portExp;
//        }
        public int getAp()
        {
            return ap;
        }
    }
    
    // another utility function at here
    
    protected HashMap<Integer, Row> levelMap;
    protected int maxLevel;
    
    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();
        
        Collection<Row> rows = getRows();
        levelMap = new HashMap<Integer, Row>(rows.size());
        for(Row row : rows)
        {
            levelMap.put(row.getLevel(), row);
            if(maxLevel < row.getLevel())
                maxLevel = row.getLevel();
        }
    }
    
    public int getMaxLevel()
    {
        return maxLevel;
    }
    
    public Optional<Row> findRowByLevel(int level)
    {
        return Optional.ofNullable(levelMap.get(level));
    }
    
    public Triplet<Row, Integer, Integer> findNextLevelByExp(int startLevel, int maxLevel, int previousExp, int addExp, ExpType expType) throws Exception
    {
        int newLevel = startLevel;
        int surplusExp = previousExp + addExp;
        for(int i = startLevel; i < maxLevel; i++)
        {
            Optional<Row> levelRow = findRowByLevel(i);
            int levelUpExp = getRowExpByType(levelRow.get(), expType);
            if(levelUpExp > surplusExp) break;
            
            surplusExp -= levelUpExp;
            newLevel++;
        }
        
        int diffExp = addExp;
        if(newLevel >= maxLevel)
        {
            diffExp -= surplusExp;
            surplusExp = 0;
        }
        
        return Triplet.with(findRowByLevel(newLevel).get(), surplusExp, diffExp);
    }
    
    protected int getRowExpByType(Row row, ExpType expType) throws Exception
    {
        switch(expType)
        {
            case Player: return row.getPlayerExp();
//            case Port: return row.getPortExp();
//            case Rare1: return row.getRareExp1();
//            case Rare2: return row.getRareExp2();
//            case Rare3: return row.getRareExp3();
//            case Rare4: return row.getRareExp4();
            default:
                throw new ExceptionByCsvId("not support ExpType level = " + expType);
        }
    }
    
//    public ExpType findExpTypeByRare(RareEnum rare) throws Exception
//    {
//        switch(rare)
//        {
//            case N: return ExpType.Rare1;
//            case R: return ExpType.Rare2;
//            case SR: return ExpType.Rare3;
//            case SSR: return ExpType.Rare4;
//            default:
//                throw new ExceptionByCsvId("not support rare level = " + rare);
//        }
//    }
}
