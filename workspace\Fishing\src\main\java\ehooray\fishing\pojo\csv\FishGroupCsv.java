package ehooray.fishing.pojo.csv;

import java.util.List;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("FishGroupTable.xlsx")
public class FishGroupCsv extends ACsv<FishGroupCsv.Row>
{
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        @ExcelCellName("Group")
        protected int group;
        @ExcelCellName("Fish")
        protected int fish;
        @ExcelCellName("Weight")
        protected int weight;
        
        public int getRowIndex()
        {
            return rowIndex;
        }

        @Override
        public int getId()
        {
            return id;
        }

        public int getGroup()
        {
            return group;
        }

        public int getFish()
        {
            return fish;
        }

        public int getWeight()
        {
            return weight;
        }
    }
    
    // another utility function at here
    protected HashMap<Integer, List<Row>> groupMap;
    
    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();
        
        Collection<Row> rows = getRows();
        groupMap = new HashMap<Integer, List<Row>>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsInGroup = groupMap.get(row.getGroup());
            if(rowsInGroup == null)
            {
                rowsInGroup = new ArrayList<Row>();
                groupMap.put(row.getGroup(), rowsInGroup);
            }
            rowsInGroup.add(row);
        }
    }

    public List<Row> findAllByGroup(int group)
    {
        return groupMap.get(group);
    }
}
