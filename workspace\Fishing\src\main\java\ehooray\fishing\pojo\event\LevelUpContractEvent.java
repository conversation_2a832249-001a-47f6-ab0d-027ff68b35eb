package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class LevelUpContractEvent extends ApplicationEvent
{
	private final PlayerEntity player;
	private final int addContractCount;
	
	public LevelUpContractEvent(Object source, PlayerEntity player, int addContractCount)
	{
		super(source);
        this.player = player;
        this.addContractCount = addContractCount;
	}
	
	public PlayerEntity getPlayer()
    {
        return player;
    }

	public int getAddContractCount() {
		return addContractCount;
	}
}
