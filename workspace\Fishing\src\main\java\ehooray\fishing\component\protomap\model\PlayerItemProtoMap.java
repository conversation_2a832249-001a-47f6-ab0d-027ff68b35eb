package ehooray.fishing.component.protomap.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.protobuf.Any;

import ehooray.fishing.dto.proto.model.PlayerItemProto;
import ehooray.fishing.entity.PlayerItemEntity;
import ehooray.fishing.service.item.PlayerItemService;

@Component
public class PlayerItemProtoMap
{

    public PlayerItemProto.PlayerItem.Builder toBuilderByEntity(PlayerItemEntity playerItem)
    {
        PlayerItemProto.PlayerItem.Builder builder = PlayerItemProto.PlayerItem.newBuilder();
        builder.setCsvId(playerItem.getCsvId());
        builder.setFinalCount(playerItem.getCount());
        
        return builder;
    }
    
    public PlayerItemProto.PlayerItem.Builder toBuilderByDiffItem(PlayerItemService.DiffItem diffItem)
    {
        PlayerItemProto.PlayerItem.Builder builder = toBuilderByEntity(diffItem.getPlayerItem());
        builder.setDiffCount(diffItem.getDiffCount());
        
        return builder;
    }
    
    public Any toAnyByEntity(PlayerItemEntity playerItem)
    {
        return Any.pack(toBuilderByEntity(playerItem).build());
    }
    
    public ArrayList<Any> toAnyByEntities(List<PlayerItemEntity> playerItems)
    {
        ArrayList<Any> playerItemProtos = new ArrayList<Any>(playerItems.size());
        for(PlayerItemEntity playerItem : playerItems)
            playerItemProtos.add(toAnyByEntity(playerItem));
        
        return playerItemProtos;
    }
    
    public Any toAnyByDiffItem(PlayerItemService.DiffItem diffItem)
    {
        return Any.pack(toProtoByDiffItem(diffItem));
    }
    
    public PlayerItemProto.PlayerItem toProtoByDiffItem(PlayerItemService.DiffItem diffItem)
    {
        return toBuilderByDiffItem(diffItem).build();
    }
    
    public ArrayList<PlayerItemProto.PlayerItem> toProtoByDiffItem(List<PlayerItemService.DiffItem> diffItems)
    {
        ArrayList<PlayerItemProto.PlayerItem> playerItemProtos = new ArrayList<PlayerItemProto.PlayerItem>(diffItems.size());
        for(PlayerItemService.DiffItem diffResult : diffItems)
            playerItemProtos.add(toProtoByDiffItem(diffResult));
        
        return playerItemProtos;
    }

}
