package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.IabTransactionEntity;
import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class IabAcceptEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final IabTransactionEntity iabTransaction;

    public IabAcceptEvent(Object source, PlayerEntity player, IabTransactionEntity iabTransaction)
    {
        super(source);
        this.player = player;
        this.iabTransaction = iabTransaction;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }

    public IabTransactionEntity getIabTransaction()
    {
        return iabTransaction;
    }
}
