package ehooray.fishing.component.utils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import org.springframework.stereotype.Component;

import com.google.protobuf.Any;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;

import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.response.ResponseProto;
import ehooray.fishing.dto.proto.response.ResponseProto.Response.State;

@Component
public class ProtoToStringConverter
{
    protected Map<String, Class<? extends Message>> typeUrlClassMap = new HashMap<>();

    public void initial(String[] packages) throws Exception
    {
        ConfigurationBuilder configBuilder = new ConfigurationBuilder();
        configBuilder.setScanners(new SubTypesScanner(false));
        configBuilder.forPackages(packages);// this will include first parent package
        // still need filter packages
        configBuilder.filterInputsBy(new FilterBuilder().includePackage(packages));

        Reflections reflections = new Reflections(configBuilder);
        Set<Class<? extends Message>> messageClasses = reflections.getSubTypesOf(Message.class);
        for (Class<? extends Message> messageClass : messageClasses)
        {
            Optional<Method> method = Arrays.stream(messageClass.getMethods()).filter(
                    m -> m.getName().equals("newBuilder") && m.getParameterCount() == 0).findAny();
            if(method.isEmpty()) continue;

            MessageOrBuilder builder = (MessageOrBuilder)method.get().invoke(null);
            typeUrlClassMap.put(getTypeUrl(builder.getDescriptorForType()), messageClass);
        }
    }

    public String logRequestProto(RequestProto.Request request) throws Exception
    {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append(String.format("%s.%s %s", request.getController(), request.getMethod(), (request.getDataListCount() > 0) ? "dataList=" : ""));

        for (Any data : request.getDataListList())
        {
            logBuilder.append("{");
            {
                Class<? extends Message> messageClass = typeUrlClassMap.get(data.getTypeUrl());

                // unpack to string
                Message message = data.unpack(messageClass);
                logBuilder.append(String.format("%s - %s", messageClass.getSimpleName(), TextFormat.shortDebugString(message)));
            }
            logBuilder.append("}, ");
        }
        return logBuilder.toString();
    }

    public String logResponseProto(ResponseProto.Response response)
    {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append(response.getState()).append(" ");
        logBuilder.append("dataListCount=").append(response.getDataListCount()).append(", ");
        logBuilder.append("dirtyFlags=").append(response.getDirtyFlagsList()).append(", ");

        if (response.getState() == State.Exception)
        {
            logBuilder.append("exception=").append("{");
            logBuilder.append(TextFormat.shortDebugString(response.getException()));
            logBuilder.append("}");
        }
        return logBuilder.toString();
    }

    protected String getTypeUrl(Descriptors.Descriptor descriptor)
    {
        return "type.googleapis.com" + "/" + descriptor.getFullName();
    }

}
