using Cysharp.Threading.Tasks;
using Ehooray;
using Fishing.UI;
using MoreMountains.Tools;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚投擲狀態策略
    /// </summary>
    public class StartStrategy : FishingStrategyBase
    {
        public StartStrategy() : base(FishingState.Start) { }

        private bool onEnterReady = false;
        private AudioClip splashClip;
        public override async UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToStart方法中的初始化邏輯
            FishingEventTrigger.GetTrigger(context.throwBaitManager.pullButton.gameObject).onPointerDown -= context.throwBaitManager.OnPullButtonPressed;

            context.fishRod.PullOutRod();
            context.fishingGamePanelManager.Close<ThrowBaitManager>();
            context.throwBaitManager.CloseRoot();
            context.cordReelManager.wheelRotate.StartRotate();
            context.RandomStartPosition();
            context.fishLine.FaceToBaitWhenThrowBait();

            splashClip = await context.resourceProvider.LoadResourceAsync<AudioClip>("Sounds/SFX_Splash_02");

            onEnterReady = true;
        }
        
        public override void OnUpdate(FishingManager context)
        {
            if (!onEnterReady)
                return;

            // 原DoStart方法中的邏輯
            if (context.CheckRodAnimatorState("leave"))
            {
                if (context.fishRod.fishRodAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime > 0.55f)
                {
                    context.baitPath.DoMove();
                    context.fishLine.PlayLineAnimation();
                    context.baitLine.gameObject.SetActive(false);
                    context.baitPath.PlayBaitAnimation();
                    context.fishLine.SetTopPointToMesh();
                    context.ThrowBaitCamera();
                }
            }
            if (context.CheckRodAnimatorState("idle"))
            {
                MMSoundManagerSoundPlayEvent.Trigger(
                    splashClip,
                    MMSoundManager.MMSoundManagerTracks.Sfx,
                    context.baitPath.transform.position
                );
                context.TransitionToState(FishingState.Wait).Forget();
            }
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 