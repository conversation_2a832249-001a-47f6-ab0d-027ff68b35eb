package ehooray.fishing.controller.shop;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.OptionalInt;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
//import ehooray.fishing.component.SpeedUpConsumeItemCalculator;
import ehooray.fishing.component.component.ConditionComponentFilter;
import ehooray.fishing.component.csv.CsvContainer;
//import ehooray.fishing.component.event.EventCsvConsumeItemCalculator;
//import ehooray.fishing.component.event.EventCsvProduceItemCalculator;
//import ehooray.fishing.component.protomap.model.CharacterProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.component.protomap.model.PlayerProtoMap;
import ehooray.fishing.component.protomap.model.ShopProtoMap;
import ehooray.fishing.component.schedule.csv.ScheduleCalculator;
import ehooray.fishing.component.trigger.ConditionComponentTrigger;
import ehooray.fishing.component.utils.WeightRandomer;
import ehooray.fishing.dto.proto.request.shop.BuyRegularItemProto;
//import ehooray.fishing.dto.proto.request.shop.GainByQuestProto;
import ehooray.fishing.dto.proto.request.shop.GetShopItemProto;
//import ehooray.fishing.dto.proto.request.shop.QuestSpeedUpProto;
//import ehooray.fishing.dto.proto.request.shop.StartQuestProto;
import ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto;
//import ehooray.fishing.dto.proto.response.shop.GainByQuestResultProto;
import ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto;
//import ehooray.fishing.dto.proto.response.shop.QuestSpeedUpResultProto;
//import ehooray.fishing.dto.proto.response.shop.StartQuestResultProto;
//import ehooray.fishing.entity.CharacterEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.entity.ShopEntity;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;
//import ehooray.fishing.pojo.csv.CharacterCsv;
import ehooray.fishing.pojo.csv.CommodityCsv;
import ehooray.fishing.pojo.csv.DisplayCsv;
//import ehooray.fishing.pojo.csv.EventCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.ShopCsv;
//import ehooray.fishing.pojo.csv.VariableCsv;
//import ehooray.fishing.service.character.CharacterService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.player.PlayerService;
import ehooray.fishing.service.shop.ShopService;
//import ehooray.fishing.service.shop.ShopService.QuestState;

@Controller("ShopController")
public class ShopController
{
    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected ShopProtoMap shopProtoMap;

    @Autowired
    protected PlayerProtoMap playerProtoMap;

//    @Autowired
//    protected CharacterProtoMap characterProtoMap;
//
//    @Autowired
//    protected CharacterService characterService;

    @Autowired
    protected ShopService shopService;

    @Autowired
    protected PlayerItemService playerItemService;

    @Autowired
    protected PlayerService playerService;

    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;

    @Autowired
    protected ConditionComponentTrigger conditionTrigger;

    @Autowired
    protected WeightRandomer weightRandomer;

    @Autowired
    protected ScheduleCalculator scheduleCalculator;

    @Autowired
    protected ConditionComponentFilter conditionComponentFilter;

//    @Autowired
//    protected EventCsvConsumeItemCalculator consumeItemCalculator;
//
//    @Autowired
//    protected EventCsvProduceItemCalculator produceItemCalculator;
//    
//    @Autowired
//    protected SpeedUpConsumeItemCalculator speedUpConsumeItemCalculator;


    @TransactionMethod(Method.Write)
	public void getShops(WebSocketHandleMediator handleMediator) throws Exception
	{
        PlayerEntity player = handleMediator.getPlayer().get();
        LocalDateTime now = handleMediator.getRequestStartTime();

        ShopCsv shopCsv = csvContainer.getCsv(ShopCsv.class);
        CommodityCsv commodityCsv = csvContainer.getCsv(CommodityCsv.class);

        // remove not on csv shop
        List<ShopEntity> missingCsvShops = shopService.getAllByPlayer(player).stream().filter(
                shop -> shopCsv.getOptionalById(shop.getCsvId()).isEmpty()).collect(Collectors.toList());
        shopService.deleteAll(player, missingCsvShops);
        // remove already out of date shops
        List<ShopEntity> expiredShops = shopService.getAllByPlayer(player).stream().filter(shop ->
        {
            //if(shop.getQuestState() != QuestState.Idle.getId()) return false;
            return scheduleCalculator.isOutStartCycle(DisplayCsv.Type.Shop, shop.getCsvId(), shop.getStartTime().get(), now);
        }).collect(Collectors.toList());
        shopService.deleteAll(player, expiredShops);
        List<ShopEntity> removedShops = new ArrayList<>();
        removedShops.addAll(missingCsvShops);
        removedShops.addAll(expiredShops);

        // find not exist shop rows
        List<Integer> existCsvIds = shopService.getAllByPlayer(player).stream().map(shop -> shop.getCsvId()).collect(Collectors.toList());
        List<ShopCsv.Row> candidateShopRows = shopCsv.getRows().stream().filter(row -> !existCsvIds.contains(row.getId())).collect(Collectors.toList());

        // filter not on schedule
        candidateShopRows.removeIf((row) -> !scheduleCalculator.isInCycle(DisplayCsv.Type.Shop, row.getId(), now));

        // filter unlock rows
        conditionComponentFilter.removeFalsedRows(player, candidateShopRows, (row) -> row.getUnlock());

        // create new shops
        for (ShopCsv.Row shopRow : candidateShopRows)
        {
            Optional<ShopEntity> previousShop = removedShops.stream().filter(shop -> shop.getCsvId() == shopRow.getId()).findFirst();
            CommodityCsv.Row pickCommodityRow = pickCommodity(previousShop, shopRow, commodityCsv.findAllByGroup(shopRow.getCommodityGroup()));

            shopService.create(player, shopRow, pickCommodityRow, now);
        }

		handleMediator.addResult(shopProtoMap.toAnyByEntities(player.getShops(), now));
	}

    protected CommodityCsv.Row pickCommodity(Optional<ShopEntity> previousShop, ShopCsv.Row shopRow, List<CommodityCsv.Row> commodities) throws Exception
    {
        switch(shopRow.getRefreshTypeEnum().get())
        {
            case Random:
                return pickCommodity_Random(shopRow, commodities);

            case Serial:
                return pickCommodity_Serial(previousShop, shopRow, commodities);

            default:
                throw new Exception("not support refresh type = " + shopRow.getRefreshTypeEnum().get());
        }
    }

    protected CommodityCsv.Row pickCommodity_Random(ShopCsv.Row shopRow, List<CommodityCsv.Row> commodities)
    {
        int[] weights = commodities.stream().mapToInt(row -> row.getWeight()).toArray();
        int randomIndex = weightRandomer.random(weights);
        return commodities.get(randomIndex);
    }

    protected CommodityCsv.Row pickCommodity_Serial(Optional<ShopEntity> previousShop, ShopCsv.Row shopRow, List<CommodityCsv.Row> commodities)
    {
        if(previousShop.isEmpty()) return commodities.get(0);

        OptionalInt previousIndex = IntStream.range(0, commodities.size())
                .filter(i -> commodities.get(i).getId() == previousShop.get().getCommodityCsvId())
                .findFirst();

        int newIndex = previousIndex.getAsInt() + 1;
        if(newIndex >= commodities.size())
            return commodities.get(0);
        else
            return commodities.get(newIndex);
    }


    @TransactionMethod(Method.Write)
    public void buyRegularItem(WebSocketHandleMediator handleMediator) throws Exception
    {
        BuyRegularItemProto.BuyRegularItem buyRegularItem = handleMediator.getRequest().getDataList(0).unpack(BuyRegularItemProto.BuyRegularItem.class);

        LocalDateTime now = handleMediator.getRequestStartTime();
        PlayerEntity player = handleMediator.getPlayer().get();
        ShopEntity shopEntity = shopService.findByEntityId(player, buyRegularItem.getShopEntity()).get();
        CommodityCsv commodityCsv = csvContainer.getCsv(CommodityCsv.class);
        CommodityCsv.Row commodityRow = commodityCsv.getById(shopEntity.getCommodityCsvId());

        // buy
        int buyCount = buyRegularItem.getCount();
        shopService.buyShopItem(shopEntity, buyCount);

        // consume items
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.ShopController_buyRegularItem, shopEntity.getCsvId()));
        int requireAmount = commodityRow.isDiscount() ? commodityRow.getDiscountRequireAmount() : commodityRow.getRequireAmount();
        int[] consumeItemIds = { commodityRow.getRequireId() };
        int[] consumeItemCounts = { requireAmount * buyCount };
        ArrayList<PlayerItemService.DiffItem> consumeItemResult = playerItemService.consumeItems(player, consumeItemIds, consumeItemCounts);

        // produce items
        int[] produceItemIds = { commodityRow.getItemId() };
        int[] produceItemCounts = { commodityRow.getItemAmount() * buyCount };
        // validate produce item limit
        // if over limit, can't do anything
        playerItemService.validateItemLimit(player, produceItemIds);
        ArrayList<PlayerItemService.DiffItem> produceItemResult = playerItemService.produceItems(player, produceItemIds, produceItemCounts);

        BuyRegularItemResultProto.BuyRegularItemResult.Builder resultBuilder = BuyRegularItemResultProto.BuyRegularItemResult.newBuilder();
        resultBuilder.addAllConsumeItems(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
        resultBuilder.addAllProduceItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));
        resultBuilder.setShop(shopProtoMap.toBuilderByEntity(shopEntity, now));

        handleMediator.addResult(Any.pack(resultBuilder.build()));

    }

//    @TransactionMethod(Method.Write)
//    public void startQuest(WebSocketHandleMediator handleMediator) throws Exception
//    {
//        StartQuestProto.StartQuest startQuest = handleMediator.getRequest().getDataList(0).unpack(StartQuestProto.StartQuest.class);
//        LocalDateTime now = handleMediator.getRequestStartTime();
//
//        PlayerEntity player = handleMediator.getPlayer().get();
//        ShopEntity shop = shopService.findByEntityId(player, startQuest.getShopEntityId()).get();
//        Optional<CharacterEntity> dispatchCharacter = characterService.findByEntityId(player, startQuest.getCharacterEntityId());
//
//        CommodityCsv.Row commodityRow = csvContainer.getCsv(CommodityCsv.class).getById(shop.getCommodityCsvId());
//        EventCsv.Row eventRow = csvContainer.getCsv(EventCsv.class).getById(commodityRow.getEventId());
//
//        // consume player items
//        List<PlayerItemService.DiffItem> consumeRequireItemResult =
//                consumeItemCalculator.calculate(player, dispatchCharacter, eventRow, shop.getId());
//
//        // shop start quest
//        characterService.dispatchCharacter(dispatchCharacter.get());
//        shopService.startQuest(player, shop, dispatchCharacter.get(), now);
//
//
//        StartQuestResultProto.StartQuestResult.Builder resultBuilder = StartQuestResultProto.StartQuestResult.newBuilder();
//        resultBuilder.setShop(shopProtoMap.toBuilderByEntity(shop, now).build());
//        resultBuilder.addAllPlayerItems(playerItemProtoMap.toProtoByDiffItem(consumeRequireItemResult));
//        resultBuilder.setCharacter(characterProtoMap.toBuilderByEntity(dispatchCharacter.get()));
//        handleMediator.addResult(Any.pack(resultBuilder.build()));
//
//    }
//
//    @TransactionMethod(Method.Write)
//    public void gainByQuest(WebSocketHandleMediator handleMediator) throws Exception
//    {
//        GainByQuestProto.GainByQuest gainByQuest = handleMediator.getRequest().getDataList(0).unpack(GainByQuestProto.GainByQuest.class);
//
//        LocalDateTime now = handleMediator.getRequestStartTime();
//        PlayerEntity player = handleMediator.getPlayer().get();
//        ShopEntity shop = shopService.findByEntityId(player, gainByQuest.getShopEntityId()).get();
//        Optional<CharacterEntity> dispatchCharacter = characterService.findByEntityId(player, shop.getDispatchCharacterId());
//
//        CommodityCsv.Row commodityRow = csvContainer.getCsv(CommodityCsv.class).getById(shop.getCommodityCsvId());
//        EventCsv.Row produceEventRow = csvContainer.getCsv(EventCsv.class).getById(commodityRow.getEventId());
//
//        // gain resource
//        shopService.gainByQuest(shop, now);
//        // character dispatch back
//        characterService.dispatchBackCharacter(dispatchCharacter.get());
//
//        // add exp
//        CharacterCsv.Row dispatchCharacterRow = csvContainer.getCsv(CharacterCsv.class).getById(dispatchCharacter.get().getCsvId());
//        CharacterService.DiffExp characterDiffExpResult = characterService
//                .addExpToCharacter(dispatchCharacter.get(), dispatchCharacterRow, produceEventRow.getExp());
//        PlayerService.DiffExp playerDiffExpResult = playerService.addExpToPlayer(player, produceEventRow.getExp());
//
//        // produce item
//        List<PlayerItemService.DiffItem> produceItemResult = produceItemCalculator.calculate(player, dispatchCharacter, produceEventRow, shop.getId());
//
//        GainByQuestResultProto.GainByQuestResult.Builder resultBuilder = GainByQuestResultProto.GainByQuestResult.newBuilder();
//        resultBuilder.setShop(shopProtoMap.toBuilderByEntity(shop, now).build());
//        resultBuilder.setCharacter(characterProtoMap.toProtoByDiffExp(characterDiffExpResult));
//        resultBuilder.addAllPlayerItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));
//        resultBuilder.setPlayer(playerProtoMap.toBuilderByDiffExp(playerDiffExpResult));
//        handleMediator.addResult(Any.pack(resultBuilder.build()));
//    }

    @TransactionMethod(Method.Read)
    public void getShopItem(WebSocketHandleMediator handleMediator) throws Exception
    {
        GetShopItemProto.GetShopItem getShopItem = handleMediator.getRequest().getDataList(0).unpack(GetShopItemProto.GetShopItem.class);

        PlayerEntity player = handleMediator.getPlayer().get();
        LocalDateTime now = handleMediator.getRequestStartTime();
        ShopEntity findShop = shopService.findByEntityId(player, getShopItem.getEntityId()).get();

        GetShopItemResultProto.GetShopItemResult.Builder resultBuilder = GetShopItemResultProto.GetShopItemResult.newBuilder();
        resultBuilder.setShop(shopProtoMap.toBuilderByEntity(findShop, now).build());
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }

//    @TransactionMethod(Method.Write)
//    public void questSpeedUp(WebSocketHandleMediator handleMediator) throws Exception
//    {
//        QuestSpeedUpProto.QuestSpeedUp questSpeedUp = handleMediator.getRequest().getDataList(0).unpack(QuestSpeedUpProto.QuestSpeedUp.class);
//
//        LocalDateTime now = handleMediator.getRequestStartTime();
//        PlayerEntity player = handleMediator.getPlayer().get();
//        ShopEntity shop = shopService.findByEntityId(player, questSpeedUp.getShopEntityId()).get();
//
//        // consume player item
//        PlayerItemCsv playerItemCsv = csvContainer.getCsv(PlayerItemCsv.class);
//        VariableCsv variableCsv = csvContainer.getCsv(VariableCsv.class);
//        int consumeItemId = variableCsv.getSpeedUpConsumeItem();
//        int consumeItemNum = speedUpConsumeItemCalculator.calculate(shop.getQuestFinishTime().get(), now);
//        PlayerItemCsv.Row playerItemRow = playerItemCsv.getById(consumeItemId);
//        PlayerItemService.DiffItem consumeItemResult = playerItemService.consumeItem(player, playerItemRow, consumeItemNum);
//
//        // do speed up
//        shopService.questSpeedUp(shop, now);
//
//        QuestSpeedUpResultProto.QuestSpeedUpResult.Builder resultBuilder = QuestSpeedUpResultProto.QuestSpeedUpResult.newBuilder();
//        resultBuilder.setShop(shopProtoMap.toBuilderByEntity(shop, now));
//        resultBuilder.setPlayerItem(playerItemProtoMap.toProtoByDiffItem(consumeItemResult));
//        handleMediator.addResult(Any.pack(resultBuilder.build()));
//    }
}
