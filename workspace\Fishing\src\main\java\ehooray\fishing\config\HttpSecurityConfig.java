package ehooray.fishing.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;

import ehooray.fishing.service.auth.HttpSecurityUserDetailService;


@SuppressWarnings("deprecation")
@Configuration
@EnableWebSecurity
public class HttpSecurityConfig extends WebSecurityConfigurerAdapter 
{
	
	@Autowired
	protected HttpSecurityUserDetailService userDetailsService;
	
    @Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception 
	{
        // Note: player login will generate a random token to replace player password for later request
		auth.userDetailsService(userDetailsService).passwordEncoder(NoOpPasswordEncoder.getInstance());
	}
    
    @Override
    protected void configure(HttpSecurity http) throws Exception 
    {
    	http
        .authorizeRequests()
        .antMatchers("/auth/**").permitAll()
        .antMatchers("/thirdparty/**").permitAll()
        // websocket authentication token from url parameter for client can't carry header when webgl version,
        // so permit all and verify when websocket WebSocketHandshakeInterceptor
        .antMatchers("/websocket/**").permitAll()
        .anyRequest().authenticated()
        .and().httpBasic().and()
        .csrf().disable();
    }
}