{"tasks": [{"id": 1, "title": "Project Setup and Core Game Structure (Phaser 3)", "description": "Initialize the Phaser 3 project, set up basic game states (e.g., TitleScreen, BoatMenuScene, FishingScene), and establish the main game loop structure as outlined in the PRD. Configure asset loading for initial sprites and sounds.", "details": "Create a new Phaser 3 project. Define main game configuration (dimensions, renderer). Implement a SceneManager to handle transitions between scenes like TitleScreen, BoatMenuScene. Create placeholder scenes for core gameplay areas. Basic UI elements for the Title Screen (e.g., 'Start Game' button) and Boat Menu (placeholder for time, weather, location). Use JSON for configuration files where appropriate (e.g., scene keys, asset paths).", "testStrategy": "Verify project compiles and runs. Test navigation between Title Screen and Boat Menu. Ensure basic UI elements are displayed correctly. Check console for errors during scene transitions.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Initialize Phaser 3 Project and Define Core Game Configuration", "description": "Set up a new Phaser 3 project using a standard template or by manually including Phaser. Define the main game configuration object including game dimensions (e.g., 800x600), renderer type (e.g., Phaser.AUTO), and a default background color. This establishes the foundational canvas and game instance.", "dependencies": [], "details": "Use npm/yarn to install Phaser 3. Create an `index.html` to host the game and a `main.js` (or `index.js`) to initialize the `Phaser.Game` instance. The configuration object should look similar to: `const config = { type: Phaser.AUTO, width: 800, height: 600, backgroundColor: '#1d212d', parent: 'game-container', physics: { default: 'arcade', arcade: { gravity: { y: 0 } } }, scene: [] }; new Phaser.Game(config);`. Ensure an HTML element with id `game-container` exists.", "status": "pending", "testStrategy": "Verify that a blank Phaser canvas renders in the browser without errors. Check the browser console for the Phaser version log and any initialization errors."}, {"id": 2, "title": "Implement Basic Scene Manager and Initial Scene Registration", "description": "Create a mechanism or utilize Phaser's built-in scene plugin for managing game scenes (TitleScreen, BoatMenuScene, FishingScene). Register these scenes with the game instance. For now, transitions can be simple `this.scene.start('SceneKey')` calls.", "dependencies": [1], "details": "Define basic scene classes (empty shells for now) for `TitleScreen`, `BoatMenuScene`, and `FishingScene`. Each class should extend `Phaser.Scene` and have a unique key. Update the main game configuration's `scene` array to include these scene classes, e.g., `scene: [BootScene, PreloadScene, TitleScreen, BoatMenuScene, FishingScene]`. Ensure one scene (e.g., a `BootScene` or `TitleScreen`) is set to start automatically. A `BootScene` could handle loading minimal assets before showing a `PreloadScene` or `TitleScreen`.", "status": "pending", "testStrategy": "Confirm that the initial scene specified in the game config loads correctly. Use browser console commands like `game.scene.start('OtherSceneKey')` to manually switch between the registered (empty) scenes to verify the scene management basics are working."}, {"id": 3, "title": "Create Placeholder Scenes with Basic Structure", "description": "Flesh out the placeholder scenes (TitleScreen, BoatMenuScene, FishingScene) defined in the previous step. Each scene should have `preload()`, `create()`, and `update()` methods. Add a simple text element to each scene's `create()` method to display its name, confirming it has loaded.", "dependencies": [2], "details": "For each scene file (e.g., `TitleScreen.js`): `class TitleScreen extends Phaser.Scene { constructor() { super({ key: 'TitleScreen' }); } preload() {} create() { this.add.text(this.cameras.main.centerX, this.cameras.main.centerY, 'Title Screen', { fontSize: '32px', fill: '#fff' }).setOrigin(0.5); } update() {} }`. Repeat for `BoatMenuScene` and `FishingScene` with their respective names.", "status": "pending", "testStrategy": "Navigate (manually via console or simple temporary button) to each scene (TitleScreen, BoatMenuScene, FishingScene) and verify that the correct scene name text is displayed in the center of the screen."}, {"id": 4, "title": "Configure Asset Loading for Initial Sprites and Sounds", "description": "Set up an asset loading system. This includes creating an `assets` directory structure and potentially a JSON configuration file (e.g., `assets.json`) to manage asset paths and keys. Load a few placeholder sprites and a sound effect, typically within a dedicated `PreloadScene` or the `preload()` method of the `TitleScreen`.", "dependencies": [3], "details": "Create `assets/images/` and `assets/audio/` folders. Add a sample `logo.png` and `click.wav` (or `.mp3`). Create `PreloadScene.js`. In its `preload()` method, use `this.load.image('logo', 'assets/images/logo.png');` and `this.load.audio('clickSound', 'assets/audio/click.wav');`. In its `create()` method, transition to the `TitleScreen`: `this.scene.start('TitleScreen');`. Ensure `PreloadScene` is the first scene started by the game config.", "status": "pending", "testStrategy": "Check the browser's network tab to confirm assets are loaded. In `TitleScreen.js`'s `create()` method, add the loaded logo: `this.add.image(100, 100, 'logo');`. Verify the logo appears. Play the sound on a click: `this.input.on('pointerdown', () => this.sound.play('clickSound'));`."}, {"id": 5, "title": "Implement Basic UI Elements for Title Screen and Boat Menu", "description": "Add basic interactive UI elements. On the TitleScreen, implement a 'Start Game' button that transitions to the BoatMenuScene. On the BoatMenuScene, add placeholder text elements for Time, Weather, and Location as specified in the PRD.", "dependencies": [2, 3, 4], "details": "In `TitleScreen.js` (`create()`): Add a 'Start Game' button: `const startButton = this.add.text(this.cameras.main.centerX, this.cameras.main.centerY + 100, 'Start Game', { fontSize: '24px', fill: '#fff', backgroundColor: '#555' }).setPadding(10).setOrigin(0.5).setInteractive(); startButton.on('pointerdown', () => this.scene.start('BoatMenuScene'));`. In `BoatMenuScene.js` (`create()`): Add placeholder texts: `this.add.text(50, 50, 'Time: [Placeholder]', { fontSize: '20px', fill: '#fff' }); this.add.text(50, 80, 'Weather: [Placeholder]', { fontSize: '20px', fill: '#fff' }); this.add.text(50, 110, 'Location: [Placeholder]', { fontSize: '20px', fill: '#fff' });`.", "status": "pending", "testStrategy": "On the TitleScreen, verify the 'Start Game' button is visible and clickable. Clicking it should transition to the BoatMenuScene. On the BoatMenuScene, verify the placeholder texts for Time, Weather, and Location are displayed correctly."}]}, {"id": 2, "title": "Time and Weather System Implementation", "description": "Develop the in-game time system with 8 distinct periods and the weather system (Sunny/Rainy). Ensure these systems can influence gameplay events and are displayed on the UI.", "details": "Implement a `TimeSystem` class: Manages 8 time periods (<PERSON>, <PERSON><PERSON>, etc.), each 3 in-game hours. Fishing advances time by 30 mins, travel by 1 hour. Implement a `WeatherSystem` class: Manages Sunny/Rainy states. Weather changes every 2 in-game hours. Sunny boosts Fish Detection, Rainy increases Bite Rate by 10%. Store current time and weather globally or pass to relevant scenes. Update Boat Menu UI to reflect current time/weather. \n```javascript\n// Pseudo-code TimeSystem\nclass TimeSystem {\n  constructor() { this.periods = ['Dawn', ...]; this.currentPeriodIndex = 0; this.gameHour = 0; }\n  advance(minutes) { this.gameHour = (this.gameHour + minutes/60) % 24; /* update period */ }\n  getCurrentPeriod() { return this.periods[Math.floor(this.gameHour / 3)]; }\n}\n// Pseudo-code WeatherSystem\nclass WeatherSystem {\n  constructor() { this.states = ['Sunny', 'Rainy']; this.currentState = 'Sunny'; this.timeSinceLastChange = 0; }\n  update(elapsedGameHours) { this.timeSinceLastChange += elapsedGameHours; if (this.timeSinceLastChange >= 2) { this.currentState = /* random state */; this.timeSinceLastChange = 0; } }\n}\n```", "testStrategy": "Unit test time advancement logic across period changes. Unit test weather change logic. Observe UI updates for time and weather in the Boat Menu. Verify game actions (fishing, travel) correctly advance time. Manually trigger weather changes to test effects if dependent systems are available.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Player and Fish Attribute Systems", "description": "Implement data structures and logic for managing all 34 player attributes (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ikini Assistant, <PERSON>) and 11 fish attributes. Store attribute data in JSON files.", "details": "Create `PlayerAttributes.js` to manage player stats (e.g., level, energy, castAccuracy). Base attributes improve with player level. Equipment attributes will stack. Create `FishData.js` (or JSON files) to define 50 fish types with their attributes (Size, Aggressiveness, Rarity, etc.). Implement functions to calculate effective player attributes by combining base stats, level bonuses, and equipment bonuses. \n```json\n// Example fish_data.json\n[\n  { \"id\": \"minnow\", \"name\": \"Minnow\", \"size\": 1, \"aggressiveness\": 2, ..., \"activeTimePeriod\": [\"Dawn\", \"Morning\"], \"weatherPreference\": \"Sunny\" }\n]\n// Example player_data.json (for save/load)\n{ \"level\": 1, \"xp\": 0, \"energy\": 100, \"maxEnergy\": 100, ... }\n```", "testStrategy": "Unit test attribute calculation logic (base + bonuses). Validate loading of fish data from JSON. Check player leveling effects on attributes. Ensure data persistence for player attributes (stub for save/load).", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "Core Fishing Mechanics - Cast Minigame", "description": "Implement the timing-based Cast Minigame. This includes the cast meter UI, logic for accurate casting to hotspots, and visual feedback (rod animation, lure trajectory).", "details": "Create a Phaser scene for the Cast Minigame. UI: A cast meter (e.g., horizontal bar) with a moving indicator and a highlighted 'Accurate Section'. Logic: Player input (click/tap) stops the indicator. If inside 'Accurate Section', lure lands in a hotspot. 'Accurate Section' size and hotspot visibility are affected by Player's Cast Accuracy and Fish Detection attributes. Visuals: 2D fishing rod casting animation. Lure with fishing line flying towards water. Splash sound on impact. Success proceeds to Lure Minigame. \n```javascript\n// CastMinigameScene.js (Phaser Scene)\n// create() { setupMeter(); this.accurateSection = calculateAccurateSection(player.getEffectiveAttribute('castAccuracy')); }\n// onInput() { if (indicatorPosition >= this.accurateSection.start && indicatorPosition <= this.accurateSection.end) { this.scene.start('LureMinigameScene', { isHotspot: true }); } else { this.scene.start('LureMinigameScene', { isHotspot: false }); } }\n```", "testStrategy": "Unit test cast accuracy logic. Playtest minigame for responsiveness and fairness. Verify visual feedback (animation, splash) matches cast outcome. Test impact of player attributes on 'Accurate Section' size.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Core Fishing Mechanics - Lure Minigame (Enhanced)", "description": "Develop the Lure Minigame, including fish shadow behavior, 3-4 luring phases with rhythm-based inputs for 5 lure types, Lure Simulation UI, and attribute integration.", "details": "Create a Phaser scene for Lure Minigame. UI: Lure Simulation UI (top-right) showing lure movement. Fish shadow approaches lure. Shadow Interest Meter. Input prompts. Logic: Fish shadow (small/medium/large) behavior reflects Fish Aggressiveness/Elusiveness. 3-4 phases; each requires rhythm inputs specific to equipped lure (Spinner: Pulse Tap; Soft Plastic: Drag and Pause; Fly: Swipe Flick Combo; Popper: Tap and <PERSON> Burst; Spoon: Circular Trace). Player Lure Success eases timing. Fish Elusiveness increases difficulty. Successful phases keep shadow; failure makes it flee. Final timed input hooks fish. Implement 'Regular Lure Control' (W/A/D for up/left/right lure movement with 1s cooldown). \n```javascript\n// LureMinigameScene.js\n// currentLureType = player.equippedLure.type;\n// currentPhase = 0; requiredInputs = getInputsForLure(currentLureType, currentPhase);\n// update() { /* handle player inputs, check against requiredInputs, update shadow interest */ }\n// onHookSuccess() { this.scene.start('ReelInMinigameScene', { fishData: currentFish }); }\n```", "testStrategy": "Test each lure type's input mechanics. Verify fish shadow behavior based on attributes. Playtest phase difficulty progression. Check Lure Simulation UI responsiveness. Validate attribute effects (Lure Success, Bite Rate, Fish Aggressiveness/Elusiveness).", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Core Fishing Mechanics - Reel-In Minigame", "description": "Implement the tension-based Reel-In Minigame with a tension meter, fish stamina, QTEs for 10 fish struggle styles, and visual feedback of a struggling fish.", "details": "Create a Phaser scene for Reel-In Minigame. UI: Tension meter, Fish Stamina bar on fish. Visuals: Hooked fish struggling, splashing water, line connecting to rod. Fish swims left/right randomly. Logic: <PERSON> press reels, reduces fish stamina (popup number), increases tension. Tension decreases when not reeling. Line snaps if tension maxes. Player Tension Stability widens safe zone; Reel Speed affects stamina reduction. Fish performs 2-3 struggle styles (e.g., Rapid Thrashing, Deep Dive) triggering QTEs (Quick Tap, Hold/Release, Swipe Chain). QTE failure increases tension to 90%. Fish caught when stamina is 0. \n```javascript\n// ReelInMinigameScene.js\n// update() {\n//   if (input.isMouseDown) { fish.stamina -= player.getEffectiveAttribute('reelSpeed'); tension += X; }\n//   else { tension -= Y; }\n//   if (tension >= MAX_TENSION) { /* line_snap_event */ }\n//   if (fish.stamina <= 0) { /* fish_caught_event */ }\n//   checkForQTETRIGGER(fish.currentStruggleStyle);\n// }\n// onQTEFail() { tension = MAX_TENSION * 0.9; }\n```", "testStrategy": "Test tension meter mechanics (increase/decrease, snapping). Verify fish stamina reduction and catch condition. Implement and test a few QTE types and struggle styles. Validate player attribute effects (Tension Stability, Reel Speed). Playtest for balance and engagement.", "priority": "high", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Inventory and Equipment System", "description": "Implement the inventory system (50-100 slots) for Fish Cards, Equipment (Rods, Lures, Boats, Clothing), and Consumables. Implement the equipment system with 7 slots and attribute stacking.", "details": "Data structure for inventory: `player.inventory = { items: [], capacity: 50 }`. Items have properties like `id`, `type`, `name`, `quantity`, `attributes`. Fishtank: `player.fishtank = { cards: [], capacity: initialValueFromBoat }`. Equipment slots: `player.equipment = { rod: null, lure: null, boat: null, clothing1: null, clothing2: null, clothing3: null, companion1: null }`. Implement functions to add/remove items, equip/unequip gear. Equipped items' stats stack additively with player attributes (modify `getEffectiveAttribute` from Task 3). UI for displaying inventory (categorized, sortable) and equipment slots. Fishtank capacity linked to boat attribute. Selling fish cards from fishtank at port. \n```javascript\n// InventoryManager.js\n// function addItem(item) { if (player.inventory.items.length < player.inventory.capacity) player.inventory.items.push(item); }\n// function equipItem(item, slot) { player.equipment[slot] = item; updatePlayerStats(); }\n```", "testStrategy": "Test adding/removing items from inventory. Test equipping/unequipping items in all slots. Verify attribute changes when equipment is changed. Test inventory capacity limits. Test fishtank capacity and selling fish cards.", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 8, "title": "Crafting System - Merge Mechanics", "description": "Implement the merge crafting system at the Boat Workshop. Define 60 merge recipes for rods, lures, boats, and clothing using Fish Cards, coins, and time.", "details": "Create a `CraftingSystem.js`. Define recipes in a JSON file: `recipes.json` (e.g., `[{ id: 'bamboo_rod', output: 'bamboo_rod_item', inputs: [{ type: 'FishCard', name: 'Minnow', quantity: 2 }], cost: 200, timeMinutes: 2 }, ...]`). Implement logic to check player inventory for required Fish Cards/items and coins. Deduct inputs/coins. Start a crafting timer (can be real-time or game-time based). On completion, add the crafted item to player's inventory. Boat's `CraftingEfficiency` attribute should reduce crafting time/cost. UI for Boat Workshop: display recipes, inputs, costs, and crafting progress. \n```javascript\n// CraftingSystem.js\n// function startCraft(recipeId) {\n//   const recipe = getRecipe(recipeId);\n//   if (hasInputs(player.inventory, recipe.inputs) && player.coins >= recipe.cost) {\n//     removeInputs(player.inventory, recipe.inputs); player.coins -= recipe.cost;\n//     const craftTime = recipe.timeMinutes * (1 - player.getEffectiveBoatAttribute('craftingEfficiency'));\n//     addCraftingJob({ recipeId: recipe.id, completionTime: Date.now() + craftTime * 60000 });\n//   }\n// }\n```", "testStrategy": "Test crafting various items: check input consumption, coin deduction, and item generation. Verify crafting timer functionality. Test recipes requiring other crafted items. Validate `CraftingEfficiency` attribute effect. Ensure UI correctly displays recipes and crafting status.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Boat Menu, Navigation, and Game Loop Integration", "description": "Fully implement the Boat Menu UI and functionality: map/spot selection (5 maps, 10 spots each), travel mechanics, energy system, and integration of core gameplay actions (Fishing, Chat, Shop, Inventory, Return to Port).", "details": "Enhance `BoatMenuScene`. UI: Display current Time, Weather, Location (Map X, Spot Y or Starting Port), Player Energy. Buttons: Travel, Go Fishing, Go Chatroom, Go Shop (active only at Starting Port), Go Inventory, Return to Port (active only at fishing spots). Travel: UI to select map (1 of 5) then spot (1 of 10). Updates player location, advances time (+1h), consumes Energy (-2). `BoatSpeed` attribute reduces travel time. Fishing: UI to choose Story/Practice Mode. Leads to Cast Minigame. Advances time (+30m), consumes Energy (-5). Energy System: Max 100 (base), +2/level. Actions consume energy. Return to Port: Sets location to Starting Port, advances time (+1h), consumes Energy (-2). Implement data for maps and spots (e.g., in JSON). \n```javascript\n// BoatMenuScene.js\n// onTravel(mapId, spotId) { player.location = {mapId, spotId}; timeSystem.advance(60); player.energy -= 2; }\n// onGoFishing() { /* select mode */ scene.start('CastMinigameScene'); timeSystem.advance(30); player.energy -= 5; }\n```", "testStrategy": "Test all Boat Menu button functionalities. Verify travel logic: map/spot selection, time/energy consumption, location update. Test fishing initiation. Check energy consumption and regeneration (if any). Ensure Shop/Return to Port buttons are conditionally active. Verify UI correctly reflects game state (time, weather, location, energy).", "priority": "high", "dependencies": [1, 2, 4, 7], "status": "pending", "subtasks": []}, {"id": 10, "title": "Social System - RenJS Integration and Basic Companion Interaction", "description": "Integrate RenJS for the story and dialog system. Implement the Cruise Chatroom UI and basic interactions (gift, chat stub) with one sample companion.", "details": "Set up RenJS library within the Phaser 3 project. This might involve an iframe or direct integration if RenJS supports it. Create data structures for 10 companions (name, personality, romanceMeter=0). Implement Cruise Chatroom UI: list companions, select one, show interaction options (<PERSON>, Chat). Gift: Allow player to select a Fish Card from inventory to gift. Stub romance point increase. Chat: On selecting 'Chat', trigger a basic RenJS scene for the selected companion (e.g., `RenJS.call('mia_intro_dialogue')`). Create a minimal RenJS script for one companion's introductory dialogue. Focus on the technical bridge between Phaser and RenJS. \n```javascript\n// CruiseChatroomScene.js (Phaser)\n// onGift(companionId, fishCardId) { /* remove fishCard from inventory, call <PERSON>J<PERSON> to update romance */ renJS.call('update_romance', { companion: companionId, points: 5 }); }\n// onChat(companionId) { renJS.call(companionId + '_dialogue_start'); /* potentially hide Phaser UI, show RenJS UI */ }\n\n// RenJS script (example)\n// label mia_intro_dialogue:\n//   mia \"Hello there, <PERSON><PERSON>!\"\n//   return\n```", "testStrategy": "Verify RenJS initializes correctly within or alongside Phaser. Test displaying a simple RenJS dialogue scene triggered from Phaser. Test gifting a Fish Card (inventory deduction, stubbed romance update). Ensure UI for chatroom and companion selection works. Check basic communication between Phaser and RenJS (e.g., passing companion ID).", "priority": "medium", "dependencies": [1, 7], "status": "pending", "subtasks": []}]}