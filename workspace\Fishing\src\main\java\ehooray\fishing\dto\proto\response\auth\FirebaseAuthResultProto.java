// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/auth/FirebaseAuthResult.proto

package ehooray.fishing.dto.proto.response.auth;

public final class FirebaseAuthResultProto {
  private FirebaseAuthResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FirebaseAuthResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.auth.FirebaseAuthResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool isResgiter = 1;</code>
     * @return The isResgiter.
     */
    boolean getIsResgiter();

    /**
     * <code>string token = 2;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <code>string token = 2;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();
  }
  /**
   * Protobuf type {@code response.auth.FirebaseAuthResult}
   */
  public static final class FirebaseAuthResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.auth.FirebaseAuthResult)
      FirebaseAuthResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FirebaseAuthResult.newBuilder() to construct.
    private FirebaseAuthResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FirebaseAuthResult() {
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FirebaseAuthResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.internal_static_response_auth_FirebaseAuthResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.internal_static_response_auth_FirebaseAuthResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.class, ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.Builder.class);
    }

    public static final int ISRESGITER_FIELD_NUMBER = 1;
    private boolean isResgiter_;
    /**
     * <code>bool isResgiter = 1;</code>
     * @return The isResgiter.
     */
    @java.lang.Override
    public boolean getIsResgiter() {
      return isResgiter_;
    }

    public static final int TOKEN_FIELD_NUMBER = 2;
    private volatile java.lang.Object token_;
    /**
     * <code>string token = 2;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <code>string token = 2;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.auth.FirebaseAuthResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.auth.FirebaseAuthResult)
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.internal_static_response_auth_FirebaseAuthResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.internal_static_response_auth_FirebaseAuthResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.class, ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isResgiter_ = false;

        token_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.internal_static_response_auth_FirebaseAuthResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult build() {
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult buildPartial() {
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult result = new ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult(this);
        result.isResgiter_ = isResgiter_;
        result.token_ = token_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private boolean isResgiter_ ;
      /**
       * <code>bool isResgiter = 1;</code>
       * @return The isResgiter.
       */
      @java.lang.Override
      public boolean getIsResgiter() {
        return isResgiter_;
      }
      /**
       * <code>bool isResgiter = 1;</code>
       * @param value The isResgiter to set.
       * @return This builder for chaining.
       */
      public Builder setIsResgiter(boolean value) {
        
        isResgiter_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isResgiter = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsResgiter() {
        
        isResgiter_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <code>string token = 2;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string token = 2;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string token = 2;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string token = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>string token = 2;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.auth.FirebaseAuthResult)
    }

    // @@protoc_insertion_point(class_scope:response.auth.FirebaseAuthResult)
    private static final ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult();
    }

    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FirebaseAuthResult>
        PARSER = new com.google.protobuf.AbstractParser<FirebaseAuthResult>() {
      @java.lang.Override
      public FirebaseAuthResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FirebaseAuthResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FirebaseAuthResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.auth.FirebaseAuthResultProto.FirebaseAuthResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_auth_FirebaseAuthResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_auth_FirebaseAuthResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&response/auth/FirebaseAuthResult.proto" +
      "\022\rresponse.auth\"7\n\022FirebaseAuthResult\022\022\n" +
      "\nisResgiter\030\001 \001(\010\022\r\n\005token\030\002 \001(\tB\\\n\'ehoo" +
      "ray.fishing.dto.proto.response.authB\027Fir" +
      "ebaseAuthResultProtoH\002\252\002\025Fishing.Network" +
      ".Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_response_auth_FirebaseAuthResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_auth_FirebaseAuthResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_auth_FirebaseAuthResult_descriptor,
        new java.lang.String[] { "IsResgiter", "Token", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
