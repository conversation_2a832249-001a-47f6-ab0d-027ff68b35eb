package ehooray.fishing.service.server;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ehooray.fishing.entity.ServerInfoEntity;
import ehooray.fishing.repository.ServerInfoRepository;

@Service
public class ServerInfoService
{
    @Autowired
    protected ServerInfoRepository serverInfoRepository;

    protected String myIp;

    public Optional<ServerInfoEntity> myServerInfo()
    {
        return serverInfoRepository.findByIp(myIp);
    }

    protected ServerInfoEntity createByIp(String myIp)
    {
        ServerInfoEntity serverInfoEntity = new ServerInfoEntity();
        serverInfoEntity.setIp(myIp);
        serverInfoRepository.save(serverInfoEntity);
        return serverInfoEntity;
    }

    public void finishClientPatch(ServerInfoEntity serverInfoEntity, LocalDateTime now)
    {
        serverInfoEntity.setClientPatch(false);
        serverInfoEntity.setUpdateClientPatchTime(now);
        serverInfoRepository.save(serverInfoEntity);
    }

    public void setMyIp(String myIp, LocalDateTime now)
    {
        this.myIp = myIp;

        Optional<ServerInfoEntity> findServerInfoEntity = serverInfoRepository.findByIp(myIp);
        if(findServerInfoEntity.isEmpty())
            createByIp(myIp);
    }

    public String getMyIp()
    {
        return myIp;
    }



}
