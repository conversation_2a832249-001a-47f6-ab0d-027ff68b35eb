// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/shop/BuyRegularItemResult.proto

package ehooray.fishing.dto.proto.response.shop;

public final class BuyRegularItemResultProto {
  private BuyRegularItemResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BuyRegularItemResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.shop.BuyRegularItemResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Shop shop = 1;</code>
     * @return Whether the shop field is set.
     */
    boolean hasShop();
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return The shop.
     */
    ehooray.fishing.dto.proto.model.ShopProto.Shop getShop();
    /**
     * <code>.model.Shop shop = 1;</code>
     */
    ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumeItemsList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    int getConsumeItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index);

    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getProduceItemsList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index);
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    int getProduceItemsCount();
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.shop.BuyRegularItemResult}
   */
  public static final class BuyRegularItemResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.shop.BuyRegularItemResult)
      BuyRegularItemResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BuyRegularItemResult.newBuilder() to construct.
    private BuyRegularItemResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BuyRegularItemResult() {
      consumeItems_ = java.util.Collections.emptyList();
      produceItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BuyRegularItemResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.internal_static_response_shop_BuyRegularItemResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.internal_static_response_shop_BuyRegularItemResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.class, ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.Builder.class);
    }

    public static final int SHOP_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.ShopProto.Shop shop_;
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return Whether the shop field is set.
     */
    @java.lang.Override
    public boolean hasShop() {
      return shop_ != null;
    }
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return The shop.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ShopProto.Shop getShop() {
      return shop_ == null ? ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
    }
    /**
     * <code>.model.Shop shop = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder() {
      return getShop();
    }

    public static final int CONSUMEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_;
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public int getConsumeItemsCount() {
      return consumeItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
      return consumeItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index) {
      return consumeItems_.get(index);
    }

    public static final int PRODUCEITEMS_FIELD_NUMBER = 3;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_;
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getProduceItemsOrBuilderList() {
      return produceItems_;
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    @java.lang.Override
    public int getProduceItemsCount() {
      return produceItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
      return produceItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem produceItems = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
        int index) {
      return produceItems_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.shop.BuyRegularItemResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.shop.BuyRegularItemResult)
        ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.internal_static_response_shop_BuyRegularItemResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.internal_static_response_shop_BuyRegularItemResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.class, ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumeItemsFieldBuilder();
          getProduceItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (shopBuilder_ == null) {
          shop_ = null;
        } else {
          shop_ = null;
          shopBuilder_ = null;
        }
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeItemsBuilder_.clear();
        }
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.internal_static_response_shop_BuyRegularItemResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult build() {
        ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult buildPartial() {
        ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult result = new ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult(this);
        int from_bitField0_ = bitField0_;
        if (shopBuilder_ == null) {
          result.shop_ = shop_;
        } else {
          result.shop_ = shopBuilder_.build();
        }
        if (consumeItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumeItems_ = java.util.Collections.unmodifiableList(consumeItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumeItems_ = consumeItems_;
        } else {
          result.consumeItems_ = consumeItemsBuilder_.build();
        }
        if (produceItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            produceItems_ = java.util.Collections.unmodifiableList(produceItems_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.produceItems_ = produceItems_;
        } else {
          result.produceItems_ = produceItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.ShopProto.Shop shop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder> shopBuilder_;
      /**
       * <code>.model.Shop shop = 1;</code>
       * @return Whether the shop field is set.
       */
      public boolean hasShop() {
        return shopBuilder_ != null || shop_ != null;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       * @return The shop.
       */
      public ehooray.fishing.dto.proto.model.ShopProto.Shop getShop() {
        if (shopBuilder_ == null) {
          return shop_ == null ? ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
        } else {
          return shopBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder setShop(ehooray.fishing.dto.proto.model.ShopProto.Shop value) {
        if (shopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          shop_ = value;
          onChanged();
        } else {
          shopBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder setShop(
          ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder builderForValue) {
        if (shopBuilder_ == null) {
          shop_ = builderForValue.build();
          onChanged();
        } else {
          shopBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder mergeShop(ehooray.fishing.dto.proto.model.ShopProto.Shop value) {
        if (shopBuilder_ == null) {
          if (shop_ != null) {
            shop_ =
              ehooray.fishing.dto.proto.model.ShopProto.Shop.newBuilder(shop_).mergeFrom(value).buildPartial();
          } else {
            shop_ = value;
          }
          onChanged();
        } else {
          shopBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder clearShop() {
        if (shopBuilder_ == null) {
          shop_ = null;
          onChanged();
        } else {
          shop_ = null;
          shopBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder getShopBuilder() {
        
        onChanged();
        return getShopFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder() {
        if (shopBuilder_ != null) {
          return shopBuilder_.getMessageOrBuilder();
        } else {
          return shop_ == null ?
              ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
        }
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder> 
          getShopFieldBuilder() {
        if (shopBuilder_ == null) {
          shopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder>(
                  getShop(),
                  getParentForChildren(),
                  isClean());
          shop_ = null;
        }
        return shopBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumeItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumeItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumeItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumeItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
        if (consumeItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeItems_);
        } else {
          return consumeItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public int getConsumeItemsCount() {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.size();
        } else {
          return consumeItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);
        } else {
          return consumeItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addAllConsumeItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeItems_);
          onChanged();
        } else {
          consumeItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder clearConsumeItems() {
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder removeConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.remove(index);
          onChanged();
        } else {
          consumeItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
          int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);  } else {
          return consumeItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumeItemsOrBuilderList() {
        if (consumeItemsBuilder_ != null) {
          return consumeItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder() {
        return getConsumeItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumeItemsBuilderList() {
        return getConsumeItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumeItemsFieldBuilder() {
        if (consumeItemsBuilder_ == null) {
          consumeItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumeItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumeItems_ = null;
        }
        return consumeItemsBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> produceItems_ =
        java.util.Collections.emptyList();
      private void ensureProduceItemsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          produceItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(produceItems_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> produceItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getProduceItemsList() {
        if (produceItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(produceItems_);
        } else {
          return produceItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public int getProduceItemsCount() {
        if (produceItemsBuilder_ == null) {
          return produceItems_.size();
        } else {
          return produceItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);
        } else {
          return produceItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.set(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder setProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder addProduceItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (produceItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProduceItemsIsMutable();
          produceItems_.add(index, value);
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder addProduceItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder addProduceItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          produceItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder addAllProduceItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, produceItems_);
          onChanged();
        } else {
          produceItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder clearProduceItems() {
        if (produceItemsBuilder_ == null) {
          produceItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          produceItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public Builder removeProduceItems(int index) {
        if (produceItemsBuilder_ == null) {
          ensureProduceItemsIsMutable();
          produceItems_.remove(index);
          onChanged();
        } else {
          produceItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getProduceItemsOrBuilder(
          int index) {
        if (produceItemsBuilder_ == null) {
          return produceItems_.get(index);  } else {
          return produceItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getProduceItemsOrBuilderList() {
        if (produceItemsBuilder_ != null) {
          return produceItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(produceItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder() {
        return getProduceItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addProduceItemsBuilder(
          int index) {
        return getProduceItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem produceItems = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getProduceItemsBuilderList() {
        return getProduceItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getProduceItemsFieldBuilder() {
        if (produceItemsBuilder_ == null) {
          produceItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  produceItems_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          produceItems_ = null;
        }
        return produceItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.shop.BuyRegularItemResult)
    }

    // @@protoc_insertion_point(class_scope:response.shop.BuyRegularItemResult)
    private static final ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult();
    }

    public static ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BuyRegularItemResult>
        PARSER = new com.google.protobuf.AbstractParser<BuyRegularItemResult>() {
      @java.lang.Override
      public BuyRegularItemResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<BuyRegularItemResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BuyRegularItemResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.shop.BuyRegularItemResultProto.BuyRegularItemResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_shop_BuyRegularItemResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_shop_BuyRegularItemResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(response/shop/BuyRegularItemResult.pro" +
      "to\022\rresponse.shop\032\026model/PlayerItem.prot" +
      "o\032\020model/Shop.proto\"\203\001\n\024BuyRegularItemRe" +
      "sult\022\031\n\004shop\030\001 \001(\0132\013.model.Shop\022\'\n\014consu" +
      "meItems\030\002 \003(\0132\021.model.PlayerItem\022\'\n\014prod" +
      "uceItems\030\003 \003(\0132\021.model.PlayerItemB^\n\'eho" +
      "oray.fishing.dto.proto.response.shopB\031Bu" +
      "yRegularItemResultProtoH\002\252\002\025Formosa.Netw" +
      "ork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.ShopProto.getDescriptor(),
        });
    internal_static_response_shop_BuyRegularItemResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_shop_BuyRegularItemResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_shop_BuyRegularItemResult_descriptor,
        new java.lang.String[] { "Shop", "ConsumeItems", "ProduceItems", });
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
    ehooray.fishing.dto.proto.model.ShopProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
