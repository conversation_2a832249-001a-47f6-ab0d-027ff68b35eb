<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatroom Fix Test - <PERSON><PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
            color: #ecf0f1;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-info {
            background: rgba(44, 62, 80, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #c0392b;
        }
        #gameContainer {
            width: 100%;
            height: 600px;
            background: #000;
            border-radius: 10px;
            border: 2px solid #34495e;
        }
        .fix-details {
            background: rgba(46, 125, 50, 0.2);
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .fix-details h4 {
            color: #4caf50;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Chatroom Scene Fix Test</h1>
            <p>Testing the fixed DialogManager dependency issue</p>
        </div>

        <div class="fix-details">
            <h4>🛠️ Fixes Applied:</h4>
            <ul>
                <li><strong>Fallback DialogManager:</strong> Creates standalone NPC data when GameScene not available</li>
                <li><strong>Safe Event Listeners:</strong> Handles missing gameScene.events gracefully</li>
                <li><strong>Error Handling:</strong> Try-catch blocks for scene transitions</li>
                <li><strong>Local Storage Recovery:</strong> Loads saved romance data independently</li>
                <li><strong>Self-Contained NPCs:</strong> Chat system works without external dependencies</li>
            </ul>
        </div>

        <div class="test-info">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li>Click "Start Game" to launch the Phaser game</li>
                <li>Navigate to Boat Menu scene</li>
                <li>Click the "CHAT" button</li>
                <li>Verify ChatroomScene loads without errors</li>
                <li>Test NPC selection and romance interactions</li>
                <li>Check console for proper fallback messages</li>
            </ol>
            
            <button class="button" onclick="startGame()">🚀 Start Game Test</button>
            <button class="button" onclick="testDirectChatroom()">💬 Test Chatroom Direct</button>
        </div>

        <div id="gameContainer">
            <!-- Phaser game will be injected here -->
        </div>

        <div class="test-info">
            <h4>🔍 Expected Results:</h4>
            <ul>
                <li>✅ ChatroomScene loads without errors</li>
                <li>✅ NPCs display with proper data (Mia, Sophie, Luna)</li>
                <li>✅ Romance meters show and update correctly</li>
                <li>✅ Chat messages send and receive responses</li>
                <li>✅ Quick actions work (gifts, flirting, etc.)</li>
                <li>✅ Console shows fallback manager messages if needed</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { game } from './src/main.js';

        window.startGame = function() {
            console.log('🔧 Starting Chatroom Fix Test...');
            console.log('Navigate: Menu → Boat Menu → CHAT button');
        };

        window.testDirectChatroom = function() {
            console.log('🧪 Testing direct ChatroomScene access...');
            
            // Try to access the scene manager and start ChatroomScene directly
            if (window.game && window.game.scene) {
                try {
                    window.game.scene.start('ChatroomScene', {
                        callingScene: 'BoatMenuScene'
                    });
                    console.log('✅ Direct ChatroomScene start attempted');
                } catch (error) {
                    console.error('❌ Direct ChatroomScene start failed:', error);
                }
            } else {
                console.log('⚠️ Game not ready for direct scene access');
            }
        };

        // Auto-detect when game is ready
        window.addEventListener('load', () => {
            console.log('🔧 Chatroom Fix Test Ready!');
            console.log('🎯 Test the CHAT button from Boat Menu to verify the fix');
            
            // Store game reference globally for testing
            window.game = game;
        });
    </script>
</body>
</html> 