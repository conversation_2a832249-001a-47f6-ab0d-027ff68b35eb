package ehooray.fishing.pojo;

import javax.servlet.http.HttpSession;

public class HttpProtoHandleMediator extends ProtoHandleMediator
{
	protected HttpSession session;
	
	protected HttpProtoHandleMediator() 
	{
	    super();
	}
	
	public HttpSession getSession() 
	{
		return session;
	}
	
	public static class Builder extends ProtoHandleMediator.Builder
	{
		protected HttpProtoHandleMediator castInstance;
		
		public Builder()
		{
		    super(new HttpProtoHandleMediator());
		    castInstance = (HttpProtoHandleMediator)instance;
		}
		
		public void setSession(HttpSession session)
		{
		    castInstance.session = session;
		}
	}
	
}
