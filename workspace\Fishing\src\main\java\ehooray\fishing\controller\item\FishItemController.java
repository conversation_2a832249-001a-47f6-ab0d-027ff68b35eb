package ehooray.fishing.controller.item;

import java.util.ArrayList;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.FishItemProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.dto.proto.request.fishitem.SellFishProto;
import ehooray.fishing.dto.proto.response.fishitem.SellFishResultProto;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.csv.FishCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.service.item.FishItemService;
import ehooray.fishing.service.item.PlayerItemService;

@Controller("FishItemController")
public class FishItemController
{
    @Autowired
    protected CsvContainer csvContainer;
    @Autowired
    protected FishItemProtoMap fishItemMap;
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    @Autowired
    protected FishItemService fishItemService;
    @Autowired
    protected PlayerItemService playerItemService;
    
    @TransactionMethod(Method.Write)
    public void getFishItems(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var nowTime = handleMediator.getRequestStartTime();
        var player = handleMediator.getPlayer().get();
        
        // action
        // remove already out of date fish
        var expiredFish = fishItemService.getAllByPlayer(player).stream().filter(
            fish -> {
                var fishRow = csvContainer.getCsv(FishCsv.class).getById(fish.getCsvId());
                boolean outTime = fish.getExpireTime().isPresent() && nowTime.isAfter(fish.getExpireTime().get());
                return outTime && !fishRow.isNFT();
            }
        ).collect(Collectors.toList());
        fishItemService.deleteAll(player, expiredFish);
        
        // response
        handleMediator.addResult(fishItemMap.toAnyByEntities(player.getFishItems()));
    }
    
    @TransactionMethod(Method.Write)
    public void sellFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var sellFish =  handleMediator.getRequest().getDataList(0).unpack(SellFishProto.SellFish.class);
        var nowTime = handleMediator.getRequestStartTime();
        var player = handleMediator.getPlayer().get();
        var fishItems = fishItemService.findByEntityIds(player, sellFish.getEntityIdsList());
        
        // action
        var produceItemResult = new ArrayList<PlayerItemService.DiffItem>();
        for (var fishItem : fishItems)
        {
            handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.FishItemController_sellFish, fishItem.getCsvId()));
            
            // sell(delete entity)
            fishItemService.sellFish(player, fishItem, nowTime);
            
            // add coin
            var fishCsvRow = csvContainer.getCsv(FishCsv.class).getById(fishItem.getCsvId());
            var coinCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.GameCoin.getId());
            int cointCount = Math.round(((float)fishItem.getSize() / fishCsvRow.getMaxSize()) * fishCsvRow.getMaxPrice());
            produceItemResult.add(playerItemService.produceItem(player, coinCsvRow, cointCount));
        }
        
        // response
        var resultBuilder = SellFishResultProto.SellFishResult.newBuilder();
        resultBuilder.addAllFishItems(fishItemMap.toProtoByEntities(fishItems));
        resultBuilder.addAllProduceItems(playerItemProtoMap.toProtoByDiffItem(produceItemResult));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
