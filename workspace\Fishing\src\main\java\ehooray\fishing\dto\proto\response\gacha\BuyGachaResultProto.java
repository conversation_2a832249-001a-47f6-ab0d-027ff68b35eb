// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/gacha/BuyGachaResult.proto

package ehooray.fishing.dto.proto.response.gacha;

public final class BuyGachaResultProto {
  private BuyGachaResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BuyGachaResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.gacha.BuyGachaResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Gacha gacha = 1;</code>
     * @return Whether the gacha field is set.
     */
    boolean hasGacha();
    /**
     * <code>.model.Gacha gacha = 1;</code>
     * @return The gacha.
     */
    ehooray.fishing.dto.proto.model.GachaProto.Gacha getGacha();
    /**
     * <code>.model.Gacha gacha = 1;</code>
     */
    ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder getGachaOrBuilder();

    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> 
        getConsumeItemsList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index);
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    int getConsumeItemsCount();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList();
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index);

    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    java.util.List<com.google.protobuf.Any> 
        getModelsList();
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    com.google.protobuf.Any getModels(int index);
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    int getModelsCount();
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getModelsOrBuilderList();
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code response.gacha.BuyGachaResult}
   */
  public static final class BuyGachaResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.gacha.BuyGachaResult)
      BuyGachaResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BuyGachaResult.newBuilder() to construct.
    private BuyGachaResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BuyGachaResult() {
      consumeItems_ = java.util.Collections.emptyList();
      models_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BuyGachaResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.internal_static_response_gacha_BuyGachaResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.internal_static_response_gacha_BuyGachaResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.class, ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.Builder.class);
    }

    public static final int GACHA_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.GachaProto.Gacha gacha_;
    /**
     * <code>.model.Gacha gacha = 1;</code>
     * @return Whether the gacha field is set.
     */
    @java.lang.Override
    public boolean hasGacha() {
      return gacha_ != null;
    }
    /**
     * <code>.model.Gacha gacha = 1;</code>
     * @return The gacha.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.GachaProto.Gacha getGacha() {
      return gacha_ == null ? ehooray.fishing.dto.proto.model.GachaProto.Gacha.getDefaultInstance() : gacha_;
    }
    /**
     * <code>.model.Gacha gacha = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder getGachaOrBuilder() {
      return getGacha();
    }

    public static final int CONSUMEITEMS_FIELD_NUMBER = 2;
    private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_;
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
        getConsumeItemsOrBuilderList() {
      return consumeItems_;
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public int getConsumeItemsCount() {
      return consumeItems_.size();
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
      return consumeItems_.get(index);
    }
    /**
     * <code>repeated .model.PlayerItem consumeItems = 2;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
        int index) {
      return consumeItems_.get(index);
    }

    public static final int MODELS_FIELD_NUMBER = 3;
    private java.util.List<com.google.protobuf.Any> models_;
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.Any> getModelsList() {
      return models_;
    }
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
        getModelsOrBuilderList() {
      return models_;
    }
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    @java.lang.Override
    public int getModelsCount() {
      return models_.size();
    }
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    @java.lang.Override
    public com.google.protobuf.Any getModels(int index) {
      return models_.get(index);
    }
    /**
     * <code>repeated .google.protobuf.Any models = 3;</code>
     */
    @java.lang.Override
    public com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
        int index) {
      return models_.get(index);
    }

    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.gacha.BuyGachaResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.gacha.BuyGachaResult)
        ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.internal_static_response_gacha_BuyGachaResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.internal_static_response_gacha_BuyGachaResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.class, ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getConsumeItemsFieldBuilder();
          getModelsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (gachaBuilder_ == null) {
          gacha_ = null;
        } else {
          gacha_ = null;
          gachaBuilder_ = null;
        }
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeItemsBuilder_.clear();
        }
        if (modelsBuilder_ == null) {
          models_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          modelsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.internal_static_response_gacha_BuyGachaResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult build() {
        ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult buildPartial() {
        ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult result = new ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult(this);
        int from_bitField0_ = bitField0_;
        if (gachaBuilder_ == null) {
          result.gacha_ = gacha_;
        } else {
          result.gacha_ = gachaBuilder_.build();
        }
        if (consumeItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            consumeItems_ = java.util.Collections.unmodifiableList(consumeItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consumeItems_ = consumeItems_;
        } else {
          result.consumeItems_ = consumeItemsBuilder_.build();
        }
        if (modelsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            models_ = java.util.Collections.unmodifiableList(models_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.models_ = models_;
        } else {
          result.models_ = modelsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private ehooray.fishing.dto.proto.model.GachaProto.Gacha gacha_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.GachaProto.Gacha, ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder, ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder> gachaBuilder_;
      /**
       * <code>.model.Gacha gacha = 1;</code>
       * @return Whether the gacha field is set.
       */
      public boolean hasGacha() {
        return gachaBuilder_ != null || gacha_ != null;
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       * @return The gacha.
       */
      public ehooray.fishing.dto.proto.model.GachaProto.Gacha getGacha() {
        if (gachaBuilder_ == null) {
          return gacha_ == null ? ehooray.fishing.dto.proto.model.GachaProto.Gacha.getDefaultInstance() : gacha_;
        } else {
          return gachaBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public Builder setGacha(ehooray.fishing.dto.proto.model.GachaProto.Gacha value) {
        if (gachaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gacha_ = value;
          onChanged();
        } else {
          gachaBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public Builder setGacha(
          ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder builderForValue) {
        if (gachaBuilder_ == null) {
          gacha_ = builderForValue.build();
          onChanged();
        } else {
          gachaBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public Builder mergeGacha(ehooray.fishing.dto.proto.model.GachaProto.Gacha value) {
        if (gachaBuilder_ == null) {
          if (gacha_ != null) {
            gacha_ =
              ehooray.fishing.dto.proto.model.GachaProto.Gacha.newBuilder(gacha_).mergeFrom(value).buildPartial();
          } else {
            gacha_ = value;
          }
          onChanged();
        } else {
          gachaBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public Builder clearGacha() {
        if (gachaBuilder_ == null) {
          gacha_ = null;
          onChanged();
        } else {
          gacha_ = null;
          gachaBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder getGachaBuilder() {
        
        onChanged();
        return getGachaFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder getGachaOrBuilder() {
        if (gachaBuilder_ != null) {
          return gachaBuilder_.getMessageOrBuilder();
        } else {
          return gacha_ == null ?
              ehooray.fishing.dto.proto.model.GachaProto.Gacha.getDefaultInstance() : gacha_;
        }
      }
      /**
       * <code>.model.Gacha gacha = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.GachaProto.Gacha, ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder, ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder> 
          getGachaFieldBuilder() {
        if (gachaBuilder_ == null) {
          gachaBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.GachaProto.Gacha, ehooray.fishing.dto.proto.model.GachaProto.Gacha.Builder, ehooray.fishing.dto.proto.model.GachaProto.GachaOrBuilder>(
                  getGacha(),
                  getParentForChildren(),
                  isClean());
          gacha_ = null;
        }
        return gachaBuilder_;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> consumeItems_ =
        java.util.Collections.emptyList();
      private void ensureConsumeItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          consumeItems_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem>(consumeItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> consumeItemsBuilder_;

      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> getConsumeItemsList() {
        if (consumeItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeItems_);
        } else {
          return consumeItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public int getConsumeItemsCount() {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.size();
        } else {
          return consumeItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);
        } else {
          return consumeItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder setConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem value) {
        if (consumeItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, value);
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addConsumeItems(
          int index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder builderForValue) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder addAllConsumeItems(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem> values) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeItems_);
          onChanged();
        } else {
          consumeItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder clearConsumeItems() {
        if (consumeItemsBuilder_ == null) {
          consumeItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public Builder removeConsumeItems(int index) {
        if (consumeItemsBuilder_ == null) {
          ensureConsumeItemsIsMutable();
          consumeItems_.remove(index);
          onChanged();
        } else {
          consumeItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder getConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder getConsumeItemsOrBuilder(
          int index) {
        if (consumeItemsBuilder_ == null) {
          return consumeItems_.get(index);  } else {
          return consumeItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
           getConsumeItemsOrBuilderList() {
        if (consumeItemsBuilder_ != null) {
          return consumeItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeItems_);
        }
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder() {
        return getConsumeItemsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder addConsumeItemsBuilder(
          int index) {
        return getConsumeItemsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance());
      }
      /**
       * <code>repeated .model.PlayerItem consumeItems = 2;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder> 
           getConsumeItemsBuilderList() {
        return getConsumeItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder> 
          getConsumeItemsFieldBuilder() {
        if (consumeItemsBuilder_ == null) {
          consumeItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder>(
                  consumeItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          consumeItems_ = null;
        }
        return consumeItemsBuilder_;
      }

      private java.util.List<com.google.protobuf.Any> models_ =
        java.util.Collections.emptyList();
      private void ensureModelsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          models_ = new java.util.ArrayList<com.google.protobuf.Any>(models_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> modelsBuilder_;

      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public java.util.List<com.google.protobuf.Any> getModelsList() {
        if (modelsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(models_);
        } else {
          return modelsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public int getModelsCount() {
        if (modelsBuilder_ == null) {
          return models_.size();
        } else {
          return modelsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public com.google.protobuf.Any getModels(int index) {
        if (modelsBuilder_ == null) {
          return models_.get(index);
        } else {
          return modelsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder setModels(
          int index, com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.set(index, value);
          onChanged();
        } else {
          modelsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder setModels(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.set(index, builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder addModels(com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.add(value);
          onChanged();
        } else {
          modelsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder addModels(
          int index, com.google.protobuf.Any value) {
        if (modelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModelsIsMutable();
          models_.add(index, value);
          onChanged();
        } else {
          modelsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder addModels(
          com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.add(builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder addModels(
          int index, com.google.protobuf.Any.Builder builderForValue) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.add(index, builderForValue.build());
          onChanged();
        } else {
          modelsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder addAllModels(
          java.lang.Iterable<? extends com.google.protobuf.Any> values) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, models_);
          onChanged();
        } else {
          modelsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder clearModels() {
        if (modelsBuilder_ == null) {
          models_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          modelsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public Builder removeModels(int index) {
        if (modelsBuilder_ == null) {
          ensureModelsIsMutable();
          models_.remove(index);
          onChanged();
        } else {
          modelsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public com.google.protobuf.Any.Builder getModelsBuilder(
          int index) {
        return getModelsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public com.google.protobuf.AnyOrBuilder getModelsOrBuilder(
          int index) {
        if (modelsBuilder_ == null) {
          return models_.get(index);  } else {
          return modelsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
           getModelsOrBuilderList() {
        if (modelsBuilder_ != null) {
          return modelsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(models_);
        }
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public com.google.protobuf.Any.Builder addModelsBuilder() {
        return getModelsFieldBuilder().addBuilder(
            com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public com.google.protobuf.Any.Builder addModelsBuilder(
          int index) {
        return getModelsFieldBuilder().addBuilder(
            index, com.google.protobuf.Any.getDefaultInstance());
      }
      /**
       * <code>repeated .google.protobuf.Any models = 3;</code>
       */
      public java.util.List<com.google.protobuf.Any.Builder> 
           getModelsBuilderList() {
        return getModelsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getModelsFieldBuilder() {
        if (modelsBuilder_ == null) {
          modelsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  models_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          models_ = null;
        }
        return modelsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.gacha.BuyGachaResult)
    }

    // @@protoc_insertion_point(class_scope:response.gacha.BuyGachaResult)
    private static final ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult();
    }

    public static ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BuyGachaResult>
        PARSER = new com.google.protobuf.AbstractParser<BuyGachaResult>() {
      @java.lang.Override
      public BuyGachaResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<BuyGachaResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BuyGachaResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.gacha.BuyGachaResultProto.BuyGachaResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_gacha_BuyGachaResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_gacha_BuyGachaResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#response/gacha/BuyGachaResult.proto\022\016r" +
      "esponse.gacha\032\031google/protobuf/any.proto" +
      "\032\021model/Gacha.proto\032\026model/PlayerItem.pr" +
      "oto\"|\n\016BuyGachaResult\022\033\n\005gacha\030\001 \001(\0132\014.m" +
      "odel.Gacha\022\'\n\014consumeItems\030\002 \003(\0132\021.model" +
      ".PlayerItem\022$\n\006models\030\003 \003(\0132\024.google.pro" +
      "tobuf.AnyBY\n(ehooray.fishing.dto.proto.r" +
      "esponse.gachaB\023BuyGachaResultProtoH\002\252\002\025F" +
      "ishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.GachaProto.getDescriptor(),
          ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor(),
        });
    internal_static_response_gacha_BuyGachaResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_gacha_BuyGachaResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_gacha_BuyGachaResult_descriptor,
        new java.lang.String[] { "Gacha", "ConsumeItems", "Models", });
    com.google.protobuf.AnyProto.getDescriptor();
    ehooray.fishing.dto.proto.model.GachaProto.getDescriptor();
    ehooray.fishing.dto.proto.model.PlayerItemProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
