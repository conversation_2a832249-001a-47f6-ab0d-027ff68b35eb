/**
 * 繁體中文文本配置
 * 集中管理遊戲中所有顯示文本的中文翻譯
 */
export const TextConfig = {
    // 主選單
    menu: {
        title: '豪華釣手',
        subtitle: '頂級釣魚體驗',
        boatMenu: '船艙選單',
        settings: '設定',
        exit: '離開',
        instructions: '使用 WASD 移動，空白鍵拋竿'
    },
    
    // 船艙選單
    boatMenu: {
        title: '船艙選單',
        startFishing: '開始釣魚',
        inventory: '背包',
        crafting: '製作',
        shop: '商店',
        cabin: '船艙',
        quest: '任務',
        collection: '收藏',
        travel: '旅行',
        tournament: '錦標賽',
        returnToPort: '返回港口',
        player: '玩家',
        welcomeBack: '歡迎回到釣魚之旅！',
        errorRecovery: '由於錯誤從釣魚返回。遊戲狀態可能已恢復。'
    },
    
    // 設定選單
    settings: {
        title: '遊戲設定',
        audio: '音效設定',
        graphics: '圖形設定',
        controls: '控制設定',
        language: '語言設定',
        back: '返回',
        save: '儲存',
        reset: '重設',
        volume: '音量',
        master: '主音量',
        music: '音樂',
        sfx: '音效',
        quality: '品質',
        fullscreen: '全螢幕',
        vsync: '垂直同步'
    },
    
    // 音效設定
    audioSettings: {
        title: '音效設定',
        advanced: '進階設定',
        quality: '音質',
        low: '低',
        medium: '中',
        high: '高',
        ultra: '超高',
        spatialAudio: '空間音效',
        reverbEffects: '混響效果',
        dynamicRange: '動態範圍',
        testAudio: '測試音效'
    },
    
    // 背包系統
    inventory: {
        title: '背包',
        equipment: '裝備',
        items: '物品',
        fish: '魚類',
        lures: '魚餌',
        tools: '工具',
        materials: '材料',
        empty: '空的',
        weight: '重量',
        capacity: '容量',
        sort: '排序',
        filter: '篩選',
        sell: '出售',
        use: '使用',
        equip: '裝備',
        unequip: '卸下'
    },
    
    // 製作系統
    crafting: {
        title: '製作',
        recipes: '配方',
        materials: '材料',
        craft: '製作',
        craftAll: '全部製作',
        required: '需要',
        available: '可用',
        insufficient: '材料不足',
        success: '製作成功',
        failed: '製作失敗',
        categories: {
            lures: '魚餌',
            equipment: '裝備',
            tools: '工具',
            consumables: '消耗品'
        }
    },
    
    // 商店系統
    shop: {
        title: '商店',
        buy: '購買',
        sell: '出售',
        price: '價格',
        quantity: '數量',
        total: '總計',
        money: '金錢',
        insufficient: '金錢不足',
        purchased: '購買成功',
        sold: '出售成功',
        categories: {
            equipment: '裝備',
            lures: '魚餌',
            tools: '工具',
            materials: '材料',
            rods: '釣竿',
            boats: '船',
            items: '道具'
        },
        currency: '金幣',
        messages: {
            notEnoughMoney: '金幣不足',
            purchased: '已購買',
            purchaseFailed: '購買失敗',
            shopOnlyAtPort: '只能在港口購買'
        },
        fishInventory: {
            title: '魚類庫存',
            noFish: '目前沒有魚類可出售',
            addTestFish: '添加測試魚類',
            sellAll: '全部出售',
            sell: '出售',
            sold: '已出售',
            soldOut: '已售完',
            soldAll: '已全部出售',
            for: '獲得',
            fishFor: '條魚，獲得',
            noFishToSell: '沒有魚類可出售',
            addedTestFish: '已添加測試魚類'
        }
    },
    
    // 釣魚系統
    fishing: {
        casting: '拋竿',
        luring: '誘餌',
        reeling: '收線',
        caught: '釣到了',
        escaped: '逃脫了',
        perfect: '完美',
        good: '良好',
        fair: '普通',
        poor: '糟糕',
        tension: '張力',
        stamina: '體力',
        patience: '耐心',
        clickToCast: '點擊拋竿',
        useWASD: '使用 WASD 控制',
        holdSpace: '按住空白鍵'
    },
    
    // 魚類收藏
    collection: {
        title: '魚類收藏',
        caught: '已釣到',
        notCaught: '未釣到',
        rarity: '稀有度',
        size: '大小',
        weight: '重量',
        location: '地點',
        time: '時間',
        weather: '天氣',
        bait: '魚餌',
        common: '普通',
        uncommon: '不常見',
        rare: '稀有',
        epic: '史詩',
        legendary: '傳說'
    },
    
    // 任務系統
    quest: {
        title: '任務',
        active: '進行中',
        completed: '已完成',
        available: '可接受',
        locked: '已鎖定',
        objective: '目標',
        reward: '獎勵',
        progress: '進度',
        accept: '接受',
        complete: '完成',
        abandon: '放棄'
    },
    
    // 錦標賽系統
    tournament: {
        title: '錦標賽',
        join: '參加',
        leave: '離開',
        leaderboard: '排行榜',
        rank: '排名',
        score: '分數',
        timeLeft: '剩餘時間',
        prize: '獎品',
        entry: '報名',
        fee: '費用',
        requirements: '要求'
    },
    
    // 玩家進度
    player: {
        level: '等級',
        experience: '經驗值',
        skill: '技能',
        stats: '統計',
        achievements: '成就',
        profile: '個人資料',
        fishing: '釣魚',
        crafting: '製作',
        trading: '交易',
        exploration: '探索'
    },
    
    // 通用按鈕和操作
    common: {
        ok: '確定',
        cancel: '取消',
        yes: '是',
        no: '否',
        confirm: '確認',
        close: '關閉',
        open: '開啟',
        save: '儲存',
        load: '載入',
        delete: '刪除',
        edit: '編輯',
        search: '搜尋',
        filter: '篩選',
        sort: '排序',
        refresh: '重新整理',
        help: '說明',
        info: '資訊',
        warning: '警告',
        error: '錯誤',
        success: '成功',
        loading: '載入中...',
        processing: '處理中...',
        connecting: '連接中...',
        disconnected: '已斷線',
        retry: '重試',
        skip: '跳過',
        next: '下一個',
        previous: '上一個',
        continue: '繼續',
        pause: '暫停',
        resume: '繼續',
        start: '開始',
        stop: '停止',
        reset: '重設'
    },
    
    // 時間和天氣
    timeWeather: {
        time: '時間',
        weather: '天氣',
        sunny: '晴天',
        cloudy: '多雲',
        rainy: '雨天',
        stormy: '暴風雨',
        foggy: '霧天',
        dawn: '黎明',
        morning: '早晨',
        noon: '中午',
        afternoon: '下午',
        evening: '傍晚',
        night: '夜晚',
        midnight: '午夜'
    },
    
    // 狀態訊息
    status: {
        gameStateError: '遊戲狀態錯誤',
        connectionLost: '連接中斷',
        savingGame: '儲存遊戲中...',
        gameLoaded: '遊戲已載入',
        autoSaveEnabled: '自動儲存已啟用',
        lowEnergy: '體力不足',
        inventoryFull: '背包已滿',
        insufficientFunds: '資金不足',
        itemNotFound: '找不到物品',
        actionNotAllowed: '不允許此操作',
        featureNotAvailable: '功能暫不可用'
    },
    
    // 教學和提示
    tutorial: {
        welcome: '歡迎來到豪華釣手！',
        castingTip: '點擊並拖拽來調整拋竿力度和方向',
        luringTip: '使用 WASD 鍵移動魚餌吸引魚類',
        reelingTip: '保持張力平衡，避免魚線斷裂',
        inventoryTip: '定期整理背包，出售不需要的物品',
        craftingTip: '收集材料製作更好的裝備',
        questTip: '完成任務獲得經驗值和獎勵'
    }
};

export default TextConfig; 