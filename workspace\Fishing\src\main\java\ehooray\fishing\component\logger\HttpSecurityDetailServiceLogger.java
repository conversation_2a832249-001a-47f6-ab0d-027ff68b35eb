package ehooray.fishing.component.logger;

import org.apache.logging.log4j.LogManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class HttpSecurityDetailServiceLogger
{
    @AfterThrowing(value = "execution(* org.springframework.security.core.userdetails.UserDetailsService.loadUserByUsername(*))", throwing="exception")
    public void afterThrowing_loadUserByUsername(JoinPoint joinPoint, Exception exception)
    {
        LogManager.getLogger(this.getClass()).error(exception.getMessage(), exception);
    }
}