<!-- Generated by SpecStory -->

# 移除動態直橫版功能請求 (2025-05-13 17:25:59)

_**User**_

請把動態更改直橫版的功能拿掉

---

_**Assistant**_



---

好的，我将移除代码中动态更改屏幕方向的功能。

首先，我将修改 `Assets/Fishing/Scripts/UI/Battle/PausePanelManager.cs` 文件。
我将移除 `UpdateReturnToSelectPlaceAsync` 方法中设置屏幕为纵向的代码。

然后，我将修改 `Assets/Fishing/Scripts/UI/MapUI/MapPanel.cs` 文件。
我将移除 `GoMapRunner` 方法中设置屏幕为横向的代码。



---

```diff
          {
              await battleService.FailFish();
- 
-             // 設定螢幕為縱向
-             Screen.orientation = ScreenOrientation.Portrait;
-             Screen.autorotateToPortrait = true;
-             Screen.autorotateToPortraitUpsideDown = true;
-             Screen.autorotateToLandscapeLeft = false;
-             Screen.autorotateToLandscapeRight = false;
  
              await sceneLoadingManager.LoadSceneAsync("Lobby", (container) =>
```

---

```diff
                  GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.BattleMpaPrefabPath + mapData.Map);
                  
-                 // 設定螢幕為橫向
-                 Screen.orientation = ScreenOrientation.LandscapeLeft; 
-                 Screen.autorotateToPortrait = false;
-                 Screen.autorotateToPortraitUpsideDown = false;
-                 Screen.autorotateToLandscapeLeft = true;
-                 Screen.autorotateToLandscapeRight = true;
- 
                  sceneLoadingManager.LoadSceneAsync("NewBattle", (container) =>
                  {
```

---

我已经移除了 `PausePanelManager.cs` 和 `MapPanel.cs` 中动态更改屏幕方向的代码。

---

