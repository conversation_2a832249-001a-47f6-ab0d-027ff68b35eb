package ehooray.fishing.entity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
//@formatter:off
@Table
(
    name = "player",
    indexes =
    {
        @Index(name = "name", columnList = "name"),
    }
)
//@formatter:on
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class PlayerEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected String name;
    protected int level;
    protected int exp;
    protected LocalDateTime apLastCountDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_auth_id", nullable = false, referencedColumnName = "id")
    protected PlayerAuthEntity playerAuth;

    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<PlayerItemEntity> playerItems;

    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<QuestEntity> quests;

    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<AchievementEntity> achievements;

    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<ShopEntity> shops;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<ExchangeEntity> exchanges;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<FishRodEntity> fishRods;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "active_fishkit", referencedColumnName = "id")
    protected FishKitEntity activeFishKit;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<FishKitEntity> fishKits;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<SpotEntity> spots;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "battle_spot", referencedColumnName = "id")
    protected SpotEntity battleSpot;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<FishItemEntity> fishItems;
    
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
    @OneToMany(mappedBy = "player", fetch = FetchType.LAZY)
    protected List<GachaEntity> gachas;

    public long getId()
    {
        return id;
    }
    
    public void setId(long id)
    {
        this.id = id;
    }
    
    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }
    
    public int getLevel()
    {
        return level;
    }

    public void setLevel(int level)
    {
        this.level = level;
    }
    
    public int getExp()
    {
        return exp;
    }

    public void setExp(int exp)
    {
        this.exp = exp;
    }

    public PlayerAuthEntity getPlayerAuth()
    {
        return playerAuth;
    }

    public void setPlayerAuth(PlayerAuthEntity playerAuth)
    {
        this.playerAuth = playerAuth;
    }

    public List<PlayerItemEntity> getPlayerItems()
    {
        return playerItems;
    }

    public void setPlayerItems(List<PlayerItemEntity> playerItems)
    {
        this.playerItems = playerItems;
    }

    public List<QuestEntity> getQuests()
    {
        return quests;
    }

    public void setQuests(List<QuestEntity> quests)
    {
        this.quests = quests;
    }

    public List<AchievementEntity> getAchievements()
    {
        return achievements;
    }

    public void setAchievements(List<AchievementEntity> achievements)
    {
        this.achievements = achievements;
    }

    public List<ShopEntity> getShops()
    {
        return shops;
    }

    public void setShops(List<ShopEntity> shops)
    {
        this.shops = shops;
    }
    
    public List<ExchangeEntity> getExchanges()
    {
        return exchanges;
    }

    public void setExchanges(List<ExchangeEntity> exchanges)
    {
        this.exchanges = exchanges;
    }

    public List<FishRodEntity> getFishRods() 
    {
        return fishRods;
    }

    public void setFishRods(List<FishRodEntity> fishRods) 
    {
        this.fishRods = fishRods;
    }
    
    public FishKitEntity getActiveFishKit()
    {
        return activeFishKit;
    }
    
    public void setActiveFishKit(FishKitEntity activeFishKit)
    {
        this.activeFishKit = activeFishKit;
    }

    public List<FishKitEntity> getFishKits()
    {
        return fishKits;
    }

    public void setFishKits(List<FishKitEntity> fishKit)
    {
        this.fishKits = fishKit;
    }

    public List<SpotEntity> getSpots()
    {
        return spots;
    }

    public void setSpots(List<SpotEntity> spots)
    {
        this.spots = spots;
    }
    
    public SpotEntity getBattleSpot()
    {
        return battleSpot;
    }

    public void setBattleSpot(SpotEntity battleSpot)
    {
        this.battleSpot = battleSpot;
    }

    public List<FishItemEntity> getFishItems()
    {
        return fishItems;
    }

    public void setFishItems(List<FishItemEntity> fishItems)
    {
        this.fishItems = fishItems;
    }

    public Optional<LocalDateTime> getApLastCountDate()
    {
        return Optional.ofNullable(apLastCountDate);
    }

    public void setApLastCountDate(LocalDateTime apLastCountDate)
    {
        this.apLastCountDate = apLastCountDate;
    }
    
    public List<GachaEntity> getGachas()
    {
        return gachas;
    }

    public void setGachas(List<GachaEntity> gachas)
    {
        this.gachas = gachas;
    }
}
