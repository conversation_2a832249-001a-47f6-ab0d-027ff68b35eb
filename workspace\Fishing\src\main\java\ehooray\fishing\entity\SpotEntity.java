package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table(name="fishing_spot")
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class SpotEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected int csvId;
    @Column(name="fish_csv_id")
    protected int fishCsvId;
    protected int hitArea;
    protected int retryFish;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false, referencedColumnName = "id")
    private PlayerEntity player;
    
    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }
    
    public int getCsvId()
    {
        return csvId;
    }
    
    public void setFishCsvId(int fishCsvId)
    {
        this.fishCsvId = fishCsvId;
    }
    
    public int getFishCsvId()
    {
        return fishCsvId;
    }
    
    public void setHitArea(int hitArea)
    {
        this.hitArea = hitArea;
    }
    
    public int getHitArea()
    {
        return hitArea;
    }
    
    public void setRetryFish(int retryFish)
    {
        this.retryFish = retryFish;
    }
    
    public int getRetryFish()
    {
        return retryFish;
    }
    
    public void setPlayer(PlayerEntity player)
    {
        this.player = player;
    }
    
    public PlayerEntity getPlayer()
    {
        return player;
    }
}
