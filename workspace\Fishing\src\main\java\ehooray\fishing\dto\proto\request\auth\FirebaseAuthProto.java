// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/auth/FirebaseAuth.proto

package ehooray.fishing.dto.proto.request.auth;

public final class FirebaseAuthProto {
  private FirebaseAuthProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FirebaseAuthOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.auth.FirebaseAuth)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string idToken = 1;</code>
     * @return The idToken.
     */
    java.lang.String getIdToken();
    /**
     * <code>string idToken = 1;</code>
     * @return The bytes for idToken.
     */
    com.google.protobuf.ByteString
        getIdTokenBytes();

    /**
     * <code>string userId = 2;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>string userId = 2;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>int32 serverId = 3;</code>
     * @return The serverId.
     */
    int getServerId();
  }
  /**
   * Protobuf type {@code request.auth.FirebaseAuth}
   */
  public static final class FirebaseAuth extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.auth.FirebaseAuth)
      FirebaseAuthOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FirebaseAuth.newBuilder() to construct.
    private FirebaseAuth(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FirebaseAuth() {
      idToken_ = "";
      userId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FirebaseAuth();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.internal_static_request_auth_FirebaseAuth_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.internal_static_request_auth_FirebaseAuth_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.class, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder.class);
    }

    public static final int IDTOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object idToken_;
    /**
     * <code>string idToken = 1;</code>
     * @return The idToken.
     */
    @java.lang.Override
    public java.lang.String getIdToken() {
      java.lang.Object ref = idToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idToken_ = s;
        return s;
      }
    }
    /**
     * <code>string idToken = 1;</code>
     * @return The bytes for idToken.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdTokenBytes() {
      java.lang.Object ref = idToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private volatile java.lang.Object userId_;
    /**
     * <code>string userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userId_ = s;
        return s;
      }
    }
    /**
     * <code>string userId = 2;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SERVERID_FIELD_NUMBER = 3;
    private int serverId_;
    /**
     * <code>int32 serverId = 3;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.auth.FirebaseAuth}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.auth.FirebaseAuth)
        ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuthOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.internal_static_request_auth_FirebaseAuth_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.internal_static_request_auth_FirebaseAuth_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.class, ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        idToken_ = "";

        userId_ = "";

        serverId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.internal_static_request_auth_FirebaseAuth_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth build() {
        ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth buildPartial() {
        ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth result = new ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth(this);
        result.idToken_ = idToken_;
        result.userId_ = userId_;
        result.serverId_ = serverId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private java.lang.Object idToken_ = "";
      /**
       * <code>string idToken = 1;</code>
       * @return The idToken.
       */
      public java.lang.String getIdToken() {
        java.lang.Object ref = idToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          idToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string idToken = 1;</code>
       * @return The bytes for idToken.
       */
      public com.google.protobuf.ByteString
          getIdTokenBytes() {
        java.lang.Object ref = idToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string idToken = 1;</code>
       * @param value The idToken to set.
       * @return This builder for chaining.
       */
      public Builder setIdToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        idToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string idToken = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdToken() {
        
        idToken_ = getDefaultInstance().getIdToken();
        onChanged();
        return this;
      }
      /**
       * <code>string idToken = 1;</code>
       * @param value The bytes for idToken to set.
       * @return This builder for chaining.
       */
      public Builder setIdTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        idToken_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object userId_ = "";
      /**
       * <code>string userId = 2;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string userId = 2;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>string userId = 2;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        userId_ = value;
        onChanged();
        return this;
      }

      private int serverId_ ;
      /**
       * <code>int32 serverId = 3;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <code>int32 serverId = 3;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 serverId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.auth.FirebaseAuth)
    }

    // @@protoc_insertion_point(class_scope:request.auth.FirebaseAuth)
    private static final ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth();
    }

    public static ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FirebaseAuth>
        PARSER = new com.google.protobuf.AbstractParser<FirebaseAuth>() {
      @java.lang.Override
      public FirebaseAuth parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FirebaseAuth> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FirebaseAuth> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.auth.FirebaseAuthProto.FirebaseAuth getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_auth_FirebaseAuth_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_auth_FirebaseAuth_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037request/auth/FirebaseAuth.proto\022\014reque" +
      "st.auth\"A\n\014FirebaseAuth\022\017\n\007idToken\030\001 \001(\t" +
      "\022\016\n\006userId\030\002 \001(\t\022\020\n\010serverId\030\003 \001(\005BU\n&eh" +
      "ooray.fishing.dto.proto.request.authB\021Fi" +
      "rebaseAuthProtoH\002\252\002\025Fishing.Network.Prot" +
      "ob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_auth_FirebaseAuth_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_auth_FirebaseAuth_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_auth_FirebaseAuth_descriptor,
        new java.lang.String[] { "IdToken", "UserId", "ServerId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
