// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/iab/unity/UnityIabCreate.proto

package ehooray.fishing.dto.proto.request.iab.unity;

public final class UnityIabCreateProto {
  private UnityIabCreateProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnityIabCreateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.iab.unity.UnityIabCreate)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 iabCsvId = 1;</code>
     * @return The iabCsvId.
     */
    int getIabCsvId();

    /**
     * <code>int32 platformId = 2;</code>
     * @return The platformId.
     */
    int getPlatformId();
  }
  /**
   * Protobuf type {@code request.iab.unity.UnityIabCreate}
   */
  public static final class UnityIabCreate extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.iab.unity.UnityIabCreate)
      UnityIabCreateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UnityIabCreate.newBuilder() to construct.
    private UnityIabCreate(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UnityIabCreate() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UnityIabCreate();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.internal_static_request_iab_unity_UnityIabCreate_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.internal_static_request_iab_unity_UnityIabCreate_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.Builder.class);
    }

    public static final int IABCSVID_FIELD_NUMBER = 1;
    private int iabCsvId_;
    /**
     * <code>int32 iabCsvId = 1;</code>
     * @return The iabCsvId.
     */
    @java.lang.Override
    public int getIabCsvId() {
      return iabCsvId_;
    }

    public static final int PLATFORMID_FIELD_NUMBER = 2;
    private int platformId_;
    /**
     * <code>int32 platformId = 2;</code>
     * @return The platformId.
     */
    @java.lang.Override
    public int getPlatformId() {
      return platformId_;
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.iab.unity.UnityIabCreate}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.iab.unity.UnityIabCreate)
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.internal_static_request_iab_unity_UnityIabCreate_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.internal_static_request_iab_unity_UnityIabCreate_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.class, ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        iabCsvId_ = 0;

        platformId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.internal_static_request_iab_unity_UnityIabCreate_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate build() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate buildPartial() {
        ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate result = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate(this);
        result.iabCsvId_ = iabCsvId_;
        result.platformId_ = platformId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int iabCsvId_ ;
      /**
       * <code>int32 iabCsvId = 1;</code>
       * @return The iabCsvId.
       */
      @java.lang.Override
      public int getIabCsvId() {
        return iabCsvId_;
      }
      /**
       * <code>int32 iabCsvId = 1;</code>
       * @param value The iabCsvId to set.
       * @return This builder for chaining.
       */
      public Builder setIabCsvId(int value) {
        
        iabCsvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 iabCsvId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIabCsvId() {
        
        iabCsvId_ = 0;
        onChanged();
        return this;
      }

      private int platformId_ ;
      /**
       * <code>int32 platformId = 2;</code>
       * @return The platformId.
       */
      @java.lang.Override
      public int getPlatformId() {
        return platformId_;
      }
      /**
       * <code>int32 platformId = 2;</code>
       * @param value The platformId to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformId(int value) {
        
        platformId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 platformId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatformId() {
        
        platformId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.iab.unity.UnityIabCreate)
    }

    // @@protoc_insertion_point(class_scope:request.iab.unity.UnityIabCreate)
    private static final ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate();
    }

    public static ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnityIabCreate>
        PARSER = new com.google.protobuf.AbstractParser<UnityIabCreate>() {
      @java.lang.Override
      public UnityIabCreate parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnityIabCreate> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnityIabCreate> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.iab.unity.UnityIabCreateProto.UnityIabCreate getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_iab_unity_UnityIabCreate_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_iab_unity_UnityIabCreate_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&request/iab/unity/UnityIabCreate.proto" +
      "\022\021request.iab.unity\"6\n\016UnityIabCreate\022\020\n" +
      "\010iabCsvId\030\001 \001(\005\022\022\n\nplatformId\030\002 \001(\005B\\\n+e" +
      "hooray.fishing.dto.proto.request.iab.uni" +
      "tyB\023UnityIabCreateProtoH\002\252\002\025Fishing.Netw" +
      "ork.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_iab_unity_UnityIabCreate_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_iab_unity_UnityIabCreate_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_iab_unity_UnityIabCreate_descriptor,
        new java.lang.String[] { "IabCsvId", "PlatformId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
