using System.Collections.Generic;
using BestHTTP.SecureProtocol.Org.BouncyCastle.Utilities;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Fishing.Strategies
{
    /// <summary>
    /// 釣魚等待狀態策略
    /// </summary>
    public class NewWaitStrategy : FishingStrategyBase
    {
        private bool enterReady = false;

        float waitTime = 1; // second

        const int Times = 4;
        List<int> times = new List<int> { 3, 3, 3, 3 };

        bool qteFinished = false;
        public NewWaitStrategy() : base(FishingState.Wait) { }
        
        public override UniTask OnEnter(FishingManager context)
        {
            // 原SetStateToWait方法中的初始化邏輯
            context.cordReelManager.wheelRotate.SetWaitFishSpeed();
            context.baitPath.StopBaitAnimation();
            context.fishRod.HideBaitObject();

            // 打开UI并等待一帧确保Canvas模式已经被正确设置
            InitLureActionSystemAsync(context).Forget();

            waitTime = 1;

            enterReady = true;

            return UniTask.CompletedTask;
        }

        private async UniTaskVoid InitLureActionSystemAsync(FishingManager context)
        {
            await context.fishingGamePanelManager.OpenAsync<LureActionSystem>();
            await context.fishingGamePanelManager.OpenAsync<LureAnimationController>();
            await UniTask.DelayFrame(1); // 等待一帧确保Canvas已经被正确设置
            LureActionSystem lureActionSystem = context.lureActionSystem;
            lureActionSystem.InitAndStartLureMinigame(Times, times, QTECallback);
            lureActionSystem.SetTargetDirection(context.lureAnimationController.OnQTESuccess);
        }

        void QTECallback(LureActionSystem.QTEResult result, int round)
        {
            if (result == LureActionSystem.QTEResult.Perfect)
                waitTime += 0;
            else if (result == LureActionSystem.QTEResult.Good)
                waitTime += 1;
            else if (result == LureActionSystem.QTEResult.Miss)
                waitTime += 2;

           if (round > Times)
           {
                qteFinished = true;
           }
        }
        
        public override void OnUpdate(FishingManager context)
        {
            if (!enterReady)
                return;

            if (!qteFinished)
                return;

            context.forceItem.PlayWaterEffectForWait();
            context.forceItem.SetForce(5);

            waitTime -= Time.deltaTime;
            if (waitTime <= 0)
            {
                enterReady = false;
                qteFinished = false;
                context.fishingGamePanelManager.Close<LureActionSystem>();
                context.fishingGamePanelManager.Close<LureAnimationController>();
                context.TransitionToState(FishingState.Fight).Forget();                
            }
        }
        
        public override UniTask OnExit(FishingManager context)
        {
            return UniTask.CompletedTask;
        }
    }
} 