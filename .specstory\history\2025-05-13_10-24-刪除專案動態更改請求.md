<!-- Generated by SpecStory -->

# 刪除專案動態更改請求 (2025-05-13 18:24:48)

_**User**_

請把該專案有動態更改直橫版的部分刪除

---

_**Assistant**_



---

好的，我将查找项目中动态更改横竖屏的相关代码，然后将其删除。

首先，我会搜索一些可能与屏幕方向相关的关键词，例如 `Screen.orientation`。


---

<details>
            <summary>Searched codebase "Screen.orientation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `Assets/Ehooray/Runtime/UI/SafeArea.cs` | L1-56 |
| `Assets/Fishing/Scripts/UI/Battle/TopInfoManager.cs` | L1-64 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMOrbitalCamera.cs` | L130-177 |
| `Assets/Naninovel/Runtime/Common/AspectMonitor.cs` | L1-62 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMCameraAspectRatio.cs` | L1-50 |
| `Assets/Fishing/Scripts/Camera/NaniCamera.cs` | L1-38 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMCameraAspectRatio.cs` | L50-95 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMOrbitalCamera.cs` | L1-60 |
| `Assets/Naninovel/Runtime/Common/ScriptableUI/ScriptableUIBehaviour.cs` | L1-83 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanARComponentScript.cs` | L42-90 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanOrbitComponentScript.cs` | L158-207 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanARComponentScript.cs` | L354-390 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMOrbitalCamera.cs` | L60-130 |
| `Assets/Naninovel/Runtime/Camera/CameraManager.cs` | L319-381 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMGyroscope/MMGyroscope.cs` | L1-36 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanOrbitComponentScript.cs` | L66-90 |
| `Assets/Naninovel/Runtime/UI/GameSettings/GameSettingsScreenModeDropdown.cs` | L47-93 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanOrbitComponentScript.cs` | L119-158 |
| `Assets/Fishing/Editor/NewFishingUICreator.cs` | L62-85 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMControls/MMTouchJoystick.cs` | L103-172 |
| `Assets/Fishing/Scripts/Fish/LureActionSystem.cs` | L45-92 |
| `Assets/Naninovel/Runtime/Camera/CameraManager.cs` | L1-46 |
| `Assets/Fingers/Prefab/Script/Components/FingersRotateOrbitComponentScript.cs` | L1-49 |
| `Assets/Naninovel/Runtime/UI/ReactToAspectRatio.cs` | L1-47 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanRotateScaleComponentScript.cs` | L1-53 |

</details>

---

搜索结果显示，`Assets/Fishing/Scripts/UI/Battle/TopInfoManager.cs` 文件中存在明确设置屏幕方向的代码。

在 `TopInfoManager.cs` 的 `BackToLobby` 方法中，有以下代码：
```csharp
            // 設定螢幕為縱向
            Screen.orientation = ScreenOrientation.Portrait;
            Screen.autorotateToPortrait = true;
            Screen.autorotateToPortraitUpsideDown = true;
            Screen.autorotateToLandscapeLeft = false;
            Screen.autorotateToLandscapeRight = false;
```
这部分代码在返回大厅时将屏幕强制设置为纵向。

另外，`Assets/Ehooray/Runtime/UI/SafeArea.cs` 文件中，`HasSafeAreaChanged` 方法会检测 `Screen.orientation` 的变化来刷新UI。这部分代码本身不主动改变屏幕方向，而是响应方向变化。删除主动改变屏幕方向的代码后，这部分逻辑应该仍然可以正常工作（或者说，如果游戏固定了方向，`lastOrientation != Screen.orientation` 这个条件将不再为真）。

`Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMOrbitalCamera.cs` 和其他一些 `MoreMountains` 及 `Naninovel`、`Fingers` 插件中的文件也引用了 `Screen.orientation` 或屏幕宽高，但它们似乎更多的是为了适应屏幕，而不是主动改变屏幕方向。

目前来看，最需要修改的是 `Assets/Fishing/Scripts/UI/Battle/TopInfoManager.cs`。

我将修改 `Assets/Fishing/Scripts/UI/Battle/TopInfoManager.cs` 文件，删除其中设置屏幕方向的代码。


---



---



---

<details>
            <summary>Searched codebase "Screen.autorotateToPortrait = 
Screen.autorotateToPortraitUpsideDown = 
Screen.autorotateToLandscapeLeft = 
Screen.autorotateToLandscapeRight = " • **25** results</summary>
        
| File | Lines |
|------|-------|
| `Assets/Ehooray/Runtime/UI/SafeArea.cs` | L1-56 |
| `Assets/Fishing/Scripts/UI/Battle/TopInfoManager.cs` | L1-64 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMControls/MMTouchControls.cs` | L32-71 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMGyroscope/MMGyroscope.cs` | L1-36 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanARComponentScript.cs` | L42-90 |
| `Assets/Fingers/Prefab/Script/Components/FingersRotateCameraComponentScript.cs` | L1-45 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanRotateScaleComponentScript.cs` | L1-53 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMControls/MMTouchControls.cs` | L1-32 |
| `Assets/Fishing/Scripts/Camera/TouchController.cs` | L1-50 |
| `Assets/Fingers/Prefab/Script/Components/FingersRotateCameraComponentScript.cs` | L45-96 |
| `Assets/Fishing/Scripts/Camera/NaniCamera.cs` | L1-38 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMCameraAspectRatio.cs` | L1-50 |
| `Assets/Fingers/Prefab/Script/Components/FingersCameraMove3DComponentScript.cs` | L1-48 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMMovement/MMAutoRotate.cs` | L71-144 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMCameraAspectRatio.cs` | L50-95 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanARComponentScript.cs` | L1-42 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMCamera/MMOrbitalCamera.cs` | L130-177 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMMovement/MMAutoRotate.cs` | L1-25 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMActivation/MMPlatformActivation.cs` | L1-41 |
| `Assets/Fingers/Prefab/Script/Components/FingersScrollViewComponentScript.cs` | L1-44 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMControls/MMTouchJoystick.cs` | L103-172 |
| `Assets/Fingers/Prefab/Script/Components/FingersCameraMove3DComponentScript.cs` | L48-93 |
| `Assets/TopDownEngine/ThirdParty/MoreMountains/MMTools/Tools/MMGyroscope/MMGyroscope.cs` | L127-188 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanRotateScaleComponentScript.cs` | L78-114 |
| `Assets/Fingers/Prefab/Script/Components/FingersPanRotateScaleComponentScript.cs` | L224-265 |

</details>

---



---

