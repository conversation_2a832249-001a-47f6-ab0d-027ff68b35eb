using UnityEngine;
using UnityEngine.UI;
using DigitalRubyShared; // 引用FingersJoystickScript所需
using System.Collections;
using Zenject;
using Ehooray.UI;
using Fishing;
using Ehooray;
using System;
using System.Collections.Generic;

[AssetInfo("Prefabs/UI/Battle/LureActionCanvas")]
public class LureActionSystem : GamePanel
{
    // 指针图片素材本身的旋转偏移量 (估算为45度，指向右上)
    // 您可以根据实际图片精确调整此值
    private const float POINTER_SPRITE_NATURAL_OFFSET_DEGREES = 45f;

    public enum QTEResult
    {
        Perfect,
        Good,
        Miss
    }
    [Header("摇杆设置")]
    public FingersJoystickScript joystick;
    public RectTransform pointerRectTransform; // 指针的RectTransform

    [Header("QTE UI元素")]
    public Image qteBackground; // QTE背景
    public Image targetAreaIndicator; // QTE背景上指示判定区域的UI
    public Sprite targetAreaNormalSprite; // 指针未进入判定区域时显示的图片
    public Sprite targetAreaHighlightSprite; // 指针进入判定区域时显示的图片
    public RectTransform scalingRingRectTransform; // 外圈UI的RectTransform
    public Image resultImage; // 显示 "Perfect", "Good", "Miss" 的图片
    public Sprite perfectSprite; // Perfect结果的图片
    public Sprite goodSprite; // Good结果的图片
    public Sprite missSprite; // Miss结果的图片
    public Button actionButton; // 右下角的动作按钮

    [Header("QTE 参数")]
    private float targetAreaAngleWidth = 90.0f; // 判定区域的角度宽度
    private float perfectTimingTolerance = 0.10f; // Perfect判定的时机百分比公差 (例如0.1代表±10%)
    private float goodTimingTolerance = 0.20f;    // Good判定的时机百分比公差 (例如0.2代表±20%)

    private float currentTargetAngle; // 当前QTE尝试的目标角度中心
    private int randomDirection; // 当前QTE尝试的目标方向
    private bool qteActive = false;
    private Coroutine scalingCoroutine;
    private int attemptsTotal; // 總嘗試次數
    private int attemptsRemaining; // 當前嘗試次數
    private List<int> eachScalingDuration;
    Action<QTEResult, int> resultCallback;
    Action<Vector2> angleDirectionCallback;

    private bool joystickWasActive = false; // 新增：用于检测摇杆是否曾被激活

    #region Zenject Inejct
    FishingManager fishingManager;
    #endregion

    [Inject]
    public void Construct(FishingManager fishingManager)
    {
        this.fishingManager = fishingManager;
    }

    void OnEnable()
    {
        // 确保Canvas处于ScreenSpaceOverlay模式，这是FingersJoystickScript所要求的
        if (PanelCanvas != null)
        {
            PanelCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        }

        if (joystick == null)
        {
            Debug.LogError("摇杆脚本未分配!");
            enabled = false;
            return;
        }
        if (pointerRectTransform == null || qteBackground == null || targetAreaIndicator == null || scalingRingRectTransform == null || resultImage == null || actionButton == null || perfectSprite == null || goodSprite == null || missSprite == null)
        {
            Debug.LogError("一个或多个QTE UI元素或Sprite未分配!");
            enabled = false;
            return;
        }
        if (targetAreaNormalSprite == null || targetAreaHighlightSprite == null)
        {
            Debug.LogError("Target area sprites (normal or highlight) not assigned!");
            enabled = false;
            return;
        }

        joystick.JoystickExecuted = OnJoystickMoved;
        actionButton.gameObject.SetActive(false); // 隐藏按钮
        resultImage.enabled = false; // 初始隐藏结果图片
        targetAreaIndicator.gameObject.SetActive(false);
        scalingRingRectTransform.gameObject.SetActive(false);
    }

    public void InitAndStartLureMinigame(int totalTimes, List<int> eachTime, Action<QTEResult, int> callback)
    {
        attemptsTotal = totalTimes;
        attemptsRemaining = totalTimes; // 重置尝试次数
        eachScalingDuration = eachTime;
        resultCallback = callback;

        resultImage.enabled = false; // 隐藏结果图片
        Debug.Log("拟饵小游戏开始。总尝试次数: " + attemptsTotal);
        StartNextAttempt(); // 开始下一次尝试
    }
    public void SetTargetDirection(Action<Vector2> callback)
    {
        angleDirectionCallback = callback;
    }

    void Update()
    {
        // Unity Editor調試按鍵
        #if UNITY_EDITOR
        if (qteActive)
        {
            // 按鍵1觸發Perfect結果
            if (Input.GetKeyDown(KeyCode.Alpha1) || Input.GetKeyDown(KeyCode.Keypad1))
            {
                Debug.Log("調試: 觸發Perfect結果");
                DebugTriggerResult(QTEResult.Perfect);
            }
            // 按鍵2觸發Good結果
            else if (Input.GetKeyDown(KeyCode.Alpha2) || Input.GetKeyDown(KeyCode.Keypad2))
            {
                Debug.Log("調試: 觸發Good結果");
                DebugTriggerResult(QTEResult.Good);
            }
            // 按鍵3觸發Miss結果
            else if (Input.GetKeyDown(KeyCode.Alpha3) || Input.GetKeyDown(KeyCode.Keypad3))
            {
                Debug.Log("調試: 觸發Miss結果");
                DebugTriggerResult(QTEResult.Miss);
            }
        }
        #endif
    }

    private void OnJoystickMoved(FingersJoystickScript script, Vector2 amount)
    {
        if (pointerRectTransform == null) return;

        Image indicatorImage = targetAreaIndicator.GetComponent<Image>();
        if (indicatorImage == null)
        {
            Debug.LogError("Target Area Indicator 没有 Image 组件!");
            return;
        }

        if (qteActive)
        {
            if (amount.magnitude > 0.1f) // 设置一个阈值，避免摇杆轻微晃动就触发旋转
            {
                joystickWasActive = true; // 标记摇杆已激活
                float angleFromJoystick = Mathf.Atan2(amount.y, amount.x) * Mathf.Rad2Deg;
                // 应用偏移校正，使得指针的视觉朝向与摇杆的 angleFromJoystick 一致
                float rotationToApply = angleFromJoystick - POINTER_SPRITE_NATURAL_OFFSET_DEGREES;
                pointerRectTransform.localEulerAngles = new Vector3(0, 0, rotationToApply);

                // 检查指针是否在判定区域内
                // 使用校正后的视觉角度进行高亮判断
                float currentTransformRotation = pointerRectTransform.localEulerAngles.z;
                float visualPointerWorldAngle = NormalizeAngle(currentTransformRotation + POINTER_SPRITE_NATURAL_OFFSET_DEGREES);
                float pointerAngleCurrent = visualPointerWorldAngle; // 使用校正后的视觉角度

                float minAngle = currentTargetAngle - (targetAreaAngleWidth / 2f);
                float maxAngle = currentTargetAngle + (targetAreaAngleWidth / 2f);

                bool isInTargetArea = IsAngleBetween(pointerAngleCurrent, minAngle, maxAngle);
                indicatorImage.sprite = isInTargetArea ? targetAreaHighlightSprite : targetAreaNormalSprite;
            }
            else // 摇杆在死区或没有输入 (可能已释放)
            {
                if (joystickWasActive) // 如果之前摇杆是激活的，现在又没有输入了，则认为是释放
                {
                    OnActionButtonClicked(); // 触发判定
                    joystickWasActive = false; // 重置状态，防止连续触发
                }
                // 如果当前是高亮状态，则恢复为普通状态
                if (indicatorImage.sprite == targetAreaHighlightSprite)
                {
                    indicatorImage.sprite = targetAreaNormalSprite;
                }
            }
        }
        else
        {
            joystickWasActive = false; // 如果QTE不激活，确保重置此状态
        }
    }

    private void StartNextAttempt()
    {
        if (attemptsRemaining <= 0) // 如果没有剩余尝试次数
        {
            StartCoroutine(EndMinigame(1.0f)); // 结束小游戏
            return;
        }

        qteActive = true; // 激活QTE
        joystickWasActive = false; // 新增：每次尝试开始时，重置摇杆激活状态
        resultImage.enabled = false; // 清空/隐藏结果图片

        // 显示QTE元素
        if (pointerRectTransform != null) pointerRectTransform.gameObject.SetActive(true);
        if (qteBackground != null) qteBackground.gameObject.SetActive(true); // 新增：显示QTE背景
        targetAreaIndicator.gameObject.SetActive(true); // 显示判定区域UI (现在是箭头)
        scalingRingRectTransform.gameObject.SetActive(true); // 显示外圈UI

        // 1. 随机设置判定区域
        // 随机选择一个方向的整数索引 (0:上, 1:右, 2:下, 3:左)
        randomDirection = UnityEngine.Random.Range(0, 4);
        currentTargetAngle = randomDirection * 90f;

        // 修改 targetAreaIndicator 的显示方式
        Image indicatorImage = targetAreaIndicator.GetComponent<Image>();
        if (indicatorImage != null)
        {
            // 假设用户已在编辑器中将 targetAreaIndicator 的 Image Type 设置为 Simple，
            // 并将 Pivot 设置为 Center。
            // targetAreaNormalSprite 应被设置为箭头的Sprite。
            indicatorImage.sprite = targetAreaNormalSprite; // 初始化为普通状态的箭头图片

            // 由于 targetAreaIndicator 现在是箭头，不再需要设置 fillAmount。
            // indicatorImage.fillAmount = targetAreaAngleWidth / 360f; // 旧代码

            // 设置箭头的旋转，使其直接指向目标角度 currentTargetAngle。
            // 假设箭头Sprite资源默认朝向是X轴正方向（0度）。
            // currentTargetAngle 的值: 0 代表右, 90 代表上, 180 代表左, 270 代表下。
            targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, currentTargetAngle - 90f);
        }
        else
        {
            Debug.LogError("Target Area Indicator 没有 Image 组件!");
            // 如果没有Image组件，指示器将无法正确显示。
            // 旧的备用逻辑: targetAreaIndicator.rectTransform.localEulerAngles = new Vector3(0, 0, currentTargetAngle);
            // 这个逻辑与新逻辑一致，但前提是Image组件存在以显示Sprite。
        }

        // targetAreaIndicator.gameObject.SetActive(true); // 已在上方统一处理显示
        Debug.Log($"尝试 {attemptsTotal - attemptsRemaining + 1}: 目标角度设置为 {currentTargetAngle}");

        // 2. 开始外圈缩放
        if (scalingCoroutine != null)
        {
            StopCoroutine(scalingCoroutine); // 如果已有缩放协程在运行，则停止它
        }
        scalingCoroutine = StartCoroutine(ScaleRingCoroutine()); // 开始新的缩放协程
    }

    private IEnumerator ScaleRingCoroutine()
    {
        float elapsedTime = 0f;
        Vector3 initialScale = Vector3.one * 2f; // 初始缩放改为2倍
        Vector3 targetScale = Vector3.zero; // 目标缩放为0 (完全消失)

        scalingRingRectTransform.localScale = initialScale; // 设置初始缩放

        while (elapsedTime < eachScalingDuration[attemptsTotal - attemptsRemaining]) // 当经过时间小于总缩放时间
        {
            if (!qteActive) yield break; // 如果QTE在中途被取消，则退出协程

            float progress = elapsedTime / eachScalingDuration[attemptsTotal - attemptsRemaining]; // 计算缩放进度
            scalingRingRectTransform.localScale = Vector3.Lerp(initialScale, targetScale, progress); // 根据进度线性插值缩放
            elapsedTime += Time.deltaTime; // 更新经过时间
            yield return null; // 等待下一帧
        }

        if (qteActive) // 如果时间到了但玩家还未操作 (QTE仍然激活)
        {
            scalingRingRectTransform.localScale = targetScale; // 将外圈设置为目标缩放 (完全消失)
            Debug.Log("当前尝试时间耗尽。");
            ProcessJudgement(0f, false); // 时间耗尽视为Miss
        }
    }

    private void OnActionButtonClicked()
    {
        if (!qteActive) return; // 如果QTE未激活，则不执行任何操作

        qteActive = false; // 暂时禁用QTE，防止重复输入
        if (scalingCoroutine != null)
        {
            StopCoroutine(scalingCoroutine); // 停止缩放协程
        }

        // 判定逻辑
        // 1. 指针是否在判定区域内？
        float transformRotation = pointerRectTransform.localEulerAngles.z; // 获取指针RectTransform当前的Z轴旋转
        // 计算指针的实际视觉世界角度
        float visualPointerWorldAngle = NormalizeAngle(transformRotation + POINTER_SPRITE_NATURAL_OFFSET_DEGREES);

        float minAngle = currentTargetAngle - (targetAreaAngleWidth / 2f); // 计算判定区域的最小角度
        float maxAngle = currentTargetAngle + (targetAreaAngleWidth / 2f); // 计算判定区域的最大角度

        bool angleMatch = IsAngleBetween(visualPointerWorldAngle, minAngle, maxAngle);
        Debug.Log($"指针Transform旋转: {transformRotation}, 校正后视觉角度: {visualPointerWorldAngle}, 目标范围: [{NormalizeAngle(minAngle)}, {NormalizeAngle(maxAngle)}], 是否匹配: {angleMatch}");

        // 2. 时机（外圈的缩放程度）如何？
        float currentRingScale = scalingRingRectTransform.localScale.x; // 获取外圈当前的缩放值 (x, y, z应该相同)
        Debug.Log($"当前外圈缩放: {currentRingScale}");

        ProcessJudgement(currentRingScale, angleMatch); // 处理判定结果
    }

    private void ProcessJudgement(float ringScale, bool angleMatched)
    {
        QTEResult result = QTEResult.Miss; // 默认判定为Miss

        if (angleMatched) // 如果角度匹配
        {
            // 新的判定逻辑
            if (ringScale > 1.0f)
            {
                result = QTEResult.Perfect;
            }
            else // ringScale <= 1.0f
            {
                result = QTEResult.Good;
            }
        }
        // 如果 angleMatched 为 false，则保持 result = QTEResult.Miss

        // 根据结果设置对应的Sprite并显示Image
        resultImage.enabled = true;
        switch (result)
        {
            case QTEResult.Perfect:
                resultImage.sprite = perfectSprite;
                break;
            case QTEResult.Good:
                resultImage.sprite = goodSprite;
                break;
            case QTEResult.Miss:
                resultImage.sprite = missSprite;
                break;
        }

        attemptsRemaining--; // 减少剩余尝试次数

        // 隐藏QTE元素
        if (pointerRectTransform != null) pointerRectTransform.gameObject.SetActive(false);
        if (qteBackground != null) qteBackground.gameObject.SetActive(false); // 新增：隐藏QTE背景
        targetAreaIndicator.gameObject.SetActive(false); // 隐藏判定区域UI
        scalingRingRectTransform.gameObject.SetActive(false); // 隐藏外圈UI
        resultCallback(result, attemptsTotal - attemptsRemaining + 1);

        if (result != QTEResult.Miss)
        {
            Vector2 directionVector;
            switch (randomDirection)
            {
                case 1: // 0 度 -> 上
                    directionVector = Vector2.up;
                    break;
                case 0: // 90 度 -> 右
                    directionVector = Vector2.right;
                    break;
                case 3: // 180 度 -> 下
                    directionVector = Vector2.zero;
                    break;
                case 2: // 270 度 -> 左
                    directionVector = Vector2.left;
                    break;
                default:
                    // UnityEngine.Random.Range(0, 4) 的结果应该是 0, 1, 2, 或 3。
                    // 此 default 分支理论上不应执行。
                    Debug.LogError($"LureActionSystem: 无效的方向索引 {currentTargetAngle}。将默认设置为向上 (0 度)。");
                    currentTargetAngle = 90f; // 确保 currentTargetAngle 也被重置为有效值
                    directionVector = Vector2.up;
                    break;
            }
            angleDirectionCallback?.Invoke(directionVector);
        }

        if (attemptsRemaining > 0) // 如果还有剩余尝试次数
        {
            // 稍等片刻后开始下一次尝试
            StartCoroutine(DelayNextAttempt(1.0f));
        }
        else // 如果没有剩余尝试次数
        {
            StartCoroutine(EndMinigame(1.0f)); // 结束小游戏
        }
    }

    private IEnumerator DelayNextAttempt(float delay)
    {
        yield return new WaitForSeconds(delay); // 等待指定时间
        StartNextAttempt(); // 开始下一次尝试
    }

    private IEnumerator EndMinigame(float delay)
    {
        yield return new WaitForSeconds(delay); // 等待指定时间
        qteActive = false; // 禁用QTE
        fishingManager.fishingGamePanelManager.Close<LureActionSystem>(); // 恢复原始的关闭面板方式
    }

    // 辅助函数：检查角度是否在指定范围内
    // 同时考虑角度环绕0-360度的情况
    private bool IsAngleBetween(float angle, float minUnnormalized, float maxUnnormalized)
    {
        // 新增：检查原始范围是否覆盖了整个圆圈
        // 使用一个小的容差来处理浮点数精度问题
        if ((maxUnnormalized - minUnnormalized) >= (360.0f - 0.001f))
        {
            return true; // 如果范围接近或超过360度，则始终匹配
        }

        float normalizedAngle = NormalizeAngle(angle); // 标准化输入角度
        float normalizedMin = NormalizeAngle(minUnnormalized);   // 标准化最小角度
        float normalizedMax = NormalizeAngle(maxUnnormalized);   // 标准化最大角度

        if (normalizedMin <= normalizedMax) // 如果范围不跨越0/360度
            return normalizedAngle >= normalizedMin && normalizedAngle <= normalizedMax;
        else // 如果范围跨越0/360度 (例如: min=350, max=10)
            return normalizedAngle >= normalizedMin || normalizedAngle <= normalizedMax;
    }

    private float NormalizeAngle(float angle) // 辅助函数：将角度标准化到0-360度范围
    {
        while (angle < 0f) angle += 360f;
        while (angle >= 360f) angle -= 360f;
        return angle;
    }

    // 為調試添加的方法，直接觸發指定結果
    #if UNITY_EDITOR
    private void DebugTriggerResult(QTEResult result)
    {
        if (!qteActive) return;
        
        qteActive = false; // 停用QTE
        
        if (scalingCoroutine != null)
        {
            StopCoroutine(scalingCoroutine); // 停止縮放協程
        }
        
        resultImage.sprite = result switch
        {
            QTEResult.Perfect => perfectSprite,
            QTEResult.Good => goodSprite,
            QTEResult.Miss => missSprite,
            _ => missSprite
        };
        resultImage.enabled = true;
        attemptsRemaining--; // 減少剩餘嘗試次數
        
        // 隱藏UI元素
        targetAreaIndicator.gameObject.SetActive(false);
        scalingRingRectTransform.gameObject.SetActive(false);
        
        // 調用回調函數
        resultCallback(result, attemptsTotal - attemptsRemaining + 1);
        
        if (attemptsRemaining > 0)
        {
            StartCoroutine(DelayNextAttempt(1.0f));
        }
        else
        {
            StartCoroutine(EndMinigame(1.0f));
        }
    }
    #endif
}