using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Zenject;
using Ehooray;
using Fishing.UI;
using Cysharp.Threading.Tasks;

namespace Fishing
{
    public class Fish : MonoBehaviour
    {
        public FishRodForceItem forceItem;
        public float waitTime;
        public LineRenderer line;
        public GameObject catchPoint;
        public GameObject topPoint;
        public GameObject showPoint;
        public GameObject movePoint;
        public GameObject fishModel;

        public GameObject showTarget;
        public GameObject continueShowFish;
        public GameObject waterEffect;

        public float moveRange;
        public float moveSpeed = 1f;
        public StruggleState state = StruggleState.Stun;
        public float catchDistance = 8f;

        public float stateTime;
        public float stunTime;
        public int stunTimes = 0;
        public float stunLifePercent = 0.2f;

        public float startMovePersent = 0.6f;
        public float moveStep = 0f;
        public bool isRight = false;
        [Range(0, 2)]
        public float stepDistanceScale = 0.4f;
        [Range(0, 1)]
        public float distanceScale = 1f;
        public float lineLength = 400f;

        public bool useSkill = false;
        public float skillTime = 1f;
        public float currentSkillTime;
        public int continueTimes = 0;

        // QTE相關屬性
        public float qteCountdown; // QTE倒計時時間
        public bool isQteTriggered = false; // 是否觸發QTE
        public bool isQteWeakened = false; // 魚是否處於QTE後的乏力狀態
        public float qteWeakenedTime = 5f; // 乏力狀態持續時間(秒)
        public float qteWeakenedTimeRemaining = 0f; // 乏力狀態剩餘時間
        public float qteSpeedBonus = 1.5f; // 乏力狀態時收線速度加成(50%)

        public const int catchMinDis = 80;
        public const int catchMaxDis = 101;

        int SkillPullMF;
        int RodPullMF;
        int RodStrMF;
        int FishPullMF;
        int LStruggleMF;
        int MStruggleMF;
        int SStruggleMF;
        int StunMF;

        public float life;
        public float distance;
        public int struggleMF;

        #region Zenject Inject
        FishingManager fishingManager;
        FishingGamePanelManager fishingGamePanelManager;
        TableContainer tableContainer;
        IResourceProvider provider;
        FishData fishData;
        BattleEquipeData equipeData;
        AssetPathConfig assetPathConfig;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
                                FishingGamePanelManager fishingGamePanelManager,
                                TableContainer tableContainer,
                                IResourceProvider provider,
                                FishData fishData,
                                BattleEquipeData equipeData,
                                AssetPathConfig assetPathConfig)
        {
            this.fishingManager = fishingManager;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.tableContainer = tableContainer;
            this.provider = provider;
            this.fishData = fishData;
            this.equipeData = equipeData;
            this.assetPathConfig = assetPathConfig;
        }
        void Start()
        {
            lineLength = 400f;
            line.positionCount = 2;
            moveRange = Mathf.Abs(forceItem.gameObject.transform.position.z - catchPoint.transform.position.z);

            LoadTableData();
        }

        void Update()
        {
            if (showTarget != null)
            {
                line.SetPosition(0, topPoint.transform.position);
                line.SetPosition(1, showTarget.GetComponent<FishLineDummy>().dummy.transform.position);
            }
        }
        public void ResetValue()
        {
            waitTime = Random.Range(3.0f, 5.0f);

            line.gameObject.SetActive(false);
            SetFishState(CalculateStruggleWt(StruggleState.Stun));

            fishData.RetryCount = 0;
            //stunTimes = continueTimes;
            this.transform.gameObject.SetActive(false);
            currentSkillTime = skillTime;
            CloseWaterEffect();
            
            // 重置QTE相關數值
            ResetQteValues();
        }
        public void Continue()
        {
            stunTimes = continueTimes;
            if (stunTimes >= 4f)
                stunTimes = 4;

            waitTime = Random.Range(3.0f, 5.0f);

            life = fishData.data.Hp * (1 - stunTimes * stunLifePercent);
            distance = Random.Range(catchMinDis, catchMaxDis);
            SetFishState(CalculateStruggleWt(StruggleState.Stun));
            stateTime = Random.Range(fishData.data.Statemintime, fishData.data.Statemaxtime) / 1000;
            this.transform.gameObject.SetActive(false);
            currentSkillTime = skillTime;
            CloseWaterEffect();
            
            // 重置QTE相關數值
            ResetQteValues();
        }
        public void Wait()
        {
            waitTime -= Time.deltaTime;
        }
        public void Fight()
        {
            // 如果魚處於乏力狀態，則收線速度增加50%
            float speedMultiplier = isQteWeakened ? qteSpeedBonus : 1f;
            if (isQteWeakened)
            {
                // 只在乏力狀態下顯示速度加成
                Debug.Log($"[QTE] 收線加速中: x{qteSpeedBonus:F1} (剩餘 {qteWeakenedTimeRemaining:F1}秒)");
            }
            distance -= Time.deltaTime * equipeData.rodData.strength * equipeData.rodData.control * RodPullMF * speedMultiplier;
            life -= Time.deltaTime * equipeData.rodData.strength * RodStrMF;
        }
        public void Leave()
        {
            distance += Time.deltaTime * fishData.data.Pull * struggleMF * FishPullMF;
        }
        public void SetInfo()
        {
            var hpPersent = life / fishData.data.Hp;
            if (fishingGamePanelManager.TryGetGamePanel<FishInfoManager>(out FishInfoManager infoPanel))
            {
                infoPanel.SetFishDistanceText(distance , lineLength + equipeData.lineData.CsvData.Parameter_1);
                infoPanel.SetHpPercentage(hpPersent);
            }
            if (fishingGamePanelManager.TryGetGamePanel<LineTensionManager>(out LineTensionManager tensionPanel))
            {
                tensionPanel.SetFishHPBar(hpPersent);
            }
        }
        public void PullUp(Camera camera)
        {
            showTarget.SetActive(true);
            showTarget.transform.position = Vector3.Lerp(showTarget.transform.position, showPoint.transform.position, 0.04f);
            showTarget.transform.rotation = Quaternion.Lerp(showTarget.transform.rotation, Quaternion.Euler(0, 0, -60f), 0.02f);
            camera.fieldOfView = Mathf.Lerp(camera.fieldOfView, 35, 0.02f);
            PlayWaterEffect();
        }
        public void PullUpAnimation()
        {
            fishModel.GetComponent<Animator>().Play("PullUpFish", 0, 0);
        }
        public void Show(Camera camera)
        {
            camera.fieldOfView = 60;
            showTarget.transform.localRotation = Quaternion.Euler(0, 0, 0);
            showTarget.transform.localPosition = Vector3.zero;
        }
        public void PlayWaterEffect()
        {
            waterEffect.SetActive(true);
        }
        public void CloseWaterEffect()
        {
            waterEffect.SetActive(false);
        }
        public void Move()
        {
            if (isRight)
            {
                if (forceItem.gameObject.transform.position.x < moveStep * stepDistanceScale * distanceScale)
                    forceItem.gameObject.transform.Translate(new Vector3(Time.deltaTime * moveSpeed, 0, 0));
                else
                {
                    if (moveStep == 4)
                    {
                        isRight = false;
                        moveStep -= 1;
                    }
                    else
                    {
                        isRight = Random.Range(0f, 1f) <= 0.5f ? true : false;
                        moveStep += isRight ? 1 : -1;
                    }
                }
            }
            else
            {
                if (forceItem.gameObject.transform.position.x > moveStep * stepDistanceScale * distanceScale)
                    forceItem.gameObject.transform.Translate(new Vector3(-Time.deltaTime * moveSpeed, 0, 0));
                else
                {
                    if (moveStep == -4)
                    {
                        isRight = true;
                        moveStep += 1;
                    }
                    else
                    {
                        isRight = Random.Range(0f, 1f) <= 0.5f ? true : false;
                        moveStep += isRight ? 1 : -1;
                    }
                }
            }
            DoSkill();
            SetInfo();
            ChangeFishState();
        }

        public void ChangeFishState()
        {
            if (life <= 0)
            {
                life = 0;
                SetFishState(StruggleState.Stun);
                stunTime -= Time.deltaTime;
                if (stunTime <= 0)
                {
                    stunTimes++;

                    if (stunTimes > 4)
                        stunTimes = 4;
                    life = fishData.data.Hp * (1 - stunTimes * stunLifePercent);
                    stunTime = fishData.data.Stuntime;
                    SetFishState(CalculateStruggleWt(state));
                    stateTime = Random.Range(fishData.data.Statemintime, fishData.data.Statemaxtime) / 1000;
                }
                return;
            }

            stateTime -= Time.deltaTime;
            if (stateTime <= 0)
            {
                SetFishState(CalculateStruggleWt(state));
                stateTime = Random.Range(fishData.data.Statemintime, fishData.data.Statemaxtime) / 1000;
            }
        }

        public void SetFishState(StruggleState state)
        {
            this.state = state;
            if (fishingGamePanelManager.TryGetGamePanel<FishInfoManager>(out FishInfoManager infoPanel))
                infoPanel.SetStruggleImage(state);

            if (fishingManager.lineTensionManager != null)
                fishingManager.lineTensionManager.SetRaiseTensionPerTimeWhenChangeState();
            switch (state)
            {
                case StruggleState.Big:
                    struggleMF = LStruggleMF;

                    break;
                case StruggleState.Mid:
                    struggleMF = MStruggleMF;

                    break;
                case StruggleState.Small:
                    struggleMF = SStruggleMF;

                    break;
                case StruggleState.Stun:
                    struggleMF = StunMF;

                    break;
            }
        }
        public void SetDistanceZ()
        {
            if (distance > lineLength * startMovePersent)
                return;
            var x = forceItem.gameObject.transform.position.x;
            var z = catchPoint.transform.position.z + ((distance - catchDistance) / lineLength / startMovePersent) * moveRange;
            distanceScale = Mathf.Abs(z - catchPoint.transform.position.z) / moveRange;

            if (Mathf.Abs(x) > Mathf.Abs(moveStep * stepDistanceScale * distanceScale) && distanceScale < 0.15f)
            {
                x = moveStep * stepDistanceScale * distanceScale;
            }

            forceItem.gameObject.transform.position = new Vector3(x, forceItem.gameObject.transform.position.y, z);
        }
        public void CheckSkill()
        {
            useSkill = true;
        }
        public void DoSkill()
        {
            if (!useSkill)
                return;

            if (currentSkillTime > 0)
            {
                currentSkillTime -= Time.deltaTime;
                distance -= Time.deltaTime * equipeData.rodData.strength * equipeData.rodData.control * SkillPullMF * (1 + equipeData.lineData.CsvData.Parameter_2 / 100);
                SetDistanceZ();
            }
            else
            {
                useSkill = false;
                currentSkillTime = skillTime;
            }
        }
        public async void ChangeContinueShowFish()
        {
            DestroyChild(continueShowFish.transform);
            GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.FishPrefabPath + fishData.data.Model);
            var fish = Instantiate(obj, continueShowFish.transform);
            fish.SetActive(true);
        }
        public async UniTask ChangeFishModel()
        {
            DestroyChild(fishModel.transform);
            GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.FishPrefabPath + fishData.data.Model);
            var fish = Instantiate(obj, fishModel.transform);
            fish.transform.localPosition = new Vector3(0, 0, 0);
            fish.transform.localRotation = Quaternion.Euler(0, 0, 0);
            fish.SetActive(true);
            showTarget = fish;
        }
        public void DestroyChild(Transform parent)
        {
            foreach (Transform child in parent)
            {
                GameObject.Destroy(child.gameObject);
            }
        }
        public StruggleState CalculateStruggleWt(StruggleState state)
        {
            List<StruggleState> list = new List<StruggleState>();

            if (state != StruggleState.Big)
                for (int i = 0; i < fishData.data.Lstrugglewt; i++)
                    list.Add(StruggleState.Big);

            if (state != StruggleState.Mid)
                for (int i = 0; i < fishData.data.Mstrugglewt; i++)
                    list.Add(StruggleState.Mid);

            if (state != StruggleState.Small)
                for (int i = 0; i < fishData.data.Sstrugglewt; i++)
                    list.Add(StruggleState.Small);

            return list[Random.Range(0, list.Count)];
        }
        public void SetFishData()
        {
            fishingManager.throwBaitManager.SetFishData(fishData.data);
            if (fishingManager.lineTensionManager != null)
            {
                fishingManager.lineTensionManager.SetFishData(fishData.data);
                fishingManager.lineTensionManager.playerHpCircleManager.SetFishData(fishData.data);
            }
            fishingManager.gameEndManager.SetFishData(fishData.data);

            if (stunTimes > 4)
                stunTimes = 4;
            life = fishData.data.Hp * (1 - stunTimes * stunLifePercent);

            stateTime = Random.Range(fishData.data.Statemintime, fishData.data.Statemaxtime) / 1000;
            stunTime = fishData.data.Stuntime;
            distance = Random.Range(catchMinDis, catchMaxDis);
            waitTime = Random.Range(3.0f, 5.0f);
            useSkill = false;
            SetFishState(CalculateStruggleWt(StruggleState.Stun));
            
            // 初始化QTE計時器
            ResetQteValues();
        }

        public void SetFishToShowPoint()
        {
            showTarget.transform.position = showPoint.transform.position;
            showTarget.transform.rotation = Quaternion.Euler(0, 0, 0);
        }

        public void LoadTableData()
        {
            SkillPullMF = tableContainer.VariableTable.SkillPullMF / 1000;
            RodPullMF = tableContainer.VariableTable.RodPullMF / 1000;
            RodStrMF = tableContainer.VariableTable.RodStrMF / 1000;
            FishPullMF = tableContainer.VariableTable.FishPullMF / 1000;
            LStruggleMF = tableContainer.VariableTable.LStruggleMF / 1000;
            MStruggleMF = tableContainer.VariableTable.MStruggleMF / 1000;
            SStruggleMF = tableContainer.VariableTable.SStruggleMF / 1000;
            StunMF = tableContainer.VariableTable.StunMF / 1000;
        }

        // QTE相關方法
        public void ResetQteValues()
        {
            isQteTriggered = false;
            isQteWeakened = false;
            qteWeakenedTimeRemaining = 0f;
            CalculateNextQteTime();
        }
        
        // 計算下次QTE觸發時間
        public void CalculateNextQteTime()
        {
            if (fishData != null && fishData.data != null && fishData.data.QteTime > 0)
            {
                float baseTime = fishData.data.QteTime / 1000f; // 轉換為秒
                float randomFactor = Random.Range(-0.3f, 0.3f); // ±30%的隨機變動
                qteCountdown = baseTime * (1 + randomFactor);
            }
            else
            {
                // 若無QTE時間設定，給一個合理的預設值
                qteCountdown = 15f;
            }
        }
        
        // 更新QTE時間和狀態
        public void UpdateQteStatus(float deltaTime)
        {
            // 更新乏力狀態計時
            if (isQteWeakened)
            {
                qteWeakenedTimeRemaining -= deltaTime;
                if (qteWeakenedTimeRemaining <= 0)
                {
                    isQteWeakened = false;
                    Debug.Log("[QTE] 魚的乏力狀態結束，回歸正常狀態");
                }
            }
            
            // 如果QTE尚未觸發，更新倒計時
            if (!isQteTriggered)
            {
                qteCountdown -= deltaTime;
                // 顯示剩餘QTE倒計時（僅在接近觸發時）
                if (qteCountdown < 3.0f)
                {
                    Debug.Log($"[QTE] 即將觸發QTE，剩餘 {qteCountdown:F1} 秒");
                }
                
                if (qteCountdown <= 0)
                {
                    isQteTriggered = true;
                    Debug.Log("[QTE] 觸發QTE事件!");
                    // QTE觸發點，由Strategy層處理具體邏輯
                }
            }
        }
        
        // QTE完成時調用
        public void OnQteCompleted(bool success)
        {
            isQteTriggered = false;
            
            // 若成功，則進入乏力狀態
            if (success)
            {
                isQteWeakened = true;
                qteWeakenedTimeRemaining = qteWeakenedTime;
                Debug.Log($"[QTE] QTE成功! 魚進入乏力狀態，持續 {qteWeakenedTime} 秒，收線速度增加 {(qteSpeedBonus-1)*100}%");
            }
            else
            {
                Debug.Log("[QTE] QTE失敗，魚正常掙扎");
            }
            
            // 重新計算下一次QTE時間
            CalculateNextQteTime();
            Debug.Log($"[QTE] 下一次QTE將在 {qteCountdown:F1} 秒後觸發");
        }
    }
}


