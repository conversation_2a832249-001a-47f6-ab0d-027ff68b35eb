package ehooray.fishing.entity;

import java.time.LocalDateTime;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

//Note: this entity may update in database directly, so don't use @OneToMany and l2cache.
@Entity
//@formatter:off
@Table
(
    name = "server_info",
    indexes =
    {
        @Index(name = "ip", columnList = "ip"),
    }
)
//@formatter:on
public class ServerInfoEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    protected String ip;
    protected boolean isMaintain;
    protected LocalDateTime updateMaintainTime;
    
    protected boolean isClientPatch;
    protected LocalDateTime updateClientPatchTime;
    
    
    public boolean isMaintain()
    {
        return isMaintain;
    }
    public void setMaintain(boolean isMaintain)
    {
        this.isMaintain = isMaintain;
    }
    public LocalDateTime getUpdateMaintainTime()
    {
        return updateMaintainTime;
    }
    public void setUpdateMaintainTime(LocalDateTime updateMaintainTime)
    {
        this.updateMaintainTime = updateMaintainTime;
    }
    public String getIp()
    {
        return ip;
    }
    public void setIp(String ip)
    {
        this.ip = ip;
    }
    public boolean isClientPatch()
    {
        return isClientPatch;
    }
    public void setClientPatch(boolean isClientPatch)
    {
        this.isClientPatch = isClientPatch;
    }
    public LocalDateTime getUpdateClientPatchTime()
    {
        return updateClientPatchTime;
    }
    public void setUpdateClientPatchTime(LocalDateTime updateClientPatchTime)
    {
        this.updateClientPatchTime = updateClientPatchTime;
    }
}
