# Task ID: 8
# Title: Crafting System - Merge Mechanics
# Status: pending
# Dependencies: 7
# Priority: medium
# Description: Implement the merge crafting system at the Boat Workshop. Define 60 merge recipes for rods, lures, boats, and clothing using Fish Cards, coins, and time.
# Details:
Create a `CraftingSystem.js`. Define recipes in a JSON file: `recipes.json` (e.g., `[{ id: 'bamboo_rod', output: 'bamboo_rod_item', inputs: [{ type: 'FishCard', name: '<PERSON>no<PERSON>', quantity: 2 }], cost: 200, timeMinutes: 2 }, ...]`). Implement logic to check player inventory for required Fish Cards/items and coins. Deduct inputs/coins. Start a crafting timer (can be real-time or game-time based). On completion, add the crafted item to player's inventory. Boat's `CraftingEfficiency` attribute should reduce crafting time/cost. UI for Boat Workshop: display recipes, inputs, costs, and crafting progress. 
```javascript
// CraftingSystem.js
// function startCraft(recipeId) {
//   const recipe = getRecipe(recipeId);
//   if (hasInputs(player.inventory, recipe.inputs) && player.coins >= recipe.cost) {
//     removeInputs(player.inventory, recipe.inputs); player.coins -= recipe.cost;
//     const craftTime = recipe.timeMinutes * (1 - player.getEffectiveBoatAttribute('craftingEfficiency'));
//     addCraftingJob({ recipeId: recipe.id, completionTime: Date.now() + craftTime * 60000 });
//   }
// }
```

# Test Strategy:
Test crafting various items: check input consumption, coin deduction, and item generation. Verify crafting timer functionality. Test recipes requiring other crafted items. Validate `CraftingEfficiency` attribute effect. Ensure UI correctly displays recipes and crafting status.
