// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/broadcast/CloseSessionRequest.proto

package ehooray.fishing.dto.proto.request.broadcast;

public final class CloseSessionRequestProto {
  private CloseSessionRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CloseSessionRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.CloseSessionRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 csvId = 1;</code>
     * @return The csvId.
     */
    int getCsvId();
  }
  /**
   * Protobuf type {@code request.CloseSessionRequest}
   */
  public static final class CloseSessionRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.CloseSessionRequest)
      CloseSessionRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CloseSessionRequest.newBuilder() to construct.
    private CloseSessionRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CloseSessionRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CloseSessionRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.internal_static_request_CloseSessionRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.internal_static_request_CloseSessionRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.class, ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.Builder.class);
    }

    public static final int CSVID_FIELD_NUMBER = 1;
    private int csvId_;
    /**
     * <code>int32 csvId = 1;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.CloseSessionRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.CloseSessionRequest)
        ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.internal_static_request_CloseSessionRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.internal_static_request_CloseSessionRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.class, ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        csvId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.internal_static_request_CloseSessionRequest_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest build() {
        ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest buildPartial() {
        ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest result = new ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest(this);
        result.csvId_ = csvId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 1;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 1;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.CloseSessionRequest)
    }

    // @@protoc_insertion_point(class_scope:request.CloseSessionRequest)
    private static final ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest();
    }

    public static ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CloseSessionRequest>
        PARSER = new com.google.protobuf.AbstractParser<CloseSessionRequest>() {
      @java.lang.Override
      public CloseSessionRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CloseSessionRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CloseSessionRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.broadcast.CloseSessionRequestProto.CloseSessionRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_CloseSessionRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_CloseSessionRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+request/broadcast/CloseSessionRequest." +
      "proto\022\007request\"$\n\023CloseSessionRequest\022\r\n" +
      "\005csvId\030\001 \001(\005Ba\n+ehooray.fishing.dto.prot" +
      "o.request.broadcastB\030CloseSessionRequest" +
      "ProtoH\002\252\002\025Fishing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_CloseSessionRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_CloseSessionRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_CloseSessionRequest_descriptor,
        new java.lang.String[] { "CsvId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
