package ehooray.fishing.component;

import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.util.UriComponentsBuilder;

import ehooray.fishing.component.auth.BasicAuthTokenConverter;
import ehooray.fishing.config.SessionName;
import ehooray.fishing.entity.PlayerAuthEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.service.auth.PlayerAuthService;
import ehooray.fishing.service.player.PlayerService;

@Component
public class WebSocketHandshakeInterceptor implements HandshakeInterceptor
{

    @Autowired
    protected PlayerAuthService playerAuthService;

    @Autowired
    protected PlayerService playerService;
    
    @Autowired
    protected BasicAuthTokenConverter tokenConverter;

    @Override
    @Transactional(readOnly = true, timeoutString = "${game.transactional.timeout}")
    public boolean beforeHandshake(
            ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
            Map<String, Object> attributes
    )
    {
        MultiValueMap<String, String> parameters = UriComponentsBuilder.fromUri(request.getURI()).build().getQueryParams();
        BasicAuthTokenConverter.DecodeResult decodeResult = tokenConverter.decode(parameters.getFirst("token"));
        String account = decodeResult.getAccount();
        String token = decodeResult.getToken();
        Optional<PlayerAuthEntity> playerAuth = playerAuthService.findByToken(token);
        if (playerAuth.isEmpty()) throw new BadCredentialsException("not found player with token = " + token);
        if(!playerAuth.get().getAccount().equals(account))
            throw new BadCredentialsException("incorrect account with token, account  = " + account + ", token = " + token);

        Optional<PlayerEntity> player = playerService.findByPlayerAuthId(playerAuth.get().getId());
        attributes.put(SessionName.AuthToken, playerAuth.get().getToken());
        attributes.put(SessionName.PlayerId, player.get().getId());
        
        return true;
    }

    @Override
    public void afterHandshake(
            ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception
    )
    {

    }

}
