<!-- Generated by SpecStory -->

# 指針範圍判斷邏輯 (2025-05-13 19:24:20)

_**User**_

現在判斷指針是否指到指定範圍的邏輯在哪裡@LureActionSystem.cs 

---

_**Assistant**_



---

好的，我来帮你定位一下。

在 `LureActionSystem.cs` 文件中，判断指针是否指向指定范围的逻辑主要分布在以下几个地方：

1.  **`OnJoystickMoved` 方法**:
    *   当摇杆移动时，这个方法会被调用。
    *   它会计算摇杆的角度 (`angleFromJoystick`)，并更新指针 `pointerRectTransform` 的旋转。
    *   然后，它会获取指针当前的视觉角度 (`visualPointerWorldAngle`)。
    *   接着，调用 `IsAngleBetween` 方法来判断这个视觉角度是否在 `currentTargetAngle` 定义的目标区域内（`minAngle` 和 `maxAngle` 之间）。
    *   根据判断结果，它会切换 `targetAreaIndicator` 的 Sprite ( `targetAreaHighlightSprite` 或 `targetAreaNormalSprite`) 来高亮或取消高亮目标区域。
    *   相关的代码片段大约在第 150 行到 193 行之间。

2.  **`OnActionButtonClicked` 方法**:
    *   当玩家点击动作按钮（或按下空格键）时，这个方法会被调用。
    *   它同样会获取指针当前的视觉角度 (`visualPointerWorldAngle`)。
    *   然后，调用 `IsAngleBetween` 方法来判断这个角度是否在目标区域内，这个结果会赋值给 `angleMatch` 变量。
    *   `angleMatch` 变量随后会用于 `ProcessJudgement` 方法中，以确定最终的QTE结果 (Perfect, Good, Miss)。
    *   相关的代码片段大约在第 277 行到 292 行之间。

3.  **`IsAngleBetween` 辅助方法**:
    *   这是一个核心的辅助函数，专门用来判断一个给定的角度 (`angle`) 是否落在由 `minUnnormalized` 和 `maxUnnormalized` 定义的范围之间。
    *   这个方法考虑了角度环绕0-360度的情况（例如，一个从350度到10度的范围）。
    *   它首先会将所有输入角度标准化到0-360度范围内，然后再进行比较。
    *   这个方法位于文件的末尾，大约在第 394 行到 413 行之间。

简单来说：
*   `OnJoystickMoved` 在摇杆操作时实时检查并更新UI提示。
*   `OnActionButtonClicked` 在玩家确认操作时进行最终的角度匹配判定。
*   `IsAngleBetween` 是执行角度范围检查的核心逻辑。

---

_**User**_

目前指針只要指到判斷方向的上下45度，就可以顯示高亮並算成功指到

---

_**Assistant**_



---

是的，你的理解是正确的。

在 `LureActionSystem.cs` 脚本中，控制这个判定区域宽度的是 `targetAreaAngleWidth` 这个公共变量。你可以在脚本的第 36 行看到它的定义：

```csharp
[Header("QTE 参数")]
public float targetAreaAngleWidth = 90.0f; // 判定区域的角度宽度
```

这个值默认被设置为 `90.0f` 度。

在 `OnJoystickMoved` 方法（大约第 178-179 行）和 `OnActionButtonClicked` 方法（大约第 286-287 行）中，计算目标区域的最小和最大角度时，都使用了这个 `targetAreaAngleWidth`：

```csharp
float minAngle = currentTargetAngle - (targetAreaAngleWidth / 2f);
float maxAngle = currentTargetAngle + (targetAreaAngleWidth / 2f);
```

因此，当 `targetAreaAngleWidth` 为 90 度时，判定区域的范围就是 `currentTargetAngle` 的上下各 45 度 (因为 `90.0f / 2f = 45.0f`)。只要指针的当前角度落在这个 ±45 度的范围内，`IsAngleBetween` 方法就会返回 `true`，从而实现高亮显示和成功判定的逻辑。

---

