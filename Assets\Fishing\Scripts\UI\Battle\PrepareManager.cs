using Cysharp.Threading.Tasks;
using Ehooray;
using Ehooray.UI;
using Fishing.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Fishing
{
    [AssetInfo("Prefabs/UI/PrepareCanvas/PrepareCanvas")]
    public class PrepareManager : GamePanel
    {
        public Button startButton;
        public Button leftArrowButton;
        public Button rightArrowButton;
        public Button tutorialButton;

        public IconFrameBox fishRodBox;
        public IconFrameBox fishlineBox;
        public IconFrameBox baitBox;
        public IconFrameBox kitButton;

        private CancellationToken cancellationTokenDestroy;

        #region Zenject Inejct
        FishingManager fishingManager;
        FishingGamePanelManager fishingGamePanelManager;
        FishingRepositories fishingRepositories;
        AssetPathConfig assetPathConfig;
        IResourceProvider resourceProvider;
        GameTeaching gameTeaching;
        #endregion

        [Inject]
        public void Construct(FishingManager fishingManager,
                                FishingGamePanelManager fishingGamePanelManager,
                                FishingRepositories fishingRepositories,
                                AssetPathConfig assetPathConfig,
                                IResourceProvider resourceProvider,
                                GameTeaching gameTeaching)
        {
            this.fishingManager = fishingManager;
            this.fishingGamePanelManager = fishingGamePanelManager;
            this.fishingRepositories = fishingRepositories;
            this.assetPathConfig = assetPathConfig;
            this.resourceProvider = resourceProvider;
            this.gameTeaching = gameTeaching;

            fishRodBox.SetSelectEvent(() => OpenEquipePanel(PopupPrepare.EquipeType.Rod));
            fishlineBox.SetSelectEvent(() => OpenEquipePanel(PopupPrepare.EquipeType.Line));
            baitBox.SetSelectEvent(() => OpenEquipePanel(PopupPrepare.EquipeType.Bait));
            kitButton.SetSelectEvent(() => OpenKitPanel());
        }
        private void Awake()
        {
            cancellationTokenDestroy = this.GetCancellationTokenOnDestroy();
            leftArrowButton.onClick.AddListener(RefreshIcon);
            tutorialButton.onClick.AddListener(ShowTutorial);
        }
        private void Start()
        {
            leftArrowButton.onClick.Invoke();
        }
        public void OnStartButtonClick(PointerEventData data)
        {
            fishingManager.TransitionToState(FishingState.BeforeStart).Forget();
        }
        public async void OpenEquipePanel(PopupPrepare.EquipeType type)
        {
            var panel = await fishingGamePanelManager.OpenAsync<PopupPrepare>();
            panel.SetType(type);
            panel.popupPrepareInfo.RepairBtn.gameObject.SetActive(type == PopupPrepare.EquipeType.Rod);
            panel.Refresh();
        }
        public async void OpenKitPanel()
        {
            var panel = await fishingGamePanelManager.OpenAsync<PopupEquipList>();
            panel.Refresh();
        }

        public void RefreshIcon()
        {
            var kit = GetActiveKit();
            if (kit == null)
                return;
            var rod = fishingRepositories.fishRodRepository.GetFishRod(kit.fishRodEntityId);
            var fishline = fishingRepositories.playerItemRepository.GetPlayerItem(kit.fishLineCsvId);
            var bait = fishingRepositories.playerItemRepository.GetPlayerItem(kit.baitCsvId);
            fishRodBox.IsLock(false);
            SetIcon(fishRodBox.PlayerItemIcon, rod.CsvData.Icon);
            SetIcon(fishlineBox.PlayerItemIcon, fishline.CsvData.Icon);
            SetIcon(baitBox.PlayerItemIcon, bait.CsvData.Icon);


            SetIconBG(fishRodBox, rod.times > 0);
            SetIconBG(fishlineBox, fishline.finalCount > 0);
            SetIconBG(baitBox, bait.finalCount > 0);

            fishRodBox.Num.text = $"{rod.times}/{rod.CsvData.Times}";
            fishlineBox.Num.text = $"{fishline.finalCount}";
            baitBox.Num.text = $"{bait.finalCount}";
        }
        public FishKitData GetActiveKit()
        {
            var fishKits = fishingRepositories.fishKitRepository.GetFishKits();
            foreach (var kit in fishKits.Values)
            {
                if (kit.isActive)
                    return kit;
            }
            Debug.Log("no active Kit");
            return null;
        }
        public void SetIcon(Image img, string path)
        {
            UniTask.Void(async () =>
            {
                var cacheAsset = await resourceProvider.LoadResourceAsync<Sprite>(assetPathConfig.PictureItemPath + path).AttachExternalCancellation(cancellationToken: cancellationTokenDestroy);
                if (img != null) // 可能會非同步途中會被destroy
                {
                    img.sprite = cacheAsset.Object;
                }
            });
        }
        public void SetIconBG(IconFrameBox icon , bool haveTimes)
        {
            if (haveTimes)
                icon.SetRareBackgroundToGray();
            else
                icon.SetRareBackgroundToRed();
        }
        public void ShowTutorial()
        {
            Debug.Log("Tutorial !");
            leftArrowButton.onClick.Invoke();
            gameTeaching.PlayGameTeachingAsync().Forget();
        }
    }
}

