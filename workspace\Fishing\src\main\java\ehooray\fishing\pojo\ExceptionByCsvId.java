package ehooray.fishing.pojo;

public class ExceptionByCsvId extends RuntimeException
{
    private static final long serialVersionUID = -8477691839109098826L;
    
    protected int csvId = 0;
    private String[] overrideContent = null;
    
    public ExceptionByCsvId()
    {
        super();
    }

    public ExceptionByCsvId(int csvId)
    {
        super("[" + csvId + "]");
        this.csvId = csvId;
    }

    public ExceptionByCsvId(String exceptionMessage)
    {
        super(exceptionMessage);
    }

    public int getCsvId()
    {
        return csvId;
    }

    public String[] getOverrideContent()
    {
        return overrideContent;
    }

    public ExceptionByCsvId setOverrideContent(String[] overrideContent)
    {
        this.overrideContent = overrideContent;
        return this;
    }
    
    public boolean isOverrideContentExist()
    {
        return overrideContent != null;
    }

}
