package ehooray.fishing.pojo.csv;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import ehooray.fishing.pojo.ExceptionByCsvId;

public abstract class ACsv<TRow extends ARow> 
{
    protected String csvPath;
    protected Map<Integer, TRow> idMap = new HashMap<Integer, TRow>();
    
    public TRow getById(int id)
    {
        return idMap.get(id);
    }
    
    public Optional<TRow> getOptionalById(int id)
    {
        if(id <= 0) return Optional.empty();
        
        return Optional.ofNullable(idMap.get(id));
    }
    
    @SuppressWarnings("unchecked")
    public void addRow(List<?> rows) throws ExceptionByCsvId
    {
        for(Object rowObj : rows)
            addRow((TRow)rowObj);
    }
    
    public void addRow(TRow row) throws ExceptionByCsvId
    {
        if(idMap.put(row.getId(), row) != null)
            throw new ExceptionByCsvId(csvPath + " have duplicate id = " + row.getId());
    }
    
    public Collection<TRow> getRows()
    {
        return idMap.values();
    }
    
    // make sure loader invoke this function, for child can cache after loading
    public void onLoadFinish() {}

    public String getCsvPath()
    {
        return csvPath;
    }

    public void setCsvPath(String csvPath)
    {
        this.csvPath = csvPath;
    }
}
