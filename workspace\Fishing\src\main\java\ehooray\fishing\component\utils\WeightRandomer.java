package ehooray.fishing.component.utils;

import org.springframework.stereotype.Component;

import java.util.Random;

@Component
public class WeightRandomer
{
    protected Random randomer = new Random();
    
    
    public int random(int[] weightGroups)
    {
        int weightSum = 0;
        for(int weight : weightGroups)
        {
            if(weight < 0)
                throw new IllegalArgumentException("weight should > 0");
            
            weightSum += weight;
        }
        // random range should from [1, weightSum]
        int randomNumber = randomer.nextInt(weightSum) + 1;
        
        // check randomNumber is in which weight group
        int currentGroupSum = 0;
        for(int index = 0; index < weightGroups.length; index++)
        {
            // ignore weight group = 0
            if(weightGroups[index] <= 0) continue;
            
            currentGroupSum += weightGroups[index];
            if(currentGroupSum < randomNumber) continue;
                
            return index;
        }
        
        // should never enter here
        return -1;
    }
}
