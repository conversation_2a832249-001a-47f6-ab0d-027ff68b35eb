// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/auth/FirebaseAuthRegisterResult.proto

package ehooray.fishing.dto.proto.response.auth;

public final class FirebaseAuthRegisterResultProto {
  private FirebaseAuthRegisterResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FirebaseAuthRegisterResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.auth.FirebaseAuthRegisterResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();
  }
  /**
   * Protobuf type {@code response.auth.FirebaseAuthRegisterResult}
   */
  public static final class FirebaseAuthRegisterResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.auth.FirebaseAuthRegisterResult)
      FirebaseAuthRegisterResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FirebaseAuthRegisterResult.newBuilder() to construct.
    private FirebaseAuthRegisterResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FirebaseAuthRegisterResult() {
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FirebaseAuthRegisterResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.internal_static_response_auth_FirebaseAuthRegisterResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.internal_static_response_auth_FirebaseAuthRegisterResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.class, ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.Builder.class);
    }

    public static final int TOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object token_;
    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.auth.FirebaseAuthRegisterResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.auth.FirebaseAuthRegisterResult)
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.internal_static_response_auth_FirebaseAuthRegisterResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.internal_static_response_auth_FirebaseAuthRegisterResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.class, ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        token_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.internal_static_response_auth_FirebaseAuthRegisterResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult build() {
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult buildPartial() {
        ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult result = new ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult(this);
        result.token_ = token_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private java.lang.Object token_ = "";
      /**
       * <code>string token = 1;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.auth.FirebaseAuthRegisterResult)
    }

    // @@protoc_insertion_point(class_scope:response.auth.FirebaseAuthRegisterResult)
    private static final ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult();
    }

    public static ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FirebaseAuthRegisterResult>
        PARSER = new com.google.protobuf.AbstractParser<FirebaseAuthRegisterResult>() {
      @java.lang.Override
      public FirebaseAuthRegisterResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FirebaseAuthRegisterResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FirebaseAuthRegisterResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.auth.FirebaseAuthRegisterResultProto.FirebaseAuthRegisterResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_auth_FirebaseAuthRegisterResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_auth_FirebaseAuthRegisterResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.response/auth/FirebaseAuthRegisterResu" +
      "lt.proto\022\rresponse.auth\"+\n\032FirebaseAuthR" +
      "egisterResult\022\r\n\005token\030\001 \001(\tBd\n\'ehooray." +
      "fishing.dto.proto.response.authB\037Firebas" +
      "eAuthRegisterResultProtoH\002\252\002\025Fishing.Net" +
      "work.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_response_auth_FirebaseAuthRegisterResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_auth_FirebaseAuthRegisterResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_auth_FirebaseAuthRegisterResult_descriptor,
        new java.lang.String[] { "Token", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
