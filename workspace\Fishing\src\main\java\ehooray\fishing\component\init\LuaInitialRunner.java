package ehooray.fishing.component.init;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.google.common.base.Strings;

import ehooray.fishing.component.lua.LuaLoader;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.lua.LuaCacheContainer;
import ehooray.fishing.pojo.csv.ComponentCsv;

@Component
@Order(RunnerOrder.Lua)
public class LuaInitialRunner implements ApplicationRunner
{
    @Autowired
    protected LuaLoader luaLoader;

    @Autowired
    protected CsvContainer csvContainer;

    @Autowired
    protected LuaCacheContainer luaCacheContainer;

    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        loadLuaFiles();

        luaCacheContainer.initial();
    }

    protected void loadLuaFiles() throws Exception
    {
        ComponentCsv compCsv = csvContainer.getCsv(ComponentCsv.class);
        List<String> luaFiles = fetchLuaFiles(compCsv);
        Map<String, String> luaMap = luaLoader.loadFiles(luaFiles);
        putLuaFiles(luaMap);
    }

    private List<String> fetchLuaFiles(ComponentCsv compCsv)
    {
        List<String> luaFiles = new ArrayList<>();
        for (ComponentCsv.Row row : compCsv.getRows())
        {
            String luaFile = row.getLua();
            if (Strings.isNullOrEmpty(luaFile) || luaFiles.contains(luaFile)) continue;
            luaFiles.add(luaFile);
        }
        return luaFiles;
    }

    private void putLuaFiles(Map<String, String> luaMap)
    {
        for(Map.Entry<String,String> kvp : luaMap.entrySet())
        {
            csvContainer.addLua(kvp.getKey(), kvp.getValue());
        }
    }

}
