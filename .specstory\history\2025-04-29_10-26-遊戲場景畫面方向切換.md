<!-- Generated by SpecStory -->

# 遊戲場景畫面方向切換 (2025-04-29 18:26:44)

_**User**_

請幫我進入NewBattleScene時，將畫面改成橫版，出來的時候改成直的

---

_**Assistant**_



---

好的，我們需要在進入 `NewBattle` 場景時將螢幕方向設定為橫向，並在離開時（例如返回大廳）恢復為縱向。

我將修改 `MapPanel.cs` 中的 `GoMapRunner` 方法，在載入場景之前設定螢幕為橫向。
然後，我將修改 `PausePanelManager.cs` 中的 `UpdateReturnToSelectPlaceAsync` 方法，在載入大廳場景之前將螢幕恢復為縱向。

首先，修改 `MapPanel.cs`。


---

```diff
                  var mapData = tableContainer.SpotTable.GetData(mapNumber);
                  GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.BattleMpaPrefabPath + mapData.Map);
+                 
+                 // 設定螢幕為橫向
+                 Screen.orientation = ScreenOrientation.LandscapeLeft; 
+                 Screen.autorotateToPortrait = false;
+                 Screen.autorotateToPortraitUpsideDown = false;
+                 Screen.autorotateToLandscapeLeft = true;
+                 Screen.autorotateToLandscapeRight = true;
+ 
                  sceneLoadingManager.LoadSceneAsync("NewBattle", (container) =>
                  {
```

---

接下來，修改 `PausePanelManager.cs`。


---

```diff
          {
              await battleService.FailFish();
+ 
+             // 設定螢幕為縱向
+             Screen.orientation = ScreenOrientation.Portrait;
+             Screen.autorotateToPortrait = true;
+             Screen.autorotateToPortraitUpsideDown = true;
+             Screen.autorotateToLandscapeLeft = false;
+             Screen.autorotateToLandscapeRight = false;
+ 
              await sceneLoadingManager.LoadSceneAsync("Lobby", (container) =>
              {
```

---



---

