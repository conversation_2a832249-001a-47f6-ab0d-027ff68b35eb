# 專案檔案結構與功能說明

## GDD 目錄參考
1.  Game Overview
2.  Core Gameplay Loop
3.  Attributes System
4.  Fishing System
    *   4.1 Fishing Mechanics
        *   4.1.1 Cast Minigame
        *   4.1.2 Lure Minigame (Enhanced)
        *   4.1.3 Reel-In Minigame
    *   4.2 Time System
    *   4.3 Weather System
    *   4.4 Lure Types
    *   4.5 Fish Struggle Styles
    *   4.6 QTE Reaction Controls
5.  Crafting System
6.  Level Progression
7.  Inventory and Equipment
    *   7.1 Inventory System
    *   7.2 Equipment System
8.  Social System
    *   8.1 Cruise Cabin
    *   8.2 Story and Dialog System (RenJS)
9.  Shop System
10. Achievement System
11. Economy System
12. Art and Audio
13. Technical Design

data:
  - attributes.json: 屬性數據，定義角色或物品的各種屬性。 [GDD: 3, 3.1, 3.2]
  - equipment.json: 裝備數據，定義各種裝備的屬性。 [GDD: 7.2]
  - fish.json: 魚類數據，定義各種魚類的屬性和行為。 [GDD: 3.2, 4.5]
  - gameConfig.json: 遊戲配置，包含全域遊戲設定。 [GDD: 13]
  - inventorySchema.json: 庫存模式，定義庫存系統的數據結構。 [GDD: 7.1]
  - LocationData.js: 地點數據，定義遊戲中不同地點的資訊。 [GDD: 2.1, 2.2, 4.4]
  - lures.json: 魚餌數據，定義各種魚餌的屬性。 [GDD: 4.4, 7.2]
  - sample-renjs-quest-dialog.yaml: RenJS 任務對話範例，用於對話系統。 [GDD: 8.2, 8.3]
  - TextConfig.js: 文字配置，可能包含遊戲中各種文字字串。 [GDD: 12, 13]

scenes:
  - AlbumScene.js: 圖鑑場景，可能用於展示收集到的魚類或其他物品。 [GDD: 8.1]
  - BoatMenuScene.js: 船隻選單場景，可能用於選擇船隻或相關操作。 [GDD: 2.1, 2.2]
  - BootScene.js: 啟動場景，遊戲的初始進入點。 [GDD: 13]
  - CabinScene.js: 小屋場景，可能是玩家的基地或休息場所。 [GDD: 8.1]
  - DialogScene.js: 對話場景，處理遊戲中的角色對話。 [GDD: 8.2]
  - GameScene.js: 核心遊戲場景，主要的釣魚活動發生在此。 [GDD: 2, 4]
  - HUDScene.js: HUD（抬頭顯示器）場景，顯示遊戲中的即時資訊。 [GDD: 2.2, 12]
  - MenuScene.js: 主選單場景。 [GDD: 2.1]
  - PreloadScene.js: 預載入場景，在遊戲開始前載入資源。 [GDD: 13]
  - QuestScene.js: 任務場景，顯示和管理任務。 [GDD: 2.2, 8.3]
  - SettingsScene.js: 設定場景，處理遊戲設定選項。 [GDD: 13]
  - ShopScene.js: 商店場景，用於購買物品。 [GDD: 2, 9]

scripts:
  - AchievementEnhancer.js: 成就增強器，可能與成就系統的擴展功能相關。 [GDD: 10]
  - AdvancedQuestSystem.js: 進階任務系統，提供更複雜的任務功能。 [GDD: 8.3]
  - AudioManager.js: 音訊管理器，處理遊戲中的聲音和音樂。 [GDD: 12]
  - CastingMiniGame.js: 拋竿迷你遊戲邏輯。 [GDD: 4.1.1]
  - CraftingManager.js: 合成管理器，處理物品的製作。 [GDD: 5]
  - DataLoader.js: 數據載入器，負責載入遊戲所需的數據檔案。 [GDD: 13]
  - DialogManager.js: 對話管理器，管理遊戲中的對話流程。 [GDD: 8.2]
  - EnhancedAudioManager.js: 增強型音訊管理器，提供更進階的音訊控制功能。 [GDD: 12]
  - EquipmentEnhancer.js: 裝備強化器，處理裝備的升級和強化。 [GDD: 7.2]
  - EventEmitter.js: 事件發射器，用於實現事件驅動的程式設計模式。 [GDD: 13]
  - FishDatabase.js: 魚類資料庫，儲存和管理魚類數據。 [GDD: 3.2, 4.5, 13]
  - FishSpawner.js: 魚類生成器，控制魚類在遊戲世界的生成。 [GDD: 4, 4.5]
  - FishingMinigames.js: 釣魚迷你遊戲的主控腳本或集合。 [GDD: 4.1]
  - GameLoop.js: 遊戲循環，驅動遊戲的核心邏輯。 [GDD: 2, 13]
  - GameState.js: 遊戲狀態管理器，追蹤和管理遊戲的整體狀態。 [GDD: 2, 13]
  - HCGNotificationManager.js: HCG 通知管理器 (具體 HCG 指代不明，可能是某個框架或系統)。 [GDD: 8.1, 10]
  - HCGUnlockSystem.js: HCG 解鎖系統 (具體 HCG 指代不明)。 [GDD: 8.1, 10]
  - InputManager.js: 輸入管理器，處理來自鍵盤、滑鼠或觸控螢幕的輸入。 [GDD: 4.1, 13]
  - InventoryManager.js: 庫存管理器，處理玩家物品的儲存和使用。 [GDD: 7.1]
  - InventoryValidator.js: 庫存驗證器，驗證庫存操作的有效性。 [GDD: 7.1, 13]
  - LocationManager.js: 地點管理器，管理遊戲中的不同地點。 [GDD: 2, 4.4]
  - LuringMiniGame.js: 誘魚迷你遊戲邏輯。 [GDD: 4.1.2]
  - PlayerController.js: 玩家控制器，處理玩家輸入和行為。 [GDD: 2, 3, 4]
  - PlayerProgression.js: 玩家進程管理器，追蹤和管理玩家的成長和解鎖內容。 [GDD: 3.1.1, 6]
  - QuestManager.js: 任務管理器，管理遊戲中的任務。 [GDD: 8.3]
  - ReelingMiniGame.js: 收線迷你遊戲邏輯。 [GDD: 4.1.3]
  - RenJsContentValidator.js: RenJS 內容驗證器，驗證 RenJS 相關內容的正確性。 [GDD: 8.2, 13]
  - RenJsLoader.js: RenJS 載入器，載入 RenJS 相關資源。 [GDD: 8.2, 13]
  - RenJsSaveIntegration.js: RenJS 存檔整合，將 RenJS 的存檔功能整合到遊戲中。 [GDD: 8.2, 13]
  - SaveUtility.js: 存檔工具，處理遊戲的儲存和載入。 [GDD: 13]
  - SceneManager.js: 場景管理器，管理不同遊戲場景的切換。 [GDD: 13]
  - SettingsManager.js: 設定管理器，管理遊戲的各項設定。 [GDD: 13]
  - TimeManager.js: 時間管理器，控制遊戲中的時間流逝。 [GDD: 4.2]
  - TournamentManager.js: 錦標賽管理器，組織和管理釣魚比賽。 [GDD: 6, specific events]
  - WeatherManager.js: 天氣管理器，控制遊戲中的天氣變化。 [GDD: 4.3]

ui:
  - AchievementPopupSystem.js: 成就彈出提示系統。 [GDD: 10, 12]
  - AudioSettingsUI.js: 音訊設定的使用者介面。 [GDD: 12, 13]
  - CraftingUI.js: 合成系統的使用者介面。 [GDD: 5, 12]
  - EquipmentEnhancementUI.js: 裝備強化系統的使用者介面。 [GDD: 7.2, 12]
  - FishCollectionUI.js: 魚類圖鑑或收集冊的使用者介面。 [GDD: 7.1, 8.1, 12]
  - InventoryUI.js: 庫存系統的使用者介面。 [GDD: 7.1, 12]
  - LoadingStateManager.js: 載入狀態管理器，可能負責顯示載入進度條等。 [GDD: 12, 13]
  - MapSelectionUI.js: 地圖選擇使用者介面。 [GDD: 2, 4.4, 12]
  - PlayerProgressionUI.js: 玩家進程的使用者介面，顯示玩家等級、經驗等。 [GDD: 6, 12]
  - RenJsDebugUI.js: RenJS 調試使用者介面，用於開發和測試。 [GDD: 8.2, 13]
  - ShopUI.js: 商店的使用者介面。 [GDD: 9, 12]
  - TimeWeatherUI.js: 時間和天氣資訊的使用者介面。 [GDD: 4.2, 4.3, 12]
  - UITheme.js: UI 主題，定義介面元素的統一樣式。 [GDD: 12]
