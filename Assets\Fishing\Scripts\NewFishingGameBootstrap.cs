using Cysharp.Threading.Tasks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using Ehooray.UI;
using Fishing.UI;
using Naninovel;
using Zenject;
using UnityEngine.UI;

namespace Fishing
{
    public class NewFishingGameBootstrap : IInitializable
    {
        private const int NewPlayerLv = 1;
        private const int NewPlayerExp = 0;
        Camera mainCamera;
        FishingManager fishingManager;
        GamePanelManager gamePanelManager;
        SceneLoadingManager sceneLoadingManager;
        FishingRepositories fishingRepositories;
        TableContainer tableContainer;
        SoundManager soundManager;
        GameCache gameCache;
        GameTeaching gameTeaching;

        public NewFishingGameBootstrap(Camera mainCamera,
            FishingManager fishingManager,
            GamePanelManager gamePanelManager,
            SceneLoadingManager sceneLoadingManager,
            FishingRepositories fishingRepositories,
            TableContainer tableContainer,
            SoundManager soundManager,
            GameCache gameCache,
            GameTeaching gameTeaching)
        {
            this.mainCamera = mainCamera;
            this.fishingManager = fishingManager;
            this.gamePanelManager = gamePanelManager;
            this.sceneLoadingManager = sceneLoadingManager;
            this.fishingRepositories = fishingRepositories;
            this.tableContainer = tableContainer;
            this.soundManager = soundManager;
            this.gameCache = gameCache;
            this.gameTeaching = gameTeaching;
        }

        public void Initialize()
        {
            InitializeAsync().Forget();
            DynamicGI.UpdateEnvironment();
        }

        private async Cysharp.Threading.Tasks.UniTaskVoid InitializeAsync()
        {
            // 初始化Naninovel相機堆疊
            InitNaninovel();
            
            // 預加載所有UI面板
            await PreloadUIPanels();
            
            // 載入釣魚管理器面板
            fishingManager.LoadPanel();

            // 完成場景加載並初始化玩家設置
            Debug.Log("Battle loading");
            sceneLoadingManager.HideSceneLoadingUI();
            LoadPlayerSetting();
            
            // 檢查是否為新手玩家，顯示教學
            // if (IsNewPlayer())
            //     gameTeaching.PlayGameTeachingAsync().Forget();
        }

        private async Cysharp.Threading.Tasks.UniTask PreloadUIPanels()
        {
            // 臨時加載後立即關閉的面板
            await PreloadTemporaryPanel<FishInfoManager>();
            await PreloadTemporaryPanel<ThrowBaitManager>();
            await PreloadTemporaryPanel<NewLineTensionManager>();
            await PreloadTemporaryPanel<GameEndManager>();
            await PreloadTemporaryPanel<ContinueManager>();
            await PreloadTemporaryPanel<CloseupTextManager>();
            await PreloadTemporaryPanel<PausePanelManager>();
            await PreloadTemporaryPanel<LureActionSystem>();
            await PreloadTemporaryPanel<LureAnimationController>();
            
            // 保持開啟的面板
            await PreloadPersistentPanel<CordReelManager>();
            await PreloadPersistentPanel<PrepareManager>();
            await PreloadPersistentPanel<TopInfoManager>();
        }

        private async Cysharp.Threading.Tasks.UniTask PreloadTemporaryPanel<T>() where T : GamePanel
        {
            T panel = await gamePanelManager.OpenAsync<T>();
            ConfigureCanvasScaler(panel);
            gamePanelManager.Close<T>();
        }
        
        private async Cysharp.Threading.Tasks.UniTask PreloadPersistentPanel<T>() where T : GamePanel
        {
            T panel = await gamePanelManager.OpenAsync<T>();
            ConfigureCanvasScaler(panel);
        }
        
        private void ConfigureCanvasScaler(GamePanel panel)
        {
            Canvas canvas = panel.PanelCanvas;
            CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
            
            if (scaler != null)
            {
                // 設置為1920x1080的橫版佈局
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.matchWidthOrHeight = 1; // 以高度對齊
                
                // 設置Canvas為螢幕空間相機模式
                canvas.renderMode = RenderMode.ScreenSpaceCamera;
                canvas.worldCamera = gamePanelManager.UICamera;
                
                Debug.Log($"已設置 {panel.GetType().Name} 為橫版1920x1080佈局，以高度對齊");
            }
            else
            {
                Debug.LogWarning($"{panel.GetType().Name} 未找到CanvasScaler組件");
            }
        }

        private void InitNaninovel()
        {
            var naniCameraManager = Naninovel.Engine.GetService<Naninovel.ICameraManager>();
            var cameraData = mainCamera.GetUniversalAdditionalCameraData();
            cameraData.cameraStack.Add(naniCameraManager.Camera);
        }

        private bool IsNewPlayer() 
        {
            var player = fishingRepositories.playerRepository.player;
            Debug.Log($"player level : {player.level}");
            Debug.Log($"player exp : {player.exp}");
            if(player.level == NewPlayerLv && player.exp == NewPlayerExp)
                return true;
            else
                return false;
        }

        private void LoadPlayerSetting()
        {
            soundManager.Bgm.volume = gameCache.playerData.loginCache.bgmVolume;
            soundManager.Bgm.Play();
            soundManager.Sfx.volume = gameCache.playerData.loginCache.sfxVolume;
            ShakeCamera.isShakingOn = gameCache.playerData.loginCache.isShake;
            Application.targetFrameRate = gameCache.playerData.loginCache.fps;
        }
    }
}