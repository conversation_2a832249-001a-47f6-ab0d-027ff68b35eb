package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("QuestTable.xlsx")
public class QuestCsv extends ACsv<QuestCsv.Row>
{
    public enum QuestType
    {
        Normal(1),
        Daily(2),
        Weekly(3),
        Monthly(4),
        Acheivement(6),
        Exchange(7)
        ;
        
        protected int id;
        private QuestType(int id)
        {
            this.id = id;
        }
        
        public int getId()
        {
            return id;
        }
        
        public static Optional<QuestType> valueOf(int id)
        {
            return Arrays.stream(QuestType.values()).filter(x->x.getId()== id).findFirst();
        }
    }
    
    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        
        @ExcelCellName("QuestTypeID")
        protected int questTypeId;
        @ExcelCellName("QuestName")
        protected int questName;
        @ExcelCellName("QuestDesc")
        protected int questDesc;
        @ExcelCellName("QuestIcon")
        protected String questIcon;
        @ExcelCellName("QuestTarget")
        protected int questTarget;
        
        @ExcelCellName("ResourceConsume1")
        protected int resourceConsume1;
        @ExcelCellName("ResourceConsume1Value")
        protected int resourceConsume1Value;
        @ExcelCellName("ResourceConsume2")
        protected int resourceConsume2;
        @ExcelCellName("ResourceConsume2Value")
        protected int resourceConsume2Value;
        @ExcelCellName("ResourceConsume3")
        protected int resourceConsume3;
        @ExcelCellName("ResourceConsume3Value")
        protected int resourceConsume3Value;
        @ExcelCellName("ResourceConsume4")
        protected int resourceConsume4;
        @ExcelCellName("ResourceConsume4Value")
        protected int resourceConsume4Value;

        @ExcelCellName("UnlockCondition")
        protected int unlockCondition;
        
        @ExcelCellName("Reward1ID")
        protected int reward1ID;
        @ExcelCellName("Reward1Value")
        protected int reward1Value;
        @ExcelCellName("Reward2ID")
        protected int reward2ID;
        @ExcelCellName("Reward2Value")
        protected int reward2Value;
        @ExcelCellName("Reward3ID")
        protected int reward3ID;
        @ExcelCellName("Reward3Value")
        protected int reward3Value;
        @ExcelCellName("ExpReward")
        protected int expReward;

        
        @Override
        public int getId()
        {
            return id;
        }
        
        public void setId(int id)
        {
            this.id = id;
        }


        public int getRowIndex()
        {
            return rowIndex;
        }


        public void setRowIndex(int rowIndex)
        {
            this.rowIndex = rowIndex;
        }


        public int getQuestTypeId()
        {
            return questTypeId;
        }


        public void setQuestTypeId(int questTypeId)
        {
            this.questTypeId = questTypeId;
        }


        public int getQuestName()
        {
            return questName;
        }


        public void setQuestName(int questName)
        {
            this.questName = questName;
        }


        public int getQuestDesc()
        {
            return questDesc;
        }


        public void setQuestDesc(int questDesc)
        {
            this.questDesc = questDesc;
        }


        public String getQuestIcon()
        {
            return questIcon;
        }


        public void setQuestIcon(String questIcon)
        {
            this.questIcon = questIcon;
        }


        public int getQuestTarget()
        {
            return questTarget;
        }


        public void setQuestTarget(int questTarget)
        {
            this.questTarget = questTarget;
        }
        
        protected int[] consumeIds;
        public int[] getConsumeIds()
        {
        	if (consumeIds != null) return consumeIds;
        	
        	// atomic operation, do not assign separately
        	consumeIds = new int[] { resourceConsume1, resourceConsume2, resourceConsume3, resourceConsume4 };
        	return consumeIds;
        }
        
        protected int[] consumeValues;
        public int[] getConsumeValues()
        {
        	if (consumeValues != null) return consumeValues;
        	
        	// atomic operation, do not assign separately
        	consumeValues = new int[] { resourceConsume1Value, resourceConsume2Value, resourceConsume3Value, resourceConsume4Value };
        	return consumeValues;
        }

        public int getUnlockCondition()
        {
            return unlockCondition;
        }


        public void setUnlockCondition(int unlockCondition)
        {
            this.unlockCondition = unlockCondition;
        }
        
        protected int[] rewardIds;
        public int[] getRewardIds()
        {
            if(rewardIds != null) return rewardIds;
            
            // atomic operation, do not assign separately
            rewardIds = new int[] { reward1ID, reward2ID, reward3ID };
            return rewardIds;
        }
        
        protected int[] rewardValues;
        public int[] getRewardValues()
        {
            if(rewardValues != null) return rewardValues;
            
            // atomic operation, do not assign separately
            rewardValues = new int[] { reward1Value, reward2Value, reward3Value };
            return rewardValues;
        }

        public int getExpReward()
        {
            return expReward;
        }


        public void setExpReward(int expReward)
        {
            this.expReward = expReward;
        }
        
        
    }
}
