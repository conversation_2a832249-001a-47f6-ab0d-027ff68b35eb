// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: request/gacha/BuyGacha.proto

package ehooray.fishing.dto.proto.request.gacha;

public final class BuyGachaProto {
  private BuyGachaProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BuyGachaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:request.gacha.BuyGacha)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 buyValueId = 2;</code>
     * @return The buyValueId.
     */
    int getBuyValueId();
  }
  /**
   * Protobuf type {@code request.gacha.BuyGacha}
   */
  public static final class BuyGacha extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:request.gacha.BuyGacha)
      BuyGachaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BuyGacha.newBuilder() to construct.
    private BuyGacha(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BuyGacha() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BuyGacha();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.internal_static_request_gacha_BuyGacha_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.internal_static_request_gacha_BuyGacha_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.class, ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.Builder.class);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int BUYVALUEID_FIELD_NUMBER = 2;
    private int buyValueId_;
    /**
     * <code>int32 buyValueId = 2;</code>
     * @return The buyValueId.
     */
    @java.lang.Override
    public int getBuyValueId() {
      return buyValueId_;
    }

    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code request.gacha.BuyGacha}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:request.gacha.BuyGacha)
        ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGachaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.internal_static_request_gacha_BuyGacha_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.internal_static_request_gacha_BuyGacha_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.class, ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        buyValueId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.internal_static_request_gacha_BuyGacha_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha build() {
        ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha buildPartial() {
        ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha result = new ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha(this);
        result.entityId_ = entityId_;
        result.buyValueId_ = buyValueId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int buyValueId_ ;
      /**
       * <code>int32 buyValueId = 2;</code>
       * @return The buyValueId.
       */
      @java.lang.Override
      public int getBuyValueId() {
        return buyValueId_;
      }
      /**
       * <code>int32 buyValueId = 2;</code>
       * @param value The buyValueId to set.
       * @return This builder for chaining.
       */
      public Builder setBuyValueId(int value) {
        
        buyValueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 buyValueId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyValueId() {
        
        buyValueId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:request.gacha.BuyGacha)
    }

    // @@protoc_insertion_point(class_scope:request.gacha.BuyGacha)
    private static final ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha();
    }

    public static ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BuyGacha>
        PARSER = new com.google.protobuf.AbstractParser<BuyGacha>() {
      @java.lang.Override
      public BuyGacha parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<BuyGacha> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BuyGacha> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.request.gacha.BuyGachaProto.BuyGacha getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_request_gacha_BuyGacha_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_request_gacha_BuyGacha_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034request/gacha/BuyGacha.proto\022\rrequest." +
      "gacha\"0\n\010BuyGacha\022\020\n\010entityId\030\001 \001(\003\022\022\n\nb" +
      "uyValueId\030\002 \001(\005BR\n\'ehooray.fishing.dto.p" +
      "roto.request.gachaB\rBuyGachaProtoH\002\252\002\025Fi" +
      "shing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_request_gacha_BuyGacha_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_request_gacha_BuyGacha_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_request_gacha_BuyGacha_descriptor,
        new java.lang.String[] { "EntityId", "BuyValueId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
