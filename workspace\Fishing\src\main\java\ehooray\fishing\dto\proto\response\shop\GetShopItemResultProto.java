// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: response/shop/GetShopItemResult.proto

package ehooray.fishing.dto.proto.response.shop;

public final class GetShopItemResultProto {
  private GetShopItemResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetShopItemResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:response.shop.GetShopItemResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.model.Shop shop = 1;</code>
     * @return Whether the shop field is set.
     */
    boolean hasShop();
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return The shop.
     */
    ehooray.fishing.dto.proto.model.ShopProto.Shop getShop();
    /**
     * <code>.model.Shop shop = 1;</code>
     */
    ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder();
  }
  /**
   * Protobuf type {@code response.shop.GetShopItemResult}
   */
  public static final class GetShopItemResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:response.shop.GetShopItemResult)
      GetShopItemResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetShopItemResult.newBuilder() to construct.
    private GetShopItemResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetShopItemResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetShopItemResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.internal_static_response_shop_GetShopItemResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.internal_static_response_shop_GetShopItemResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.class, ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.Builder.class);
    }

    public static final int SHOP_FIELD_NUMBER = 1;
    private ehooray.fishing.dto.proto.model.ShopProto.Shop shop_;
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return Whether the shop field is set.
     */
    @java.lang.Override
    public boolean hasShop() {
      return shop_ != null;
    }
    /**
     * <code>.model.Shop shop = 1;</code>
     * @return The shop.
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ShopProto.Shop getShop() {
      return shop_ == null ? ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
    }
    /**
     * <code>.model.Shop shop = 1;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder() {
      return getShop();
    }

    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code response.shop.GetShopItemResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:response.shop.GetShopItemResult)
        ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.internal_static_response_shop_GetShopItemResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.internal_static_response_shop_GetShopItemResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.class, ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (shopBuilder_ == null) {
          shop_ = null;
        } else {
          shop_ = null;
          shopBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.internal_static_response_shop_GetShopItemResult_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult build() {
        ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult buildPartial() {
        ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult result = new ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult(this);
        if (shopBuilder_ == null) {
          result.shop_ = shop_;
        } else {
          result.shop_ = shopBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private ehooray.fishing.dto.proto.model.ShopProto.Shop shop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder> shopBuilder_;
      /**
       * <code>.model.Shop shop = 1;</code>
       * @return Whether the shop field is set.
       */
      public boolean hasShop() {
        return shopBuilder_ != null || shop_ != null;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       * @return The shop.
       */
      public ehooray.fishing.dto.proto.model.ShopProto.Shop getShop() {
        if (shopBuilder_ == null) {
          return shop_ == null ? ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
        } else {
          return shopBuilder_.getMessage();
        }
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder setShop(ehooray.fishing.dto.proto.model.ShopProto.Shop value) {
        if (shopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          shop_ = value;
          onChanged();
        } else {
          shopBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder setShop(
          ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder builderForValue) {
        if (shopBuilder_ == null) {
          shop_ = builderForValue.build();
          onChanged();
        } else {
          shopBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder mergeShop(ehooray.fishing.dto.proto.model.ShopProto.Shop value) {
        if (shopBuilder_ == null) {
          if (shop_ != null) {
            shop_ =
              ehooray.fishing.dto.proto.model.ShopProto.Shop.newBuilder(shop_).mergeFrom(value).buildPartial();
          } else {
            shop_ = value;
          }
          onChanged();
        } else {
          shopBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public Builder clearShop() {
        if (shopBuilder_ == null) {
          shop_ = null;
          onChanged();
        } else {
          shop_ = null;
          shopBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder getShopBuilder() {
        
        onChanged();
        return getShopFieldBuilder().getBuilder();
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      public ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder getShopOrBuilder() {
        if (shopBuilder_ != null) {
          return shopBuilder_.getMessageOrBuilder();
        } else {
          return shop_ == null ?
              ehooray.fishing.dto.proto.model.ShopProto.Shop.getDefaultInstance() : shop_;
        }
      }
      /**
       * <code>.model.Shop shop = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder> 
          getShopFieldBuilder() {
        if (shopBuilder_ == null) {
          shopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ehooray.fishing.dto.proto.model.ShopProto.Shop, ehooray.fishing.dto.proto.model.ShopProto.Shop.Builder, ehooray.fishing.dto.proto.model.ShopProto.ShopOrBuilder>(
                  getShop(),
                  getParentForChildren(),
                  isClean());
          shop_ = null;
        }
        return shopBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:response.shop.GetShopItemResult)
    }

    // @@protoc_insertion_point(class_scope:response.shop.GetShopItemResult)
    private static final ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult();
    }

    public static ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetShopItemResult>
        PARSER = new com.google.protobuf.AbstractParser<GetShopItemResult>() {
      @java.lang.Override
      public GetShopItemResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetShopItemResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetShopItemResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.response.shop.GetShopItemResultProto.GetShopItemResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_response_shop_GetShopItemResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_response_shop_GetShopItemResult_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%response/shop/GetShopItemResult.proto\022" +
      "\rresponse.shop\032\020model/Shop.proto\".\n\021GetS" +
      "hopItemResult\022\031\n\004shop\030\001 \001(\0132\013.model.Shop" +
      "B[\n\'ehooray.fishing.dto.proto.response.s" +
      "hopB\026GetShopItemResultProtoH\002\252\002\025Fishing." +
      "Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ehooray.fishing.dto.proto.model.ShopProto.getDescriptor(),
        });
    internal_static_response_shop_GetShopItemResult_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_response_shop_GetShopItemResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_response_shop_GetShopItemResult_descriptor,
        new java.lang.String[] { "Shop", });
    ehooray.fishing.dto.proto.model.ShopProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
