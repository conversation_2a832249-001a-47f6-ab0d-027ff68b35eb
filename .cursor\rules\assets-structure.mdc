---
description: Unity fishing game project Assets directory structure guide, detailing the purposes and functions of various directories including game core resources, third-party plugins, environment assets, and service integrations.
globs: 
alwaysApply: false
---
# Assets目录结构说明

此规则文件描述了Unity项目中Assets目录下的一级子目录结构及其用途。

## 游戏核心资源
- [Fishing](mdc:Assets/Fishing): 游戏主要资源，包含钓鱼游戏的核心资产、脚本和资源
- [Scenes](mdc:Assets/Scenes): 游戏场景文件，包含所有游戏关卡和UI场景
- [Resources](mdc:Assets/Resources): 运行时动态加载的资源

## 水和环境相关
- [StylizedWater2](mdc:Assets/StylizedWater2): 风格化水体渲染插件
- [Stone Rocks Pack](mdc:Assets/Stone Rocks Pack): 石头和岩石资源包
- [Rocks_1](mdc:Assets/Rocks_1): 额外的岩石模型资源
- [IL3DN](mdc:Assets/IL3DN): 可能是自然环境资源包
- [Animated Trees Package](mdc:Assets/Animated Trees Package): 动画树木资源包

## 插件和框架
- [TopDownEngine](mdc:Assets/TopDownEngine): 俯视角游戏引擎插件
- [TextMesh Pro](mdc:Assets/TextMesh Pro): Unity文本渲染插件
- [DOTween](mdc:Assets/DOTween): 动画补间插件
- [PathCreator](mdc:Assets/PathCreator): 路径创建工具
- [Parse](mdc:Assets/Parse): Parse后端集成
- [Naninovel](mdc:Assets/Naninovel): 视觉小说框架
- [NaninovelData](mdc:Assets/NaninovelData): Naninovel数据文件
- [Fingers](mdc:Assets/Fingers): 触控输入插件
- [XLua](mdc:Assets/XLua): Lua脚本集成
- [QuickSheet](mdc:Assets/QuickSheet): 可能是表格数据导入工具
- [Polybrush Data](mdc:Assets/Polybrush Data): Unity的Polybrush工具数据

## 第三方服务集成
- [Firebase](mdc:Assets/Firebase): Firebase服务集成
- [ExternalDependencyManager](mdc:Assets/ExternalDependencyManager): 外部依赖管理器，可能与Firebase相关
- [Best HTTP](mdc:Assets/Best HTTP): HTTP请求处理插件

## 其他辅助资源
- [BOXOPHOBIC](mdc:Assets/BOXOPHOBIC): 可能是着色器或环境渲染资源
- [Ehooray](mdc:Assets/Ehooray): 自定义工具或资源
- [ZLowPoly](mdc:Assets/ZLowPoly): 低多边形模型资源
- [StreamingAssets](mdc:Assets/StreamingAssets): 运行时加载的资源
- [Plugins](mdc:Assets/Plugins): 原生插件
- [Editor Default Resources](mdc:Assets/Editor Default Resources): Unity编辑器默认资源
- [Demo](mdc:Assets/Demo): 示例场景或资源
- [AddressableAssetsData](mdc:Assets/AddressableAssetsData): Unity可寻址资产系统数据

