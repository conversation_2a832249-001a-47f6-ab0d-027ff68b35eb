package ehooray.fishing.component.iab.unity;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Optional;

import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;

import ehooray.fishing.pojo.iab.unity.UnityIabReceiptResult;
import ehooray.fishing.pojo.iab.unity.UnityIabReceiptVerifyException;

@Component
public class UnityEditorIabReceiptVerifier implements IIabReceiptVerifier
{
    public enum ResultCode
    {
        Success(1),
        Fail(2)
        ;

        private int id;
        private ResultCode(int id)
        {
            this.id = id;
        }
        public int getId()
        {
            return id;
        }

        public static Optional<ResultCode> valueOf(int id)
        {
            return Arrays.stream(ResultCode.values()).filter(x-> x.getId() == id).findFirst();
        }
    }
    
    protected String md5Key;
	protected String appId;
	
	public UnityIabReceiptResult verify(String receiptStr) throws Exception
	{
		
		// Note: simple verify, this is for unity editor testing iab flow.
		//       client should not export this protocol to other platform.
		
		Receipt receipt = new ObjectMapper().readValue(receiptStr, Receipt.class);
		
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(receipt.ProductId);
		stringBuffer.append(receipt.TransactionId);
		stringBuffer.append(receipt.PurchaseTime);
		stringBuffer.append(md5Key);
		stringBuffer.append(receipt.AppId);
		
		
		String signature = DigestUtils.md5Hex(StringUtils.getBytesUtf8(stringBuffer.toString()));
		if(!signature.equalsIgnoreCase(receipt.Signature))
			throw new UnityIabReceiptVerifyException(receipt.TransactionId, ResultCode.Fail.getId(), "signature failed");
		
		if(!appId.equals(receipt.AppId))
		    throw new UnityIabReceiptVerifyException(receipt.TransactionId, ResultCode.Fail.getId(), "appId not the same");
		
		UnityIabReceiptResult receiptResult = new UnityIabReceiptResult();
		receiptResult.transactionId = receipt.TransactionId;
		receiptResult.productId = receipt.ProductId;
		receiptResult.purchaseDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(receipt.PurchaseTime), ZoneId.systemDefault());
		receiptResult.isTest = true;
		receiptResult.receiptVerifyResultStr = receiptStr;
		receiptResult.platformState = ResultCode.Success.getId();
		return receiptResult;
	}
	
	@JsonIgnoreProperties(ignoreUnknown=true)
	public static class Receipt
	{
		@JsonProperty public String AppId;
		@JsonProperty public String ProductId;
		@JsonProperty public String TransactionId;
		@JsonProperty public long PurchaseTime;
		@JsonProperty public String Signature;
		
		public Receipt() {}

	}// end class Receipt
	
	public void initial(String appId, String md5Key) throws Exception
    {	    	
		this.appId = appId;
		this.md5Key = md5Key;
    }
}
