package ehooray.fishing.pojo.csv;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("GachaMachineTable.xlsx")
public class GachaMachineCsv extends ACsv<GachaMachineCsv.Row>
{

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;

        @ExcelCellName("ID")
        protected int id;

        @ExcelCellName("Group")
        protected int group;

        @ExcelCellName("ItemGroup")
        protected int itemGroup;

        @ExcelCellName("FreeTime")
        protected int freeTime;

        @ExcelCellName("CostItem")
        protected int costItem;

        @ExcelCellName("CostValue")
        protected int costValue;

        @Override
        public int getId()
        {
            return id;
        }

        public int getItemGroup()
        {
            return itemGroup;
        }

        public void setItemGroup(int itemGroup)
        {
            this.itemGroup = itemGroup;
        }

        public int getCostItem()
        {
            return costItem;
        }

        public void setCostItem(int costItem)
        {
            this.costItem = costItem;
        }

        public int getCostValue()
        {
            return costValue;
        }

        public void setCostValue(int costValue)
        {
            this.costValue = costValue;
        }

        public int getGroup()
        {
            return group;
        }

        public int getFreeTime()
        {
            return freeTime;
        }

        public boolean isFree()
        {
            return getFreeTime() > 0;
        }
    }

    // another utility function at here
    protected HashMap<Integer, List<Row>> groupMap;

    @Override
    public void onLoadFinish()
    {
        super.onLoadFinish();

        Collection<Row> rows = getRows();
        groupMap = new HashMap<>(rows.size());
        for(Row row : rows)
        {
            List<Row> rowsInGroup = groupMap.get(row.getItemGroup());
            if(rowsInGroup == null)
            {
                rowsInGroup = new ArrayList<>();
                groupMap.put(row.getItemGroup(), rowsInGroup);
            }
            rowsInGroup.add(row);
        }
    }

    public List<Row> findAllByGroup(int group)
    {
        return groupMap.get(group);
    }
}
