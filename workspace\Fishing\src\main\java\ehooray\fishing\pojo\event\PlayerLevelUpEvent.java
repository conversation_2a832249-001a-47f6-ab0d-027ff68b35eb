package ehooray.fishing.pojo.event;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class PlayerLevelUpEvent extends ApplicationEvent
{
	private final PlayerEntity player;

    public PlayerLevelUpEvent(Object source, PlayerEntity player)
    {
        super(source);
        this.player = player;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }
}
