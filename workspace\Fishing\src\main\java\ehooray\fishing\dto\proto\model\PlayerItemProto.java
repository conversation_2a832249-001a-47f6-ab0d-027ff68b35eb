// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/PlayerItem.proto

package ehooray.fishing.dto.proto.model;

public final class PlayerItemProto {
  private PlayerItemProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PlayerItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.PlayerItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 csvId = 1;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>int32 diffCount = 2;</code>
     * @return The diffCount.
     */
    int getDiffCount();

    /**
     * <code>int32 finalCount = 3;</code>
     * @return The finalCount.
     */
    int getFinalCount();
  }
  /**
   * Protobuf type {@code model.PlayerItem}
   */
  public static final class PlayerItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.PlayerItem)
      PlayerItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerItem.newBuilder() to construct.
    private PlayerItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerItem() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.PlayerItemProto.internal_static_model_PlayerItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.PlayerItemProto.internal_static_model_PlayerItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.class, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder.class);
    }

    public static final int CSVID_FIELD_NUMBER = 1;
    private int csvId_;
    /**
     * <code>int32 csvId = 1;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int DIFFCOUNT_FIELD_NUMBER = 2;
    private int diffCount_;
    /**
     * <code>int32 diffCount = 2;</code>
     * @return The diffCount.
     */
    @java.lang.Override
    public int getDiffCount() {
      return diffCount_;
    }

    public static final int FINALCOUNT_FIELD_NUMBER = 3;
    private int finalCount_;
    /**
     * <code>int32 finalCount = 3;</code>
     * @return The finalCount.
     */
    @java.lang.Override
    public int getFinalCount() {
      return finalCount_;
    }

    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.PlayerItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.PlayerItem)
        ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.PlayerItemProto.internal_static_model_PlayerItem_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.PlayerItemProto.internal_static_model_PlayerItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.class, ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        csvId_ = 0;

        diffCount_ = 0;

        finalCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.PlayerItemProto.internal_static_model_PlayerItem_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem build() {
        ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem buildPartial() {
        ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem result = new ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem(this);
        result.csvId_ = csvId_;
        result.diffCount_ = diffCount_;
        result.finalCount_ = finalCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 1;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 1;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private int diffCount_ ;
      /**
       * <code>int32 diffCount = 2;</code>
       * @return The diffCount.
       */
      @java.lang.Override
      public int getDiffCount() {
        return diffCount_;
      }
      /**
       * <code>int32 diffCount = 2;</code>
       * @param value The diffCount to set.
       * @return This builder for chaining.
       */
      public Builder setDiffCount(int value) {
        
        diffCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 diffCount = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiffCount() {
        
        diffCount_ = 0;
        onChanged();
        return this;
      }

      private int finalCount_ ;
      /**
       * <code>int32 finalCount = 3;</code>
       * @return The finalCount.
       */
      @java.lang.Override
      public int getFinalCount() {
        return finalCount_;
      }
      /**
       * <code>int32 finalCount = 3;</code>
       * @param value The finalCount to set.
       * @return This builder for chaining.
       */
      public Builder setFinalCount(int value) {
        
        finalCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 finalCount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFinalCount() {
        
        finalCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.PlayerItem)
    }

    // @@protoc_insertion_point(class_scope:model.PlayerItem)
    private static final ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem();
    }

    public static ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PlayerItem>
        PARSER = new com.google.protobuf.AbstractParser<PlayerItem>() {
      @java.lang.Override
      public PlayerItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<PlayerItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.PlayerItemProto.PlayerItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_PlayerItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_PlayerItem_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\026model/PlayerItem.proto\022\005model\"B\n\nPlaye" +
      "rItem\022\r\n\005csvId\030\001 \001(\005\022\021\n\tdiffCount\030\002 \001(\005\022" +
      "\022\n\nfinalCount\030\003 \001(\005BL\n\037ehooray.fishing.d" +
      "to.proto.modelB\017PlayerItemProtoH\002\252\002\025Fish" +
      "ing.Network.Protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_PlayerItem_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_PlayerItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_PlayerItem_descriptor,
        new java.lang.String[] { "CsvId", "DiffCount", "FinalCount", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
