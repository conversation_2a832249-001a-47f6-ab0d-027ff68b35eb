package ehooray.fishing.component.logger;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.utils.ProtoToStringConverter;
import ehooray.fishing.config.SessionName;
import ehooray.fishing.dto.proto.request.RequestProto;
import ehooray.fishing.dto.proto.response.ResponseProto;

@Component
@Aspect
public class ProtoHandlerLogger
{
    @Autowired
    protected ProtoToStringConverter protoToStringConverter;

    @Pointcut("execution(* ehooray.fishing.component.ProtoHandler.handleRequest(*, *))")
    protected void handleRequest()
    {
    }

    @Before("handleRequest()")
    protected void before_handleRequest(JoinPoint joinPoint) throws Exception
    {
        RequestProto.Request request = (RequestProto.Request) joinPoint.getArgs()[0];
        ThreadContext.put(SessionName.RequestId, String.valueOf(request.getRequestId()));

        LogManager.getLogger(this.getClass()).info(protoToStringConverter.logRequestProto(request));
    }

    @AfterReturning(value = "handleRequest()", returning = "response")
    protected void afterReturning_handleRequest(JoinPoint joinPoint, ResponseProto.Response response)
    {
        LogManager.getLogger(this.getClass()).info(protoToStringConverter.logResponseProto(response));

        ThreadContext.remove(SessionName.RequestId);
    }

    @AfterThrowing(value = "handleRequest()", throwing="exception")
    protected void afterThrowing_handleRequest(JoinPoint joinPoint, Exception exception)
    {
        ThreadContext.remove(SessionName.RequestId);
    }

    @Before("execution(* ehooray.fishing.component.ProtoHandler.handleRequestException*(*, *))")
    protected void before_handleRequestException(JoinPoint joinPoint)
    {
        Throwable throwable = (Throwable) joinPoint.getArgs()[0];
        LogManager.getLogger(this.getClass()).error("handle known request exception", throwable);
    }

    @Pointcut("execution(* ehooray.fishing.component.ProtoHandler.handleUnknownException(*))")
    protected void handleUnknownException()
    {
    }

    @Before("handleUnknownException()")
    protected void before_handleUnknownException(JoinPoint joinPoint)
    {
        Throwable throwable = (Throwable) joinPoint.getArgs()[0];
        LogManager.getLogger(this.getClass()).error("unknown exception", throwable);
    }

    @AfterReturning(value = "handleUnknownException()", returning = "response")
    protected void afterReturning_handleUnknownException(JoinPoint joinPoint, ResponseProto.Response response)
    {
        LogManager.getLogger(this.getClass()).info(protoToStringConverter.logResponseProto(response));
    }
}