using UnityEngine;
using System.Collections;

///
/// !!! Machine generated code !!!
/// !!! DO NOT CHANGE Tabs to Spaces !!!
/// 
[System.Serializable]
public class FishTableData
{
  [SerializeField]
  int id;
  public int ID { get {return id; } set { this.id = value;} }
  
  [SerializeField]
  int name;
  public int Name { get {return name; } set { this.name = value;} }
  
  [SerializeField]
  int info;
  public int Info { get {return info; } set { this.info = value;} }
  
  [SerializeField]
  int rare;
  public int Rare { get {return rare; } set { this.rare = value;} }
  
  [SerializeField]
  int speed;
  public int Speed { get {return speed; } set { this.speed = value;} }
  
  [SerializeField]
  int hp;
  public int Hp { get {return hp; } set { this.hp = value;} }

  [SerializeField]
  int qtetime;
  public int QteTime { get {return qtetime; } set { this.qtetime = value;} }

  [SerializeField]
  int stuntime;
  public int Stuntime { get {return stuntime; } set { this.stuntime = value;} }
  
  [SerializeField]
  int sstrugglewt;
  public int Sstrugglewt { get {return sstrugglewt; } set { this.sstrugglewt = value;} }
  
  [SerializeField]
  int mstrugglewt;
  public int Mstrugglewt { get {return mstrugglewt; } set { this.mstrugglewt = value;} }
  
  [SerializeField]
  int lstrugglewt;
  public int Lstrugglewt { get {return lstrugglewt; } set { this.lstrugglewt = value;} }
  
  [SerializeField]
  int statemintime;
  public int Statemintime { get {return statemintime; } set { this.statemintime = value;} }
  
  [SerializeField]
  int statemaxtime;
  public int Statemaxtime { get {return statemaxtime; } set { this.statemaxtime = value;} }
  
  [SerializeField]
  int pull;
  public int Pull { get {return pull; } set { this.pull = value;} }
  
  [SerializeField]
  int extension;
  public int Extension { get {return extension; } set { this.extension = value;} }
  
  [SerializeField]
  int maxprice;
  public int Maxprice { get {return maxprice; } set { this.maxprice = value;} }
  
  [SerializeField]
  int minsize;
  public int Minsize { get {return minsize; } set { this.minsize = value;} }
  
  [SerializeField]
  int maxsize;
  public int Maxsize { get {return maxsize; } set { this.maxsize = value;} }
  
  [SerializeField]
  int randsizepct;
  public int Randsizepct { get {return randsizepct; } set { this.randsizepct = value;} }
  
  [SerializeField]
  int reducehppertime;
  public int Reducehppertime { get {return reducehppertime; } set { this.reducehppertime = value;} }
  
  [SerializeField]
  int exp;
  public int Exp { get {return exp; } set { this.exp = value;} }
  
  [SerializeField]
  string model;
  public string Model { get {return model; } set { this.model = value;} }
  
  [SerializeField]
  string icon;
  public string Icon { get {return icon; } set { this.icon = value;} }
  
  [SerializeField]
  int countdown;
  public int Countdown { get {return countdown; } set { this.countdown = value;} }
  
  [SerializeField]
  int nft;
  public int NFT { get {return nft; } set { this.nft = value;} }
  
}