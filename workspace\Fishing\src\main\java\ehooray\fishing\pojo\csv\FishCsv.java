package ehooray.fishing.pojo.csv;

import java.util.Arrays;
import java.util.Optional;

import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;

import ehooray.fishing.annotation.CsvPath;

@CsvPath("FishTable.xlsx")
public class FishCsv extends ACsv<FishCsv.Row>
{
    public enum Rare
    {
        C(1),
        B(2),
        A(3),
        S(4);
        
        protected int id;
        
        private Rare(int id)
        {
            this.id = id;
        }

        public int getId()
        {
            return id;
        }

        public static Optional<Rare> valueOf(int id)
        {
            return Arrays.stream(Rare.values()).filter(x -> x.getId() == id).findFirst();
        }
    }

    public static class Row extends ARow
    {
        @ExcelRow
        protected int rowIndex;
        
        @ExcelCellName("ID")
        protected int id;
        @ExcelCellName("Rare")
        protected int rare;
        @ExcelCellName("MaxPrice")
        protected int maxPrice;
        @ExcelCellName("MinSize")
        protected int minSize;
        @ExcelCellName("MaxSize")
        protected int maxSize;
        @ExcelCellName("RandSizePct")
        protected int randSizePct;
        @ExcelCellName("Exp")
        protected int exp;
        @ExcelCellName("item")
        protected int item;
        @ExcelCellName("Countdown")
        protected int countDown;
        @ExcelCellName("NFT")
        protected int nft;

        public int getRowIndex()
        {
            return rowIndex;
        }
        
        @Override
        public int getId()
        {
            return id;
        }

        public int getRare()
        {
            return rare;
        }

        public int getMaxPrice()
        {
            return maxPrice;
        }
        
        public int getMinSize()
        {
            return minSize;
        }
        
        public int getMaxSize()
        {
            return maxSize;
        }
        
        public int getRandSizePct()
        {
            return randSizePct;
        }
        
        public int getExp()
        {
            return exp;
        }
        
        public int getItem()
        {
            return item;
        }
        
        public int getCountDown()
        {
            return countDown;
        }
        
        public int getNFT()
        {
            return nft;
        }
        
        public boolean isNFT()
        {
            return nft != 0;
        }
    }

}
