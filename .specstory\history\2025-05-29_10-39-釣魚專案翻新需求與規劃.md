<!-- Generated by SpecStory -->

# 釣魚專案翻新需求與規劃 (2025-05-29 18:39:59)

_**User**_

公司目前釣魚專案需要做翻新，重新設計玩法等等，已經從零開始試作了一個html phaser的版本原型，但是目前要做翻新修改

@Luxury_Angler_GDD(updated).txt 這是phaser的遊戲設計文件
@Luxury_Angler_GDD_Unity.txt 及 @Luxury_Angler_GDD_Unity_zhtw.txt 是轉成unity後的文件

FishingPhaser是一個phaser的prototype釣魚遊戲，需求要轉移到舊有項目翻新，正式項目是使用spring boot+unity製作，請幫我看看有哪些適合放在java server，有哪些適合放在unity client，我初步看起來是inventory 玩家身上的equipment需要放在server使用database儲存，我們正式專案是使用mysql，而phaser版本是使用json當作資料，但是實際上是使用db當作玩家、裝備、道具資料；csv放在client才是某些物品的數值定義

你可以參考Fishing是spring boot專案 fishing-client是unity專案

請不要實作任何功能，先規劃就好

請依照提供的文件系統描述 需不需要修改，要不要實作，以利後續在專案管理dart上新增任務與task breakdown

---

_**Assistant**_



---

好的，這是一個將《Luxury Angler》從 Phaser 原型移植到 Spring Boot + Unity 架構的規劃，旨在明確伺服器端 (Java Server) 和客戶端 (Unity Client) 的職責，並為後續在專案管理工具 (如 Dart) 中建立任務提供基礎。

我將依照您提供的遊戲設計文件（主要參考 `Luxury_Angler_GDD_Unity_zhtw.txt` 並結合 `Luxury_Angler_GDD(updated).txt` 中的Phaser特定信息進行對比），分析各個系統的職責劃分。

**核心原則：**

*   **伺服器 (Spring Boot + MySQL):** 負責儲存所有玩家的持久性數據、核心遊戲邏輯的計算與驗證、以及需要安全控制的系統（如經濟、商店、成就）。MySQL 將作為主要的數據庫。
*   **客戶端 (Unity):** 負責遊戲的視覺聽覺呈現、玩家的直接交互操作、小遊戲的具體實現，以及從伺服器同步數據並展示。客戶端會使用 CSV/JSON 檔案來儲存靜態配置數據（如物品定義、魚類基礎屬性等）。

**系統職責劃分及規劃建議：**

| GDD 系統/子系統 (參考 Unity zhtw GDD) | 主要職責                                   | 實作位置    | 數據儲存                                     | 備註/修改建議 (針對GDD或實現)                                                                                                                                                              | GDD是否需修改 | 任務類型 (Dart參考)             |
| :------------------------------------ | :----------------------------------------- | :---------- | :------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------ | :------------------------------ |
| **1. 遊戲概述**                       | 遊戲背景、概念                             | N/A         | N/A                                          | 提供專案上下文。                                                                                                                                                                     | 否            | 文檔參考                        |
| **2. 核心遊戲迴圈**                   | 遊戲流程控制，關鍵狀態同步                   | Client/Server | Server (玩家狀態、時間、天氣、位置、體力)      | Client 發起行動，Server 驗證並更新核心狀態 (如時間推進、體力消耗)。Client 同步 Server 狀態顯示。                                                                                                  | 否            | Epic: 核心循環與狀態同步        |
| **3. 屬性系統**                       |                                            |             |                                              |                                                                                                                                                                                          |               | Epic: 屬性系統                  |
|   3.1.1 玩家升級屬性                  | 玩家等級、經驗、基礎屬性計算與儲存         | Server      | MySQL (玩家等級、經驗、基礎值)               | Server 負責等級提升邏輯、經驗計算、屬性點分配與儲存。Client 顯示屬性。                                                                                                                             | 否            | Task: 玩家屬性管理 (Server)     |
|   3.1.2 釣竿屬性                      | 裝備屬性定義、實例數據、加成計算           | Client/Server | Client CSV (屬性定義), MySQL (玩家擁有的裝備實例) | 釣竿等裝備的基礎屬性、等級對應數值等靜態數據存於 Client CSV。玩家獲得的特定釣竿實例及其強化等級存於 Server。最終屬性加成由 Server 計算或 Client 計算後 Server 驗證。                                   | 否            | Task: 裝備屬性 (Server/Client)  |
|   3.1.3 誘餌屬性                      | 同上 (釣竿屬性)                            | Client/Server | Client CSV (屬性定義), MySQL (玩家擁有的誘餌實例) | 同上。                                                                                                                                                                               | 否            | Task: 裝備屬性 (Server/Client)  |
|   3.1.4 比基尼助手屬性                | 同伴獲得、狀態、等級、提供的屬性加成         | Server      | MySQL (同伴數據、好感度、等級)             | 同伴的解鎖狀態、好感度、等級以及其提供的屬性加成由 Server 管理。                                                                                                                                     | 否            | Task: 同伴屬性與狀態 (Server)   |
|   3.1.5 船隻屬性                      | 同上 (釣竿屬性)。魚艙容量等影響伺服器邏輯 | Client/Server | Client CSV (屬性定義), MySQL (玩家擁有的船隻實例) | 自動釣魚相關屬性 (效率、產出) 的實際效果由 Server 控制。魚艙容量 (Fishtank Storage) 限制 Server 端的物品欄。                                                                                           | 否            | Task: 裝備屬性 (Server/Client)  |
|   3.2 魚類屬性                        | 魚的種類、基礎屬性定義、行為模式           | Client      | Client CSV (魚類靜態數據庫)                | 魚的ID、名稱、描述、稀有度分類、基礎屬性範圍 (大小、重量等)、偏好 (時間、天氣、誘餌) 等靜態數據存於 Client CSV。Server 在生成魚實例時使用這些定義。                                                           | 否            | Task: 魚類數據定義 (Client)     |
| **4. 釣魚系統**                       |                                            |             |                                              |                                                                                                                                                                                          |               | Epic: 釣魚核心玩法            |
|   4.1.1 投竿小遊戲                    | 小遊戲實現 (Client)，落點與結果判定 (Server) | Client/Server | Server (釣魚嘗試結果)                        | Client 負責小遊戲的視覺與操作。玩家操作結果 (如是否命中準確區) 發送至 Server。Server 根據此結果、玩家屬性、裝備屬性等綜合判定最終拋竿落點 (熱點/普通) 及是否觸發後續流程。                             | 輕微調整GDD強調Server判定 | Task: 投竿小遊戲 (Client/Server) |
|   4.1.2 誘餌小遊戲 (強化版)           | 小遊戲實現 (Client)，魚的反應與咬鉤判定 (Server) | Client/Server | Server (誘餌階段結果)                        | Client 負責誘餌模擬UI、魚影行為表現、玩家節奏輸入。Server 根據玩家操作、魚的AI、誘餌類型等判定魚是否感興趣、是否咬鉤。Lure Control Scheme 可配置於 Client CSV。                                              | 輕微調整GDD強調Server判定 | Task: 誘餌小遊戲 (Client/Server) |
|   4.1.3 收線小遊戲                    | 小遊戲實現、QTE (Client)，釣獲結果判定 (Server) | Client/Server | Server (釣獲的魚、物品欄更新)              | Client 負責張力條、魚的掙扎動畫、QTE操作。Server 根據玩家操作、魚的耐力、線的強度等判定是否成功釣獲，若成功則生成具體魚的實例 (隨機其精確屬性)並添加到玩家物品欄。魚的Stamina由Server管理。                   | 輕微調整GDD強調Server判定 | Task: 收線小遊戲 (Client/Server) |
|   4.2 時間系統                        | 時間流逝控制與狀態管理                     | Server      | MySQL (當前遊戲時間)                       | Server 作為時間的唯一權威，按規則推進遊戲內時間。Client 從 Server 同步時間並顯示。玩家的某些行動會請求 Server 推進時間 (如旅行、釣魚)。                                                                | 否            | Task: 時間系統 (Server)         |
|   4.3 天氣系統                        | 天氣變化控制與狀態管理                     | Server      | MySQL (當前遊戲天氣)                       | Server 作為天氣的唯一權威，按規則或隨機改變天氣。Client 從 Server 同步天氣並顯示。天氣會影響魚的出現率等，此邏輯在 Server 端應用。                                                                   | 否            | Task: 天氣系統 (Server)         |
|   4.4 地圖選擇 (GDD Section 4.4)      | 地圖數據管理，模式邏輯                     | Client/Server | Client CSV (地圖基礎信息), MySQL (玩家地圖解鎖狀態) | 地圖的靜態信息 (名稱、背景等) 存於 Client CSV。玩家已解鎖的地圖列表由 Server 管理。選擇故事模式或練習模式會影響 Server 端使用的魚池或觸發的事件。                                                              | 否            | Task: 地圖管理 (Server/Client)  |
| **5. 製作系統**                       |                                            |             |                                              |                                                                                                                                                                                          |               | Epic: 製作系統                |
|   5.1 合成機制 & 5.2 配方           | 配方展示 (Client)，合成操作與結果處理 (Server) | Client/Server | Client CSV (配方定義), MySQL (玩家材料、成品、製作隊列) | 配方數據 (所需材料、產出、時間、金錢) 存於 Client CSV 供玩家查看。玩家發起合成請求 -> Server 驗證材料、金錢 -> 扣除消耗 -> 添加製作任務到隊列 (若有製作時間) -> 完成後給予成品並更新物品欄。                 | 否            | Task: 合成功能 (Client/Server)  |
| **6. 等級進度**                       | 玩家等級、地圖解鎖、Boss戰狀態管理         | Server      | MySQL (玩家經驗、等級、劇情節點、Boss狀態)   | Server 管理玩家經驗獲取、升級。劇情進度 (如解鎖新地圖、Boss挑戰權) 由 Server 記錄和控制。魚的屬性隨玩家等級提升的規則在 Server 端應用。                                                               | 否            | Task: 等級與進度管理 (Server)   |
| **7. 物品欄和裝備**                 |                                            |             |                                              | GDD中已提到使用DB，這裡細化。                                                                                                                                                                |               | Epic: 物品與裝備系統          |
|   7.1 物品欄系統                      | 玩家物品數據的儲存與管理                   | Server      | MySQL (玩家完整的物品列表及數量)             | 所有玩家獲得的物品 (魚卡、裝備、材料、消耗品等) 及其數量都安全地儲存在 Server。Client 同步數據進行展示，並發起整理、出售、使用等操作請求。物品欄容量由 Server 控制。                                 | 確認GDD細節與DB一致 | Task: 物品欄核心 (Server)     |
|   7.2 裝備系統                        | 玩家裝備穿戴狀態管理，屬性疊加             | Server      | MySQL (玩家當前穿戴的裝備列表), Client CSV (裝備定義) | Server 記錄玩家在各個裝備槽位上穿戴的具體裝備實例。裝備的基礎定義在 Client CSV。穿戴裝備時的最終屬性加成由 Server 計算並告知 Client。                                                                 | 確認GDD細節與DB一致 | Task: 裝備穿戴與加成 (Server) |
| **8. 社交系統**                       |                                            |             |                                              |                                                                                                                                                                                          |               | Epic: 社交與劇情系統          |
|   8.1 郵輪聊天室                      | 同伴互動邏輯、好感度系統、獎勵解鎖         | Client/Server | MySQL (同伴好感度、已解鎖事件/HCG)       | 聊天界面和基礎對話選擇由 Client (Naninovel) 處理。贈送禮物、完成同伴任務等互動請求發往 Server，Server 更新好感度、記錄關鍵事件解鎖狀態 (如HCG場景)。                                                    | 否            | Task: 同伴互動系統 (Client/Server)|
|   8.2 故事和對話系統 (Naninovel)      | 劇情文本、分支、視覺小說引擎集成           | Client      | Client (Naninovel 專案文件, CSV/JSON for dialogs) | Naninovel 作為 Client 端視覺小說引擎，負責劇情展示、對話流程、分支選擇。影響遊戲全局狀態的關鍵選擇結果 (如解鎖新釣魚點、獲得特殊Buff) 需要通知 Server 記錄。                                              | 否            | Task: Naninovel集成 (Client)   |
| **9. 商店系統**                       | 商品數據管理、交易流程、貨幣管理           | Client/Server | MySQL (商品列表、價格、玩家貨幣、交易記錄)   | 商店的商品 (包括Fish Cards、裝備、寶石等) 及其價格、刷新邏輯由 Server 管理。Client 從 Server 獲取商品信息展示。購買操作：Client 發起 -> Server 驗證玩家貨幣 -> 扣款 -> 發放商品到物品欄。金幣、寶石等貨幣由 Server 嚴格管理。 | 否            | Task: 商店系統 (Client/Server)  |
| **10. 成就系統**                      | 成就定義、進度追蹤、達成判定、獎勵發放     | Client/Server | Client CSV (成就文本定義), MySQL (玩家成就進度、完成狀態) | 成就的描述、圖標等靜態數據存於 Client CSV。玩家行為觸發的成就進度更新由 Server 追蹤。Server 判定成就達成並發放獎勵 (金幣、寶石、特殊物品等)。                                                              | 否            | Task: 成就系統 (Client/Server)  |
| **11. 經濟系統**                      | 遊戲內貨幣的產出、消耗、總量控制           | Server      | MySQL (玩家貨幣總量、所有貨幣變動流水)       | 所有遊戲內貨幣 (金幣、寶石) 的產生 (釣魚、任務獎勵、出售物品、IAP) 和消耗 (購買、製作) 都必須經過 Server 處理和記錄，以保證經濟系統的平衡和安全。                                                         | 否            | Task: 經濟核心邏輯 (Server)   |
| **12. 美術和音訊**                    | 遊戲視覺與聽覺資源                         | Client      | Unity Assets                                 | 所有 3D 模型、2D UI 元素、動畫、特效、背景音樂、音效等都屬於 Client 端資源。GDD 中關於 Phaser 2D 的描述應更新為 Unity 的 3D 實現。                                                                   | 統一為3D描述  | Task: 美術與音訊資源集成(Client)|
| **13. 技術設計**                      | 整體架構、數據交互、第三方工具集成         | Client/Server | MySQL (Server DB), Client CSV/JSON (靜態配置) | 明確 Spring Boot 作為後端，Unity 作為前端。數據存儲分離：動態玩家數據在 Server MySQL，靜態配置數據在 Client。雲存檔通過 Server 實現。Naninovel 用於 Client 劇情。                                      | 強調數據分離模型 | Task: 整體架構設計與確認    |

**GDD 修改建議摘要:**

1.  **一致性更新**: 確保所有 GDD 版本（特別是最終給團隊參考的版本）在核心概念上一致，例如遊戲是 2D 還是 3D (應為 3D for Unity)，使用的對話引擎 (Naninovel for Unity)。
2.  **職責明確化**: 在各系統描述中，可以簡要提及數據的權威方 (Server) 和主要交互方 (Client)，避免混淆。例如，在釣魚小遊戲描述中，補充說明最終結果由伺服器判定。
3.  **數據存儲**: 明確 Phaser 版本中使用的 JSON 文件作為臨時數據存儲的方式，在正式的 Spring Boot + Unity 架構中，將由伺服器端的 MySQL 資料庫（用於玩家動態數據）和客戶端的 CSV/JSON 文件（用於遊戲靜態配置數據）取代。

此規劃應能幫助您將專案需求拆解為更具體的開發任務。祝您的專案順利！

---

_**User**_

@todo.md 請幫我把你以上的填寫在這

---

_**Assistant**_



---

好的，我將把先前規劃的內容轉換成 Markdown 格式，並填入 `todo.md` 檔案中。

我將使用標題來表示 Epic，並將各個子任務列在下方，同時標註其主要職責、實作位置和建議。

現在，我將編輯 `todo.md` 文件。

---

