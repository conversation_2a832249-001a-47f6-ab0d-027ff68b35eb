package ehooray.fishing.entity;

import javax.persistence.Cacheable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table
(
    name = "quest_target"
)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE) //Provide cache strategy.
public class QuestTargetEntity extends AEntity
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected long id;
    
    protected int csvId;   
    protected int targetCount;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "quest_id", nullable = false, referencedColumnName = "id")
    private QuestEntity quest;
    
    public long getId()
    {
        return id;
    }

    public void setId(long id)
    {
        this.id = id;
    }
    
    public QuestEntity getQuest()
    {
        return quest;
    }

    public void setQuest(QuestEntity quest)
    {
        this.quest = quest;
    }

    public int getCsvId()
    {
        return csvId;
    }

    public void setCsvId(int csvId)
    {
        this.csvId = csvId;
    }

    public int getTargetCount()
    {
        return targetCount;
    }

    public void setTargetCount(int targetCount)
    {
        this.targetCount = targetCount;
    }
    
}
