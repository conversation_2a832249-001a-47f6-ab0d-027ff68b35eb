package ehooray.fishing.pojo.event;

import java.util.List;

import org.springframework.context.ApplicationEvent;

import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;

@SuppressWarnings("serial")
public class UpgradeRodEvent extends ApplicationEvent
{
    private final PlayerEntity player;
    private final FishRodEntity fishRod;
    private final List<FishRodEntity> materials;

    public UpgradeRodEvent(Object source, PlayerEntity player, FishRodEntity fishRod, List<FishRodEntity> materials)
    {
        super(source);
        this.player = player;
        this.fishRod = fishRod;
        this.materials = materials;
    }

    public PlayerEntity getPlayer()
    {
        return player;
    }
    
    public FishRodEntity getFishRod()
    {
        return fishRod;
    }
    
    public List<FishRodEntity> getMaterials()
    {
        return materials;
    }
}
