using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using UnityEngine.UI;
using Ehooray;
using Ehooray.UI;
using Fishing.Network;
using Zenject;
using Cysharp.Threading.Tasks;
using Fishing.Network.Proto;

namespace Fishing.UI
{
    [AssetInfo("Prefabs/UI/MapUI/MapPanel")]
    public class MapPanel : GamePanel
    {
        public Button RetrunBtn;

        #region Zenject Inject
        SceneLoadingManager sceneLoadingManager;
        IResourceProvider provider;
        ServiceContainer serviceContainer;
        TableContainer tableContainer;
        LobbyContainer lobbyContainer;
        AssetPathConfig assetPathConfig;
        #endregion
        [Inject]
        public void Construct(
            SceneLoadingManager sceneLoadingManager,
            IResourceProvider provider,
            ServiceContainer serviceContainer,
            TableContainer tableContainer,
            LobbyContainer lobbyContainer,
            AssetPathConfig assetPathConfig
            )
        {
            Debug.Log("lobby choose inject");
            this.sceneLoadingManager = sceneLoadingManager;
            this.provider = provider;
            this.serviceContainer = serviceContainer;
            this.tableContainer = tableContainer;
            this.lobbyContainer = lobbyContainer;
            this.assetPathConfig = assetPathConfig;
        }

        private void Awake()
        {
            RetrunBtn.onClick.AddListener(ReturnPanel);
        }


        public void GoMap(int mapNumber)
        {
            var runner = GoMapRunner(mapNumber);
            runner.RunAsync(showMask: true).Forget();

        }

        public AsyncProgressRunner GoMapRunner(int mapNumber)
        {
            var runner = lobbyContainer.fishingContainer.runnerFactory.Create();
            runner.AddDefer(async () =>
            {
                var selectSpot = new SelectSpot() { CsvId = mapNumber };
                var selectSpotResult = await serviceContainer.battleService.SelectSpot(selectSpot);
                var mapData = tableContainer.SpotTable.GetData(mapNumber);
                GameObject obj = await provider.LoadResourceAsync<GameObject>(assetPathConfig.BattleMpaPrefabPath + mapData.Map);
                
                sceneLoadingManager.LoadSceneAsync("NewBattle", (container) =>
                {
                    container.BindInstance(mapData).WhenInjectedInto<BattleLevelManager>();
                    container.BindInstance(obj).WhenInjectedInto<BattleLevelManager>();
                    container.BindInstance(selectSpotResult).WhenInjectedInto<FishData>();
                }).Forget();
            });
            return runner;
        }
        private void ReturnPanel()
        {
            gamePanelManager.Close<MapPanel>();
        }

    }
}
