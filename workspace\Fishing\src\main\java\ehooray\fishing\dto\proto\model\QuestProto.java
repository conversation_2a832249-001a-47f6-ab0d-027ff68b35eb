// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: model/Quest.proto

package ehooray.fishing.dto.proto.model;

public final class QuestProto {
  private QuestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QuestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:model.Quest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    int getCsvId();

    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> 
        getTargetsList();
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getTargets(int index);
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    int getTargetsCount();
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    java.util.List<? extends ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder> 
        getTargetsOrBuilderList();
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder getTargetsOrBuilder(
        int index);

    /**
     * <code>bool isAward = 4;</code>
     * @return The isAward.
     */
    boolean getIsAward();
  }
  /**
   * Protobuf type {@code model.Quest}
   */
  public static final class Quest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:model.Quest)
      QuestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Quest.newBuilder() to construct.
    private Quest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Quest() {
      targets_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Quest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ehooray.fishing.dto.proto.model.QuestProto.Quest.class, ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder.class);
    }

    public interface TargetOrBuilder extends
        // @@protoc_insertion_point(interface_extends:model.Quest.Target)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>int32 csvId = 1;</code>
       * @return The csvId.
       */
      int getCsvId();

      /**
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      int getCount();
    }
    /**
     * Protobuf type {@code model.Quest.Target}
     */
    public static final class Target extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:model.Quest.Target)
        TargetOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Target.newBuilder() to construct.
      private Target(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Target() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Target();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_Target_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_Target_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.class, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder.class);
      }

      public static final int CSVID_FIELD_NUMBER = 1;
      private int csvId_;
      /**
       * <code>int32 csvId = 1;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }

      public static final int COUNT_FIELD_NUMBER = 2;
      private int count_;
      /**
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }

      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(ehooray.fishing.dto.proto.model.QuestProto.Quest.Target prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code model.Quest.Target}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:model.Quest.Target)
          ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_Target_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_Target_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.class, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder.class);
        }

        // Construct using ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          csvId_ = 0;

          count_ = 0;

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_Target_descriptor;
        }

        @java.lang.Override
        public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getDefaultInstanceForType() {
          return ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.getDefaultInstance();
        }

        @java.lang.Override
        public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target build() {
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Target result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target buildPartial() {
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Target result = new ehooray.fishing.dto.proto.model.QuestProto.Quest.Target(this);
          result.csvId_ = csvId_;
          result.count_ = count_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }

        private int csvId_ ;
        /**
         * <code>int32 csvId = 1;</code>
         * @return The csvId.
         */
        @java.lang.Override
        public int getCsvId() {
          return csvId_;
        }
        /**
         * <code>int32 csvId = 1;</code>
         * @param value The csvId to set.
         * @return This builder for chaining.
         */
        public Builder setCsvId(int value) {
          
          csvId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>int32 csvId = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearCsvId() {
          
          csvId_ = 0;
          onChanged();
          return this;
        }

        private int count_ ;
        /**
         * <code>int32 count = 2;</code>
         * @return The count.
         */
        @java.lang.Override
        public int getCount() {
          return count_;
        }
        /**
         * <code>int32 count = 2;</code>
         * @param value The count to set.
         * @return This builder for chaining.
         */
        public Builder setCount(int value) {
          
          count_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>int32 count = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearCount() {
          
          count_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:model.Quest.Target)
      }

      // @@protoc_insertion_point(class_scope:model.Quest.Target)
      private static final ehooray.fishing.dto.proto.model.QuestProto.Quest.Target DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.QuestProto.Quest.Target();
      }

      public static ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Target>
          PARSER = new com.google.protobuf.AbstractParser<Target>() {
        @java.lang.Override
        public Target parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(
                e.getMessage()).setUnfinishedMessage(
                    builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Target> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Target> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int CSVID_FIELD_NUMBER = 2;
    private int csvId_;
    /**
     * <code>int32 csvId = 2;</code>
     * @return The csvId.
     */
    @java.lang.Override
    public int getCsvId() {
      return csvId_;
    }

    public static final int TARGETS_FIELD_NUMBER = 3;
    private java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> targets_;
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    @java.lang.Override
    public java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> getTargetsList() {
      return targets_;
    }
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder> 
        getTargetsOrBuilderList() {
      return targets_;
    }
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    @java.lang.Override
    public int getTargetsCount() {
      return targets_.size();
    }
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getTargets(int index) {
      return targets_.get(index);
    }
    /**
     * <code>repeated .model.Quest.Target targets = 3;</code>
     */
    @java.lang.Override
    public ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder getTargetsOrBuilder(
        int index) {
      return targets_.get(index);
    }

    public static final int ISAWARD_FIELD_NUMBER = 4;
    private boolean isAward_;
    /**
     * <code>bool isAward = 4;</code>
     * @return The isAward.
     */
    @java.lang.Override
    public boolean getIsAward() {
      return isAward_;
    }

    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ehooray.fishing.dto.proto.model.QuestProto.Quest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ehooray.fishing.dto.proto.model.QuestProto.Quest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code model.Quest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:model.Quest)
        ehooray.fishing.dto.proto.model.QuestProto.QuestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ehooray.fishing.dto.proto.model.QuestProto.Quest.class, ehooray.fishing.dto.proto.model.QuestProto.Quest.Builder.class);
      }

      // Construct using ehooray.fishing.dto.proto.model.QuestProto.Quest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTargetsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;

        csvId_ = 0;

        if (targetsBuilder_ == null) {
          targets_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          targetsBuilder_.clear();
        }
        isAward_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ehooray.fishing.dto.proto.model.QuestProto.internal_static_model_Quest_descriptor;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.QuestProto.Quest getDefaultInstanceForType() {
        return ehooray.fishing.dto.proto.model.QuestProto.Quest.getDefaultInstance();
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.QuestProto.Quest build() {
        ehooray.fishing.dto.proto.model.QuestProto.Quest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ehooray.fishing.dto.proto.model.QuestProto.Quest buildPartial() {
        ehooray.fishing.dto.proto.model.QuestProto.Quest result = new ehooray.fishing.dto.proto.model.QuestProto.Quest(this);
        int from_bitField0_ = bitField0_;
        result.entityId_ = entityId_;
        result.csvId_ = csvId_;
        if (targetsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            targets_ = java.util.Collections.unmodifiableList(targets_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.targets_ = targets_;
        } else {
          result.targets_ = targetsBuilder_.build();
        }
        result.isAward_ = isAward_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int csvId_ ;
      /**
       * <code>int32 csvId = 2;</code>
       * @return The csvId.
       */
      @java.lang.Override
      public int getCsvId() {
        return csvId_;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @param value The csvId to set.
       * @return This builder for chaining.
       */
      public Builder setCsvId(int value) {
        
        csvId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 csvId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCsvId() {
        
        csvId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> targets_ =
        java.util.Collections.emptyList();
      private void ensureTargetsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          targets_ = new java.util.ArrayList<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target>(targets_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Target, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder, ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder> targetsBuilder_;

      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> getTargetsList() {
        if (targetsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(targets_);
        } else {
          return targetsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public int getTargetsCount() {
        if (targetsBuilder_ == null) {
          return targets_.size();
        } else {
          return targetsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target getTargets(int index) {
        if (targetsBuilder_ == null) {
          return targets_.get(index);
        } else {
          return targetsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder setTargets(
          int index, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target value) {
        if (targetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTargetsIsMutable();
          targets_.set(index, value);
          onChanged();
        } else {
          targetsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder setTargets(
          int index, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder builderForValue) {
        if (targetsBuilder_ == null) {
          ensureTargetsIsMutable();
          targets_.set(index, builderForValue.build());
          onChanged();
        } else {
          targetsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder addTargets(ehooray.fishing.dto.proto.model.QuestProto.Quest.Target value) {
        if (targetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTargetsIsMutable();
          targets_.add(value);
          onChanged();
        } else {
          targetsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder addTargets(
          int index, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target value) {
        if (targetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTargetsIsMutable();
          targets_.add(index, value);
          onChanged();
        } else {
          targetsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder addTargets(
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder builderForValue) {
        if (targetsBuilder_ == null) {
          ensureTargetsIsMutable();
          targets_.add(builderForValue.build());
          onChanged();
        } else {
          targetsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder addTargets(
          int index, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder builderForValue) {
        if (targetsBuilder_ == null) {
          ensureTargetsIsMutable();
          targets_.add(index, builderForValue.build());
          onChanged();
        } else {
          targetsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder addAllTargets(
          java.lang.Iterable<? extends ehooray.fishing.dto.proto.model.QuestProto.Quest.Target> values) {
        if (targetsBuilder_ == null) {
          ensureTargetsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, targets_);
          onChanged();
        } else {
          targetsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder clearTargets() {
        if (targetsBuilder_ == null) {
          targets_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          targetsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public Builder removeTargets(int index) {
        if (targetsBuilder_ == null) {
          ensureTargetsIsMutable();
          targets_.remove(index);
          onChanged();
        } else {
          targetsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder getTargetsBuilder(
          int index) {
        return getTargetsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder getTargetsOrBuilder(
          int index) {
        if (targetsBuilder_ == null) {
          return targets_.get(index);  } else {
          return targetsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public java.util.List<? extends ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder> 
           getTargetsOrBuilderList() {
        if (targetsBuilder_ != null) {
          return targetsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(targets_);
        }
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder addTargetsBuilder() {
        return getTargetsFieldBuilder().addBuilder(
            ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.getDefaultInstance());
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder addTargetsBuilder(
          int index) {
        return getTargetsFieldBuilder().addBuilder(
            index, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.getDefaultInstance());
      }
      /**
       * <code>repeated .model.Quest.Target targets = 3;</code>
       */
      public java.util.List<ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder> 
           getTargetsBuilderList() {
        return getTargetsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ehooray.fishing.dto.proto.model.QuestProto.Quest.Target, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder, ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder> 
          getTargetsFieldBuilder() {
        if (targetsBuilder_ == null) {
          targetsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ehooray.fishing.dto.proto.model.QuestProto.Quest.Target, ehooray.fishing.dto.proto.model.QuestProto.Quest.Target.Builder, ehooray.fishing.dto.proto.model.QuestProto.Quest.TargetOrBuilder>(
                  targets_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          targets_ = null;
        }
        return targetsBuilder_;
      }

      private boolean isAward_ ;
      /**
       * <code>bool isAward = 4;</code>
       * @return The isAward.
       */
      @java.lang.Override
      public boolean getIsAward() {
        return isAward_;
      }
      /**
       * <code>bool isAward = 4;</code>
       * @param value The isAward to set.
       * @return This builder for chaining.
       */
      public Builder setIsAward(boolean value) {
        
        isAward_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isAward = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAward() {
        
        isAward_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:model.Quest)
    }

    // @@protoc_insertion_point(class_scope:model.Quest)
    private static final ehooray.fishing.dto.proto.model.QuestProto.Quest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ehooray.fishing.dto.proto.model.QuestProto.Quest();
    }

    public static ehooray.fishing.dto.proto.model.QuestProto.Quest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Quest>
        PARSER = new com.google.protobuf.AbstractParser<Quest>() {
      @java.lang.Override
      public Quest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e.getMessage()).setUnfinishedMessage(
                  builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Quest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Quest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ehooray.fishing.dto.proto.model.QuestProto.Quest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Quest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Quest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_model_Quest_Target_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_model_Quest_Target_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021model/Quest.proto\022\005model\"\207\001\n\005Quest\022\020\n\010" +
      "entityId\030\001 \001(\003\022\r\n\005csvId\030\002 \001(\005\022$\n\007targets" +
      "\030\003 \003(\0132\023.model.Quest.Target\022\017\n\007isAward\030\004" +
      " \001(\010\032&\n\006Target\022\r\n\005csvId\030\001 \001(\005\022\r\n\005count\030\002" +
      " \001(\005BG\n\037ehooray.fishing.dto.proto.modelB" +
      "\nQuestProtoH\002\252\002\025Fishing.Network.Protob\006p" +
      "roto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_model_Quest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_model_Quest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Quest_descriptor,
        new java.lang.String[] { "EntityId", "CsvId", "Targets", "IsAward", });
    internal_static_model_Quest_Target_descriptor =
      internal_static_model_Quest_descriptor.getNestedTypes().get(0);
    internal_static_model_Quest_Target_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_model_Quest_Target_descriptor,
        new java.lang.String[] { "CsvId", "Count", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
