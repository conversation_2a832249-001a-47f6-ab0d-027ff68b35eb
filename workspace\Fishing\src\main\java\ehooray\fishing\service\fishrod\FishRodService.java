package ehooray.fishing.service.fishrod;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.entity.FishRodEntity;
import ehooray.fishing.entity.PlayerEntity;
import ehooray.fishing.pojo.ExceptionByCsvId;
import ehooray.fishing.pojo.csv.RodCsv;
import ehooray.fishing.pojo.csv.UpgradeCsv;
import ehooray.fishing.pojo.event.FishRodEvent;
import ehooray.fishing.pojo.event.UpgradeRodEvent;
import ehooray.fishing.pojo.utils.MathUtils;
import ehooray.fishing.repository.FishRodRepository;

@Service
public class FishRodService
{
    protected final CsvContainer csvContainer;
    protected final FishRodRepository fishRodRepository;
    protected final ApplicationEventPublisher publisher;
    
    @Autowired
    public FishRodService(CsvContainer csvContainer, FishRodRepository fishRodRepository, ApplicationEventPublisher publisher)
    {
        this.csvContainer = csvContainer;
        this.fishRodRepository = fishRodRepository;
        this.publisher = publisher;
    }

    public List<FishRodEntity> getAllByPlayer(PlayerEntity player)
    {
        return player.getFishRods();
    }

    public Optional<FishRodEntity> findByEntityId(PlayerEntity player, long entityId)
    {
        return getAllByPlayer(player).stream().filter(x -> x.getId() == entityId).findFirst();
    }
    
    public List<FishRodEntity> findByEntityIds(PlayerEntity player, Iterable<Long> ids) throws Exception
    {
        var result = new ArrayList<FishRodEntity>();
        for (long id : ids)
        {
            var findRod = findByEntityId(player, id);
            if (findRod.isEmpty())
                throw new ExceptionByCsvId(String.format("id:%d is not in player rods:%d", id, player.getId()));
            result.add(findRod.get());
        }
        
        return result;
    }

    public FishRodEntity create(PlayerEntity player, RodCsv.Row rodCsvRow) throws Exception
    {
        int totalPoint = MathUtils.randomRange(rodCsvRow.getPointMin(), rodCsvRow.getPointMax()+1); // max is exclusive, +1
        totalPoint = Math.max(totalPoint - rodCsvRow.getStrength(), 0);
        totalPoint = Math.max(totalPoint - rodCsvRow.getControl(), 0);
        totalPoint = Math.max(totalPoint - rodCsvRow.getLuck(), 0);
        totalPoint = Math.max(totalPoint - rodCsvRow.getDqr(), 0);
        
        List<Integer> randomNumbers = MathUtils.numbersWithSum(4, totalPoint);
        
        FishRodEntity newFishRod = new FishRodEntity();
        newFishRod.setCsvId(rodCsvRow.getId());
        newFishRod.setStrength(rodCsvRow.getStrength() + randomNumbers.get(0));
        newFishRod.setControl(rodCsvRow.getControl() + randomNumbers.get(1));
        newFishRod.setLucky(rodCsvRow.getLuck() + randomNumbers.get(2));
        newFishRod.setDqr(rodCsvRow.getDqr() + randomNumbers.get(3));
        newFishRod.setTimes(rodCsvRow.getTimes());
        newFishRod.setLock(false);
        newFishRod.setLevel(0);
        newFishRod.setPlayer(player);

        getAllByPlayer(player).add(newFishRod);
        fishRodRepository.save(newFishRod);
        
        publisher.publishEvent(new FishRodEvent(this, player, newFishRod)); // for log

        return newFishRod;
    }

    public void lockFishRod(FishRodEntity fishRod, boolean isLock) throws Exception
    {
        fishRod.setLock(isLock);
        fishRodRepository.save(fishRod);
    }
    
    public void repairFishRod(FishRodEntity fishRod, RodCsv.Row rodRowData) throws Exception
    {
        fishRod.setTimes(rodRowData.getTimes());
        fishRodRepository.save(fishRod);
        
        publisher.publishEvent(new FishRodEvent(this, fishRod.getPlayer(), fishRod)); // for log
    }
    
    public void sellFishRod(PlayerEntity player, FishRodEntity fishRod) throws Exception
    {
        if (fishRod.isLock())
            throw new ExceptionByCsvId(String.format("fishRod id:%d lock, cannot sell", fishRod.getId()));
        
        var fishCsvRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
        if (fishCsvRow.getSell() == 0)
            throw new ExceptionByCsvId(String.format("fishRod id:%d sell prize is 0, cannot sell", fishRod.getId()));
        
        getAllByPlayer(player).remove(fishRod);
        fishRodRepository.delete(fishRod);
        
        publisher.publishEvent(new FishRodEvent(this, fishRod.getPlayer(), fishRod)); // for log
    }
    
    public boolean upgradeFishRod(PlayerEntity player, FishRodEntity fishRod, List<FishRodEntity> materials) throws Exception
    {
        boolean successResult = upgradeSuccess(player, fishRod, materials);
        // remove materials
        for (var materialRod : materials)
        {
            var equipFishRod = player.getFishKits().stream()
                    .filter(kit -> kit.getFishRod().getId() == materialRod.getId()).findFirst();
            if (equipFishRod.isPresent())
                throw new ExceptionByCsvId(String.format("fishRod id:%d is equipped, cannot remove", materialRod.getId()));
            
            getAllByPlayer(player).remove(materialRod);
            fishRodRepository.delete(materialRod);
        }
         
        // upgrade
        if (successResult)
        {
            var rodRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
            int randomStat = MathUtils.randomRange(0, 4);
            switch (randomStat)
            {
                case 0: fishRod.setStrength(fishRod.getStrength() + rodRow.getLvUpPoint()); break;
                case 1: fishRod.setControl(fishRod.getControl() + rodRow.getLvUpPoint()); break;
                case 2: fishRod.setLucky(fishRod.getLucky() + rodRow.getLvUpPoint()); break;
                case 3: fishRod.setDqr(fishRod.getDqr() + rodRow.getLvUpPoint()); break;
            }
            
            fishRod.setLevel(fishRod.getLevel() + 1);
            
            fishRodRepository.save(fishRod);
            
            successResult = true;
        }
        
        publisher.publishEvent(new UpgradeRodEvent(this, player, fishRod, materials));
        
        return successResult;
    }

    public void consumeTimes(FishRodEntity fishRod, int times) throws Exception
    {
        if (times > fishRod.getTimes())
            throw new ExceptionByCsvId(String.format("fishRod id:%d can't consume %d times", fishRod.getId(), times));
        
        int remainTimes = fishRod.getTimes() - times;
        fishRod.setTimes(remainTimes);
        fishRodRepository.save(fishRod);
    }
    
    protected boolean upgradeSuccess(PlayerEntity player, FishRodEntity fishRod, List<FishRodEntity> materials)
    {
        int prob = 0;
        var rodRow = csvContainer.getCsv(RodCsv.class).getById(fishRod.getCsvId());
        var upgradeRow = csvContainer.getCsv(UpgradeCsv.class).findByRareAndLv(rodRow.getRare(), fishRod.getLevel());
        prob += upgradeRow.get().getUpgradeSuccessProb();
        
        for (var marteial : materials)
        {
            var mateialRodRow = csvContainer.getCsv(RodCsv.class).getById(marteial.getCsvId());
            var upgradeMateialRow = csvContainer.getCsv(UpgradeCsv.class).findByRareAndLv(mateialRodRow.getRare(), marteial.getLevel());
            prob += upgradeMateialRow.get().getMaterialBonus();
        }
            
        return prob > MathUtils.randomRange(0, 1000);
    }
}
