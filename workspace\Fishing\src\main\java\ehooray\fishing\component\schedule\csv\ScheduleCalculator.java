package ehooray.fishing.component.schedule.csv;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.pojo.csv.DisplayCsv;
import ehooray.fishing.pojo.csv.ScheduleCsv;
import ehooray.fishing.pojo.utils.LocalDateTimeUtils;

@Component
public class ScheduleCalculator
{
    @Autowired
    protected CsvContainer csvContainer;

    public boolean isOutStartCycle(DisplayCsv.Type displayType, int mapCsvId, LocalDateTime startTime, LocalDateTime now)
    {
        DisplayCsv displayCsv = csvContainer.getCsv(DisplayCsv.class);
        ScheduleCsv scheduleCsv = csvContainer.getCsv(ScheduleCsv.class);

        List<DisplayCsv.Row> displayRows = displayCsv.findByTypeAndMapId(displayType.getId(), mapCsvId);
        for(DisplayCsv.Row displayRow : displayRows)
        {
            // no schedule mean always in cycle
            Optional<ScheduleCsv.Row> scheduleRow = scheduleCsv.getOptionalById(displayRow.getSchedule());
            if(scheduleRow.isEmpty()) return false;
            
            if(scheduleRow.get().isOutStartCycle(startTime, now)) continue;
            
            // any schedule row is in cycle will enter here
            return false;
        }
        
        // only all out start cycle will return 
        return true;
    }
    
    public boolean isInCycle(DisplayCsv.Type displayType, int mapCsvId, LocalDateTime now)
    {
        return !isOutStartCycle(displayType, mapCsvId, now, now);
    }
    
    public Optional<LocalDateTime> getNextCycleStartDate(DisplayCsv.Type displayType, int mapCsvId, LocalDateTime now)
    {
        DisplayCsv displayCsv = csvContainer.getCsv(DisplayCsv.class);
        ScheduleCsv scheduleCsv = csvContainer.getCsv(ScheduleCsv.class);

        List<DisplayCsv.Row> displayRows = displayCsv.findByTypeAndMapId(displayType.getId(), mapCsvId);
        
        Optional<LocalDateTime> earliestCycleStartDate = Optional.empty();
        for(DisplayCsv.Row displayRow : displayRows)
        {
            // no schedule mean always in cycle
            Optional<ScheduleCsv.Row> scheduleRow = scheduleCsv.getOptionalById(displayRow.getSchedule());
            if(scheduleRow.isEmpty()) return Optional.empty();
            
            // any schedule row is in cycle will enter here
            Optional<LocalDateTime> nextCycleStartDate = scheduleRow.get().getNextCycleStartDate(now);
            if(nextCycleStartDate.isEmpty()) continue;
            if(earliestCycleStartDate.isEmpty())
            {
                earliestCycleStartDate = nextCycleStartDate;
                continue;
            }
            
            LocalDateTime earlierCycleStartDate = LocalDateTimeUtils.Min(earliestCycleStartDate.get(), nextCycleStartDate.get());
            earliestCycleStartDate = Optional.of(earlierCycleStartDate);
        }
        
        // only all out start cycle will return 
        return earliestCycleStartDate;
    }
}
