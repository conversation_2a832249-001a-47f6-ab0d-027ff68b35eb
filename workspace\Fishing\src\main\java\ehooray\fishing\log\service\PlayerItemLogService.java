package ehooray.fishing.log.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import ehooray.fishing.component.ProtoHandleMediatorContainer;
import ehooray.fishing.log.entity.PlayerItemLogEntity;
import ehooray.fishing.log.repository.PlayerItemLogRepository;
import ehooray.fishing.pojo.ProtoHandleMediator;
import ehooray.fishing.pojo.event.DiffItemEvent;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;

@Service
public class PlayerItemLogService
{
    @Autowired
    protected PlayerItemLogRepository playerItemLogRepository;
    
    @Autowired
    protected ProtoHandleMediatorContainer protoHandleMediatorContainer;

    @EventListener
    public PlayerItemLogEntity createLog(DiffItemEvent diffItemEvent)
    {
        DiffItem diffItem = diffItemEvent.getDiffItem();
        if(diffItem.getDiffCount() == 0) return null;
        
        ProtoHandleMediator mediator = protoHandleMediatorContainer.getMediator();
        
        PlayerItemLogEntity log = new PlayerItemLogEntity();
        log.setCsvId(diffItem.getPlayerItem().getCsvId());
        log.setAffectCount(diffItem.getDiffCount());
        log.setCurrentCount(diffItem.getPlayerItem().getCount());
        log.setFromSystem(mediator.getLogSource().getBehaviour().getSystemId());
        log.setBehaviour(mediator.getLogSource().getBehaviour().getId());
        log.setPlayerId(diffItem.getPlayerItem().getPlayer().getId());
        for(int i = 0, iMax = mediator.getLogSource().getBehaviourInputs().length; i < iMax; i++)
            log.setBehaviourInput(i, mediator.getLogSource().getBehaviourInputs()[i]);
        LocalDateTime now = mediator.getRequestStartTime();
        log.setCreateDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));

        playerItemLogRepository.save(log);

        return log;
    }
}
