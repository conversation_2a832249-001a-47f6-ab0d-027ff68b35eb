package ehooray.fishing.controller.battle;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.google.protobuf.Any;

import ehooray.fishing.annotation.TransactionMethod;
import ehooray.fishing.annotation.TransactionMethod.Method;
import ehooray.fishing.component.csv.CsvContainer;
import ehooray.fishing.component.protomap.model.FishItemProtoMap;
import ehooray.fishing.component.protomap.model.FishRodProtoMap;
import ehooray.fishing.component.protomap.model.PlayerItemProtoMap;
import ehooray.fishing.component.protomap.model.PlayerProtoMap;
import ehooray.fishing.dto.proto.request.selectspot.SelectSpotProto.SelectSpot;
import ehooray.fishing.dto.proto.request.startfish.StartFishProto;
import ehooray.fishing.dto.proto.response.battle.FailFishResultProto;
import ehooray.fishing.dto.proto.response.battle.RetryFishResultProto;
import ehooray.fishing.dto.proto.response.battle.SelectSpotResultProto;
import ehooray.fishing.dto.proto.response.battle.WinFishResultProto;
import ehooray.fishing.log.config.SystemBehaviourEnum;
import ehooray.fishing.log.pojo.LogSource;
import ehooray.fishing.pojo.WebSocketHandleMediator;
import ehooray.fishing.pojo.battle.HitArea;
import ehooray.fishing.pojo.csv.FishCsv;
import ehooray.fishing.pojo.csv.PlayerItemCsv;
import ehooray.fishing.pojo.csv.SpotCsv;
import ehooray.fishing.pojo.csv.VariableCsv;
import ehooray.fishing.service.battle.SpotService;
import ehooray.fishing.service.fishrod.FishRodService;
import ehooray.fishing.service.item.FishItemService;
import ehooray.fishing.service.item.PlayerItemService;
import ehooray.fishing.service.item.PlayerItemService.DiffItem;
import ehooray.fishing.service.player.PlayerService;

@Controller("BattleController")
public class BattleController
{
    @Autowired
    protected CsvContainer csvContainer;
    
    @Autowired
    protected SpotService spotService;
    @Autowired
    protected FishRodService fishRodService;
    @Autowired
    protected FishItemService fishItemService;
    @Autowired
    protected PlayerItemService playerItemService;
    @Autowired
    protected PlayerService playerService;
    
    @Autowired
    protected PlayerProtoMap playerProtoMap;
    @Autowired
    protected FishItemProtoMap fishItemProtoMap;
    @Autowired
    protected PlayerItemProtoMap playerItemProtoMap;
    @Autowired
    protected FishRodProtoMap fishRodProtoMap;
    
    
    @TransactionMethod(Method.Write)
    public void selectSpot(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var selectSpot = handleMediator.getRequest().getDataList(0).unpack(SelectSpot.class);
        var player = handleMediator.getPlayer().get();
        var spotCsvRow = csvContainer.getCsv(SpotCsv.class).getById(selectSpot.getCsvId());
        var findSpot = spotService.findByCsvId(player, spotCsvRow.getId());
        
        // action
        var battleSpot = findSpot.isPresent() ? findSpot.get() : spotService.createSpot(player, spotCsvRow);
        playerService.battleSpot(player, battleSpot);
        
        // response
        var resultBuilder = SelectSpotResultProto.SelectSpotResult.newBuilder();
        resultBuilder.setFishCsvId(battleSpot.getFishCsvId());
        resultBuilder.setRetryCount(battleSpot.getRetryFish());
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void startFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var startFish = handleMediator.getRequest().getDataList(0).unpack(StartFishProto.StartFish.class);
        var player = handleMediator.getPlayer().get();
        var hitArea = HitArea.valueOf(startFish.getHitArea()).get();
         
        // action
        spotService.startFish(player.getBattleSpot(), hitArea);
            
        // response
    }
    
    @TransactionMethod(Method.Write)
    public void winFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var player = handleMediator.getPlayer().get();
        var spotCsvRow = csvContainer.getCsv(SpotCsv.class).getById(player.getBattleSpot().getCsvId());
        var fishRow = csvContainer.getCsv(FishCsv.class).getById(player.getBattleSpot().getFishCsvId());
        var hitArea = HitArea.valueOf(player.getBattleSpot().getHitArea()).get();
        var fishLineItem = csvContainer.getCsv(PlayerItemCsv.class).getById(player.getActiveFishKit().getFishLine().getCsvId());
        var baitItem = csvContainer.getCsv(PlayerItemCsv.class).getById(player.getActiveFishKit().getBait().getCsvId()); 
        var apCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.Ap.getId());
        
        // action
        // consume rod times, fish line, bait
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.BattleController_winFish, spotCsvRow.getId()));
        var consumeItems = new ArrayList<DiffItem>();
        fishRodService.consumeTimes(player.getActiveFishKit().getFishRod(), 1);
        consumeItems.add(playerItemService.consumeItem(player, fishLineItem, 1));
        consumeItems.add(playerItemService.consumeItem(player, baitItem, 1));
        // cost ap
        var apDiffItem = playerItemService.consumeItem(player, apCsvRow, spotCsvRow.getApCost());
        consumeItems.add(apDiffItem);
        // produce fishItem, playerItem(temp), add player exp 
        var fishItem = fishItemService.produceFish(player, fishRow, hitArea, handleMediator.getRequestStartTime());
        var playerDiffExpResult = playerService.addExpToPlayer(player, fishRow.getExp());
        // random next fish
        spotService.endFish(player, spotCsvRow);    
              
        // response
        var resultBuilder = WinFishResultProto.WinFishResult.newBuilder();
        resultBuilder.setPlayer(playerProtoMap.toBuilderByDiffExp(playerDiffExpResult));
        resultBuilder.setFishItem(fishItemProtoMap.toBuilderByEntity(fishItem).build());
        resultBuilder.setFishRod(fishRodProtoMap.toProtoByEntity(player.getActiveFishKit().getFishRod()));
        resultBuilder.addAllConsumePlayerItems(playerItemProtoMap.toProtoByDiffItem(consumeItems));
        resultBuilder.setNextFishCsvId(player.getBattleSpot().getFishCsvId());
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void failFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var player = handleMediator.getPlayer().get();
        var spotCsvRow = csvContainer.getCsv(SpotCsv.class).getById(player.getBattleSpot().getCsvId());
        var fishLineItem = csvContainer.getCsv(PlayerItemCsv.class).getById(player.getActiveFishKit().getFishLine().getCsvId());
        var baitItem = csvContainer.getCsv(PlayerItemCsv.class).getById(player.getActiveFishKit().getBait().getCsvId());
        var apCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.Ap.getId());
        
        // action
        // consume rod times, fish line, bait
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.BattleController_failFish, spotCsvRow.getId()));
        var consumeItems = new ArrayList<DiffItem>();
        fishRodService.consumeTimes(player.getActiveFishKit().getFishRod(), 1);
        consumeItems.add(playerItemService.consumeItem(player, fishLineItem, 1));
        consumeItems.add(playerItemService.consumeItem(player, baitItem, 1));
        // cost ap
        var apDiffItem = playerItemService.consumeItem(player, apCsvRow, spotCsvRow.getApCost());
        consumeItems.add(apDiffItem);
        // random next fish
        spotService.endFish(player, spotCsvRow);
        
        // response
        var resultBuilder = FailFishResultProto.FailFishResult.newBuilder();
        resultBuilder.setPlayer(playerProtoMap.toBuilderByEntity(player));
        resultBuilder.setFishRod(fishRodProtoMap.toProtoByEntity(player.getActiveFishKit().getFishRod()));
        resultBuilder.addAllConsumePlayerItems(playerItemProtoMap.toProtoByDiffItem(consumeItems));
        resultBuilder.setNextFishCsvId(player.getBattleSpot().getFishCsvId());
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
    
    @TransactionMethod(Method.Write)
    public void retryFish(WebSocketHandleMediator handleMediator) throws Exception
    {
        // request
        var player = handleMediator.getPlayer().get();
        var spotCsvRow = csvContainer.getCsv(SpotCsv.class).getById(player.getBattleSpot().getCsvId());
        var coinCsvRow = csvContainer.getCsv(PlayerItemCsv.class).findByTag(PlayerItemCsv.Tag.GameCoin.getId());
        
        // action
        // consume coin
        handleMediator.setLogSource(new LogSource(SystemBehaviourEnum.BattleController_retryFish, spotCsvRow.getId()));
        int coinBase = csvContainer.getCsv(VariableCsv.class).getContCost();
        double contMf = Math.pow(csvContainer.getCsv(VariableCsv.class).getContCostMF() * 0.001d, player.getBattleSpot().getRetryFish());
        int coinAfterMF = (int) Math.round(coinBase * contMf);
        var diffItem = playerItemService.consumeItem(player, coinCsvRow, coinAfterMF);
        // add retry count
        spotService.retryFish(player.getBattleSpot(), player.getBattleSpot().getRetryFish() + 1);
        
        // response
        var resultBuilder = RetryFishResultProto.RetryFishResult.newBuilder();
        resultBuilder.setRetryCount(player.getBattleSpot().getRetryFish());
        resultBuilder.setConsumePlayerItem(playerItemProtoMap.toProtoByDiffItem(diffItem));
        handleMediator.addResult(Any.pack(resultBuilder.build()));
    }
}
